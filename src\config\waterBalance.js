/**
 * Find in an object array, where `id` matches the id of some object in the array, return that object if the match succeeds.
 * @param {int} id
 * @param {[{id: int, name: string}]} enumArray
 * @returns
 */
export const findEnumItem = (id, enumArray) => {
    return enumArray.find(item => id === item.id);
};

// 常规水源类型
export const convWaterSourceTypeEnum = [
    { id: 1, name: "自来水" },
    { id: 2, name: "地表水" },
    { id: 3, name: "地下水" },
    { id: 4, name: "外购软化水" },
    { id: 5, name: "外购蒸汽" },
    { id: 6, name: "外购水" },
];

// 非常规水源类型
export const unconvWaterSourceTypeEnum = [
    { id: 101, name: "海水" },
    { id: 102, name: "苦咸水" },
    { id: 103, name: "城镇污水再生水" },
    { id: 104, name: "矿井水" },
    { id: 105, name: "雨水" },
];

// 水源类型
export const waterSourceTypeEnum = [...convWaterSourceTypeEnum, ...unconvWaterSourceTypeEnum,];
export const findWaterSource = id => findEnumItem(id, waterSourceTypeEnum);

// 准确度级别
// 一级: 误差+/-0.5%, 二级: 误差+/-2%, 智能水表: ABCD级
export const waterMeterAccuracyLevelEnum = [{ id: 1, name: "一级" }, { id: 2, name: "二级" }, { id: 101, name: "A(智能水表)" }, { id: 102, name: "B(智能水表)" }, { id: 103, name: "C(智能水表)" }, { id: 104, name: "D(智能水表)" }, ];

// 计量器具类型
export const meterDeviceTypeEnum = [{ id: 1, name: "水计量器具" }, { id: 0, name: "其它计量器具" },];

// 水表接入管道级别
export const meterPipeLevelEnum = [{ id: 1, name: "一级(总水表)" }, { id: 2, name: "二级水表" }, { id: 3, name: "三级水表" }, { id: 4, name: "四级水表" }, { id: 5, name: "五级水表" },];

// 设备状态
export const waterMeterEquipStateEnum = [{ id: 1, name: "合格" }, { id: 2, name: "不合格" },];
// 设备状态-表
export const waterMeterEquipStateEnum4Table = [{ id: 1, name: "正常" }, { id: 2, name: "不正常" },];

// 检验周期单位: 0未指定, 1日, 2月, 3年
export const waterMeterCalibrateUnitEnum = [{ id: 2, name: "月" }, { id: 3, name: "年" },];

// 是否接入企业用水在线监测平台
export const monitoredStateEnum = [{ id: 1, name: "是" }, { id: 2, name: "否" },];

// 工业用水分类
export const waterClassEnum = [{ id: 1, name: "主要生产用水" }, { id: 2, name: "辅助生产用水" }, { id: 3, name: "附属生产用水" },];
export const industryWaterClassEnum = waterClassEnum;
// 服务业用水分类
export const serviceWaterClassEnum = [{ id: 1, name: "主要功能用水" }, { id: 2, name: "辅助功能用水" }, { id: 3, name: "附属功能用水" },];
// 根据用水分类编号返回其名称
export const findIndustryWaterClassName = id => findEnumItem(id, industryWaterClassEnum).name;
export const findServiceWaterClassName = id => findEnumItem(id, serviceWaterClassEnum).name;


// 主要用途
export const waterMainUsageEnum = [
    { id: 1,  name: "主要生产用水" },
    { id: 2,  name: "辅助生产用水" },
    { id: 3,  name: "附属生产用水" },
    { id: 11, name: "生产用水" },
    { id: 12, name: "生活用水" },
];

// 根据行业类型获得水源主要用途枚举
export const getWaterMainUsageEnum = indType => {
    if (indType == 1) {
        return waterMainUsageEnum;
    } else if (indType == 2) {
        return [
            { id: 1,  name: "主要功能用水" },
            { id: 2,  name: "辅助功能用水" },
            { id: 3,  name: "附属功能用水" },
        ];
    } else {
        return waterMainUsageEnum;
    }
};

// 水量单位
export const waterUnitScaleEnum = [{ id: 0, name: "m³" }, { id: 4, name: "万m³" },];

// 产量单位
export const productOutputUnitScaleEnum = [{ id: 0, name: "t/d" }, { id: 4, name: "万t/d" },];
// 产能单位
export const productCapacityScaleEnum = [{ id: 0, name: "t/a" }, { id: 4, name: "万t/a" },];
// 通用单位
export const commonScaleEnum = [{ id: 0, name: "×1" }, { id: 4, name: "×10000" },];

/**
 * 生成最近几年的枚举数组, 本年度数据表示测试期间的采样(默认7日)
 * @param {int} years
 * @returns
 */
const genComProductionStatYearEnum = (years, thisYearTips = "") => {
    const currDate = new Date();
    const currYear = currDate.getFullYear();
    let yearEnum = [];

    for (let i = 0; i < years; i++) {
        if(i ==0) {
            yearEnum.push({ id: currYear, name: `${currYear}` + (thisYearTips ? `(${thisYearTips})` : thisYearTips) });
        } else {
            yearEnum.push({ id: currYear - i, name: `${currYear - i}` });
        }

    }

    return yearEnum;
};
// 表8  企业近三年生产情况统计表 年份
//export const comProductionStatYearEnum = [{ id: 2019, name: "2019" }, { id: 2020, name: "2020" }, { id: 2021, name: "2021" }, { id: 2022, name: "2022" }, { id: 2023, name: "2023" }, { id: 2024, name: "2024" }, { id: 7, name: "测试期间7天" },];
//export const comProductionStatYearEnum = genComProductionStatYearEnum(6, "测试7日期间");
export const comProductionStatYearEnum_WithTips = genComProductionStatYearEnum(6, "测试7日期间");
export const comProductionStatYearEnum_WithoutTips = genComProductionStatYearEnum(6, "");

// 客户资料清单-生产系统
export const prodSystemEnum = [{ id: 1, name: "单班" }, { id: 2, name: "两班" }, { id: 3, name: "三班" },];

// 单位缩放
export const clientUnitScaleEnum = [{ id: 0, name: "×1" }, { id: 1, name: "×10" }, { id: 2, name: "×100" }, { id: 3, name: "×1,000" }, { id: 4, name: "×10,000" }, { id: 5, name: "×100,000" }, { id: 6, name: "×1,000,000" }];

// 用水定额参考
// 子行业类型(用水定额参考中选定的项目类型)
export const subindustryEnum = [
    { id: 1, name: "医院" },
    { id: 2, name: "学校" },
    { id: 3, name: "机关" },
    { id: 4, name: "酒店-住宿业" },
    { id: 5, name: "写字楼" },
    { id: 6, name: "餐饮" },
    { id: 7, name: "商超" },
    { id: 8, name: "文化娱乐" },
    { id: 0, name: "其他服务业" },
];
export const instHasCanteenEnum   = [{ id: 0, name: "无" }, { id: 1, name: "有" }, ]; // 机关是否有食堂
export const instHasDormitoryEnum = [{ id: 0, name: "无" }, { id: 1, name: "有" }, ]; // 机关是否有宿舍
export const instHasBathroomEnum  = [{ id: 0, name: "无" }, { id: 1, name: "有" }, ]; // 机关是否有浴室
export const commonBinaryEnum     = [{ id: 0, name: "否" }, { id: 1, name: "是" }, ]; // 通用的二义枚举
export const hospitalGradeEnum = [                                                   // 医院级别
    {id: 31, name: "三甲"}, {id: 32, name: "三乙"}, {id: 33, name: "三丙"},
    {id: 21, name: "二甲"}, {id: 22, name: "二乙"}, {id: 23, name: "二丙"},
    {id: 11, name: "一甲"}, {id: 12, name: "一乙"}, {id: 13, name: "一丙"},
];

// 服务业, 单位运营情况统计表
// 服务类别, 101机关(基本) 102机关(附加)-绿化 201酒店-住宿业 301学校类 401医院类-住院 402医院类-门诊 501写字楼 601餐饮 701商场/超市 801文化娱乐
export const serviceTypeEnum = [
    { id: 101, name: "机关(基本)" },
    { id: 102, name: "机关(附加)-绿化" },
    { id: 201, name: "酒店-住宿业" },
    { id: 301, name: "学校类" },
    { id: 401, name: "医院类-住院" },
    { id: 402, name: "医院类-门诊" },
    { id: 501, name: "写字楼" },
    { id: 601, name: "餐饮" },
    { id: 701, name: "商场/超市" },
    { id: 801, name: "文化娱乐(影剧院、歌剧院、图书馆、博物馆、文化站、文化馆酒吧、夜总会、歌舞厅、网吧)" },
];

// 主要用水设备、设施一览表
export const workStatusEnum = [{ id: 1, name: "正常" }, { id: 2, name: "异常" }, { id: 3, name: "停用" },];
export const usingTypeEnum  = [{ id: 1, name: "自来水" }, { id: 2, name: "串联水" }, { id: 3, name: "循环水" }, { id: 4, name: "净水" }, { id: 5, name: "软水" }, { id: 6, name: "回用水" }, { id: -1, name: "其它" },];

// 主要生产用水
// 设备用水时间, 0默认, 1常规, 2间歇, 3季节
export const equipRumtimeEnum = [{id: 1, name: "常规"}, {id: 2, name: "间歇"}, {id: 3, name: "季节"}];
// 测试方法, 0默认, 1水表读数法, 2超声波流量计法, 3调查法
export const testMethodEnum   = [{id: 1, name: "水表读数法"}, {id: 2, name: "超声波流量计法"}, {id: 3, name: "调查法"}];

// 用途
// 生活用水和绿化用水是因为<服务业表>的用水量计算表中需要统计生活用水量和绿化用水量, 进沟通, 绿化用水不属于生活用水, 因此不存在交集
// 中央空调用水是因为<服务业书>的用水单位用水分析表含有中央空调冷却水补水率
export const intakePurposeEnum = [{id: -1, name: "其它"}, {id: 1, name: "生活用水"}, {id: 2, name: "绿化用水"}, {id: 3, name: "中央空调用水"}];


// 是否节水设备
export const waterSavePStateEnum = [{ id: 0, name: "否" }, { id: 1, name: "是" },];
