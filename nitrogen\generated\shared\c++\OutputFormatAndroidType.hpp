///
/// OutputFormatAndroidType.hpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc Rousavy @ Margelo
///

#pragma once

#include <cmath>
#if __has_include(<NitroModules/JSIConverter.hpp>)
#include <NitroModules/JSIConverter.hpp>
#else
#error NitroModules cannot be found! Are you sure you installed NitroModules properly?
#endif
#if __has_include(<NitroModules/NitroDefines.hpp>)
#include <NitroModules/NitroDefines.hpp>
#else
#error NitroModules cannot be found! Are you sure you installed NitroModules properly?
#endif

namespace margelo::nitro::react_native_audio_recorder_player {

  /**
   * An enum which can be represented as a JavaScript enum (OutputFormatAndroidType).
   */
  enum class OutputFormatAndroidType {
    DEFAULT      SWIFT_NAME(default) = 0,
    THREE_GPP      SWIFT_NAME(threeGpp) = 1,
    MPEG_4      SWIFT_NAME(mpeg4) = 2,
    AMR_NB      SWIFT_NAME(amrNb) = 3,
    AMR_WB      SWIFT_NAME(amrWb) = 4,
    AAC_ADIF      SWIFT_NAME(aacAdif) = 5,
    AAC_ADTS      SWIFT_NAME(aacAdts) = 6,
    OUTPUT_FORMAT_RTP_AVP      SWIFT_NAME(outputFormatRtpAvp) = 7,
    MPEG_2_TS      SWIFT_NAME(mpeg2Ts) = 8,
    WEBM      SWIFT_NAME(webm) = 9,
  } CLOSED_ENUM;

} // namespace margelo::nitro::react_native_audio_recorder_player

namespace margelo::nitro {

  using namespace margelo::nitro::react_native_audio_recorder_player;

  // C++ OutputFormatAndroidType <> JS OutputFormatAndroidType (enum)
  template <>
  struct JSIConverter<OutputFormatAndroidType> final {
    static inline OutputFormatAndroidType fromJSI(jsi::Runtime& runtime, const jsi::Value& arg) {
      int enumValue = JSIConverter<int>::fromJSI(runtime, arg);
      return static_cast<OutputFormatAndroidType>(enumValue);
    }
    static inline jsi::Value toJSI(jsi::Runtime& runtime, OutputFormatAndroidType arg) {
      int enumValue = static_cast<int>(arg);
      return JSIConverter<int>::toJSI(runtime, enumValue);
    }
    static inline bool canConvert(jsi::Runtime&, const jsi::Value& value) {
      if (!value.isNumber()) {
        return false;
      }
      double integer;
      double fraction = modf(value.getNumber(), &integer);
      if (fraction != 0.0) {
        // It is some kind of floating point number - our enums are ints.
        return false;
      }
      // Check if we are within the bounds of the enum.
      return integer >= 0 && integer <= 9;
    }
  };

} // namespace margelo::nitro
