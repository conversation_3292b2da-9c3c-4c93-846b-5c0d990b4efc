///
/// HybridAudioRecorderPlayerSpec.hpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc Rousavy @ Margelo
///

#pragma once

#include <NitroModules/JHybridObject.hpp>
#include <fbjni/fbjni.h>
#include "HybridAudioRecorderPlayerSpec.hpp"




namespace margelo::nitro::react_native_audio_recorder_player {

  using namespace facebook;

  class JHybridAudioRecorderPlayerSpec: public jni::HybridClass<JHybridAudioRecorderPlayerSpec, JHybridObject>,
                                        public virtual HybridAudioRecorderPlayerSpec {
  public:
    static auto constexpr kJavaDescriptor = "Lcom/margelo/nitro/reactnativeaudiorecorderplayer/HybridAudioRecorderPlayerSpec;";
    static jni::local_ref<jhybriddata> initHybrid(jni::alias_ref<jhybridobject> jThis);
    static void registerNatives();

  protected:
    // C++ constructor (called from Java via `initHybrid()`)
    explicit JHybridAudioRecorderPlayerSpec(jni::alias_ref<jhybridobject> jThis) :
      HybridObject(HybridAudioRecorderPlayerSpec::TAG),
      _javaPart(jni::make_global(jThis)) {}

  public:
    ~JHybridAudioRecorderPlayerSpec() override {
      // Hermes GC can destroy JS objects on a non-JNI Thread.
      jni::ThreadScope::WithClassLoader([&] { _javaPart.reset(); });
    }

  public:
    size_t getExternalMemorySize() noexcept override;

  public:
    inline const jni::global_ref<JHybridAudioRecorderPlayerSpec::javaobject>& getJavaPart() const noexcept {
      return _javaPart;
    }

  public:
    // Properties
    

  public:
    // Methods
    std::shared_ptr<Promise<std::string>> startRecorder(const std::optional<std::string>& uri, const std::optional<AudioSet>& audioSets, std::optional<bool> meteringEnabled) override;
    std::shared_ptr<Promise<std::string>> pauseRecorder() override;
    std::shared_ptr<Promise<std::string>> resumeRecorder() override;
    std::shared_ptr<Promise<std::string>> stopRecorder() override;
    std::shared_ptr<Promise<std::string>> startPlayer(const std::optional<std::string>& uri, const std::optional<std::unordered_map<std::string, std::string>>& httpHeaders) override;
    std::shared_ptr<Promise<std::string>> stopPlayer() override;
    std::shared_ptr<Promise<std::string>> pausePlayer() override;
    std::shared_ptr<Promise<std::string>> resumePlayer() override;
    std::shared_ptr<Promise<std::string>> seekToPlayer(double time) override;
    std::shared_ptr<Promise<std::string>> setVolume(double volume) override;
    std::shared_ptr<Promise<std::string>> setPlaybackSpeed(double playbackSpeed) override;
    void setSubscriptionDuration(double sec) override;
    void addRecordBackListener(const std::function<void(const RecordBackType& /* recordingMeta */)>& callback) override;
    void removeRecordBackListener() override;
    void addPlayBackListener(const std::function<void(const PlayBackType& /* playbackMeta */)>& callback) override;
    void removePlayBackListener() override;
    std::string mmss(double secs) override;
    std::string mmssss(double milisecs) override;

  private:
    friend HybridBase;
    using HybridBase::HybridBase;
    jni::global_ref<JHybridAudioRecorderPlayerSpec::javaobject> _javaPart;
  };

} // namespace margelo::nitro::react_native_audio_recorder_player
