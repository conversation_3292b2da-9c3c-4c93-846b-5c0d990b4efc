///
/// AudioSourceAndroidType.swift
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON> @ Margelo
///

/**
 * Represents the JS enum `AudioSourceAndroidType`, backed by a C++ enum.
 */
public typealias AudioSourceAndroidType = margelo.nitro.react_native_audio_recorder_player.AudioSourceAndroidType

public extension AudioSourceAndroidType {
  /**
   * Get a AudioSourceAndroidType for the given String value, or
   * return `nil` if the given value was invalid/unknown.
   */
  init?(fromString string: String) {
    switch string {
      case "DEFAULT":
        self = .default
      case "MIC":
        self = .mic
      case "VOICE_UPLINK":
        self = .voiceUplink
      case "VOICE_DOWNLINK":
        self = .voiceDownlink
      case "VOICE_CALL":
        self = .voiceCall
      case "CAMCORDER":
        self = .camcorder
      case "VOICE_RECOGNITION":
        self = .voiceRecognition
      case "VOICE_COMMUNICATION":
        self = .voiceCommunication
      case "REMOTE_SUBMIX":
        self = .remoteSubmix
      case "UNPROCESSED":
        self = .unprocessed
      case "RADIO_TUNER":
        self = .radioTuner
      case "HOTWORD":
        self = .hotword
      default:
        return nil
    }
  }

  /**
   * Get the String value this AudioSourceAndroidType represents.
   */
  var stringValue: String {
    switch self {
      case .default:
        return "DEFAULT"
      case .mic:
        return "MIC"
      case .voiceUplink:
        return "VOICE_UPLINK"
      case .voiceDownlink:
        return "VOICE_DOWNLINK"
      case .voiceCall:
        return "VOICE_CALL"
      case .camcorder:
        return "CAMCORDER"
      case .voiceRecognition:
        return "VOICE_RECOGNITION"
      case .voiceCommunication:
        return "VOICE_COMMUNICATION"
      case .remoteSubmix:
        return "REMOTE_SUBMIX"
      case .unprocessed:
        return "UNPROCESSED"
      case .radioTuner:
        return "RADIO_TUNER"
      case .hotword:
        return "HOTWORD"
    }
  }
}
