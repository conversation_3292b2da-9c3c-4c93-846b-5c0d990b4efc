import React from "react";
import { DataTable, Text, useTheme } from "react-native-paper";
import { Controller } from "react-hook-form";
import { Pressable, StyleSheet, View } from "react-native";
import { ErrorMessage } from "@hookform/error-message";
import ControlledCellRangeInput from "./ControlledCellRangeInput";
import { isFunction, isObject } from "../utils";
//import PropTypes from "prop-types";

/**
 * DataTable中一个2列的记录行, 左边是文本标签列, 右边是数据列.
 * 数据列的思想是, radio选项是一个形如{name, id}的对象,
 * react-hook-form的control使用id部分, 显示文本使用name部分, mmkv存储整个对象.
 * @param {Object} arg
 * @param {string} arg.rowLabel
 * @param {function} arg.onDialogConfirm
 * @param {boolean} arg.disabled
 * @param {"flat" | "outlined"} arg.mode
 */
const ControlledRangeInput = ({ name, rowLabel, control, placeholder, onDialogConfirm, okBtnLabel, cancelBtnLabel, dialogState, setDialogState, toolTip, editable = true, style={}, rowStyle = {}, ...props }) => {
    const theme = useTheme();
    const styles = StyleSheet.create({
        container: {
            paddingLeft: 4,
            paddingRight: 0,
            marginLeft: 0,
            marginRight: -8,
            //borderWidth: 1,
            //height: 60,
            //borderColor: "black",
        },
        firstColumn: {
            flex: 1,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 2,
            //borderWidth: 1,
            // height: 40,
        },
        inputBox: {
            width: "100%"
        },
        errMsg: {
            fontSize: 16,
            color: theme.colors.error,
        },
        ...(isObject(style) ? style : {}),
        ...rowStyle,
    });

    return (
        <DataTable.Row style={styles.container}>
            <DataTable.Cell style={styles.firstColumn}>
                {toolTip ?
                    <Pressable onPress={()=>{
                        isFunction(props.setSnackbarMessage) && props.setSnackbarMessage(toolTip);
                        isFunction(props.setDisplaySnackbar) && props.setDisplaySnackbar(true);
                    }}>
                        <Text style={styles.firstColumn.content}>
                            {(props.required ? rowLabel + " *" : rowLabel) + " ℹ️"}
                        </Text>
                    </Pressable> :
                    <Text style={styles.firstColumn.content}>
                        {props.required ? rowLabel + " *" : rowLabel}
                    </Text>}
            </DataTable.Cell>
            <DataTable.Cell style={styles.secondColumn}>
                <Controller
                    control={control}
                    render={({ field: { onChange, value } }) => (
                        <View style={styles.inputBox}>
                            <ControlledCellRangeInput
                                title={rowLabel}
                                placeholder={placeholder}
                                //controlledValue={value}
                                editable={editable}
                                autoCapitalize="none"
                                autoComplete="off"
                                textContentType="none"
                                onDialogConfirm={(value) => { // value: {"id": 1, "name": "DEPT-01"}
                                    console.log("onDialogConfirm", value);
                                    onChange(value);      // value.id保存到control
                                    onDialogConfirm(value);   // 整个value对象保存到mmkv
                                }}
                                okBtnLabel={okBtnLabel}
                                cancelBtnLabel={cancelBtnLabel}
                                dialogState={dialogState}
                                setDialogState={setDialogState}
                                {...props}
                            />
                            {props?.formError &&
                                <ErrorMessage
                                    errors={props.formError}
                                    name={name}
                                    render={({ message }) => {
                                        return (
                                            <Text style={styles.errMsg}>
                                                {"⚠ " + message}
                                            </Text>);
                                    }}
                                />
                            }
                        </View>
                    )}
                    name={name}
                    rules={{ required: true }}
                />

            </DataTable.Cell>
        </DataTable.Row>
    );
};


export default ControlledRangeInput;
