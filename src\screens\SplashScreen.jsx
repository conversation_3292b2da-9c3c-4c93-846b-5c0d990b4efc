import PropTypes from "prop-types";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { ActivityIndicator, StyleSheet } from "react-native";
import { useShallow } from "zustand/shallow";
import { useTokenLogin } from "../api/userLogin";
import Background from "../components/Background";
import Header from "../components/Header";
import Logo from "../components/Logo";
import { userLoginState } from "../hooks/loginStoredState";
import log from "../services/logging";
import { assignPermits } from "../utils/permits";


/**
 * 通过token登录, 登录后更新token, 并将登录返回的用户信息写入本地存储.
 * 在用户信息存储中, roles是"工作台->角色权限管理"中的自定义项目的编号, permits是定义在服务端, 位于*role-bit-mask*的权限细项
 * @param {object} args
 * @returns
 */
const SplashScreen = ({navigation}) => {
    const [animating, setAnimating] = useState(true);
    const { t } = useTranslation(["screenSplash"]);

    const [
        userToken, setUserToken,
        userPubid, setUserPubid,
        userName, setUserName,
        userMobile, setUserMobile,
        setUserRoles,
        setUserPermits,
        setUserDepartment,
        setUserPosition,
        setUserType,
        setLatestVersion,
    ] = userLoginState(useShallow((state) => [state.token, state.setToken, state.pubid, state.setPubid, state.name, state.setName, state.mobile, state.setMobile, state.setRoles, state.setPermits, state.setDepartment, state.setPosition, state.setUserType, state.setLatestVersion]));

    const { isSuccess, isError, status, error } = useTokenLogin((json) => {
        setUserToken(json.token);
        setUserPubid(json.pubid);
        setUserName(json.name);
        setUserMobile(json.mobile);
        setUserRoles(json.roles);
        setUserPermits(json.permits);
        setUserDepartment(json.department);
        setUserPosition(json.position);
        setUserType(json.userType);
        setLatestVersion(json.latestVersion);

        assignPermits(json.permits || []);
    });

    //log.debug("<SplashScreen> query status: %s, userToken: %s, pubid: %s, name: %s, mobile: %s", status, userToken, userPubid, userName, userMobile);
    if (status === "error") {
        log.info("<SplashScreen> login failed with error: <%s>, token: %s", `${error.name}: ${error.message}`, userToken);
    }

    useEffect(() => {
        if(isSuccess) {
            setAnimating(false);
            log.debug("token verify passed, goto HomeScreen");
            navigation.replace("HomeScreen");
            return;
        }
    }, [isSuccess, navigation]);

    useEffect(() => {
        if (isError) {
            setAnimating(false);
            log.debug("token verify failed, goto LoginScreen");
            navigation.replace("LoginScreen");
        }
    }, [isError, navigation]);

    return (
        <Background>
            <Logo />
            <Header>{t("screenSplash:header")}</Header>
            <ActivityIndicator
                animating={animating}
                color="#888888"
                size="large"
                style={styles.activityIndicator}
            />
        </Background>
    );
};

SplashScreen.propTypes = {
    navigation: PropTypes.shape({
        replace: PropTypes.func.isRequired,
    }).isRequired,
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "#307ecc",
    },
    activityIndicator: {
        alignItems: "center",
        height: 80,
    },
});

export default SplashScreen;
