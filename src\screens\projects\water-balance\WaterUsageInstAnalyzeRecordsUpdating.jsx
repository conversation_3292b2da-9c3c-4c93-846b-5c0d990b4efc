import { joi<PERSON>esolver } from "@hookform/resolvers/joi";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { Alert, StyleSheet, View } from "react-native";
import { Snackbar } from "react-native-paper";
//import debounce from "lodash/debounce";
import "fast-text-encoding";
import <PERSON><PERSON> from "joi";
import Button from "../../../components/Button";
import { DialogToConfirm } from "../../../components/DialogToConfirm";
import HeaderBar from "../../../components/HeaderBar";
import { LoadingIndicator } from "../../../components/LoadingIndicator";
import ScreenWrapper from "../../ScreenWrapper";
//import ControlledTextInput from "../../../components/ControlledTextInput";
import { FormListNodesMapper } from "../../../components/FormListNodesMapper";
import { creatMMKVStore } from "../../../services/local-storage";
import { callOneByOne, makeDataFeeder } from "../../../utils";
import { checkPermits } from "../../../utils/permits";
import { makeGetClientCversion, radioIdToObject } from "../../../utils/records";
import { validatorBase } from "../../../utils/validatorBase";


// 新组件需要重新!!
import { useShallow } from "zustand/shallow";
import {
    makeReqDelWaterBalanceRecords as recordDeleteQueryMaker,
    makeReqGetWaterBalanceRecords as recordSelectQueryMaker,
    makeReqUpdWaterBalanceRecords as recordUpdateQueryMaker,
} from "../../../api/projectRecordsQueries";
import { UPD_WATER_BALANCE_RECORDS as pageMainKey } from "../../../config/keysConfig";
import { waterClassEnum } from "../../../config/waterBalance";
import { wbWaterUsageInstAnalyzeStates as selectorStates } from "../../../hooks/selectorStates";
import { onPreSubmitError } from "../../../utils/screens";


const dataFeeder = makeDataFeeder();

/**
 * 用水单位用水分析(似乎已废弃)
 * 注意, 项目数据表单与工作台管理表单的数据有一个显著的区别:
 * 项目数据不需要像用户编号那样脱敏, 两边传输的数据是直接的, 不需要做{id, name}这种映射, 部分需要映射的可以在客户端本地完成,
 * @param {object} args
 * @param {object} args.navigation
 * @param {object} args.route
 * @returns
 */
const RecordsUpdating = ({ navigation, route }) => {
    console.log("RecordsUpdating pageMeta from nav:", route.params.pageMeta);
    console.log("RecordsUpdating projMeta from nav:", route.params.projMeta);

    const { queryKwd, id: recordPubid, name: recordName } = route.params.pageMeta; // recordPubid is the alias of id
    const saveButtonIcon = "content-save-all-outline";
    const formDisabledGlobal = checkPermits() ? false : true;

    // 使用memoize来优化
    const { setStore, getStore, clearStore, setStoreObject } = useMemo(() => {
        console.log("creatMMKVStore memo:", pageMainKey.store, queryKwd, recordPubid);
        return creatMMKVStore(`${pageMainKey.store}/${queryKwd}/${recordPubid}`);
    }, [queryKwd, recordPubid]);

    //const storedValueToFormValue = useRef(makeStoredValueToFormValue(getStore));
    const getClientCversion = useRef(makeGetClientCversion(() => getStore("mainCversion"), () => getStore("subCversion")));

    // 客户端cversion = `${mainCversion}.${subCversion}`
    (getStore("mainCversion") === undefined) ? setStore("mainCversion", 0) : undefined; // 客户端主版本号
    (getStore("subCversion")  === undefined) ? setStore("subCversion",  0) : undefined; // 客户端次版本号, 修改一次自增一次
    //let mainCversion = getStore("mainCversion"); // 主版本号, 与服务端cversion对应
    // 次版本号, 表示本地更改次数, 用于程序上对子版本号的增加, 用于判断与服务端版本的差异, 同时可用于判断避免重复从MMKV获取数据
    const subCversionRef = useRef(getStore("subCversion"));

    // 新组件需要修改!!
    const bottomBarLabels = { ok: "提交" };                    // 底部按钮显示文字
    const [snackBarMessage, setSnackBarMessage] = useState("更新表单遇到错误"); // 下方错误提示文字内容

    const [
        waterClassRadioState,
        setWaterClassRadioState,
        resetSelectorStates,
    ] = selectorStates(useShallow(state => [
        state.waterClassRadio,
        state.setWaterClassRadio,
        state.resetStates,
    ])); // radio组件状态

    const [confimDialogConfig, setConfirmDialogConfig] = useState({});       // 对话框信息, {title, text, okLabel, onOK}, 但显示对话框的变量在showConfirm

    // 新组件不需改动
    const [screenTitle,      setScreenTitle]      = useState(route.params.pageMeta.name); // 屏幕上方导航栏标题
    const [okButtonLoading,  setOkButtonLoading]  = useState(false);            // 右下方按钮图标是否旋转
    const [okButtonDisabled, setOkButtonDisabled] = useState(!checkPermits());  // 右下方按钮是否禁用
    const [showSnackbar,     setShowSnackbar]     = useState(false);            // 下方是否显示错误通知
    const [showConfirm,      setShowConfirm]      = useState(false);            // 是否显示确认对话框
    const [loadingIndicatorVisible, setLoadingIndicatorVisible] = useState(false); // 是否显示loading
    const delQueryInfo = useRef({ id: recordPubid, name: recordName });         // 用于删除本记录的查询变量

    // 用水分类: 主要生产用水, 辅助生产用水, 附属生产用水
    const waterClassStateStateDataProviderRef = useRef(waterClassEnum);
    // 原始excel注释: 自来水、市政水、地表水、水库、蒸汽等等常规水源, 这里为了与前面枚举匹配, 调整了顺序
    //const convWaterSourceTypeStateDataProviderRef = useRef([{ id: 1, name: "自来水" }, { id: 2, name: "地表水" }, { id: 3, name: "地下水" }, { id: 4, name: "市政水" }, { id: 5, name: "水库水" }, { id: 6, name: "蒸汽" }]);
    // 原始excel注释: 本单位污水处理站处理后的中水、市政污水处理厂产出的中水、雨水等非常规水源
    //const unconvWaterSourceTypeStateDataProviderRef = useRef([{ id: 1, name: "本单位污水处理站处理后的中水" }, { id: 2, name: "市政污水处理厂产出的中水" }, { id: 3, name: "雨水"}]);

    const onRecordSelectSuccess = (data) => { // data: {"id": 3, "leader": 0, "name": "DEPT-03", "superior": 0}
        // 注意, 进入onSuccess之前就意味着服务端版本比客户端版本新, 因此要更新客户端主版本号
        console.log("onRecordSelectSuccess:", data);
        if (data.STATUS === 0) {
            setStore("mainCversion", data.DATA.cversion);
            setStore("subCversion", 0);
            const formObject  = {
                name:           String(data.DATA.name),
                waterClass:     String(data.DATA.waterClass),
                //waterSubclass:  String(data.DATA.waterSubclass),
                waterUsageUnit: String(data.DATA.waterUsageUnit),
                waterInput:     String(data.DATA.waterInput),
                inputPercent:   String(data.DATA.inputPercent),
                waterIntake:    String(data.DATA.waterIntake),
                intakePercent:  String(data.DATA.intakePercent),
                recycleWater:   String(data.DATA.recycleWater),
                dumpSewage:     String(data.DATA.dumpSewage),
                dumpOutside:    String(data.DATA.dumpOutside),
                waterOutput:    String(data.DATA.waterOutput),
                waterLeakage:   String(data.DATA.waterLeakage),
                //others:         String(data.DATA.others),
                //remarks:        String(data.DATA.remarks),
            };
            reset(formObject);           // 重置react-form

            const storeObjects = checkPermits() && {
                ...formObject,
                // selector fields
                waterClass:        radioIdToObject(waterClassStateStateDataProviderRef.current, data.DATA.waterClass),
            };
            storeObjects && setStoreObject(storeObjects, false); // 判断用户权限, 把服务端数据写到mmkv存储, 只有可写用户才需要本地存储, 不会首先清空老数据

            // 设置selector菜单状态
            setWaterClassRadioState(storeObjects.waterClass);

            // 设置屏幕标题
            (screenTitle !== formObject.name) && setScreenTitle(formObject.name);
        } else  {
            console.log("STATUS !== 0", data.STATUS);
            setSnackBarMessage(`表单数据拉取发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        }
    };
    const onRecordSelectError = (err) => {
        console.log("Record selecting error:", err);
    };
    const onRecordSelectSettled = (err) => {
        loadingIndicatorVisible && setLoadingIndicatorVisible(false);
    };
    const recordSelectQuery = recordSelectQueryMaker([queryKwd, "get", recordPubid], onRecordSelectSuccess, onRecordSelectError, onRecordSelectSettled);

    // 新组件需要重新定义!!
    const schema = Joi.object({
        name:           validatorBase.waterBalance.waterUsageInstAnalyze.name,
        waterClass:     validatorBase.waterBalance.waterUsageInstAnalyze.waterClass,
        //waterSubclass:  validatorBase.waterBalance.waterUsageInstAnalyze.intField,
        waterUsageUnit: validatorBase.waterBalance.waterUsageInstAnalyze.textField,
        waterInput:     validatorBase.waterBalance.waterUsageInstAnalyze.floatField,
        inputPercent:   validatorBase.waterBalance.waterUsageInstAnalyze.percentField,
        waterIntake:    validatorBase.waterBalance.waterUsageInstAnalyze.floatField,
        intakePercent:  validatorBase.waterBalance.waterUsageInstAnalyze.percentField,
        recycleWater:   validatorBase.waterBalance.waterUsageInstAnalyze.floatField,
        dumpSewage:     validatorBase.waterBalance.waterUsageInstAnalyze.floatField,
        dumpOutside:    validatorBase.waterBalance.waterUsageInstAnalyze.floatField,
        waterOutput:    validatorBase.waterBalance.waterUsageInstAnalyze.floatField,
        waterLeakage:   validatorBase.waterBalance.waterUsageInstAnalyze.floatField,
        //others:  validatorBase.waterBalance.waterBalanceInspectStats.textField,
        //remarks: validatorBase.waterBalance.waterBalanceInspectStats.longTextField,
    });

    // 新组件需要重新定义defaultValues!!
    const {
        handleSubmit,
        control,
        resetField,
        reset,
        formState: { errors },
        //setValue,
        //getValues,
    } = useForm({
        resolver: joiResolver(schema),
        //reValidateMode: "onBlur", // https://github.com/orgs/react-hook-form/discussions/10327
        defaultValues: { // 默认值在组件载入时会被更新, 这里的初始值正常情况下没有用处
            name:           "",
            waterClass:     "",
            //waterSubclass: "",
            waterUsageUnit: "",
            waterInput:     "",
            inputPercent:   "",
            waterIntake:    "",
            intakePercent:  "",
            recycleWater:   "",
            dumpSewage:     "",
            dumpOutside:    "",
            waterOutput:    "",
            waterLeakage:   "",
            //others:         "",
            //remarks:        "",
        },
    });

    // remarks不需配置
    const FieldsConfig = [
        { inputs: [
            { name: "name", label: "表单名称", unit: "", type: "PLAIN", editable: true, },
            { name: "waterClass", label: "用水分类", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: waterClassRadioState, setSelectorState: setWaterClassRadioState, dataProvider: waterClassStateStateDataProviderRef },
            { name: "waterUsageUnit", label: "主要生产用水", unit: "", type: "PLAIN", editable: true, placeholder: "", },
            { name: "waterInput", label: "用水量", unit: "m³", type: "PLAIN", editable: true, placeholder: "", },
            { name: "inputPercent", label: "占总用水量比例", unit: "%", type: "PLAIN", editable: true, placeholder: "", },
            { name: "waterIntake", label: "取水量", unit: "m³", type: "PLAIN", editable: true, placeholder: "", },
            { name: "intakePercent", label: "占总取水量比例", unit: "%", type: "PLAIN", editable: true, placeholder: "", },
            { name: "recycleWater", label: "重复利用水量", unit: "m³", type: "PLAIN", editable: true, placeholder: "", },
            { name: "dumpSewage", label: "排至污水处理站水量", unit: "m³", type: "PLAIN", editable: true, placeholder: "", },
            { name: "dumpOutside", label: "排水量(排至厂外)", unit: "m³", type: "PLAIN", editable: true, placeholder: "", },
            { name: "waterOutput", label: "耗水量", unit: "m³", type: "PLAIN", editable: true, placeholder: "", },
            { name: "waterLeakage", label: "漏损水量", unit: "m³", type: "PLAIN", editable: true, placeholder: "", },
        ]},
    ];

    // 新组件需要重新定义setDiagText!!
    const onHandleSubmit = (data) => {
        setOkButtonDisabled(true);
        setOkButtonLoading(true);
        dataFeeder({...data, cversion: getClientCversion.current()}); // append client cversion
        recordUpdateQuery.mutate();
    };

    // 新组件不需改动
    const onRecordUpdateSuccess = (data) => {
        console.log("response success, data: ", data);
        if(data.STATUS !== 0) {
            setSnackBarMessage(`表单更新发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        } else {
            // 设置确认框数据
            setConfirmDialogConfig({
                title: "更新表单",
                text: "表单信息更新成功!",
                okLabel: "确定",
                onOK: () => {
                    setShowConfirm(false);
                    recordSelectQuery.mutate();
                }});
            setShowConfirm(true);
        }
    };
    const onRecordUpdateError = (error) => {
        setSnackBarMessage(`表单更新发生错误, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const onRecordUpdateSettled = (data, error) => {
        setOkButtonDisabled(false);
        setOkButtonLoading(false);
    };
    const recordUpdateQuery = recordUpdateQueryMaker(dataFeeder, [queryKwd, "upd", recordPubid], onRecordUpdateSuccess, onRecordUpdateError, onRecordUpdateSettled);

    useEffect(() => {
        recordSelectQuery.mutate();
    }, []);

    const recordDeleteOnSuccess = () => {
        clearStore();
        navigation.goBack();
    };
    const recordDeleteOnError = (error) => {
        setSnackBarMessage(`表单删除失败, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const recordDeleteQuery = recordDeleteQueryMaker([queryKwd, "del", recordPubid], recordDeleteOnSuccess, recordDeleteOnError);

    const deleteAlert = () =>{
        return (
            Alert.alert("删除表单", `删除后不可撤销, 请确认是否删除: ${delQueryInfo.current?.name}?`, [
                { text: "取消" },
                { text: "确定", onPress: () => recordDeleteQuery.mutate() },
            ])
        );
    };

    const clearCache = () => {
        return (
            Alert.alert("清空本地缓存", "1. 用于未知原因导致数据无法提交的情况\r\n2. 未提交的内容将会丢失, 请做好数据截图备份\r\n3. 已提交到服务端的数据不受影响\r\n4. 确认清空请点击“确认”按钮", [
                { text: "取消" },
                { text: "确定", onPress: () => { clearStore(); navigation.goBack(); } },
            ])
        );
    };

    return (
        <View style={{ flex: 1 }}>

            <HeaderBar
                //title={screenTitle}
                navigation={navigation}
                goBackCallback={() => {
                    callOneByOne(
                        //() => { _mainCversionGetter=undefined; _subCversionGetter=undefined;},
                        resetSelectorStates,
                    );
                    /*callOneByOne(clearStore, resetSelectorStates)*/  // 返回前清空mmkv存储因为临时数据不再需要, 重置状态使得页面切换后不会使用前面的数据
                }}
                menuItemArray={[{ title: "删除表单", action: deleteAlert }, { title: "清空缓存", action: clearCache }]}
            />

            <ScreenWrapper contentContainerStyle={styles.container}>

                <View style={styles.formEntry}>
                    <FormListNodesMapper
                        fieldsConfig={FieldsConfig}
                        subCversionRef={subCversionRef}
                        control={control}
                        setStore={setStore}
                        resetField={resetField}
                        errors={errors}
                        setSnackBarMessage={setSnackBarMessage}
                        setShowSnackbar={setShowSnackbar}
                        formDisabledGlobal={formDisabledGlobal}
                        rowStyle={styles.rowContainer}
                    />

                </View>

                <View style={styles.button}>
                    <Button mode="contained"
                        disabled={okButtonDisabled}
                        loading={okButtonLoading}
                        icon={saveButtonIcon}
                        onPress={handleSubmit(onHandleSubmit, (formErr) => onPreSubmitError(formErr, getStore, "WaterUsageInstAnalyzeRecordsUpdating"))}
                    >{bottomBarLabels.ok}</Button>
                </View>
            </ScreenWrapper>

            <LoadingIndicator
                title={""}
                message={"加载中....."}
                visible={loadingIndicatorVisible}
                onClose={()=>setLoadingIndicatorVisible(false)}
            />

            <Snackbar
                style={styles.snackBar}
                visible={showSnackbar}
                onDismiss={() => setShowSnackbar(false)}
                onIconPress={() => setShowSnackbar(false)}
                duration={Snackbar.DURATION_LONG}
            >
                {snackBarMessage}
            </Snackbar>

            <DialogToConfirm
                visible={showConfirm}
                title={confimDialogConfig.title ? confimDialogConfig.title : ""}
                text={confimDialogConfig.text ? confimDialogConfig.text : ""}
                onOK={confimDialogConfig.onOK ? confimDialogConfig.onOK : () => setShowConfirm(false)}
                okBtnLabel={confimDialogConfig.okLabel ? confimDialogConfig.okLabel : "确认"}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    rowContainer:{
        firstColumn: {
            flex: 1,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 1,
        // /borderWidth: 1,
        // height: 40,
        },
    },
    rowContainerLongText: {
        firstColumn: {
            flex: 1,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 4,
            // /borderWidth: 1,
            // height: 40,
        },
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        width: "80%",
        alignSelf: "center",
    },
    formEntry: {
        margin: 8,
    },
    snackBar: {
        flexDirection: "column",
    },
});

export default RecordsUpdating;
