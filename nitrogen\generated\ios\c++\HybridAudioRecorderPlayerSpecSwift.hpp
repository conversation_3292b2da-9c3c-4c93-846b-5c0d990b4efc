///
/// HybridAudioRecorderPlayerSpecSwift.hpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc Rousavy @ Margelo
///

#pragma once

#include "HybridAudioRecorderPlayerSpec.hpp"

// Forward declaration of `HybridAudioRecorderPlayerSpec_cxx` to properly resolve imports.
namespace ReactNativeAudioRecorderPlayer { class HybridAudioRecorderPlayerSpec_cxx; }

// Forward declaration of `AudioSet` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { struct AudioSet; }
// Forward declaration of `AudioSourceAndroidType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AudioSourceAndroidType; }
// Forward declaration of `OutputFormatAndroidType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class OutputFormatAndroidType; }
// Forward declaration of `AudioEncoderAndroidType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AudioEncoderAndroidType; }
// Forward declaration of `AVEncoderAudioQualityIOSType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AVEncoderAudioQualityIOSType; }
// Forward declaration of `AVModeIOSOption` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AVModeIOSOption; }
// Forward declaration of `AVEncodingOption` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AVEncodingOption; }
// Forward declaration of `AVLinearPCMBitDepthKeyIOSType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AVLinearPCMBitDepthKeyIOSType; }
// Forward declaration of `AudioQualityType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AudioQualityType; }
// Forward declaration of `RecordBackType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { struct RecordBackType; }
// Forward declaration of `PlayBackType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { struct PlayBackType; }

#include <NitroModules/Promise.hpp>
#include <string>
#include <optional>
#include "AudioSet.hpp"
#include "AudioSourceAndroidType.hpp"
#include "OutputFormatAndroidType.hpp"
#include "AudioEncoderAndroidType.hpp"
#include "AVEncoderAudioQualityIOSType.hpp"
#include "AVModeIOSOption.hpp"
#include "AVEncodingOption.hpp"
#include "AVLinearPCMBitDepthKeyIOSType.hpp"
#include "AudioQualityType.hpp"
#include <unordered_map>
#include <functional>
#include "RecordBackType.hpp"
#include "PlayBackType.hpp"

#include "ReactNativeAudioRecorderPlayer-Swift-Cxx-Umbrella.hpp"

namespace margelo::nitro::react_native_audio_recorder_player {

  /**
   * The C++ part of HybridAudioRecorderPlayerSpec_cxx.swift.
   *
   * HybridAudioRecorderPlayerSpecSwift (C++) accesses HybridAudioRecorderPlayerSpec_cxx (Swift), and might
   * contain some additional bridging code for C++ <> Swift interop.
   *
   * Since this obviously introduces an overhead, I hope at some point in
   * the future, HybridAudioRecorderPlayerSpec_cxx can directly inherit from the C++ class HybridAudioRecorderPlayerSpec
   * to simplify the whole structure and memory management.
   */
  class HybridAudioRecorderPlayerSpecSwift: public virtual HybridAudioRecorderPlayerSpec {
  public:
    // Constructor from a Swift instance
    explicit HybridAudioRecorderPlayerSpecSwift(const ReactNativeAudioRecorderPlayer::HybridAudioRecorderPlayerSpec_cxx& swiftPart):
      HybridObject(HybridAudioRecorderPlayerSpec::TAG),
      _swiftPart(swiftPart) { }

  public:
    // Get the Swift part
    inline ReactNativeAudioRecorderPlayer::HybridAudioRecorderPlayerSpec_cxx& getSwiftPart() noexcept {
      return _swiftPart;
    }

  public:
    // Get memory pressure
    inline size_t getExternalMemorySize() noexcept override {
      return _swiftPart.getMemorySize();
    }

  public:
    // Properties
    

  public:
    // Methods
    inline std::shared_ptr<Promise<std::string>> startRecorder(const std::optional<std::string>& uri, const std::optional<AudioSet>& audioSets, std::optional<bool> meteringEnabled) override {
      auto __result = _swiftPart.startRecorder(uri, audioSets, meteringEnabled);
      if (__result.hasError()) [[unlikely]] {
        std::rethrow_exception(__result.error());
      }
      auto __value = std::move(__result.value());
      return __value;
    }
    inline std::shared_ptr<Promise<std::string>> pauseRecorder() override {
      auto __result = _swiftPart.pauseRecorder();
      if (__result.hasError()) [[unlikely]] {
        std::rethrow_exception(__result.error());
      }
      auto __value = std::move(__result.value());
      return __value;
    }
    inline std::shared_ptr<Promise<std::string>> resumeRecorder() override {
      auto __result = _swiftPart.resumeRecorder();
      if (__result.hasError()) [[unlikely]] {
        std::rethrow_exception(__result.error());
      }
      auto __value = std::move(__result.value());
      return __value;
    }
    inline std::shared_ptr<Promise<std::string>> stopRecorder() override {
      auto __result = _swiftPart.stopRecorder();
      if (__result.hasError()) [[unlikely]] {
        std::rethrow_exception(__result.error());
      }
      auto __value = std::move(__result.value());
      return __value;
    }
    inline std::shared_ptr<Promise<std::string>> startPlayer(const std::optional<std::string>& uri, const std::optional<std::unordered_map<std::string, std::string>>& httpHeaders) override {
      auto __result = _swiftPart.startPlayer(uri, httpHeaders);
      if (__result.hasError()) [[unlikely]] {
        std::rethrow_exception(__result.error());
      }
      auto __value = std::move(__result.value());
      return __value;
    }
    inline std::shared_ptr<Promise<std::string>> stopPlayer() override {
      auto __result = _swiftPart.stopPlayer();
      if (__result.hasError()) [[unlikely]] {
        std::rethrow_exception(__result.error());
      }
      auto __value = std::move(__result.value());
      return __value;
    }
    inline std::shared_ptr<Promise<std::string>> pausePlayer() override {
      auto __result = _swiftPart.pausePlayer();
      if (__result.hasError()) [[unlikely]] {
        std::rethrow_exception(__result.error());
      }
      auto __value = std::move(__result.value());
      return __value;
    }
    inline std::shared_ptr<Promise<std::string>> resumePlayer() override {
      auto __result = _swiftPart.resumePlayer();
      if (__result.hasError()) [[unlikely]] {
        std::rethrow_exception(__result.error());
      }
      auto __value = std::move(__result.value());
      return __value;
    }
    inline std::shared_ptr<Promise<std::string>> seekToPlayer(double time) override {
      auto __result = _swiftPart.seekToPlayer(std::forward<decltype(time)>(time));
      if (__result.hasError()) [[unlikely]] {
        std::rethrow_exception(__result.error());
      }
      auto __value = std::move(__result.value());
      return __value;
    }
    inline std::shared_ptr<Promise<std::string>> setVolume(double volume) override {
      auto __result = _swiftPart.setVolume(std::forward<decltype(volume)>(volume));
      if (__result.hasError()) [[unlikely]] {
        std::rethrow_exception(__result.error());
      }
      auto __value = std::move(__result.value());
      return __value;
    }
    inline std::shared_ptr<Promise<std::string>> setPlaybackSpeed(double playbackSpeed) override {
      auto __result = _swiftPart.setPlaybackSpeed(std::forward<decltype(playbackSpeed)>(playbackSpeed));
      if (__result.hasError()) [[unlikely]] {
        std::rethrow_exception(__result.error());
      }
      auto __value = std::move(__result.value());
      return __value;
    }
    inline void setSubscriptionDuration(double sec) override {
      auto __result = _swiftPart.setSubscriptionDuration(std::forward<decltype(sec)>(sec));
      if (__result.hasError()) [[unlikely]] {
        std::rethrow_exception(__result.error());
      }
    }
    inline void addRecordBackListener(const std::function<void(const RecordBackType& /* recordingMeta */)>& callback) override {
      auto __result = _swiftPart.addRecordBackListener(callback);
      if (__result.hasError()) [[unlikely]] {
        std::rethrow_exception(__result.error());
      }
    }
    inline void removeRecordBackListener() override {
      auto __result = _swiftPart.removeRecordBackListener();
      if (__result.hasError()) [[unlikely]] {
        std::rethrow_exception(__result.error());
      }
    }
    inline void addPlayBackListener(const std::function<void(const PlayBackType& /* playbackMeta */)>& callback) override {
      auto __result = _swiftPart.addPlayBackListener(callback);
      if (__result.hasError()) [[unlikely]] {
        std::rethrow_exception(__result.error());
      }
    }
    inline void removePlayBackListener() override {
      auto __result = _swiftPart.removePlayBackListener();
      if (__result.hasError()) [[unlikely]] {
        std::rethrow_exception(__result.error());
      }
    }
    inline std::string mmss(double secs) override {
      auto __result = _swiftPart.mmss(std::forward<decltype(secs)>(secs));
      if (__result.hasError()) [[unlikely]] {
        std::rethrow_exception(__result.error());
      }
      auto __value = std::move(__result.value());
      return __value;
    }
    inline std::string mmssss(double milisecs) override {
      auto __result = _swiftPart.mmssss(std::forward<decltype(milisecs)>(milisecs));
      if (__result.hasError()) [[unlikely]] {
        std::rethrow_exception(__result.error());
      }
      auto __value = std::move(__result.value());
      return __value;
    }

  private:
    ReactNativeAudioRecorderPlayer::HybridAudioRecorderPlayerSpec_cxx _swiftPart;
  };

} // namespace margelo::nitro::react_native_audio_recorder_player
