///
/// JPlayBackType.hpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON> @ Margelo
///

#pragma once

#include <fbjni/fbjni.h>
#include "PlayBackType.hpp"

#include <optional>

namespace margelo::nitro::react_native_audio_recorder_player {

  using namespace facebook;

  /**
   * The C++ JNI bridge between the C++ struct "PlayBackType" and the the Kotlin data class "PlayBackType".
   */
  struct JPlayBackType final: public jni::JavaClass<JPlayBackType> {
  public:
    static auto constexpr kJavaDescriptor = "Lcom/margelo/nitro/reactnativeaudiorecorderplayer/PlayBackType;";

  public:
    /**
     * Convert this Java/Kotlin-based struct to the C++ struct PlayBackType by copying all values to C++.
     */
    [[maybe_unused]]
    [[nodiscard]]
    PlayBackType toCpp() const {
      static const auto clazz = javaClassStatic();
      static const auto fieldIsMuted = clazz->getField<jni::JBoolean>("isMuted");
      jni::local_ref<jni::JBoolean> isMuted = this->getFieldValue(fieldIsMuted);
      static const auto fieldDuration = clazz->getField<double>("duration");
      double duration = this->getFieldValue(fieldDuration);
      static const auto fieldCurrentPosition = clazz->getField<double>("currentPosition");
      double currentPosition = this->getFieldValue(fieldCurrentPosition);
      return PlayBackType(
        isMuted != nullptr ? std::make_optional(static_cast<bool>(isMuted->value())) : std::nullopt,
        duration,
        currentPosition
      );
    }

  public:
    /**
     * Create a Java/Kotlin-based struct by copying all values from the given C++ struct to Java.
     */
    [[maybe_unused]]
    static jni::local_ref<JPlayBackType::javaobject> fromCpp(const PlayBackType& value) {
      return newInstance(
        value.isMuted.has_value() ? jni::JBoolean::valueOf(value.isMuted.value()) : nullptr,
        value.duration,
        value.currentPosition
      );
    }
  };

} // namespace margelo::nitro::react_native_audio_recorder_player
