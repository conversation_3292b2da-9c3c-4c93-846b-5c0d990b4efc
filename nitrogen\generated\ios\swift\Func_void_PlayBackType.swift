///
/// Func_void_PlayBackType.swift
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON> @ Margelo
///

import NitroModules

/**
 * Wraps a Swift `(_ playbackMeta: PlayBackType) -> Void` as a class.
 * This class can be used from C++, e.g. to wrap the Swift closure as a `std::function`.
 */
public final class Func_void_PlayBackType {
  public typealias bridge = margelo.nitro.react_native_audio_recorder_player.bridge.swift

  private let closure: (_ playbackMeta: PlayBackType) -> Void

  public init(_ closure: @escaping (_ playbackMeta: PlayBackType) -> Void) {
    self.closure = closure
  }

  @inline(__always)
  public func call(playbackMeta: PlayBackType) -> Void {
    self.closure(playbackMeta)
  }

  /**
   * Casts this instance to a retained unsafe raw pointer.
   * This acquires one additional strong reference on the object!
   */
  @inline(__always)
  public func toUnsafe() -> UnsafeMutableRawPointer {
    return Unmanaged.passRetained(self).toOpaque()
  }

  /**
   * Casts an unsafe pointer to a `Func_void_PlayBackType`.
   * The pointer has to be a retained opaque `Unmanaged<Func_void_PlayBackType>`.
   * This removes one strong reference from the object!
   */
  @inline(__always)
  public static func fromUnsafe(_ pointer: UnsafeMutableRawPointer) -> Func_void_PlayBackType {
    return Unmanaged<Func_void_PlayBackType>.fromOpaque(pointer).takeRetainedValue()
  }
}
