import PropTypes from "prop-types";
import React, { useCallback, useState } from "react";
import { useTranslation } from "react-i18next";
import { Alert, StyleSheet, TouchableOpacity, View } from "react-native";
import { Text, useTheme } from "react-native-paper";
import { makeRegisterAppClient as httpClient } from "../api/registerAppClient";
import Background from "../components/Background";
import Button from "../components/Button";
import Header from "../components/Header";
import Logo from "../components/Logo";
import PasswordTextInput from "../components/PasswordTextInput";
import TextInput from "../components/TextInput";
import { CUSTOMER_SERVICE_HOTLINE_2 } from "../config";
import { REG_APP_CLIENT_KEY as queryKey, REG_APP_CLIENT_URL as queryUrl } from "../config/keysConfig";
import { instNameValidator, mobileValidator, passwordValidator, promteCodeValidator } from "../helpers/validators";
import log from "../services/logging";
import { makeDataFeeder } from "../utils";
//import registerMockData from "./mock/screenRegisterMock.json";


const queryHTTP = httpClient(queryKey, queryUrl);
const dataFeeder = makeDataFeeder();

const RegisterScreen = ({ navigation }) => {
    const { t } = useTranslation(["screenRegister"]);

    //const [userName, userAccount, userPassword] = userStoredState(useShallow((state) => [state.name, state.account, state.password]));
    //const [setUserName, setUserAccount, setUserPassword] = userStoredState(useShallow((state) => [state.setName, state.setAccount, state.setPassword]));

    const [instName, setInstName] = useState({ value: "", error: "" });         // 单位名称
    const [mobile, setMobile] = useState({ value: "", error: "" });             // 联系人手机
    const [userPassword, setUserPassword] = useState({ value: "", error: "" }); // 密码
    const [promteCode, setPromteCode] = useState({ value: "", error: "" });     // 推荐码

    const theme = useTheme();
    const styles = StyleSheet.create({
        row: {
            flexDirection: "row",
            marginTop: 4,
        },
        link: {
            fontWeight: "bold",
            color: theme.colors.primary,
            marginLeft: 6
        },
        screenWrapper: {
            //width: "70%",
        },
        contentContainer: {
            alignItems: "center",
            //width: "100%",
            //paddingHorizontal: 20,
            //borderWidth: 1,
            //borderColor: "red",
        },
    });

    /**
     * 服务端以200返回时调用本函数, 但需要根据STATUS码进一步判断是否注册成功.
     */
    const onServerSuccess = (data) => {
        //console.log("onServerSuccess, data:", data);
        if (data.STATUS === 0) {
            Alert.alert("感谢注册", "请使用手机号和密码登录!", [
                //{ text: "取消", onPress: () => { } }, // 调试用, 免得每次都返回登录
                { text: "确定", onPress: () => {
                    navigation.reset({
                        index: 0,
                        routes: [{ name: "LoginScreen" }],
                    });
                } },
            ]);
        } else if (data.STATUS === 14) { // 推荐码无效
            Alert.alert("注册失败", `${data.DATA.info}\n1. 请核实推荐码是否正确.\n2. 请联系您的推荐人获取正确的推荐码. \n3. 请拨打客服电话获得推荐码: ${CUSTOMER_SERVICE_HOTLINE_2}.`, [
                { text: "确定", onPress: () => { } },
            ]);
        } else {
            Alert.alert("注册失败", `${data.DATA.info}\n请根据上述提示修改注册信息后重新提交.\n客服电话: ${CUSTOMER_SERVICE_HOTLINE_2}`, [
                { text: "确定", onPress: () => { } },
            ]);
        }

    };

    const onQueryError = (err) => {
        log.error("RegisterScreen client error:", err.message, instName.value, mobile.value, userPassword.value, promteCode.value);
        Alert.alert("注册失败", `${err.message}.\n注册发生错误, 请联系客服: ${CUSTOMER_SERVICE_HOTLINE_2}`, [
            { text: "确定", onPress: () => { } },
        ]);
    };
    const onQuerySettled = () => {};

    const query = queryHTTP(dataFeeder, onServerSuccess, onQueryError, onQuerySettled );

    const onSignUpPressed = useCallback(() => {
        const instNameError   = instNameValidator(instName.value);
        const mobileError     = mobileValidator(mobile.value);
        const passwordError   = passwordValidator(userPassword.value);
        const promteCodeError = promteCodeValidator(promteCode.value);

        if (instNameError || mobileError || passwordError || promteCodeError) {
            setInstName({ value: instName.value, error: instNameError });
            setMobile({ value: mobile.value, error: mobileError });
            setUserPassword({ value: userPassword.value, error: passwordError });
            setPromteCode({ value: promteCode.value, error: promteCodeError });

            return;
        }

        dataFeeder({
            inst: instName.value,
            mobile: mobile.value,
            pass: userPassword.value,
            promote: promteCode.value,
        });
        query.mutate();

    }, [instName, mobile, userPassword, promteCode]);

    return (
        <Background>
            {/*<BackButton goBack={navigation.goBack} />*/}
            <Logo />
            <Header>{t("screenRegister:header")}</Header>

            <TextInput
                label={t("screenRegister:instNameInputTextLabel")}
                multiline={true}
                returnKeyType="next"
                value={instName.value}
                onChangeText={(text) => setInstName({ value: text, error: "" })}
                error={!!instName.error}
                errorText={instName.error}
            />
            <TextInput
                label={t("screenRegister:mobileInputTextLabel")}
                returnKeyType="next"
                value={mobile.value}
                onChangeText={(text) => setMobile({ value: text, error: "" })}
                error={!!mobile.error}
                errorText={mobile.error}
                autoCapitalize="none"
                autoComplete="off"
                textContentType="none"
                keyboardType="numeric"
            />
            <PasswordTextInput
                label={t("screenRegister:passwordInutTextLabel")}
                returnKeyType="next"
                value={userPassword.value}
                onChangeText={(text) => setUserPassword({ value: text, error: "" })}
                error={!!userPassword.error}
                errorText={userPassword.error}
            />
            <TextInput
                label={t("screenRegister:promteCodeInputTextLabel")}
                returnKeyType="done"
                value={promteCode.value}
                onChangeText={(text) => setPromteCode({ value: text, error: "" })}
                error={!!promteCode.error}
                errorText={promteCode.error}
                autoCapitalize="characters"
                autoComplete="off"
                textContentType="none"
            />
            <Button mode="contained" onPress={onSignUpPressed} style={{ marginTop: 24 }} >
                {t("screenRegister:signButton")}
            </Button>
            <View style={styles.row}>
                <Text>{t("screenRegister:haveAccount")}</Text>
                <TouchableOpacity onPress={() => navigation.replace("LoginScreen")}>
                    <Text style={styles.link}>{t("screenRegister:haveAccountAndLogin")}</Text>
                </TouchableOpacity>
            </View>

        </Background>
    );
};

RegisterScreen.propTypes = {
    navigation: PropTypes.shape({
        navigate: PropTypes.func.isRequired,
        reset: PropTypes.func.isRequired,
        goBack: PropTypes.func.isRequired,
        replace: PropTypes.func.isRequired,
    }).isRequired,
};

export default RegisterScreen;
