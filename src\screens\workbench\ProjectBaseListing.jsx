import React from "react";
import ListingTemplate from "./ListingTemplate";
import { makeRequestListingProjBase as queryClient } from "../../api/listingQueries";
import { orgProjBaseState as pageDataState } from "../../hooks/globalStates";

/**
 * Ther view of list-org-project-base of the server.
 * @param {*} param0
 * @returns
 */
const ProjectBaseListing = ({ navigation }) => {
    return (
        <ListingTemplate
            bottomBarLabel={"添加项目库"}
            emptyScreenText={"请点击下方按钮添加项目库"}
            snackBarDefaultText={"添加项目库遇到错误"}
            saveButtonIcon={"account-plus-outline"}
            queryClient={queryClient}
            pageDataState={pageDataState}
            listItemNavigateTo={"WorkbenchProjectBaseUpdating"}
            addingButtonNavigateTo={"WorkbenchProjectBaseInserting"}
            navigation={navigation}
        />
    );
};

export default ProjectBaseListing;
