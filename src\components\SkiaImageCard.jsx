import { Canvas, Group, Image as SkiaImage, useCanvasRef, useImage } from "@shopify/react-native-skia";
import React, { useEffect, useRef, useState } from "react";
import { ActivityIndicator, StyleSheet, TouchableOpacity, View } from "react-native";
import { Text } from "react-native-paper";
import { layoutTransform } from "../utils/image";


const SkiaImageCard = ({ imageURL, title, subtitle, content, onPress, onLongPress, theme}) => {
    const image = useImage(imageURL || require("../assets/images/new-card.png"));
    const canvasRef = useCanvasRef();
    const containerRef = useRef(null);
    const [canvasPosition, setCanvasPosition] = useState({ x: 0, y: 0, width: 0, height: 0 });
    const [canvasHeight, setCanvasHeight] = useState(200); // 初始高度
    const [imageLayout, setImageLayout] = useState({ x: 0, y: 0, width: 0, height: 0, rotate: false });
    const [isImageLoading, setIsImageLoading] = useState(true);


    // 当容器宽度变化时记录
    const onContainerLayout = (event) => {
        //const { width } = event.nativeEvent.layout;
        const { x, y, width, height } = event.nativeEvent.layout;
        setCanvasPosition( { x, y, width, height });
    };

    // 当图片加载完成或容器宽度变化时，计算画布高度和图片布局
    useEffect(() => {
        if (image && canvasPosition.width > 0) {
            setIsImageLoading(false);

            const imageWidth = image.width();
            const imageHeight = image.height();

            const {layout, canvasHeight} = layoutTransform(imageWidth, imageHeight, canvasPosition.width);
            setCanvasHeight(canvasHeight);
            setImageLayout(layout);

        }
    }, [image, canvasPosition]);

    return (
        <View
            ref={containerRef}
            onLayout={onContainerLayout}
            style={{...styles.container,
                backgroundColor: theme.colors.elevation.level3,
                borderColor: theme.colors.onSurfaceVariant,
                paddingTop: title ? styles.container.paddingTop : 3,
                paddingBottom: content ? styles.container.paddingBottom : 3,
                shadowColor: theme.colors.onSurfaceVariant,
                shadowOffset: { width: 0, height: 2 }, // 添加阴影偏移量
                shadowOpacity: 0.23,  // 设置透明度
                shadowRadius: 2.62,  // 设置模糊半径
                elevation: 4,  // Android 阴影效果
            }}
        >
            <TouchableOpacity onPress={() => {onPress && onPress();}} onLongPress={() => {onLongPress && onLongPress();}}>
                {title && <Text variant="titleLarge" style={styles.title}>{title}</Text>}
                {subtitle && <Text variant="titleSmall" style={styles.subtitle}>{subtitle}</Text>}
                <View
                    style={{
                        ...styles.canvasContainer,
                        height: canvasHeight,
                        borderWidth: 1,
                        borderColor: theme.colors.secondaryContainer,
                    }}
                >
                    {isImageLoading && (
                        <View style={styles.loadingContainer}>
                            <ActivityIndicator size="large" color={theme.colors.primary} />
                        </View>
                    )}
                    <Canvas
                        ref={canvasRef}
                        style={styles.canvasStyle}
                        // opaque={false}
                    >
                        {image && (
                            (imageLayout.rotate ? (<Group
                                transform={
                                    [
                                        { translateX: imageLayout.translateX },
                                        { translateY: imageLayout.translateY },
                                        { rotate: imageLayout.rotate },
                                        { scale: imageLayout.scale }
                                    ]
                                }
                            >
                                <SkiaImage
                                    image={image}
                                    x={imageLayout.x}
                                    y={imageLayout.y}
                                    width={imageLayout.width}
                                    height={imageLayout.height}
                                    //fit="cover"
                                />
                            </Group>) : (<SkiaImage
                                image={image}
                                x={imageLayout.x}
                                y={imageLayout.y}
                                width={imageLayout.width}
                                height={imageLayout.height}
                                //fit="cover"
                            />))
                        )}
                    </Canvas>
                </View>
                {content && <Text variant="bodyMedium" style={styles.content}>{content}</Text>}
            </TouchableOpacity>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        marginBottom: 10,
        borderWidth: 1,
        paddingTop: 10,
        paddingBottom: 10,
        borderRadius: 6,        // 添加圆角
        shadowColor: "#000",    // 添加阴影颜色
    },
    title: {
        marginLeft: 20,
        paddingBottom: 5,
    },
    subtitle: {
        marginLeft: 20,
        paddingBottom: 5,
    },
    content: {
        marginTop: 5,
        marginLeft: 20,
    },
    canvasContainer: {
        flex: 1,
        minHeight: 200,
        position: "relative",
    },
    canvasStyle: {
        flex: 1,
    },
    loadingContainer: {
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: "center",
        alignItems: "center",
        zIndex: 1,
    },
});

export default SkiaImageCard;
