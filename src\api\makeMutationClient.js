import { useMutation } from "@tanstack/react-query";
import log from "../services/logging";


const RETRY_CODES = [401, 501, 502, 503, 504];
const RETRY_METHODS = ["GET", "POST", "PUT", "HEAD", "DELETE", "OPTIONS", "TRACE"];
const RETRY_INTERVALS = [2000, 3000, 5000];
const RETRY_LIMIT = RETRY_INTERVALS.length;

/**
 * 返回一个函数, 返回的函数调用后得到一个useMutation客户端.
 * 这里返回一个返回函数的函数, 是因为hook不允许在组件之外的地方运行, 而这样做的目的也是为了跟makeRecordsUpdatingClient等函数风格保持一直, 但这个函数本身是过度包装.
 * @returns
 */
export const makeMutationClientGenerator = () => {
    /**
     * 返回一个函数, 这个返回的函数创建一个useMutation查询, 用于发起update请求.
     * @param {Function} queryFn function
     * @param {()=>{}} onSuccess callback function
     * @param {()=>{}} onError callback function
     * @param {()=>{}} onSettled callback function
     */
    return (queryKey, queryFn, onSuccess, onError, onSettled) => {
        return useMutation({
            mutationKey: queryKey,
            mutationFn: async () => {
                const response = await queryFn();
                const resp = await response; // 这里不需要response.json(), 因为queryFn可能执行了， 与其它查询不同
                log.debug("Mutation query client %s, mutationFn receive data: %s", queryKey, resp);
                return resp;
            },
            onSuccess: (data, variables, context) => {
                console.log("Call onSuccess");
                onSuccess?.(data);
            },
            onError: (error, variables, context) => {
                console.log("Call onError");
                onError?.(error);
            },
            onSettled: (data, error, variables, context) => {
                console.log("Call onSettled");
                onSettled?.(data, error);
            },

            // 逻辑上, 首次失败后, 立即检测retryDelay, 经延迟后再检测retry触发重试
            // 注意, 有Ky的retry有bug, 这里使用React Query的retry!!!!!!
            retry: (failureCount, error) => { // failureCount from 0
                log.debug("Retry makeMutationClient, error: %s, failureCount: %s", error, failureCount);
                if (failureCount < RETRY_LIMIT
                    && error.name === "HTTPError"
                    && RETRY_CODES.includes(error.response.status)
                    && RETRY_METHODS.includes(error.request.method)) {
                    return true;
                } else {
                    return false;
                }
            },
            retryDelay: (attemptIndex) => RETRY_INTERVALS[attemptIndex], // attemptIndex from 0
        });
    };
};
