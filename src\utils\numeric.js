import { isInteger, isNumber, isNumberSpecifier } from ".";

/**
 * Round a number specifier to the nearest number with the precise provided.
 * Return NaN if `numSpecifier` is not a number specifier.
 * @param {string | number} numSpecifier
 * @param {int} precise
 */
export const roundNearest = (numSpecifier, precise = 10) => {
    if (isInteger(numSpecifier)) {
        return numSpecifier;
    } else if (isNumber(numSpecifier)) {
        const sign = Math.sign(numSpecifier) === -1 ? "-" : "";
        const scale = 10 ** precise;
        const expandRound = Math.round(Math.abs(numSpecifier) * scale);
        const expandLen = String(expandRound).length;
        const expandStr = expandLen > precise ? `${expandRound}` : `${"0".repeat(precise - expandLen)}${expandRound}`;
        const intPartLen = expandLen - precise;
        const intPart = intPartLen > 0 ? expandStr.substring(0, intPartLen) : "0";
        const decPart = expandStr.substring(intPartLen);
        return Number(`${sign}${intPart}.${decPart}`);

        /*
        const scale = 10 ** precise;
        const integerPart = Math.trunc(numSpecifier);
        const decimalPart = numSpecifier - integerPart;
        const decimalRound = Math.round(decimalPart * scale);
        const decimalLen = String(decimalRound).length;
        const zeros = "0".repeat(precise - decimalLen > 0 ? precise - decimalLen : 0);
        //return integerPart + decimalRound; // 3+0.28 -> 3.2800000000000002
        console.log(numSpecifier, "intPart:", integerPart, "decmPart:", decimalPart, "decmRound:", decimalRound);
        // 考虑进位情况, 例如5.999999999999999
        return decimalRound === scale ? integerPart + 1 : Number(`${integerPart}.${zeros}${decimalRound}`);
*/
    } else if (isNumberSpecifier(numSpecifier)) {
        return roundNearest(Number(numSpecifier));
    } else {
        return NaN;
    }
};


/**
 * Round a decimal number specifier to the nearest percent number.
 * Return NaN if `numSpecifier` is not a number specifier.
 * eg. roundNearestTo(0.12345678) -> 0.123
 * @param {string | number} numSpecifier
 * @param {int} precise
 */
export const roundNearestTo = (numSpecifier, decimals = 3, precise = 10) => {
    const scaleNum = 10 ** decimals;
    if (isInteger(numSpecifier)) {
        return numSpecifier;
    } else if (isNumber(numSpecifier)) {
        return roundNearest(Math.round(numSpecifier * scaleNum) / scaleNum, precise);
    } else if (isNumberSpecifier(numSpecifier)) {
        return roundNearestTo(Number(numSpecifier), decimals, precise);
    } else {
        return NaN;
    }
};


/**
 * Round a decimal number specifier to the nearest percent number.
 * Return NaN if `numSpecifier` is not a number specifier.
 * eg. roundPercent(0.12345678) -> 12.346
 * @param {string | number} numSpecifier
 * @param {int} precise
 */
export const roundPercent = (numSpecifier, decimals = 2, precise = 10) => {
    const percentDigits = 2;
    const scaleNum = 10 ** decimals;
    if (isInteger(numSpecifier)) {
        return numSpecifier * 100;
    } else if (isNumber(numSpecifier)) {
        return roundNearest(Math.round(numSpecifier * 10 ** (decimals + percentDigits)) / scaleNum, precise);
    } else if (isNumberSpecifier(numSpecifier)) {
        return roundPercent(Number(numSpecifier), decimals, precise);
    } else {
        return NaN;
    }
};
