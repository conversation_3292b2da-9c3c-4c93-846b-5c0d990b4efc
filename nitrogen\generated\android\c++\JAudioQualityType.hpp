///
/// JAudioQualityType.hpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON> @ Margelo
///

#pragma once

#include <fbjni/fbjni.h>
#include "AudioQualityType.hpp"

namespace margelo::nitro::react_native_audio_recorder_player {

  using namespace facebook;

  /**
   * The C++ JNI bridge between the C++ enum "AudioQualityType" and the the Kotlin enum "AudioQualityType".
   */
  struct JAudioQualityType final: public jni::JavaClass<JAudioQualityType> {
  public:
    static auto constexpr kJavaDescriptor = "Lcom/margelo/nitro/reactnativeaudiorecorderplayer/AudioQualityType;";

  public:
    /**
     * Convert this Java/Kotlin-based enum to the C++ enum AudioQualityType.
     */
    [[maybe_unused]]
    [[nodiscard]]
    AudioQualityType toCpp() const {
      static const auto clazz = javaClassStatic();
      static const auto fieldOrdinal = clazz->getField<int>("_ordinal");
      int ordinal = this->getFieldValue(fieldOrdinal);
      return static_cast<AudioQualityType>(ordinal);
    }

  public:
    /**
     * Create a Java/Kotlin-based enum with the given C++ enum's value.
     */
    [[maybe_unused]]
    static jni::alias_ref<JAudioQualityType> fromCpp(AudioQualityType value) {
      static const auto clazz = javaClassStatic();
      static const auto fieldLOW = clazz->getStaticField<JAudioQualityType>("LOW");
      static const auto fieldMEDIUM = clazz->getStaticField<JAudioQualityType>("MEDIUM");
      static const auto fieldHIGH = clazz->getStaticField<JAudioQualityType>("HIGH");
      
      switch (value) {
        case AudioQualityType::LOW:
          return clazz->getStaticFieldValue(fieldLOW);
        case AudioQualityType::MEDIUM:
          return clazz->getStaticFieldValue(fieldMEDIUM);
        case AudioQualityType::HIGH:
          return clazz->getStaticFieldValue(fieldHIGH);
        default:
          std::string stringValue = std::to_string(static_cast<int>(value));
          throw std::invalid_argument("Invalid enum value (" + stringValue + "!");
      }
    }
  };

} // namespace margelo::nitro::react_native_audio_recorder_player
