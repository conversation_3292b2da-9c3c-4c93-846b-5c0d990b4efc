///
/// AudioQualityType.swift
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON> @ Margelo
///

/**
 * Represents the JS union `AudioQualityType`, backed by a C++ enum.
 */
public typealias AudioQualityType = margelo.nitro.react_native_audio_recorder_player.AudioQualityType

public extension AudioQualityType {
  /**
   * Get a AudioQualityType for the given String value, or
   * return `nil` if the given value was invalid/unknown.
   */
  init?(fromString string: String) {
    switch string {
      case "low":
        self = .low
      case "medium":
        self = .medium
      case "high":
        self = .high
      default:
        return nil
    }
  }

  /**
   * Get the String value this AudioQualityType represents.
   */
  var stringValue: String {
    switch self {
      case .low:
        return "low"
      case .medium:
        return "medium"
      case .high:
        return "high"
    }
  }
}
