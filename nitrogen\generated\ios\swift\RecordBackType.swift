///
/// RecordBackType.swift
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON> @ Margelo
///

import NitroModules

/**
 * Represents an instance of `RecordBackType`, backed by a C++ struct.
 */
public typealias RecordBackType = margelo.nitro.react_native_audio_recorder_player.RecordBackType

public extension RecordBackType {
  private typealias bridge = margelo.nitro.react_native_audio_recorder_player.bridge.swift

  /**
   * Create a new instance of `RecordBackType`.
   */
  init(isRecording: Bool?, currentPosition: Double, currentMetering: Double?, recordSecs: Double?) {
    self.init({ () -> bridge.std__optional_bool_ in
      if let __unwrappedValue = isRecording {
        return bridge.create_std__optional_bool_(__unwrappedValue)
      } else {
        return .init()
      }
    }(), currentPosition, { () -> bridge.std__optional_double_ in
      if let __unwrappedValue = currentMetering {
        return bridge.create_std__optional_double_(__unwrappedValue)
      } else {
        return .init()
      }
    }(), { () -> bridge.std__optional_double_ in
      if let __unwrappedValue = recordSecs {
        return bridge.create_std__optional_double_(__unwrappedValue)
      } else {
        return .init()
      }
    }())
  }

  var isRecording: Bool? {
    @inline(__always)
    get {
      return self.__isRecording.value
    }
    @inline(__always)
    set {
      self.__isRecording = { () -> bridge.std__optional_bool_ in
        if let __unwrappedValue = newValue {
          return bridge.create_std__optional_bool_(__unwrappedValue)
        } else {
          return .init()
        }
      }()
    }
  }
  
  var currentPosition: Double {
    @inline(__always)
    get {
      return self.__currentPosition
    }
    @inline(__always)
    set {
      self.__currentPosition = newValue
    }
  }
  
  var currentMetering: Double? {
    @inline(__always)
    get {
      return self.__currentMetering.value
    }
    @inline(__always)
    set {
      self.__currentMetering = { () -> bridge.std__optional_double_ in
        if let __unwrappedValue = newValue {
          return bridge.create_std__optional_double_(__unwrappedValue)
        } else {
          return .init()
        }
      }()
    }
  }
  
  var recordSecs: Double? {
    @inline(__always)
    get {
      return self.__recordSecs.value
    }
    @inline(__always)
    set {
      self.__recordSecs = { () -> bridge.std__optional_double_ in
        if let __unwrappedValue = newValue {
          return bridge.create_std__optional_double_(__unwrappedValue)
        } else {
          return .init()
        }
      }()
    }
  }
}
