import { joiResolver } from "@hookform/resolvers/joi";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { Alert, StyleSheet, View } from "react-native";
import { Divider, Snackbar } from "react-native-paper";
//import debounce from "lodash/debounce";
import "fast-text-encoding";
import Jo<PERSON> from "joi";
import Button from "../../../components/Button";
import { DialogToConfirm } from "../../../components/DialogToConfirm";
import HeaderBar from "../../../components/HeaderBar";
import { LoadingIndicator } from "../../../components/LoadingIndicator";
import ScreenWrapper from "../../ScreenWrapper";
//import ControlledTextInput from "../../../components/ControlledTextInput";
import { FormListNodesMapper } from "../../../components/FormListNodesMapper";
import { creatMMKVStore, makeStoredValueToFormValue } from "../../../services/local-storage";
import { assembleUrl, callOneByOne, makeDataFeeder, makeFileUploadUrl } from "../../../utils";
import { checkPermits } from "../../../utils/permits";
import { isServerDataNewer, makeGetClientCversion } from "../../../utils/records";
import { validatorBase } from "../../../utils/validatorBase";


// 新组件需要重新!! 如果遇到非水平衡表格就要更改key
import { UPD_WATER_BALANCE_RECORDS as pageMainKey } from "../../../config/keysConfig";
//import { wbwbClientInfoStates as selectorStates } from "../../../hooks/selectorStates";
import {
    makeReqDelWaterBalanceRecords as recordDeleteQueryMaker,
    makeReqGetWaterBalanceRecords as recordSelectQueryMaker,
    makeReqUpdWaterBalanceRecords as recordUpdateQueryMaker,
} from "../../../api/projectRecordsQueries";
import { uploadFile } from "../../../api/uploadFile";
import { MAX_IMAGES_PICK } from "../../../config";
import { isLocalFile } from "../../../services/system/file";
import { onPreSubmitError } from "../../../utils/screens";
//import { prodSystemEnum } from "../../../config/waterBalance";
//import { roundNearest } from "../../../utils/numeric";
//import log from "../../../services/logging";


const dataFeeder = makeDataFeeder();

/**
 * 公用工程
 * 目前只有表有此需求, 因此在表格类型列表中只有表项目会显示出来.
 * 对于文件类型, 添加了一个fctrl字段, 用于控制客户端某些字段的修改情况, 服务端据此做进一步判断.
 * fctrl的键是对应文件字段的名称. 其值目前有三种:
 *   1. 默认空串, ""表示未改动;
 *   2. "a"表示添加(append), 表示向文件列表中添加了新文件, 服务端将接受新文件并向数据库添加新路径;
 *   3. "c"表示清空(clear), 表示执行了一次清空操作, 服务端需要先将原文件及数据库清空, 客户端在清空后可以继续添加文件, 但不会改变"c"的情况.
 * 注意onRecordUpdateSuccess使用了异步函数:
 * 能够使用async的理由: "...only callbacks on useMutation are awaited...", https://github.com/TanStack/query/discussions/3397
 * 文档: https://tanstack.com/query/latest/docs/framework/react/reference/useMutation
 * @param {object} args
 * @param {object} args.navigation
 * @param {object} args.route
 * @returns
 */
const RecordsUpdating = ({ navigation, route }) => {
    console.log("RecordsUpdating pageMeta from nav:", route.params.pageMeta);
    console.log("RecordsUpdating projMeta from nav:", route.params.projMeta);
    const projSubclass = route.params.projMeta.subclass; // 1表, 2书
    const apiUri = "api/proj/wb"; // 目前仅用于上传文件

    // formType对应着服务端的form-type, 为0时表示表单是用户填写, 为1表示服务端生成, 为1时只读
    const { queryKwd, id: recordPubid, name: recordName, formType } = route.params.pageMeta; // recordPubid is the alias of id
    const saveButtonIcon = "content-save-all-outline";
    const formEditableGlobal = checkPermits() ? (formType === 0 ? true : false) : false;

    // 使用memoize来优化
    const { setStore, getStore, clearStore, setStoreObject } = useMemo(() => {
        console.log("creatMMKVStore memo:", pageMainKey.store, queryKwd, recordPubid);
        return creatMMKVStore(`${pageMainKey.store}/${queryKwd}/${recordPubid}`);
    }, [queryKwd, recordPubid]);

    const storedValueToFormValue = useRef(makeStoredValueToFormValue(getStore));
    const getClientCversion = useRef(makeGetClientCversion(() => getStore("mainCversion"), () => getStore("subCversion")));

    // 客户端cversion = `${mainCversion}.${subCversion}`
    (getStore("mainCversion") === undefined) ? setStore("mainCversion", 0) : undefined; // 客户端主版本号
    (getStore("subCversion")  === undefined) ? setStore("subCversion",  0) : undefined; // 客户端次版本号, 修改一次自增一次
    //let mainCversion = getStore("mainCversion"); // 主版本号, 与服务端cversion对应
    // 次版本号, 表示本地更改次数, 用于程序上对子版本号的增加, 用于判断与服务端版本的差异, 同时可用于判断避免重复从MMKV获取数据
    const subCversionRef = useRef(getStore("subCversion"));

    // 新组件需要修改!!
    const bottomBarLabels = { ok: "提交" };                    // 底部按钮显示文字
    const [snackBarMessage, setSnackBarMessage] = useState("更新表单遇到错误"); // 下方错误提示文字内容

    // Image pick num config
    const imageFields = ["processChart"];
    const pcPick = MAX_IMAGES_PICK; // processChart image pick num

    /*
    const [
        prodSystemRadioState,
        exptProdDatePickerState,
        setProdSystemRadioState,
        setExptProdDatePickerState,
        resetSelectorStates,
    ] = selectorStates(useShallow(state => [
        state.prodSystemRadio,
        state.exptProdDatePicker,
        state.setProdSystemRadio,
        state.setExptProdDatePicker,
        state.resetStates,
    ])); // radio组件状态
    */

    const [confimDialogConfig, setConfirmDialogConfig] = useState({});       // 对话框信息, {title, text, okLabel, onOK}, 但显示对话框的变量在showConfirm

    // 新组件不需改动
    const [screenTitle,      setScreenTitle]      = useState(route.params.pageMeta.name); // 屏幕上方导航栏标题
    const [okButtonLoading,  setOkButtonLoading]  = useState(false);            // 右下方按钮图标是否旋转
    const [okButtonDisabled, setOkButtonDisabled] = useState(!checkPermits());  // 右下方按钮是否禁用
    const [showSnackbar,     setShowSnackbar]     = useState(false);            // 下方是否显示错误通知
    const [showConfirm,      setShowConfirm]      = useState(false);            // 是否显示确认对话框
    const [loadingIndicatorVisible, setLoadingIndicatorVisible] = useState(false); // 是否显示loading
    const delQueryInfo = useRef({ id: recordPubid, name: recordName });         // 用于删除本记录的查询变量

    //const waterTypeStateDataProviderRef     = useRef([{ id: 1, name: "自来水" }, { id: 2, name: "地表水" }, { id: 3, name: "地下水" }, { id: 4, name: "市政中水" }]);
    //const accuracyLevelStateDataProviderRef = useRef([{ id: 1, name: "一级" }, { id: 2, name: "二级" }, ]);
    //const equipStateStateDataProviderRef    = useRef([{ id: 1, name: "合格" }, { id: 2, name: "不合格" },]);
    //const prodSystemStateDataProviderRef      = useRef(prodSystemEnum);

    // 用于存储waterScale的值, 当unitToggleState发生改变时需要通过该值更新表单字段
    //const waterUnitScaleRef = useRef(0);

    //const prodSystemValidatorRule = useRef(genRadioValidatorRule(0, "生产制度", 1, 2, 3));
    // Define form
    // 新组件需要重新定义!!
    const schema = Joi.object({
        waterSource:    validatorBase.waterBalance.commons.textField,
        waterUsageY:    validatorBase.waterBalance.commons.floatField,
        freshWater:     validatorBase.waterBalance.commons.floatField,
        elecSource:     validatorBase.waterBalance.commons.textField,
        elecUsageY:     validatorBase.waterBalance.commons.floatField,
        gasSource:      validatorBase.waterBalance.commons.textField,
        gasUsageY:      validatorBase.waterBalance.commons.floatField,
        coalSource:     validatorBase.waterBalance.commons.textField,
        coalUsageY:     validatorBase.waterBalance.commons.floatField,
        petrolSource:   validatorBase.waterBalance.commons.textField,
        petrolUsageY:   validatorBase.waterBalance.commons.floatField,
        pHeatSource:    validatorBase.waterBalance.commons.textField,
        pHeatUsageY:    validatorBase.waterBalance.commons.floatField,
        lHeatType:      validatorBase.waterBalance.commons.textField,
        lHeatNum:       validatorBase.waterBalance.commons.floatField,
        drainageMethod: validatorBase.waterBalance.commons.textField,
        processDesc:    validatorBase.waterBalance.commons.textField,
        processChart:   Joi.array().items(Joi.string()).min(1).max(pcPick).messages({"*": "请选择图像"}),
        airTreatment:   validatorBase.waterBalance.commons.textField,
        waterTreaement: validatorBase.waterBalance.commons.textField,
        noiseTreaement: validatorBase.waterBalance.commons.textField,
        wasteTreatment: validatorBase.waterBalance.commons.textField,
        // other fields
        others:       validatorBase.waterBalance.commons.textField,
        remarks:      validatorBase.waterBalance.commons.longTextField,
        // control fields, 用于控制客户端某些字段的修改情况, 服务端据此做进一步判断
        fctrl:        Joi.object(), // 由于不是显示字段, 看不到错误提示, 因此这里使用最简单的规则, 只保证数据类型是对象
    });

    // 新组件需要重新定义defaultValues!!
    const {
        handleSubmit,
        control,
        resetField,
        reset,
        formState: { errors },
        //setValue,
        //getValues,
    } = useForm({
        resolver: joiResolver(schema),
        //reValidateMode: "onBlur", // https://github.com/orgs/react-hook-form/discussions/10327
        defaultValues: { // 默认值在组件载入时会被更新, 这里的初始值正常情况下没有用处
            // 注意, 默认为0的是允许默认为0的值, 或者通过计算得到的值
            waterSource:    "",
            waterUsageY:    "",
            freshWater:     "",
            elecSource:     "",
            elecUsageY:     "",
            gasSource:      "",
            gasUsageY:      "",
            coalSource:     "",
            coalUsageY:     "",
            petrolSource:   "",
            petrolUsageY:   "",
            pHeatSource:    "",
            pHeatUsageY:    "",
            lHeatType:      "",
            lHeatNum:       "",
            drainageMethod: "",
            processDesc:    "",
            processChart:   [],
            airTreatment:   "",
            waterTreaement: "",
            noiseTreaement: "",
            wasteTreatment: "",
            // other fields
            others:        "",
            remarks:       "",
            // control fields, 用于控制客户端某些字段的修改情况, 服务端据此做进一步判断
            fctrl:         { processChart: "" }, // processChart字段, ""表示未修改, "a"表示添加到尾部append, "c"表示清空clear
        },
    });

    // Query: select record
    const onRecordSelectSuccess = (data) => { // data: {"id": 3, "leader": 0, "name": "DEPT-03", "superior": 0}
        // 注意, 进入onSuccess之前就意味着服务端版本比客户端版本新, 因此要更新客户端主版本号
        if (data.STATUS === 0) {
            setStore("mainCversion", data.DATA.cversion);
            setStore("subCversion", 0);

            const formObject = {
                waterSource: String(data.DATA.waterSource),
                waterUsageY: String(data.DATA.waterUsageY),
                freshWater: String(data.DATA.freshWater),
                elecSource: String(data.DATA.elecSource),
                elecUsageY: String(data.DATA.elecUsageY),
                gasSource: String(data.DATA.gasSource),
                gasUsageY: String(data.DATA.gasUsageY),
                coalSource: String(data.DATA.coalSource),
                coalUsageY: String(data.DATA.coalUsageY),
                petrolSource: String(data.DATA.petrolSource),
                petrolUsageY: String(data.DATA.petrolUsageY),
                pHeatSource: String(data.DATA.pHeatSource),
                pHeatUsageY: String(data.DATA.pHeatUsageY),
                lHeatType: String(data.DATA.lHeatType),
                lHeatNum: String(data.DATA.lHeatNum),
                drainageMethod: String(data.DATA.drainageMethod),
                processDesc: String(data.DATA.processDesc),
                processChart: data.DATA.processChart.sort().map(uri => `${assembleUrl(uri)}`), // 从服务端获取的uri是相对路径, 需要组装成绝对路径
                airTreatment: String(data.DATA.airTreatment),
                waterTreaement: String(data.DATA.waterTreaement),
                noiseTreaement: String(data.DATA.noiseTreaement),
                wasteTreatment: String(data.DATA.wasteTreatment),

                // other fields
                others:       String(data.DATA.others),
                remarks:      String(data.DATA.remarks),
                // control fields, 用于控制客户端某些字段的修改情况, 服务端据此做进一步判断
                fctrl:        { processChart: "" }, // processChart字段, ""表示未修改, "a"表示添加到尾部append, "c"表示清空clear
            };
            reset(formObject);           // 重置react-form

            const storeObjects = checkPermits() && (formType === 0) && {
                ...formObject,
                //prodSystem:      radioIdToObject(prodSystemStateDataProviderRef.current, data.DATA.prodSystem),
                //waterType:     radioIdToObject(waterTypeStateDataProviderRef.current,     data.DATA.waterType),
                //accuracyLevel: radioIdToObject(accuracyLevelStateDataProviderRef.current, data.DATA.accuracyLevel),
                //equipState:    radioIdToObject(equipStateStateDataProviderRef.current,    data.DATA.equipState),
            };
            storeObjects && setStoreObject(storeObjects, false); // 判断用户权限, 把服务端数据写到mmkv存储, 只有可写用户才需要本地存储, 不会首先清空老数据

            // 设置selector菜单状态
            //setProdSystemRadioState(radioIdToObject(prodSystemStateDataProviderRef.current, data.DATA.prodSystem));
            //setExptProdDatePickerState(formObject.exptProdDate);
            //setEndDatePickerState(formObject.endDate);

            //waterUnitScaleRef.current = data.DATA.waterScale || 0; // 根据服务端数据更新
            // 设置屏幕标题
            (screenTitle !== formObject.name) && setScreenTitle(formObject.name);
        } else {
            console.log("STATUS !== 0", data.STATUS);
            setSnackBarMessage(`表单数据拉取发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        }
    };
    const onRecordSelectError = (err) => {
        console.log("Record selecting error:", err);
    };
    const onRecordSelectSettled = (err) => {
        loadingIndicatorVisible && setLoadingIndicatorVisible(false);
    };
    const getPath = formType === 0 ? "get" : "getsum";
    const recordSelectQuery = recordSelectQueryMaker([queryKwd, getPath, recordPubid], onRecordSelectSuccess, onRecordSelectError, onRecordSelectSettled);

    // Query: update record
    const onHandleSubmit = (data) => {
        setOkButtonDisabled(true);
        setOkButtonLoading(true);
        dataFeeder({ ...data,
            cversion: getClientCversion.current(),
            name:     "公用工程",
            // 对象类型和复合数组必需转换为字符串, 否则发送消息时客户端会报错: Network request failed!
            processChart: JSON.stringify(data.processChart),
            fctrl:    JSON.stringify(getStore("fctrl")),
            // unifify data units
            // ...
        }); // append client cversion
        recordUpdateQuery.mutate();
    };

    // 新组件不需改动
    const onRecordUpdateSuccess = async (data) => {
        console.log(" Record update success response data: ", data);
        if(data.STATUS !== 0) {
            setSnackBarMessage(`表单更新发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        } else {
            // 传图片
            for (const field of imageFields) {
                const filePaths = getStore(field);
                const uploadUrl = makeFileUploadUrl(apiUri, [queryKwd, "upfile", field, recordPubid]);
                // 不使用数组forEach: https://medium.com/@steven234/遇到-async-別用-foreach-7cea84f4242f
                // https://stackoverflow.com/questions/37576685/using-async-await-with-a-foreach-loop
                await Promise.all(filePaths.map(async (filePath, i) => {
                    if(isLocalFile(filePath)) {
                        await uploadFile(filePath, `${i}`.padStart(4, "0"), uploadUrl);
                    }
                }));
            }
            //console.log("All files uploaded successfully..............................!");

            // 设置确认框数据
            setConfirmDialogConfig({
                title: "更新表单",
                text: "表单信息更新成功!",
                okLabel: "确定",
                onOK: () => {
                    setShowConfirm(false);
                    recordSelectQuery.mutate();
                }});
            setShowConfirm(true);
        }
    };
    const onRecordUpdateError = (error) => {
        setSnackBarMessage(`表单更新发生错误, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const onRecordUpdateSettled = (data, error) => {
        setOkButtonDisabled(false);
        setOkButtonLoading(false);
    };
    const recordUpdateQuery = recordUpdateQueryMaker(dataFeeder, [queryKwd, "upd", recordPubid], onRecordUpdateSuccess, onRecordUpdateError, onRecordUpdateSettled);

    // Query: delete record
    const recordDeleteOnSuccess = () => {
        clearStore();
        navigation.goBack();
    };
    const recordDeleteOnError = (error) => {
        setSnackBarMessage(`表单删除失败, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const recordDeleteQuery = recordDeleteQueryMaker([queryKwd, "del", recordPubid], recordDeleteOnSuccess, recordDeleteOnError);

    const deleteAlert = () => {
        return (
            Alert.alert("删除表单", `删除后不可撤销, 请确认是否删除: ${delQueryInfo.current?.name}?`, [
                { text: "取消" },
                { text: "确定", onPress: () => recordDeleteQuery.mutate() },
            ])
        );
    };

    const clearCache = () => {
        return (
            Alert.alert("清空本地缓存", "1. 用于未知原因导致数据无法提交的情况\r\n2. 未提交的内容将会丢失, 请做好数据截图备份\r\n3. 已提交到服务端的数据不受影响\r\n4. 确认清空请点击“确认”按钮", [
                { text: "取消" },
                { text: "确定", onPress: () => { clearStore(); navigation.goBack(); } },
            ])
        );
    };

    // Hook: onMount fetch data
    useEffect(() => {
        // 优化方案: 根据用户权限进行mutate, 只有可写用户才需要本地存储
        const localCversion = getClientCversion.current();
        const remoteCversion = route.params.pageMeta.cversion;
        console.log("localCversion:", localCversion, "remoteSversion:", remoteCversion, "subCversionRef:", subCversionRef.current);

        const restoreLocalData = () => {
            const formObject = { // 默认值用户表单的数据显示
                waterSource: storedValueToFormValue.current("waterSource"),
                waterUsageY: storedValueToFormValue.current("waterUsageY"),
                freshWater: storedValueToFormValue.current("freshWater"),
                elecSource: storedValueToFormValue.current("elecSource"),
                elecUsageY: storedValueToFormValue.current("elecUsageY"),
                gasSource: storedValueToFormValue.current("gasSource"),
                gasUsageY: storedValueToFormValue.current("gasUsageY"),
                coalSource: storedValueToFormValue.current("coalSource"),
                coalUsageY: storedValueToFormValue.current("coalUsageY"),
                petrolSource: storedValueToFormValue.current("petrolSource"),
                petrolUsageY: storedValueToFormValue.current("petrolUsageY"),
                pHeatSource: storedValueToFormValue.current("pHeatSource"),
                pHeatUsageY: storedValueToFormValue.current("pHeatUsageY"),
                lHeatType: storedValueToFormValue.current("lHeatType"),
                lHeatNum: storedValueToFormValue.current("lHeatNum"),
                drainageMethod: storedValueToFormValue.current("drainageMethod"),
                processDesc: storedValueToFormValue.current("processDesc"),
                processChart: storedValueToFormValue.current("processChart"),
                airTreatment: storedValueToFormValue.current("airTreatment"),
                waterTreaement: storedValueToFormValue.current("waterTreaement"),
                noiseTreaement: storedValueToFormValue.current("noiseTreaement"),
                wasteTreatment: storedValueToFormValue.current("wasteTreatment"),
                // other fields
                others:       storedValueToFormValue.current("others"),
                remarks:      storedValueToFormValue.current("remarks"),
                // field control
                fctrl:        storedValueToFormValue.current("fctrl"),
            };
            reset(formObject); // 重置react-form数据

            // 设置selector数据
            //const defaultProdSystem = 0;
            //setProdSystemRadioState(getStore("prodSystem") || radioIdToObject(prodSystemStateDataProviderRef.current, defaultProdSystem));
            //setExptProdDatePickerState(formObject.exptProdDate);
            //setEndDatePickerState(formObject.endDate);

            //waterUnitScaleRef.current = getStore("waterScale")?.id || defaultWaterUnitScale; // 根据本地存储更新
        };

        // 根据反馈, 要求默认显示0
        if (localCversion === 0) {
            recordSelectQuery.mutate();
            return;
        }

        if(formType === 1) {
            recordSelectQuery.mutate();
        } else if (localCversion === 0 && remoteCversion > 0) {
            console.log("Local cversion === 0, serve may have something nontrivial initialized, query mutate!");
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        } else if (isServerDataNewer(localCversion, remoteCversion) && subCversionRef.current > 0) { // 服务端有更新数据, 但本地有修改
            console.log("Client cversion fell behind the server cversion, someone might modified the database, choose to use the data version!");
            Alert.alert("注意", "由于其他人向服务器提交了数据, 服务端数据比本地数据新, 请选择使用哪份数据?", [
                { text: "使用本地数据", onPress: () => { setStore("mainCversion", remoteCversion); restoreLocalData(); } },
                { text: "使用服务端数据", onPress: () => { setLoadingIndicatorVisible(true); recordSelectQuery.mutate(); } },
            ]);
        } else if (isServerDataNewer(localCversion, remoteCversion)) { // localCversion==0时要mutate是因为服务端可能在创建记录时生成了必要数据
            console.log("Server cversion may be newer, query mutate:", localCversion, remoteCversion);
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        }
        else {
            console.log("Local cversion >= server cversion, query will not mutate!");
            restoreLocalData();
        }
    }, []);


    // remarks不需配置
    const FieldsConfig_1_book = [
        {
            inputs: [
                //{ name: "name",        label: "表单名称",     unit: "",     type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                { name: "waterSource",    label: "供水来源",        unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {required: true}, },
                { name: "waterUsageY",    label: "年使用量",        unit: "立方米", type: "PLAIN", editable: true, placeholder: "", props: {required: true}, },
                { name: "freshWater",     label: "新鲜用水量",       unit: "立方米", type: "PLAIN", editable: true, placeholder: "", props: {required: true}, },
                { name: "elecSource",     label: "供电来源",        unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {required: true}, },
                { name: "elecUsageY",     label: "年电用量",        unit: "度",     type: "PLAIN", editable: true, placeholder: "", props: {required: true}, },
                { name: "gasSource",      label: "供气来源",        unit: "",       type: "PLAIN", editable: true, placeholder: "", props: {required: true}, },
                { name: "gasUsageY",      label: "年气用量",        unit: "立方米" , type: "PLAIN", editable: true, placeholder: "", props: {required: true}, },
                { name: "coalSource",     label: "煤炭",           unit: "",       type: "PLAIN", editable: true, placeholder: "", props: {required: true}, },
                { name: "coalUsageY",     label: "煤炭年用量",      unit: "吨",      type: "PLAIN", editable: true, placeholder: "", props: {required: true}, },
                { name: "petrolSource",   label: "汽油或煤油",      unit: "",       type: "PLAIN", editable: true, placeholder: "", props: {required: true}, },
                { name: "petrolUsageY",   label: "油年用量",        unit: "吨",     type: "PLAIN", editable: true, placeholder: "", props: {required: true}, },
                { name: "pHeatSource",    label: "生产供热来源",     unit: "",       type: "PLAIN", editable: true, placeholder: "", props: {required: true}, },
                { name: "pHeatUsageY",    label: "热年用量",        unit: "GJ",     type: "PLAIN", editable: true, placeholder: "", props: {required: true}, },
                { name: "lHeatType",      label: "生活取暖方式",     unit: "",       type: "PLAIN", editable: true, placeholder: "", props: {required: true}, },
                { name: "lHeatNum",       label: "数量",            unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {required: true}, },
                { name: "drainageMethod", label: "排水方式",        unit: "",       type: "PLAIN", editable: true, placeholder: "", props: {multiline: true, required: true}, },
                { name: "processDesc",    label: "生产工艺说明",     unit: "",       type: "PLAIN", editable: true, placeholder: "", props: {multiline: true, required: true}, },
                { name: "processChart",   label: "工艺流程图",                       type: "IMAGE", editable: true, placeholder: "", pickNum: pcPick, crop: false, compress: 0.8, height: 150,  props: { required: true}, },
                { name: "airTreatment",   label: "空气污染物处理方式", unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                { name: "waterTreaement", label: "水污染物处理方式",   unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                { name: "noiseTreaement", label: "噪声处理方式",      unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                { name: "wasteTreatment", label: "固废处理方式",      unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },

            ]
        },
    ];
    const FieldsConfig_1_table = [
        {
            inputs: [
                //{ name: "name",        label: "表单名称",     unit: "",     type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                { name: "waterSource",    label: "供水来源",        unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {required: true}, },
                { name: "waterUsageY",    label: "年使用量",        unit: "立方米", type: "PLAIN", editable: true, placeholder: "", props: {required: true}, },
                { name: "freshWater",     label: "新鲜用水量",       unit: "立方米", type: "PLAIN", editable: true, placeholder: "", props: {required: true}, },
                { name: "elecSource",     label: "供电来源",        unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {required: true}, },
                { name: "elecUsageY",     label: "年电用量",        unit: "度",     type: "PLAIN", editable: true, placeholder: "", props: {required: true}, },
                { name: "gasSource",      label: "供气来源",        unit: "",       type: "PLAIN", editable: true, placeholder: "", props: {required: true}, },
                { name: "gasUsageY",      label: "年气用量",        unit: "立方米" , type: "PLAIN", editable: true, placeholder: "", props: {required: true}, },
                { name: "coalSource",     label: "煤炭",           unit: "",       type: "PLAIN", editable: true, placeholder: "", props: {required: true}, },
                { name: "coalUsageY",     label: "煤炭年用量",      unit: "吨",      type: "PLAIN", editable: true, placeholder: "", props: {required: true}, },
                { name: "petrolSource",   label: "汽油或煤油",      unit: "",       type: "PLAIN", editable: true, placeholder: "", props: {required: true}, },
                { name: "petrolUsageY",   label: "油年用量",        unit: "吨",     type: "PLAIN", editable: true, placeholder: "", props: {required: true}, },
                { name: "pHeatSource",    label: "生产供热来源",     unit: "",       type: "PLAIN", editable: true, placeholder: "", props: {required: true}, },
                { name: "pHeatUsageY",    label: "热年用量",        unit: "GJ",     type: "PLAIN", editable: true, placeholder: "", props: {required: true}, },
                { name: "lHeatType",      label: "生活取暖方式",     unit: "",       type: "PLAIN", editable: true, placeholder: "", props: {required: true}, },
                { name: "lHeatNum",       label: "数量",            unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {required: true}, },
                { name: "drainageMethod", label: "排水方式",        unit: "",       type: "PLAIN", editable: true, placeholder: "", props: {multiline: true, required: true}, },
                { name: "processDesc",    label: "生产工艺说明",     unit: "",       type: "PLAIN", editable: true, placeholder: "", props: {multiline: true, required: true}, },
                { name: "processChart",   label: "工艺流程图",                       type: "IMAGE", editable: true, placeholder: "", pickNum: pcPick, crop: false, compress: 0.8, height: 150,  props: { required: true}, },
                { name: "airTreatment",   label: "空气污染物处理方式", unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                { name: "waterTreaement", label: "水污染物处理方式",   unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                { name: "noiseTreaement", label: "噪声处理方式",      unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                { name: "wasteTreatment", label: "固废处理方式",      unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },

            ]
        },
    ];

    const FieldsConfig_2_book  = [];
    const FieldsConfig_2_table = [];

    const FieldsConfig_3_book = [
        // 其它部分
        {
            inputs: [
                //{ name: "others", label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "",  props: {multiline: true}, },
            ]
        },
    ];
    const FieldsConfig_3_table = [
        // 其它部分
        {
            inputs: [
                //{ name: "others", label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "",  props: {multiline: true}, },
            ]
        },
    ];

    let FieldsConfig = [];
    switch(projSubclass) {
        case 1: // 表
            FieldsConfig = [...FieldsConfig_1_table, ...FieldsConfig_2_table, ...FieldsConfig_3_table];
            break;
        case 2: // 书
            FieldsConfig = [...FieldsConfig_1_book, ...FieldsConfig_2_book, ...FieldsConfig_3_book];
            break;
        default:
            console.warn("Unknown subclass:", projSubclass);
            FieldsConfig = [];
            break;
    }

    //const FieldsConfig = [...FieldsConfig_1_book, ...FieldsConfig_2_book, ...FieldsConfig_3_book];

    return (
        <View style={{ flex: 1 }}>

            <HeaderBar
                //title={screenTitle}
                navigation={navigation}
                goBackCallback={() => {
                    callOneByOne(
                        //() => { _mainCversionGetter=undefined; _subCversionGetter=undefined;},
                        //resetSelectorStates,
                    );
                    /*callOneByOne(clearStore, resetSelectorStates)*/  // 返回前清空mmkv存储因为临时数据不再需要, 重置状态使得页面切换后不会使用前面的数据
                }}
                menuItemArray={formType === 0 ? [{ title: "删除表单", action: deleteAlert }, { title: "清空缓存", action: clearCache }] : []}
            />

            <ScreenWrapper contentContainerStyle={styles.container}>

                <View style={styles.formEntry}>
                    <FormListNodesMapper
                        fieldsConfig={FieldsConfig}
                        subCversionRef={subCversionRef}
                        control={control}
                        setStore={setStore}
                        getStore={getStore}
                        resetField={resetField}
                        errors={errors}
                        setSnackBarMessage={setSnackBarMessage}
                        setShowSnackbar={setShowSnackbar}
                        //formDisabledGlobal={formDisabledGlobal}
                        editable={formEditableGlobal}
                        rowStyle={styles.rowContainer}
                    />
                    <Divider bold={true}/>

                    {/*formType === 0 && <ControlledTextInput
                        name="remarks"
                        rowLabel="备注"
                        control={control}
                        placeholder=""
                        onChangeText={(text) => {
                            subCversionRef.current++;
                            setStore("subCversion", subCversionRef.current);
                            setStore("remarks", text);
                        }}
                        onClearText={() => {
                            subCversionRef.current++;
                            setStore("subCversion", subCversionRef.current);
                            setStore("remarks", "");
                            resetField("remarks", { defaultValue: "" });
                        }}
                        editable={formEditableGlobal}
                        mode="flat"
                        multiline={true}
                        formError={errors}
                        style={styles.rowContainerLongText}
                        //disabled={formDisabledGlobal}
                        required={false}
                    />*/}
                </View>

                {formType === 0 && <View style={styles.button}>
                    <Button mode="contained"
                        disabled={okButtonDisabled}
                        loading={okButtonLoading}
                        icon={saveButtonIcon}
                        onPress={handleSubmit(onHandleSubmit, (formErr) => onPreSubmitError(formErr, getStore, "ClientPublicUtilityRecordsUpdating"))}
                    >{bottomBarLabels.ok}</Button>
                </View>}
            </ScreenWrapper>

            <LoadingIndicator
                title={""}
                message={"加载中....."}
                visible={loadingIndicatorVisible}
                onClose={()=>setLoadingIndicatorVisible(false)}
            />

            <Snackbar
                style={styles.snackBar}
                visible={showSnackbar}
                onDismiss={() => setShowSnackbar(false)}
                onIconPress={() => setShowSnackbar(false)}
                duration={Snackbar.DURATION_LONG}
            >
                {snackBarMessage}
            </Snackbar>

            <DialogToConfirm
                visible={showConfirm}
                title={confimDialogConfig.title ? confimDialogConfig.title : ""}
                text={confimDialogConfig.text ? confimDialogConfig.text : ""}
                onOK={confimDialogConfig.onOK ? confimDialogConfig.onOK : () => setShowConfirm(false)}
                okBtnLabel={confimDialogConfig.okLabel ? confimDialogConfig.okLabel : "确认"}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    rowContainer:{
        firstColumn: {
            flex: 2,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 3,
        // /borderWidth: 1,
        // height: 40,
        },
    },
    rowContainerLongText: {
        firstColumn: {
            flex: 1,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 4,
            // /borderWidth: 1,
            // height: 40,
        },
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        width: "80%",
        alignSelf: "center",
    },
    formEntry: {
        margin: 8,
    },
    snackBar: {
        flexDirection: "column",
    },
});

export default RecordsUpdating;
