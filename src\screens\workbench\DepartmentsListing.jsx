import React from "react";
import ListingTemplate from "./ListingTemplate";
import { makeRequestListingDepartments as queryClient } from "../../api/listingQueries";
import { allDepartmentsState as pageDataState } from "../../hooks/globalStates";


const DepartmentsListing = ({ navigation }) => {
    return (
        <ListingTemplate
            bottomBarLabel={"添加部门"}
            emptyScreenText={"请点击下方按钮添加部门"}
            snackBarDefaultText={"添加部门遇到错误"}
            saveButtonIcon={"account-plus-outline"}
            queryClient={queryClient}
            pageDataState={pageDataState}
            listItemNavigateTo={"WorkbenchDepartmentsUpdating"}
            addingButtonNavigateTo={"WorkbenchDepartmentsInserting"}
            navigation={navigation}
        />
    );
};

export default DepartmentsListing;
