import { useMutation } from "@tanstack/react-query";
import { CLIENT_OS } from "../config";
import { PING as key } from "../config/keysConfig";
import { httpClient } from "../services/http";
import { makeCounter } from "../utils";
import { getUniversalTime } from "../utils/time";

const counter = makeCounter(0, 1);



/**
 * Return a useQuery of react query.
 * @param {(msg)=>{}} action A function that will be called if the client receives a message from the server.
 */
const makePingQuery = (onSuccess, onError, onSettled, action) => {
    // no retry for both ky and query, user will retry by himself
    return useMutation({
        mutationKey: [key.query],
        mutationFn: async () => {
            const formData = new FormData();
            formData.append("os", CLIENT_OS);
            formData.append("id", counter());
            formData.append("time", getUniversalTime());
            //console.debug(`Make a PING to server for path ${key.url} with data ${formData.getAll()}.`);
            const response = await httpClient.post(key.url, { body: formData, retry: { limit: 0 } });
            const json = await response.json();
            action ? action(json) : undefined;
            return json;
        },
        onSuccess: (data, variables, context) => {
            //console.log("Call onSuccess");
            onSuccess?.(data);
        },
        onError: (error, variables, context) => {
            //console.log("Call onError");
            onError?.(error);
        },
        onSettled: (data, error, variables, context) => {
            //console.log("Call onSettled");
            onSettled?.(data, error);
        },

        retry: false,
    });
};

export { makePingQuery };

