import MaterialDesignIcons from "@react-native-vector-icons/material-design-icons";
import React from "react";
import { StyleSheet } from "react-native";
import { Badge } from "react-native-paper";
//import Icon from "react-native-vector-icons/MaterialCommunityIcons";

const BadgedIcon = ({ iconName, iconSize, iconColor, budgeVisible, budgeText }) => {
    return (
        <>
            <MaterialDesignIcons name={iconName} size={iconSize} color={iconColor} />
            <Badge visible={budgeVisible} style={styles.badge} size={8}>{budgeText || ""}</Badge>
        </>
    );
};

const styles = StyleSheet.create({
    badge: {
        position: "absolute",
        top: 0,
        right: -5,
    },
});

export default BadgedIcon;
