///
/// JAVEncoderAudioQualityIOSType.hpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc Rousavy @ Margelo
///

#pragma once

#include <fbjni/fbjni.h>
#include "AVEncoderAudioQualityIOSType.hpp"

namespace margelo::nitro::react_native_audio_recorder_player {

  using namespace facebook;

  /**
   * The C++ JNI bridge between the C++ enum "AVEncoderAudioQualityIOSType" and the the Kotlin enum "AVEncoderAudioQualityIOSType".
   */
  struct JAVEncoderAudioQualityIOSType final: public jni::JavaClass<JAVEncoderAudioQualityIOSType> {
  public:
    static auto constexpr kJavaDescriptor = "Lcom/margelo/nitro/reactnativeaudiorecorderplayer/AVEncoderAudioQualityIOSType;";

  public:
    /**
     * Convert this Java/Kotlin-based enum to the C++ enum AVEncoderAudioQualityIOSType.
     */
    [[maybe_unused]]
    [[nodiscard]]
    AVEncoderAudioQualityIOSType toCpp() const {
      static const auto clazz = javaClassStatic();
      static const auto fieldOrdinal = clazz->getField<int>("_ordinal");
      int ordinal = this->getFieldValue(fieldOrdinal);
      return static_cast<AVEncoderAudioQualityIOSType>(ordinal);
    }

  public:
    /**
     * Create a Java/Kotlin-based enum with the given C++ enum's value.
     */
    [[maybe_unused]]
    static jni::alias_ref<JAVEncoderAudioQualityIOSType> fromCpp(AVEncoderAudioQualityIOSType value) {
      static const auto clazz = javaClassStatic();
      static const auto fieldMIN = clazz->getStaticField<JAVEncoderAudioQualityIOSType>("MIN");
      static const auto fieldLOW = clazz->getStaticField<JAVEncoderAudioQualityIOSType>("LOW");
      static const auto fieldMEDIUM = clazz->getStaticField<JAVEncoderAudioQualityIOSType>("MEDIUM");
      static const auto fieldHIGH = clazz->getStaticField<JAVEncoderAudioQualityIOSType>("HIGH");
      static const auto fieldMAX = clazz->getStaticField<JAVEncoderAudioQualityIOSType>("MAX");
      
      switch (value) {
        case AVEncoderAudioQualityIOSType::MIN:
          return clazz->getStaticFieldValue(fieldMIN);
        case AVEncoderAudioQualityIOSType::LOW:
          return clazz->getStaticFieldValue(fieldLOW);
        case AVEncoderAudioQualityIOSType::MEDIUM:
          return clazz->getStaticFieldValue(fieldMEDIUM);
        case AVEncoderAudioQualityIOSType::HIGH:
          return clazz->getStaticFieldValue(fieldHIGH);
        case AVEncoderAudioQualityIOSType::MAX:
          return clazz->getStaticFieldValue(fieldMAX);
        default:
          std::string stringValue = std::to_string(static_cast<int>(value));
          throw std::invalid_argument("Invalid enum value (" + stringValue + "!");
      }
    }
  };

} // namespace margelo::nitro::react_native_audio_recorder_player
