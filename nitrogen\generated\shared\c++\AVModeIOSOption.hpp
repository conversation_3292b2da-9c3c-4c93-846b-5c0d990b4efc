///
/// AVModeIOSOption.hpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON> @ Margelo
///

#pragma once

#if __has_include(<NitroModules/NitroHash.hpp>)
#include <NitroModules/NitroHash.hpp>
#else
#error NitroModules cannot be found! Are you sure you installed NitroModules properly?
#endif
#if __has_include(<NitroModules/JSIConverter.hpp>)
#include <NitroModules/JSIConverter.hpp>
#else
#error NitroModules cannot be found! Are you sure you installed NitroModules properly?
#endif
#if __has_include(<NitroModules/NitroDefines.hpp>)
#include <NitroModules/NitroDefines.hpp>
#else
#error NitroModules cannot be found! Are you sure you installed NitroModules properly?
#endif

namespace margelo::nitro::react_native_audio_recorder_player {

  /**
   * An enum which can be represented as a JavaScript union (AVModeIOSOption).
   */
  enum class AVModeIOSOption {
    GAMECHATAUDIO      SWIFT_NAME(gamechataudio) = 0,
    VIDEORECORDING      SWIFT_NAME(videorecording) = 1,
    VOICECHAT      SWIFT_NAME(voicechat) = 2,
    VIDEOCHAT      SWIFT_NAME(videochat) = 3,
  } CLOSED_ENUM;

} // namespace margelo::nitro::react_native_audio_recorder_player

namespace margelo::nitro {

  using namespace margelo::nitro::react_native_audio_recorder_player;

  // C++ AVModeIOSOption <> JS AVModeIOSOption (union)
  template <>
  struct JSIConverter<AVModeIOSOption> final {
    static inline AVModeIOSOption fromJSI(jsi::Runtime& runtime, const jsi::Value& arg) {
      std::string unionValue = JSIConverter<std::string>::fromJSI(runtime, arg);
      switch (hashString(unionValue.c_str(), unionValue.size())) {
        case hashString("gameChatAudio"): return AVModeIOSOption::GAMECHATAUDIO;
        case hashString("videoRecording"): return AVModeIOSOption::VIDEORECORDING;
        case hashString("voiceChat"): return AVModeIOSOption::VOICECHAT;
        case hashString("videoChat"): return AVModeIOSOption::VIDEOCHAT;
        default: [[unlikely]]
          throw std::invalid_argument("Cannot convert \"" + unionValue + "\" to enum AVModeIOSOption - invalid value!");
      }
    }
    static inline jsi::Value toJSI(jsi::Runtime& runtime, AVModeIOSOption arg) {
      switch (arg) {
        case AVModeIOSOption::GAMECHATAUDIO: return JSIConverter<std::string>::toJSI(runtime, "gameChatAudio");
        case AVModeIOSOption::VIDEORECORDING: return JSIConverter<std::string>::toJSI(runtime, "videoRecording");
        case AVModeIOSOption::VOICECHAT: return JSIConverter<std::string>::toJSI(runtime, "voiceChat");
        case AVModeIOSOption::VIDEOCHAT: return JSIConverter<std::string>::toJSI(runtime, "videoChat");
        default: [[unlikely]]
          throw std::invalid_argument("Cannot convert AVModeIOSOption to JS - invalid value: "
                                    + std::to_string(static_cast<int>(arg)) + "!");
      }
    }
    static inline bool canConvert(jsi::Runtime& runtime, const jsi::Value& value) {
      if (!value.isString()) {
        return false;
      }
      std::string unionValue = JSIConverter<std::string>::fromJSI(runtime, value);
      switch (hashString(unionValue.c_str(), unionValue.size())) {
        case hashString("gameChatAudio"):
        case hashString("videoRecording"):
        case hashString("voiceChat"):
        case hashString("videoChat"):
          return true;
        default:
          return false;
      }
    }
  };

} // namespace margelo::nitro
