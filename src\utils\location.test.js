import { coordsToDMS, showLocationInputCoords, showLocationCoordsObj } from './location';

describe('location utils', () => {
  describe('coordsToDMS', () => {
    it('should convert positive coordinates correctly in Chinese', () => {
      const result = coordsToDMS(116, 39);
      expect(result).toEqual({
        longitude: '东经116°0′0″',
        latitude: '北纬39°0′0″'
      });
    });

    it('should convert negative coordinates correctly in Chinese', () => {
      const result = coordsToDMS(-151, -33);
      expect(result).toEqual({
        longitude: '西经151°0′0″',
        latitude: '南纬33°0′0″'
      });
    });

    it('should convert coordinates correctly in English', () => {
      const result = coordsToDMS(51, -0, 0, 'en');
      expect(result).toEqual({
        longitude: '51°0′0″E',
        latitude: '0°0′0″N'
      });
    });

    it('should handle decimal seconds with precision', () => {
      const result = coordsToDMS(116.5, 39.5, 2);
      expect(result).toEqual({
        longitude: '东经116°30′0″',
        latitude: '北纬39°30′0″'
      });
    });

    it('should handle edge cases (near 60 minutes/seconds)', () => {
      const result = coordsToDMS(116.999999, 39.999999);
      expect(result).toEqual({
        longitude: '东经117°0′0″',
        latitude: '北纬40°0′0″'
      });
    });

    it('should handle zero coordinates', () => {
      const result = coordsToDMS(0, 0);
      expect(result).toEqual({
        longitude: '东经0°0′0″',
        latitude: '北纬0°0′0″'
      });
    });
  });

  describe('showLocationInputCoords', () => {
    it('should format valid coordinates correctly', () => {
      const result = showLocationInputCoords([116, 39]);
      expect(result).toBe('东经116°0′0″\n北纬39°0′0″');
    });

    it('should return empty string for [0,0] coordinates', () => {
      const result = showLocationInputCoords([0, 0]);
      expect(result).toBe('');
    });

    it('should return empty string for null/undefined input', () => {
      expect(showLocationInputCoords(null)).toBe('');
      expect(showLocationInputCoords(undefined)).toBe('');
    });

    it('should show coordinates when only one is 0', () => {
      const result1 = showLocationInputCoords([116, 0]);
      const result2 = showLocationInputCoords([0, 39]);
      expect(result1).toBe('东经116°0′0″\n北纬0°0′0″');
      expect(result2).toBe('东经0°0′0″\n北纬39°0′0″');
    });

    it('should handle negative coordinates', () => {
      const result = showLocationInputCoords([-151, -33]);
      expect(result).toBe('西经151°0′0″\n南纬33°0′0″');
    });

    it('should use custom separator and precision', () => {
      const result = showLocationInputCoords([116.5, 39.5], ', ', 2);
      expect(result).toBe('东经116°30′0″, 北纬39°30′0″');
    });

    it('should handle English language format', () => {
      const result = showLocationInputCoords([116, 39], '\n', 0, 'en');
      expect(result).toBe('116°0′0″E\n39°0′0″N');
    });
  });

  describe('showLocationCoordsObj', () => {
    it('should format complete location object correctly', () => {
      const result = showLocationCoordsObj({
        name: '测试地点',
        desc: '测试描述',
        x: 116,
        y: 39
      });
      expect(result).toBe('测试地点\n测试描述\n东经116°0′0″\n北纬39°0′0″');
    });

    it('should handle missing name and desc', () => {
      const result = showLocationCoordsObj({
        x: 116,
        y: 39
      });
      expect(result).toBe('东经116°0′0″\n北纬39°0′0″');
    });

    it('should handle null/undefined input', () => {
      expect(showLocationCoordsObj(null)).toBe('');
      expect(showLocationCoordsObj(undefined)).toBe('');
    });

    it('should handle missing or zero coordinates', () => {
      const result1 = showLocationCoordsObj({
        name: '测试地点',
        desc: '测试描述'
      });
      const result2 = showLocationCoordsObj({
        name: '测试地点',
        desc: '测试描述',
        x: 0,
        y: 0
      });
      expect(result1).toBe('测试地点\n测试描述\n东经0°0′0″\n北纬0°0′0″');
      expect(result2).toBe('测试地点\n测试描述\n东经0°0′0″\n北纬0°0′0″');
    });

    it('should handle partial coordinates', () => {
      const result1 = showLocationCoordsObj({
        name: '测试地点',
        x: 116
      });
      const result2 = showLocationCoordsObj({
        desc: '测试描述',
        y: 39
      });
      expect(result1).toBe('测试地点\n东经116°0′0″\n北纬0°0′0″');
      expect(result2).toBe('测试描述\n东经0°0′0″\n北纬39°0′0″');
    });
  });
});
