import { joi<PERSON>esolver } from "@hookform/resolvers/joi";
import "fast-text-encoding";
import <PERSON><PERSON> from "joi";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { Alert, StyleSheet, View } from "react-native";
import { Divider, Snackbar } from "react-native-paper";
import Button from "../../../components/Button";
import { DialogToConfirm } from "../../../components/DialogToConfirm";
import { FormListNodesMapper } from "../../../components/FormListNodesMapper";
import HeaderBar from "../../../components/HeaderBar";
import { LoadingIndicator } from "../../../components/LoadingIndicator";
import { creatMMKVStore, makeStoredValueToFormValue } from "../../../services/local-storage";
import { callOneByOne, ifTruthLet, isNullness, makeDataFeeder, parseIndustryCode } from "../../../utils";
import { checkPermits } from "../../../utils/permits";
import { isServerDataNewer, makeGetClientCversion, radioIdToObject } from "../../../utils/records";
import { validatorBase } from "../../../utils/validatorBase";
import ScreenWrapper from "../../ScreenWrapper";

// 新组件需要重新!! 如果遇到非水平衡表格就要更改key
import { useShallow } from "zustand/shallow";
import {
    makeReqDelWaterBalanceRecords as recordDeleteQueryMaker,
    makeReqGetWaterBalanceRecords as recordSelectQueryMaker,
    makeReqUpdWaterBalanceRecords as recordUpdateQueryMaker,
} from "../../../api/projectRecordsQueries";
import { UPD_WATER_BALANCE_RECORDS as pageMainKey } from "../../../config/keysConfig";
import { commonBinaryEnum, hospitalGradeEnum, instHasBathroomEnum, instHasCanteenEnum, instHasDormitoryEnum, subindustryEnum } from "../../../config/waterBalance";
import { wbWaterQuotaInfoStates as selectorStates } from "../../../hooks/selectorStates";
import { onPreSubmitError } from "../../../utils/screens";

import WQS_Catering from "../../../assets/water-quota/WQS_Catering.png";
import WQS_Culture from "../../../assets/water-quota/WQS_Culture.png";
import WQS_Education from "../../../assets/water-quota/WQS_Education.png";
import WQS_Hospital from "../../../assets/water-quota/WQS_Hospital.png";
import WQS_Hotel from "../../../assets/water-quota/WQS_Hotel.png";
import WQS_Institute from "../../../assets/water-quota/WQS_Institute.png";
import WQS_Office from "../../../assets/water-quota/WQS_Office.png";
import WQS_Supermarkt from "../../../assets/water-quota/WQS_Supermarkt.png";

import { StaticImageView } from "../../../components/StaticImageView";
const dataFeeder = makeDataFeeder();

const collectFieldsObject = (fieldNameArray, ValsObject) => {
    let res = {};
    fieldNameArray.forEach(fieldName => {
        res[fieldName] = ValsObject[fieldName];
    });
    return res;
};

/**
 * 用水定额参考表
 * 目前只有表有此需求, 因此在表格类型列表中只有表项目会显示出来.
 * @param {object} args
 * @param {object} args.navigation
 * @param {object} args.route
 * @returns
 */
const RecordsUpdating = ({ navigation, route }) => {
    console.log("RecordsUpdating pageMeta from nav:", route.params.pageMeta);
    console.log("RecordsUpdating projMeta from nav:", route.params.projMeta);
    const projSubclass = route.params.projMeta.subclass; // 1表, 2书
    const projIndustry = parseIndustryCode(route.params.projMeta.industry); // 1 -> industry, 2 -> service

    //const { bottom, left, right } = useSafeAreaInsets();

    // formType对应着服务端的form-type, 为0时表示表单是用户填写, 为1表示服务端生成, 为1时只读
    const { queryKwd, id: recordPubid, name: recordName, formType } = route.params.pageMeta; // recordPubid is the alias of id
    const saveButtonIcon = "content-save-all-outline";
    const formEditableGlobal = checkPermits() ? (formType === 0 ? true : false) : false;

    // 使用memoize来优化
    const { setStore, getStore, clearStore, setStoreObject } = useMemo(() => {
        console.log("creatMMKVStore memo:", pageMainKey.store, queryKwd, recordPubid);
        return creatMMKVStore(`${pageMainKey.store}/${queryKwd}/${recordPubid}`);
    }, [queryKwd, recordPubid]);

    const storedValueToFormValue = useRef(makeStoredValueToFormValue(getStore));
    const getClientCversion = useRef(makeGetClientCversion(() => getStore("mainCversion"), () => getStore("subCversion")));

    // 客户端cversion = `${mainCversion}.${subCversion}`
    (getStore("mainCversion") === undefined) ? setStore("mainCversion", 0) : undefined; // 客户端主版本号
    (getStore("subCversion")  === undefined) ? setStore("subCversion",  0) : undefined; // 客户端次版本号, 修改一次自增一次
    //let mainCversion = getStore("mainCversion"); // 主版本号, 与服务端cversion对应
    // 次版本号, 表示本地更改次数, 用于程序上对子版本号的增加, 用于判断与服务端版本的差异, 同时可用于判断避免重复从MMKV获取数据
    const subCversionRef = useRef(getStore("subCversion"));

    // 新组件需要修改!!
    const bottomBarLabels = { ok: "提交" };                    // 底部按钮显示文字
    const [snackBarMessage, setSnackBarMessage] = useState("更新表单遇到错误"); // 下方错误提示文字内容

    const [
        // states
        commonBinaryState1,
        commonBinaryState2,
        commonBinaryState3,
        subindustryState,
        hospGradeState,
        instHasCanteenState,
        instHasDormitoryState,
        instHasBathroomState,
        // set states
        setCommonBinaryState1,
        setCommonBinaryState2,
        setCommonBinaryState3,
        setSubindustryState,
        setHospGradeState,
        setInstHasCanteenState,
        setInstHasDormitoryState,
        setInstHasBathroomState,
        resetSelectorStates,
    ] = selectorStates(useShallow(state => [
        // states
        state.commonBinaryRadio1,
        state.commonBinaryRadio2,
        state.commonBinaryRadio3,
        state.subindustryRadio,
        state.hospGradeRadio,
        state.instHasCanteenRadio,
        state.instHasDormitoryRadio,
        state.instHasBathroomRadio,
        // set states
        state.setCommonBinaryRadio1,
        state.setCommonBinaryRadio2,
        state.setCommonBinaryRadio3,
        state.setSubindustryRadio,
        state.setHospGradeRadio,
        state.setInstHasCanteenRadio,
        state.setInstHasDormitoryRadio,
        state.setInstHasBathroomRadio,
        state.resetStates,
    ])); // radio组件状态

    const [confimDialogConfig, setConfirmDialogConfig] = useState({});       // 对话框信息, {title, text, okLabel, onOK}, 但显示对话框的变量在showConfirm

    // 新组件不需改动
    const [screenTitle,      setScreenTitle]      = useState(route.params.pageMeta.name); // 屏幕上方导航栏标题
    const [okButtonLoading,  setOkButtonLoading]  = useState(false);            // 右下方按钮图标是否旋转
    const [okButtonDisabled, setOkButtonDisabled] = useState(!checkPermits());  // 右下方按钮是否禁用
    const [showSnackbar,     setShowSnackbar]     = useState(false);            // 下方是否显示错误通知
    const [showConfirm,      setShowConfirm]      = useState(false);            // 是否显示确认对话框
    const [loadingIndicatorVisible, setLoadingIndicatorVisible] = useState(false); // 是否显示loading
    const delQueryInfo = useRef({ id: recordPubid, name: recordName });         // 用于删除本记录的查询变量

    const [quotaRefImage, setQuotaRefImg] = useState(null);

    const subindustryStateDataProviderRef      = useRef(subindustryEnum);
    const hospGradeStateDataProviderRef        = useRef(hospitalGradeEnum);
    const instHasCanteenStateDataProviderRef   = useRef(instHasCanteenEnum);
    const instHasDormitoryStateDataProviderRef = useRef(instHasDormitoryEnum);
    const instHasBathroomStateDataProviderRef  = useRef(instHasBathroomEnum);
    const commonBinaryStateDataProviderRef    = useRef(commonBinaryEnum);

    /*
    export const subindustryEnum = [
        { id: 1, name: "医院" },
        { id: 2, name: "学校" },
        { id: 3, name: "机关" },
        { id: 4, name: "酒店-住宿业" },
        { id: 5, name: "写字楼" },
        { id: 6, name: "餐饮" },
        { id: 7, name: "商超" },
        { id: 8, name: "文化娱乐" },
        { id: 0, name: "其他服务业" },
    ];
    1 医院类 医院年门诊人次 医院编制床位数量 医院年住院人数 医务人员人数 医院工作人员（食堂、保安、保洁、物业） 医院级别（三甲、二甲等）
    2 教育类 学生人数（非住宿） 学生人数（住宿） 中学、高中生人数（非住宿） 中学、高中生人数（非住宿） 大学生人数（住宿） 教职工人数
    3 机关 机关绿化面积 机关在编人数 机关工作人员人数（食堂、保安、保洁、物业） 机关是否有食堂 机关是否有宿舍 机关是否有浴室
    4 酒店-住宿业 出租床数量（床） 年营业天数 建筑面积
    5 写字楼 服务对象人数 建筑面积 是有有水冷空调 营业天数
    6 餐饮 营业建筑面积 营业天数 服务人数
    7 商超 营业建筑面积 营业天数 服务人数
    8 文化娱乐 营业建筑面积 营业天数 服务人数
    0 其他 绿化面积 在编人数 工作人员人数（食堂、保安、保洁、物业） 是否有食堂 是否有宿舍 是否有浴室
    */
    const fieldByIndustry = useRef({
        1: ["hospOutpatientsAnnual", "hospBedsNum", "hospInpatientsAnnual", "hospMedicalStaffs", "hospWorkerStaffs", "hospGrade", ],
        2: ["eduNonResiPrimStu", "eduResiPrimStu", "eduNonResiMidStu", "eduResiMidStu", "eduCollResiStu", "eduStaffs", ],
        3: ["instGreenArea", "instInAgencyStaffs", "instOutAgencyStaffs", "instHasCanteen", "instHasDormitory", "instHasBathroom", ],
        4: ["hotelBedNum", "hotelAopDays", "hotelBuildArea", ],
        5: ["officeSrvObjNum", "officeBuildArea", "officeHasWaterAC", "officeAopDays", ],
        6: [ "caterBuildArea", "caterAopDays", "caterSrvObjNum", ],
        7: [ "superBuildArea", "superAopDays", "superSrvObjNum", ],
        8: [ "cultBuildArea", "cultAopDays", "cultSrvObjNum", ],
        0: [ "otherGreenArea", "otherInAgencyStaffs", "otherOutAgencyStaffs", "otherHasCanteen", "otherHasDormitory", "otherHasBathroom", ],
    });

    const validFieldsArray = fieldByIndustry.current[subindustryState.id];
    /**
     * 返回“真值”时对应配置的表单不会显示出来
     * @param {string} fieldName
     * @returns
     */
    const inputFilterIf = (fieldName) =>  {
        return !validFieldsArray?.includes(fieldName);
    };

    // Define form
    // 新组件需要重新定义!!
    const schema = Joi.object({
        //year:          validatorBase.waterBalance.commons.yearFieldUnrequired, // 年份, 预留
        industry:        validatorBase.waterBalance.factBase.industryUnrequired, // 行业, 1工业, 2服务业, 目前默认服务业
        subindustry:     validatorBase.waterBalance.waterQuotaInfo.subindustry,  // 1医院, 2学校, 3机关
        //subindustryCode: validatorBase.waterBalance.commons.textField,         // 行业代码, 预留
        //subindustryName: validatorBase.waterBalance.commons.textField,         // 对应于subindustry的名称, 预留
        waterUsageInst:  validatorBase.waterBalance.commons.longTextField,       // 用水户名称
        // 1 医院
        hospOutpatientsAnnual: validatorBase.waterBalance.commons.intFieldUnrequired,   // 医院年门诊人次
        hospBedsNum:           validatorBase.waterBalance.commons.intFieldUnrequired,   // 医院编制床位数量
        hospInpatientsAnnual:  validatorBase.waterBalance.commons.intFieldUnrequired,   // 医院年住院人数
        hospMedicalStaffs:     validatorBase.waterBalance.commons.intFieldUnrequired,   // 医务人员人数
        hospWorkerStaffs:      validatorBase.waterBalance.commons.intFieldUnrequired,   // 医院工作人员(食堂、保安、保洁、物业)
        hospGrade:             validatorBase.waterBalance.commons.intFieldUnrequired,   // 医院级别(三甲、二甲等)
        // 2 学校
        eduNonResiPrimStu:     validatorBase.waterBalance.commons.intFieldUnrequired,   // 小学生人数（非住宿）
        eduResiPrimStu:        validatorBase.waterBalance.commons.intFieldUnrequired,   // 小学生人数（住宿）
        eduNonResiMidStu:      validatorBase.waterBalance.commons.intFieldUnrequired,   // 中学、高中生人数（非住宿）
        eduResiMidStu:         validatorBase.waterBalance.commons.intFieldUnrequired,   // 中学、高中生人数（非住宿）
        eduCollResiStu:        validatorBase.waterBalance.commons.intFieldUnrequired,   // 大学生人数（住宿）
        eduStaffs:             validatorBase.waterBalance.commons.intFieldUnrequired,   // 教职工人数
        // 3 机关
        instGreenArea:         validatorBase.waterBalance.commons.intFieldUnrequired,   // 机关绿化面积
        instInAgencyStaffs:    validatorBase.waterBalance.commons.intFieldUnrequired,   // 机关在编人数
        instOutAgencyStaffs:   validatorBase.waterBalance.commons.intFieldUnrequired,   // 机关工作人员人数(食堂、保安、保洁、物业)
        instHasCanteen:        validatorBase.waterBalance.commons.boolFieldExt,         // 机关是否有食堂
        instHasDormitory:      validatorBase.waterBalance.commons.boolFieldExt,         // 机关是否有宿舍
        instHasBathroom:       validatorBase.waterBalance.commons.boolFieldExt,         // 机关是否有浴室
        // 4 酒店-住宿业
        hotelBedNum:           validatorBase.waterBalance.commons.intFieldUnrequired,   // 出租床数量（床）
        hotelAopDays:          validatorBase.waterBalance.commons.intFieldUnrequired,   // 年营业天数
        hotelBuildArea:        validatorBase.waterBalance.commons.intFieldUnrequired,   // 建筑面积
        // 5 写字楼
        officeSrvObjNum:       validatorBase.waterBalance.commons.intFieldUnrequired,   // 服务对象人数
        officeBuildArea:       validatorBase.waterBalance.commons.intFieldUnrequired,   // 建筑面积
        officeHasWaterAC:      validatorBase.waterBalance.commons.boolFieldExt,         // 是有有水冷空调
        officeAopDays:         validatorBase.waterBalance.commons.intFieldUnrequired,   // 营业天数
        // 6 餐饮
        caterBuildArea:        validatorBase.waterBalance.commons.intFieldUnrequired,   // 营业建筑面积
        caterAopDays:          validatorBase.waterBalance.commons.intFieldUnrequired,   // 营业天数
        caterSrvObjNum:        validatorBase.waterBalance.commons.intFieldUnrequired,   // 服务人数
        // 7 商超
        superBuildArea:        validatorBase.waterBalance.commons.intFieldUnrequired,   // 营业建筑面积
        superAopDays:          validatorBase.waterBalance.commons.intFieldUnrequired,   // 营业天数
        superSrvObjNum:        validatorBase.waterBalance.commons.intFieldUnrequired,   // 服务人数
        // 8 文化娱乐
        cultBuildArea:         validatorBase.waterBalance.commons.intFieldUnrequired,   // 营业建筑面积
        cultAopDays:           validatorBase.waterBalance.commons.intFieldUnrequired,   // 营业天数
        cultSrvObjNum:         validatorBase.waterBalance.commons.intFieldUnrequired,   // 服务人数
        // 0 其他
        otherGreenArea:        validatorBase.waterBalance.commons.intFieldUnrequired,   // 绿化面积
        otherInAgencyStaffs:   validatorBase.waterBalance.commons.intFieldUnrequired,   // 在编人数
        otherOutAgencyStaffs:  validatorBase.waterBalance.commons.intFieldUnrequired,   // 工作人员人数(食堂、保安、保洁、物业)
        otherHasCanteen:       validatorBase.waterBalance.commons.boolFieldExt,         // 是否有食堂
        otherHasDormitory:     validatorBase.waterBalance.commons.boolFieldExt,         // 是否有宿舍
        otherHasBathroom:      validatorBase.waterBalance.commons.boolFieldExt,         // 是否有浴室
        // other fields
        others:       validatorBase.waterBalance.commons.textField,
        remarks:      validatorBase.waterBalance.commons.longTextField,
    });

    // 新组件需要重新定义defaultValues!!
    const {
        handleSubmit,
        control,
        resetField,
        reset,
        formState: { errors },
        //setValue,
        //getValues,
    } = useForm({
        resolver: joiResolver(schema),
        //reValidateMode: "onBlur", // https://github.com/orgs/react-hook-form/discussions/10327
        defaultValues: { // 默认值在组件载入时会被更新, 这里的初始值正常情况下没有用处
            industry:              "", // 行业, 1工业, 2服务业, 目前默认服务业
            subindustry:           "-1",  // 1医院, 2学校, 3机关
            waterUsageInst:        "",  // 用水户名称
            // 1 医院
            hospOutpatientsAnnual: "",  // 医院年门诊人次
            hospBedsNum:           "",  // 医院编制床位数量
            hospInpatientsAnnual:  "",  // 医院年住院人数
            hospMedicalStaffs:     "",  // 医务人员人数
            hospWorkerStaffs:      "",  // 医院工作人员(食堂、保安、保洁、物业)
            hospGrade:             "",  // 医院级别(三甲、二甲等)
            // 2 学校
            eduNonResiPrimStu:     "",  // 小学生人数（非住宿）
            eduResiPrimStu:        "",  // 小学生人数（住宿）
            eduNonResiMidStu:      "",  // 中学、高中生人数（非住宿）
            eduResiMidStu:         "",  // 中学、高中生人数（非住宿）
            eduCollResiStu:        "",  // 大学生人数（住宿）
            eduStaffs:             "",  // 教职工人数
            // 3 机关
            instGreenArea:         "",  // 机关绿化面积
            instInAgencyStaffs:    "",  // 机关在编人数
            instOutAgencyStaffs:   "",  // 机关工作人员人数(食堂、保安、保洁、物业)
            instHasCanteen:        "",  // 机关是否有食堂
            instHasDormitory:      "",  // 机关是否有宿舍
            instHasBathroom:       "",  // 机关是否有浴室
            // 4 酒店-住宿业
            hotelBedNum:           "",   // 出租床数量（床）
            hotelAopDays:          "",   // 年营业天数
            hotelBuildArea:        "",   // 建筑面积
            // 5 写字楼
            officeSrvObjNum:       "",   // 服务对象人数
            officeBuildArea:       "",   // 建筑面积
            officeHasWaterAC:      "",   // 是有有水冷空调
            officeAopDays:         "",   // 营业天数
            // 6 餐饮
            caterBuildArea:        "",   // 营业建筑面积
            caterAopDays:          "",   // 营业天数
            caterSrvObjNum:        "",   // 服务人数
            // 7 商超
            superBuildArea:        "",   // 营业建筑面积
            superAopDays:          "",   // 营业天数
            superSrvObjNum:        "",   // 服务人数
            // 8 文化娱乐
            cultBuildArea:         "",   // 营业建筑面积
            cultAopDays:           "",   // 营业天数
            cultSrvObjNum:         "",   // 服务人数
            // 0 其他
            otherGreenArea:        "",   // 绿化面积
            otherInAgencyStaffs:   "",   // 在编人数
            otherOutAgencyStaffs:  "",   // 工作人员人数(食堂、保安、保洁、物业)
            otherHasCanteen:       "",   // 是否有食堂
            otherHasDormitory:     "",   // 是否有宿舍
            otherHasBathroom:      "",   // 是否有浴室
            // other fields
            others:        "",
            remarks:       "",
        },
    });

    // Query: select record
    const onRecordSelectSuccess = (data) => { // data: {"id": 3, "leader": 0, "name": "DEPT-03", "superior": 0}
        // 注意, 进入onSuccess之前就意味着服务端版本比客户端版本新, 因此要更新客户端主版本号
        console.log("onRecordSelectSuccess:", data);
        if (data.STATUS === 0) {
            setStore("mainCversion", data.DATA.cversion);
            setStore("subCversion", 0);
            const info = JSON.parse(data.DATA.info);     // info在数据库是一个jsonb字段

            const formObject = {
                industry:              String(data.DATA.industry),          // 行业, 1工业, 2服务业, 目前默认服务业
                subindustry:           String(data.DATA.subindustry || "-1"),       // 1医院, 2学校, 3机关, 4酒店-住宿业, 5写字楼, 6餐饮, 7商超, 8文化娱乐, 0其他服务业
                waterUsageInst:        String(data.DATA.waterUsageInst),    // 用水户名称
                // 1 医院
                hospOutpatientsAnnual: String(info?.hospOutpatientsAnnual || ""),  // 医院年门诊人次
                hospBedsNum:           String(info?.hospBedsNum || ""),            // 医院编制床位数量
                hospInpatientsAnnual:  String(info?.hospInpatientsAnnual || ""),   // 医院年住院人数
                hospMedicalStaffs:     String(info?.hospMedicalStaffs || ""),      // 医务人员人数
                hospWorkerStaffs:      String(info?.hospWorkerStaffs || ""),       // 医院工作人员(食堂、保安、保洁、物业)
                hospGrade:             String(info?.hospGrade || ""),              // 医院级别(三甲、二甲等)
                // 2 学校
                eduNonResiPrimStu:     String(info?.eduNonResiPrimStu || ""),      // 小学生人数（非住宿）
                eduResiPrimStu:        String(info?.eduResiPrimStu || ""),         // 小学生人数（住宿）
                eduNonResiMidStu:      String(info?.eduNonResiMidStu || ""),       // 中学、高中生人数（非住宿）
                eduResiMidStu:         String(info?.eduResiMidStu || ""),          // 中学、高中生人数（非住宿）
                eduCollResiStu:        String(info?.eduCollResiStu || ""),         // 大学生人数（住宿）
                eduStaffs:             String(info?.eduStaffs || ""),              // 教职工人数
                // 3 机关
                instGreenArea:         String(info?.instGreenArea || ""),          // 机关绿化面积
                instInAgencyStaffs:    String(info?.instInAgencyStaffs || ""),     // 机关在编人数
                instOutAgencyStaffs:   String(info?.instOutAgencyStaffs || ""),    // 机关工作人员人数(食堂、保安、保洁、物业)
                // 这里0是有意义的, 表示无, 而-1表示未填写, 1表示有
                instHasCanteen:        String(info?.instHasCanteen   || (info?.instHasCanteen   === 0 ? 0 : "")),  // 机关是否有食堂
                instHasDormitory:      String(info?.instHasDormitory || (info?.instHasDormitory === 0 ? 0 : "")),  // 机关是否有宿舍
                instHasBathroom:       String(info?.instHasBathroom  || (info?.instHasBathroom  === 0 ? 0 : "")),  // 机关是否有浴室
                // 4 酒店-住宿业
                hotelBedNum:           String(info?.hotelBedNum    || ""),          // 出租床数量（床）
                hotelAopDays:          String(info?.hotelAopDays   || ""),          // 年营业天数
                hotelBuildArea:        String(info?.hotelBuildArea || ""),          // 建筑面积
                // 5 写字楼
                officeSrvObjNum:       String(info?.officeSrvObjNum  || ""),        // 服务对象人数
                officeBuildArea:       String(info?.officeBuildArea  || ""),        // 建筑面积
                officeHasWaterAC:      String(info?.officeHasWaterAC || (info?.officeHasWaterAC  === 0 ? 0 : "")),  // 是有有水冷空调
                officeAopDays:         String(info?.officeAopDays    || ""),        // 营业天数
                // 6 餐饮
                caterBuildArea:        String(info?.caterBuildArea   || ""),        // 营业建筑面积
                caterAopDays:          String(info?.caterAopDays     || ""),        // 营业天数
                caterSrvObjNum:        String(info?.caterSrvObjNum   || ""),        // 服务人数
                // 7 商超
                superBuildArea:        String(info?.superBuildArea   || ""),        // 营业建筑面积
                superAopDays:          String(info?.superAopDays     || ""),        // 营业天数
                superSrvObjNum:        String(info?.superSrvObjNum   || ""),        // 服务人数
                // 8 文化娱乐
                cultBuildArea:         String(info?.cultBuildArea    || ""),        // 营业建筑面积
                cultAopDays:           String(info?.cultAopDays      || ""),        // 营业天数
                cultSrvObjNum:         String(info?.cultSrvObjNum    || ""),        // 服务人数
                // 0 其他
                otherGreenArea:        String(info?.otherGreenArea   || ""),        // 绿化面积
                otherInAgencyStaffs:   String(info?.otherInAgencyStaffs || ""),     // 在编人数
                otherOutAgencyStaffs:  String(info?.otherOutAgencyStaffs|| ""),     // 工作人员人数(食堂、保安、保洁、物业)
                otherHasCanteen:       String(info?.otherHasCanteen  || (info?.otherHasCanteen === 0 ? 0 : "")),        // 是否有食堂
                otherHasDormitory:     String(info?.otherHasDormitory|| (info?.otherHasDormitory === 0 ? 0 : "")),        // 是否有宿舍
                otherHasBathroom:      String(info?.otherHasBathroom || (info?.otherHasBathroom === 0 ? 0 : "")),        // 是否有浴室
                // other fields
                others:       String(data.DATA.others),
                remarks:      String(data.DATA.remarks),
            };
            reset(formObject);           // 重置react-form

            const storeObject = checkPermits() && (formType === 0) && {
                ...formObject,
                subindustry:      radioIdToObject(subindustryStateDataProviderRef.current,      data.DATA.subindustry, { id: -1, name: "" }),
                hospGrade:        radioIdToObject(hospGradeStateDataProviderRef.current,        info.hospGrade,        { id:  0, name: "" }),
                instHasCanteen:   radioIdToObject(instHasCanteenStateDataProviderRef.current,   info.instHasCanteen,   { id: -1, name: "" }),
                instHasDormitory: radioIdToObject(instHasDormitoryStateDataProviderRef.current, info.instHasDormitory, { id: -1, name: "" }),
                instHasBathroom:  radioIdToObject(instHasBathroomStateDataProviderRef.current,  info.instHasBathroom,  { id: -1, name: "" }),
                officeHasWaterAC: radioIdToObject(commonBinaryStateDataProviderRef.current,     info.officeHasWaterAC, { id: -1, name: "" }),
                otherHasCanteen:  radioIdToObject(commonBinaryStateDataProviderRef.current,     info.otherHasCanteen,  { id: -1, name: "" }),
                otherHasDormitory:radioIdToObject(commonBinaryStateDataProviderRef.current,     info.otherHasDormitory,{ id: -1, name: "" }),
                otherHasBathroom: radioIdToObject(commonBinaryStateDataProviderRef.current,     info.otherHasBathroom, { id: -1, name: "" }),

            };
            storeObject && setStoreObject(storeObject, false); // 判断用户权限, 把服务端数据写到mmkv存储, 只有可写用户才需要本地存储, 不会首先清空老数据

            // 设置selector菜单状态
            setSubindustryState(storeObject.subindustry);
            console.log("服务业类型: ", data.DATA.subindustry, typeof(data.DATA.subindustry));
            switch(Number(data.DATA.subindustry)) {
                case 1: // 医院
                    setHospGradeState(storeObject.hospGrade);
                    break;
                case 2: // 学校
                    break;
                case 3: //机关
                    setInstHasCanteenState(storeObject.instHasCanteen);
                    setInstHasDormitoryState(storeObject.instHasDormitory);
                    setInstHasBathroomState(storeObject.instHasBathroom);
                    break;
                case 4: // 酒店-住宿业
                    break;
                case 5: // 写字楼
                    setCommonBinaryState1(storeObject.officeHasWaterAC);
                    break;
                case 6: // 餐饮
                    break;
                case 7: // 商超
                    break;
                case 8: // 文化娱乐
                    break;
                case 0: // 其他服务业
                    console.log("从网络恢复下拉菜单数据: ", storeObject.otherHasCanteen);
                    setCommonBinaryState1(storeObject.otherHasCanteen);
                    setCommonBinaryState2(storeObject.otherHasDormitory);
                    setCommonBinaryState3(storeObject.otherHasBathroom);
                    break;
                default:
                    break;
            }

            // 设置屏幕标题
            (screenTitle !== formObject.name) && setScreenTitle(formObject.name);
        } else {
            console.log("STATUS !== 0", data.STATUS);
            setSnackBarMessage(`表单数据拉取发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        }
    };
    const onRecordSelectError = (err) => {
        console.log("Record selecting error:", err);
    };
    const onRecordSelectSettled = (err) => {
        loadingIndicatorVisible && setLoadingIndicatorVisible(false);
    };
    const getPath = formType === 0 ? "get" : "getsum";
    const recordSelectQuery = recordSelectQueryMaker([queryKwd, getPath, recordPubid], onRecordSelectSuccess, onRecordSelectError, onRecordSelectSettled);

    // Query: update record
    const onHandleSubmit = (data) => {
        setOkButtonDisabled(true);
        setOkButtonLoading(true);

        dataFeeder({ ...data,
            cversion: getClientCversion.current(),
            name:           `${subindustryState.name}/${data.waterUsageInst}`,
            industry:       data.industry,
            subindustry:    data.subindustry,
            waterUsageInst: data.waterUsageInst,
            info:           JSON.stringify(collectFieldsObject(validFieldsArray, data)),

            // unifify data units
            // ...
        }); // append client cversion
        recordUpdateQuery.mutate();
    };

    // 新组件不需改动
    const onRecordUpdateSuccess = (data) => {
        console.log(" Record update success response data: ", data);
        if(data.STATUS !== 0) {
            setSnackBarMessage(`表单更新发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        } else {
            // 设置确认框数据
            setConfirmDialogConfig({
                title: "更新表单",
                text: "表单信息更新成功!",
                okLabel: "确定",
                onOK: () => {
                    setShowConfirm(false);
                    recordSelectQuery.mutate();
                }});
            setShowConfirm(true);
        }
    };
    const onRecordUpdateError = (error) => {
        setSnackBarMessage(`表单更新发生错误, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const onRecordUpdateSettled = (data, error) => {
        setOkButtonDisabled(false);
        setOkButtonLoading(false);
    };
    const recordUpdateQuery = recordUpdateQueryMaker(dataFeeder, [queryKwd, "upd", recordPubid], onRecordUpdateSuccess, onRecordUpdateError, onRecordUpdateSettled);

    // Query: delete record
    const recordDeleteOnSuccess = () => {
        clearStore();
        navigation.goBack();
    };
    const recordDeleteOnError = (error) => {
        setSnackBarMessage(`表单删除失败, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const recordDeleteQuery = recordDeleteQueryMaker([queryKwd, "del", recordPubid], recordDeleteOnSuccess, recordDeleteOnError);

    const deleteAlert = () => {
        console.log("触发弹出警告框!!!!!!!!");
        return (
            Alert.alert("删除表单", `删除后不可撤销, 请确认是否删除: ${delQueryInfo.current?.name}?`, [
                { text: "取消" },
                { text: "确定", onPress: () => recordDeleteQuery.mutate() },
            ])
        );
    };

    const clearCache = () => {
        return (
            Alert.alert("清空本地缓存", "1. 用于未知原因导致数据无法提交的情况\r\n2. 未提交的内容将会丢失, 请做好数据截图备份\r\n3. 已提交到服务端的数据不受影响\r\n4. 确认清空请点击“确认”按钮", [
                { text: "取消" },
                { text: "确定", onPress: () => { clearStore(); navigation.goBack(); } },
            ])
        );
    };

    // Hook: onMount fetch data
    useEffect(() => {
        // 优化方案: 根据用户权限进行mutate, 只有可写用户才需要本地存储
        const localCversion = getClientCversion.current();
        const remoteCversion = route.params.pageMeta.cversion;
        console.log("localCversion:", localCversion, "remoteSversion:", remoteCversion, "subCversionRef:", subCversionRef.current);

        const restoreLocalData = () => {
            const formObject = { // 默认值用户表单的数据显示
                industry:              storedValueToFormValue.current("industry", -1),          // 行业, 1工业, 2服务业, 目前默认服务业
                subindustry:           storedValueToFormValue.current("subindustry"),       // 1医院, 2学校, 3机关
                waterUsageInst:        storedValueToFormValue.current("waterUsageInst"),    // 用水户名称
                // 1 医院
                hospOutpatientsAnnual:  storedValueToFormValue.current("hospOutpatientsAnnual"),  // 医院年门诊人次
                hospBedsNum:            storedValueToFormValue.current("hospBedsNum"),            // 医院编制床位数量
                hospInpatientsAnnual:   storedValueToFormValue.current("hospInpatientsAnnual"),   // 医院年住院人数
                hospMedicalStaffs:      storedValueToFormValue.current("hospMedicalStaffs"),      // 医务人员人数
                hospWorkerStaffs:       storedValueToFormValue.current("hospWorkerStaffs"),       // 医院工作人员(食堂、保安、保洁、物业)
                hospGrade:              storedValueToFormValue.current("hospGrade"),              // 医院级别(三甲、二甲等)
                // 2 学校
                eduNonResiPrimStu:      storedValueToFormValue.current("eduNonResiPrimStu"),      // 小学生人数（非住宿）
                eduResiPrimStu:         storedValueToFormValue.current("eduResiPrimStu"),         // 小学生人数（住宿）
                eduNonResiMidStu:       storedValueToFormValue.current("eduNonResiMidStu"),       // 中学、高中生人数（非住宿）
                eduResiMidStu:          storedValueToFormValue.current("eduResiMidStu"),          // 中学、高中生人数（非住宿）
                eduCollResiStu:         storedValueToFormValue.current("eduCollResiStu"),         // 大学生人数（住宿）
                eduStaffs:              storedValueToFormValue.current("eduStaffs"),              // 教职工人数
                // 3 机关
                instGreenArea:          storedValueToFormValue.current("instGreenArea"),          // 机关绿化面积
                instInAgencyStaffs:     storedValueToFormValue.current("instInAgencyStaffs"),     // 机关在编人数
                instOutAgencyStaffs:    storedValueToFormValue.current("instOutAgencyStaffs"),    // 机关工作人员人数(食堂、保安、保洁、物业)
                instHasCanteen:         storedValueToFormValue.current("instHasCanteen"),         // 机关是否有食堂
                instHasDormitory:       storedValueToFormValue.current("instHasDormitory"),       // 机关是否有宿舍
                instHasBathroom:        storedValueToFormValue.current("instHasBathroom"),        // 机关是否有浴室
                // 4 酒店-住宿业
                hotelBedNum:            storedValueToFormValue.current("hotelBedNum"),            // 出租床数量（床）
                hotelAopDays:           storedValueToFormValue.current("hotelAopDays"),           // 年营业天数
                hotelBuildArea:         storedValueToFormValue.current("hotelBuildArea"),         // 建筑面积
                // 5 写字楼
                officeSrvObjNum:        storedValueToFormValue.current("officeSrvObjNum"),        // 服务对象人数
                officeBuildArea:        storedValueToFormValue.current("officeBuildArea"),        // 建筑面积
                officeHasWaterAC:       storedValueToFormValue.current("officeHasWaterAC"),       // 是有有水冷空调
                officeAopDays:          storedValueToFormValue.current("officeAopDays"),          // 营业天数
                // 6 餐饮
                caterBuildArea:         storedValueToFormValue.current("caterBuildArea"),         // 营业建筑面积
                caterAopDays:           storedValueToFormValue.current("caterAopDays"),           // 营业天数
                caterSrvObjNum:         storedValueToFormValue.current("caterSrvObjNum"),         // 服务人数
                // 7 商超
                superBuildArea:         storedValueToFormValue.current("superBuildArea"),         // 营业建筑面积
                superAopDays:           storedValueToFormValue.current("superAopDays"),           // 营业天数
                superSrvObjNum:         storedValueToFormValue.current("superSrvObjNum"),         // 服务人数
                // 8 文化娱乐
                cultBuildArea:          storedValueToFormValue.current("cultBuildArea"),          // 营业建筑面积
                cultAopDays:            storedValueToFormValue.current("cultAopDays"),            // 营业天数
                cultSrvObjNum:          storedValueToFormValue.current("cultSrvObjNum"),          // 服务人数
                // 0 其他
                otherGreenArea:         storedValueToFormValue.current("otherGreenArea"),         // 绿化面积
                otherInAgencyStaffs:    storedValueToFormValue.current("otherInAgencyStaffs"),    // 在编人数
                otherOutAgencyStaffs:   storedValueToFormValue.current("otherOutAgencyStaffs"),   // 工作人员人数(食堂、保安、保洁、物业)
                otherHasCanteen:        storedValueToFormValue.current("otherHasCanteen"),        // 是否有食堂
                otherHasDormitory:      storedValueToFormValue.current("otherHasDormitory"),      // 是否有宿舍
                otherHasBathroom:       storedValueToFormValue.current("otherHasBathroom"),       // 是否有浴室
                // other fields
                others:        storedValueToFormValue.current("others"),
                remarks:       storedValueToFormValue.current("remarks"),
            };
            reset(formObject); // 重置react-form数据

            // 设置selector数据
            setSubindustryState(getStore("subindustry"));

            switch(formObject.subindustry) {
                case 1: // 医院
                    setHospGradeState(ifTruthLet(getStore("hospGrade"), isNullness, () => {return {id: 0, name: ""};}, value => value));
                    break;
                case 2: // 学校
                    break;
                case 3: //机关
                    setInstHasCanteenState(ifTruthLet(getStore("instHasCanteen"),     isNullness, () => {return {id: -1, name: ""};}, value => value));
                    setInstHasDormitoryState(ifTruthLet(getStore("instHasDormitory"), isNullness, () => {return {id: -1, name: ""};}, value => value));
                    setInstHasBathroomState(ifTruthLet(getStore("instHasDormitory"),  isNullness, () => {return {id: -1, name: ""};}, value => value));
                    break;
                case 4: // 酒店-住宿业
                    break;
                case 5: // 写字楼
                    setCommonBinaryState1(ifTruthLet(getStore("officeHasWaterAC"),  isNullness, () => {return {id: -1, name: ""};}, value => value));
                    break;
                case 6: // 餐饮
                    break;
                case 7: // 商超
                    break;
                case 8: // 文化娱乐
                    break;
                case 0: // 其他服务业
                    setCommonBinaryState1(ifTruthLet(getStore("otherHasCanteen"),   isNullness, () => {return {id: -1, name: ""};}, value => value));
                    setCommonBinaryState2(ifTruthLet(getStore("otherHasDormitory"), isNullness, () => {return {id: -1, name: ""};}, value => value));
                    setCommonBinaryState3(ifTruthLet(getStore("otherHasBathroom"),  isNullness, () => {return {id: -1, name: ""};}, value => value));
                    break;
                default:
                    break;
            }
        };

        // 根据反馈, 要求默认显示0
        if (localCversion === 0) {
            recordSelectQuery.mutate();
            return;
        }

        if(formType === 1) {
            recordSelectQuery.mutate();
        } else if (localCversion === 0 && remoteCversion > 0) {
            console.log("Local cversion === 0, serve may have something nontrivial initialized, query mutate!");
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        } else if (isServerDataNewer(localCversion, remoteCversion) && subCversionRef.current > 0) { // 服务端有更新数据, 但本地有修改
            console.log("Client cversion fell behind the server cversion, someone might modified the database, choose to use the data version!");
            Alert.alert("注意", "由于其他人向服务器提交了数据, 服务端数据比本地数据新, 请选择使用哪份数据?", [
                { text: "使用本地数据", onPress: () => { setStore("mainCversion", remoteCversion); restoreLocalData(); } },
                { text: "使用服务端数据", onPress: () => { setLoadingIndicatorVisible(true); recordSelectQuery.mutate(); } },
            ]);
        } else if (isServerDataNewer(localCversion, remoteCversion)) { // localCversion==0时要mutate是因为服务端可能在创建记录时生成了必要数据
            console.log("Server cversion may be newer, query mutate:", localCversion, remoteCversion);
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        }
        else {
            console.log("Local cversion >= server cversion, query will not mutate!");
            restoreLocalData();
        }
    }, []);

    useEffect(() => {
        if(subindustryState?.id === 1) { // 医院类
            setQuotaRefImg(WQS_Hospital);
        } else if(subindustryState?.id === 2) { // 教育类
            setQuotaRefImg(WQS_Education);
        } else if(subindustryState?.id === 3) { // 机关
            setQuotaRefImg(WQS_Institute);
        } else if(subindustryState?.id === 4) { // 酒店-住宿业
            setQuotaRefImg(WQS_Hotel);
        } else if(subindustryState?.id === 5) { // 写字楼
            setQuotaRefImg(WQS_Office);
        } else if(subindustryState?.id === 6) { // 餐饮
            setQuotaRefImg(WQS_Catering);
        } else if(subindustryState?.id === 7) { // 商超
            setQuotaRefImg(WQS_Supermarkt);
        } else if(subindustryState?.id === 8) { // 文化娱乐
            setQuotaRefImg(WQS_Culture);
        } else if(subindustryState?.id === 0) { // 其他服务业
            setQuotaRefImg(WQS_Institute);
        } else {
            console.log(`Type of subindustry not defined: ${subindustryState?.id}, image not displayed!`);
        }
    }, [subindustryState]);

    // remarks不需配置
    const FieldsConfig_1_book = {
        industry: [
            {
                inputs: [

                    //{ name: "name",        label: "表单名称",     unit: "",     type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                    //{ name: "industry", label: "行业类型",   unit: "",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "subindustry",    label: "行业类别",     unit: "",      type: "RADIO", editable: true, placeholder: "", selectorState: subindustryState, setSelectorState: setSubindustryState, dataProvider: subindustryEnum, },
                    { name: "waterUsageInst", label: "用水户名称",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true, }, },
                    // 1 医院
                    { name: "hospOutpatientsAnnual", label: "医院年门诊人次",   unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "hospBedsNum",           label: "医院编制床位数量", unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "hospInpatientsAnnual",  label: "医院年住院人数",   unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "hospMedicalStaffs",     label: "医务人员人数",     unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "hospWorkerStaffs",      label: "医院工作人员(食堂、保安、保洁、物业)",unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "hospGrade",             label: "医院级别",        unit: "",      type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: hospGradeState, setSelectorState: setHospGradeState, dataProvider: hospitalGradeEnum, },
                    // 2 学校
                    { name: "eduNonResiPrimStu", label: "小学生人数(非住宿)",     unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "eduResiPrimStu",    label: "小学生人数(住宿)",       unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "eduNonResiMidStu",  label: "中学、高中生人数(非住宿)", unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "eduResiMidStu",     label: "中学、高中生人数(住宿)",   unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "eduCollResiStu",    label: "大学生人数(住宿)",        unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "eduStaffs",         label: "教职工人数",             unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    // 3 机关
                    { name: "instGreenArea",       label: "机关绿化面积",  unit: "m²", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "instInAgencyStaffs",  label: "机关在编人数",  unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "instOutAgencyStaffs", label: "机关工作人员人数(食堂、保安、保洁、物业)", unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "instHasCanteen",      label: "机关是否有食堂", unit: "", type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: instHasCanteenState, setSelectorState: setInstHasCanteenState, dataProvider: instHasBathroomEnum, },
                    { name: "instHasDormitory",    label: "机关是否有宿舍", unit: "", type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: instHasDormitoryState, setSelectorState: setInstHasDormitoryState, dataProvider: instHasDormitoryEnum, },
                    { name: "instHasBathroom",     label: "机关是否有浴室", unit: "", type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: instHasBathroomState, setSelectorState: setInstHasBathroomState, dataProvider: instHasBathroomEnum, },
                    // 4 酒店-住宿业
                    { name: "hotelBedNum",      label: "出租床数量", unit: "床", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "hotelAopDays",     label: "年营业天数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "hotelBuildArea",   label: "建筑面积",       unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    // 5 写字楼
                    { name: "officeSrvObjNum",  label: "服务对象人数", unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "officeBuildArea",  label: "建筑面积",     unit: "m²", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "officeHasWaterAC", label: "是否有水冷空调", unit: "", type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: commonBinaryState1, setSelectorState: setCommonBinaryState1, dataProvider: commonBinaryEnum, },
                    { name: "officeAopDays",    label: "营业天数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    // 6 餐饮
                    { name: "caterBuildArea",   label: "营业建筑面积", unit: "m²", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "caterAopDays",     label: "营业天数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "caterSrvObjNum",   label: "服务人数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    // 7 商超
                    { name: "superBuildArea",   label: "营业建筑面积", unit: "m²", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "superAopDays",     label: "营业天数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "superSrvObjNum",   label: "服务人数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    // 8 文化娱乐
                    { name: "cultBuildArea",    label: "营业建筑面积", unit: "m²", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "cultAopDays",      label: "营业天数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "cultSrvObjNum",    label: "服务人数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    // 0 其他
                    { name: "otherGreenArea",      label: "绿化面积", unit: "m²", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "otherInAgencyStaffs", label: "在编人数", unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "otherOutAgencyStaffs",label: "工作人员人数(食堂、保安、保洁、物业)", unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "otherHasCanteen",     label: "是否有食堂", unit: "", type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: commonBinaryState1, setSelectorState: setCommonBinaryState1, dataProvider: commonBinaryEnum, },
                    { name: "otherHasDormitory",   label: "是否有宿舍", unit: "", type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: commonBinaryState2, setSelectorState: setCommonBinaryState2, dataProvider: commonBinaryEnum, },
                    { name: "otherHasBathroom",    label: "是否有浴室", unit: "", type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: commonBinaryState3, setSelectorState: setCommonBinaryState3, dataProvider: commonBinaryEnum, },

                ]
            },
        ],

        service: [
            {
                inputs: [

                    //{ name: "name",        label: "表单名称",     unit: "",     type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                    //{ name: "industry", label: "行业类型",   unit: "",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "subindustry",    label: "行业类别",     unit: "",      type: "RADIO", editable: true, placeholder: "", selectorState: subindustryState, setSelectorState: setSubindustryState, dataProvider: subindustryEnum, },
                    { name: "waterUsageInst", label: "用水户名称",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true, }, },
                    // 1 医院
                    { name: "hospOutpatientsAnnual", label: "医院年门诊人次",   unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "hospBedsNum",           label: "医院编制床位数量", unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "hospInpatientsAnnual",  label: "医院年住院人数",   unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "hospMedicalStaffs",     label: "医务人员人数",     unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "hospWorkerStaffs",      label: "医院工作人员(食堂、保安、保洁、物业)",unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "hospGrade",             label: "医院级别",        unit: "",      type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: hospGradeState, setSelectorState: setHospGradeState, dataProvider: hospitalGradeEnum, },
                    // 2 学校
                    { name: "eduNonResiPrimStu", label: "小学生人数(非住宿)",     unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "eduResiPrimStu",    label: "小学生人数(住宿)",       unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "eduNonResiMidStu",  label: "中学、高中生人数(非住宿)", unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "eduResiMidStu",     label: "中学、高中生人数(住宿)",   unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "eduCollResiStu",    label: "大学生人数(住宿)",        unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "eduStaffs",         label: "教职工人数",             unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    // 3 机关
                    { name: "instGreenArea",       label: "机关绿化面积",  unit: "m²", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "instInAgencyStaffs",  label: "机关在编人数",  unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "instOutAgencyStaffs", label: "机关工作人员人数(食堂、保安、保洁、物业)", unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "instHasCanteen",      label: "机关是否有食堂", unit: "", type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: instHasCanteenState, setSelectorState: setInstHasCanteenState, dataProvider: instHasBathroomEnum, },
                    { name: "instHasDormitory",    label: "机关是否有宿舍", unit: "", type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: instHasDormitoryState, setSelectorState: setInstHasDormitoryState, dataProvider: instHasDormitoryEnum, },
                    { name: "instHasBathroom",     label: "机关是否有浴室", unit: "", type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: instHasBathroomState, setSelectorState: setInstHasBathroomState, dataProvider: instHasBathroomEnum, },
                    // 4 酒店-住宿业
                    { name: "hotelBedNum",      label: "出租床数量", unit: "床", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "hotelAopDays",     label: "年营业天数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "hotelBuildArea",   label: "建筑面积",       unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    // 5 写字楼
                    { name: "officeSrvObjNum",  label: "服务对象人数", unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "officeBuildArea",  label: "建筑面积",     unit: "m²", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "officeHasWaterAC", label: "是否有水冷空调", unit: "", type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: commonBinaryState1, setSelectorState: setCommonBinaryState1, dataProvider: commonBinaryEnum, },
                    { name: "officeAopDays",    label: "营业天数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    // 6 餐饮
                    { name: "caterBuildArea",   label: "营业建筑面积", unit: "m²", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "caterAopDays",     label: "营业天数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "caterSrvObjNum",   label: "服务人数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    // 7 商超
                    { name: "superBuildArea",   label: "营业建筑面积", unit: "m²", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "superAopDays",     label: "营业天数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "superSrvObjNum",   label: "服务人数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    // 8 文化娱乐
                    { name: "cultBuildArea",    label: "营业建筑面积", unit: "m²", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "cultAopDays",      label: "营业天数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "cultSrvObjNum",    label: "服务人数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    // 0 其他
                    { name: "otherGreenArea",      label: "绿化面积", unit: "m²", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "otherInAgencyStaffs", label: "在编人数", unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "otherOutAgencyStaffs",label: "工作人员人数(食堂、保安、保洁、物业)", unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "otherHasCanteen",     label: "是否有食堂", unit: "", type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: commonBinaryState1, setSelectorState: setCommonBinaryState1, dataProvider: commonBinaryEnum, },
                    { name: "otherHasDormitory",   label: "是否有宿舍", unit: "", type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: commonBinaryState2, setSelectorState: setCommonBinaryState2, dataProvider: commonBinaryEnum, },
                    { name: "otherHasBathroom",    label: "是否有浴室", unit: "", type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: commonBinaryState3, setSelectorState: setCommonBinaryState3, dataProvider: commonBinaryEnum, },

                ]
            },
        ],
    };

    const FieldsConfig_1_table = {
        industry: [
            {
                inputs: [

                    //{ name: "name",        label: "表单名称",     unit: "",     type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                    //{ name: "industry", label: "行业类型",   unit: "",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "subindustry",    label: "行业类别",     unit: "",      type: "RADIO", editable: true, placeholder: "", selectorState: subindustryState, setSelectorState: setSubindustryState, dataProvider: subindustryEnum, },
                    { name: "waterUsageInst", label: "用水户名称",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true, }, },
                    // 1 医院
                    { name: "hospOutpatientsAnnual", label: "医院年门诊人次",   unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "hospBedsNum",           label: "医院编制床位数量", unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "hospInpatientsAnnual",  label: "医院年住院人数",   unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "hospMedicalStaffs",     label: "医务人员人数",     unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "hospWorkerStaffs",      label: "医院工作人员(食堂、保安、保洁、物业)",unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "hospGrade",             label: "医院级别",        unit: "",      type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: hospGradeState, setSelectorState: setHospGradeState, dataProvider: hospitalGradeEnum, },
                    // 2 学校
                    { name: "eduNonResiPrimStu", label: "小学生人数(非住宿)",     unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "eduResiPrimStu",    label: "小学生人数(住宿)",       unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "eduNonResiMidStu",  label: "中学、高中生人数(非住宿)", unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "eduResiMidStu",     label: "中学、高中生人数(住宿)",   unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "eduCollResiStu",    label: "大学生人数(住宿)",        unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "eduStaffs",         label: "教职工人数",             unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    // 3 机关
                    { name: "instGreenArea",       label: "机关绿化面积",  unit: "m²", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "instInAgencyStaffs",  label: "机关在编人数",  unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "instOutAgencyStaffs", label: "机关工作人员人数(食堂、保安、保洁、物业)", unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "instHasCanteen",      label: "机关是否有食堂", unit: "", type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: instHasCanteenState, setSelectorState: setInstHasCanteenState, dataProvider: instHasBathroomEnum, },
                    { name: "instHasDormitory",    label: "机关是否有宿舍", unit: "", type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: instHasDormitoryState, setSelectorState: setInstHasDormitoryState, dataProvider: instHasDormitoryEnum, },
                    { name: "instHasBathroom",     label: "机关是否有浴室", unit: "", type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: instHasBathroomState, setSelectorState: setInstHasBathroomState, dataProvider: instHasBathroomEnum, },
                    // 4 酒店-住宿业
                    { name: "hotelBedNum",      label: "出租床数量", unit: "床", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "hotelAopDays",     label: "年营业天数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "hotelBuildArea",   label: "建筑面积",       unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    // 5 写字楼
                    { name: "officeSrvObjNum",  label: "服务对象人数", unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "officeBuildArea",  label: "建筑面积",     unit: "m²", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "officeHasWaterAC", label: "是否有水冷空调", unit: "", type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: commonBinaryState1, setSelectorState: setCommonBinaryState1, dataProvider: commonBinaryEnum, },
                    { name: "officeAopDays",    label: "营业天数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    // 6 餐饮
                    { name: "caterBuildArea",   label: "营业建筑面积", unit: "m²", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "caterAopDays",     label: "营业天数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "caterSrvObjNum",   label: "服务人数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    // 7 商超
                    { name: "superBuildArea",   label: "营业建筑面积", unit: "m²", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "superAopDays",     label: "营业天数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "superSrvObjNum",   label: "服务人数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    // 8 文化娱乐
                    { name: "cultBuildArea",    label: "营业建筑面积", unit: "m²", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "cultAopDays",      label: "营业天数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "cultSrvObjNum",    label: "服务人数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    // 0 其他
                    { name: "otherGreenArea",      label: "绿化面积", unit: "m²", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "otherInAgencyStaffs", label: "在编人数", unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "otherOutAgencyStaffs",label: "工作人员人数(食堂、保安、保洁、物业)", unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "otherHasCanteen",     label: "是否有食堂", unit: "", type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: commonBinaryState1, setSelectorState: setCommonBinaryState1, dataProvider: commonBinaryEnum, },
                    { name: "otherHasDormitory",   label: "是否有宿舍", unit: "", type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: commonBinaryState2, setSelectorState: setCommonBinaryState2, dataProvider: commonBinaryEnum, },
                    { name: "otherHasBathroom",    label: "是否有浴室", unit: "", type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: commonBinaryState3, setSelectorState: setCommonBinaryState3, dataProvider: commonBinaryEnum, },

                ]
            },
        ],
        service: [
            {
                inputs: [

                    //{ name: "name",        label: "表单名称",     unit: "",     type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                    //{ name: "industry", label: "行业类型",   unit: "",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "subindustry",    label: "行业类别",     unit: "",      type: "RADIO", editable: true, placeholder: "", selectorState: subindustryState, setSelectorState: setSubindustryState, dataProvider: subindustryEnum, },
                    { name: "waterUsageInst", label: "用水户名称",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true, }, },
                    // 1 医院
                    { name: "hospOutpatientsAnnual", label: "医院年门诊人次",   unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "hospBedsNum",           label: "医院编制床位数量", unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "hospInpatientsAnnual",  label: "医院年住院人数",   unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "hospMedicalStaffs",     label: "医务人员人数",     unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "hospWorkerStaffs",      label: "医院工作人员(食堂、保安、保洁、物业)",unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "hospGrade",             label: "医院级别",        unit: "",      type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: hospGradeState, setSelectorState: setHospGradeState, dataProvider: hospitalGradeEnum, },
                    // 2 学校
                    { name: "eduNonResiPrimStu", label: "小学生人数(非住宿)",     unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "eduResiPrimStu",    label: "小学生人数(住宿)",       unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "eduNonResiMidStu",  label: "中学、高中生人数(非住宿)", unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "eduResiMidStu",     label: "中学、高中生人数(住宿)",   unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "eduCollResiStu",    label: "大学生人数(住宿)",        unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "eduStaffs",         label: "教职工人数",             unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    // 3 机关
                    { name: "instGreenArea",       label: "机关绿化面积",  unit: "m²", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "instInAgencyStaffs",  label: "机关在编人数",  unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "instOutAgencyStaffs", label: "机关工作人员人数(食堂、保安、保洁、物业)", unit: "",      type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "instHasCanteen",      label: "机关是否有食堂", unit: "", type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: instHasCanteenState, setSelectorState: setInstHasCanteenState, dataProvider: instHasBathroomEnum, },
                    { name: "instHasDormitory",    label: "机关是否有宿舍", unit: "", type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: instHasDormitoryState, setSelectorState: setInstHasDormitoryState, dataProvider: instHasDormitoryEnum, },
                    { name: "instHasBathroom",     label: "机关是否有浴室", unit: "", type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: instHasBathroomState, setSelectorState: setInstHasBathroomState, dataProvider: instHasBathroomEnum, },
                    // 4 酒店-住宿业
                    { name: "hotelBedNum",      label: "出租床数量", unit: "床", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "hotelAopDays",     label: "年营业天数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "hotelBuildArea",   label: "建筑面积",       unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    // 5 写字楼
                    { name: "officeSrvObjNum",  label: "服务对象人数", unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "officeBuildArea",  label: "建筑面积",     unit: "m²", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "officeHasWaterAC", label: "是否有水冷空调", unit: "", type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: commonBinaryState1, setSelectorState: setCommonBinaryState1, dataProvider: commonBinaryEnum, },
                    { name: "officeAopDays",    label: "营业天数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    // 6 餐饮
                    { name: "caterBuildArea",   label: "营业建筑面积", unit: "m²", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "caterAopDays",     label: "营业天数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "caterSrvObjNum",   label: "服务人数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    // 7 商超
                    { name: "superBuildArea",   label: "营业建筑面积", unit: "m²", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "superAopDays",     label: "营业天数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "superSrvObjNum",   label: "服务人数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    // 8 文化娱乐
                    { name: "cultBuildArea",    label: "营业建筑面积", unit: "m²", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "cultAopDays",      label: "营业天数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "cultSrvObjNum",    label: "服务人数",     unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    // 0 其他
                    { name: "otherGreenArea",      label: "绿化面积", unit: "m²", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "otherInAgencyStaffs", label: "在编人数", unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "otherOutAgencyStaffs",label: "工作人员人数(食堂、保安、保洁、物业)", unit: "", type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                    { name: "otherHasCanteen",     label: "是否有食堂", unit: "", type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: commonBinaryState1, setSelectorState: setCommonBinaryState1, dataProvider: commonBinaryEnum, },
                    { name: "otherHasDormitory",   label: "是否有宿舍", unit: "", type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: commonBinaryState2, setSelectorState: setCommonBinaryState2, dataProvider: commonBinaryEnum, },
                    { name: "otherHasBathroom",    label: "是否有浴室", unit: "", type: "RADIO", editable: true, placeholder: "", filterIf: inputFilterIf, selectorState: commonBinaryState3, setSelectorState: setCommonBinaryState3, dataProvider: commonBinaryEnum, },

                ]
            },
        ],
    };

    const FieldsConfig_2_book  = {industry: [], service: []};
    const FieldsConfig_2_table = {industry: [], service: []};

    const FieldsConfig_3_book = {
        industry: [
        // 其它部分
            {
                inputs: [
                //{ name: "others", label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "",  props: {multiline: true}, },
                ]
            },
        ],
        service: [
            // 其它部分
            {
                inputs: [
                    //{ name: "others", label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "",  props: {multiline: true}, },
                ]
            },
        ],
    };
    const FieldsConfig_3_table = {
        industry: [
        // 其它部分
            {
                inputs: [
                //{ name: "others", label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "",  props: {multiline: true}, },
                ]
            },
        ],
        service: [
            // 其它部分
            {
                inputs: [
                    //{ name: "others", label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "",  props: {multiline: true}, },
                ]
            },
        ],
    };

    let FieldsConfig = [];
    switch(projSubclass) {
        case 1: // 表
            FieldsConfig = [...FieldsConfig_1_table[projIndustry], ...FieldsConfig_2_table[projIndustry], ...FieldsConfig_3_table[projIndustry]];
            break;
        case 2: // 书
            FieldsConfig = [...FieldsConfig_1_book[projIndustry], ...FieldsConfig_2_book[projIndustry], ...FieldsConfig_3_book[projIndustry]];
            break;
        default:
            console.warn("Unknown subclass:", projSubclass);
            FieldsConfig = [];
            break;
    }

    //const FieldsConfig = [...FieldsConfig_1_book, ...FieldsConfig_2_book, ...FieldsConfig_3_book];

    return (
        <View style={{ flex: 1 }}>

            <HeaderBar
                //title={screenTitle}
                navigation={navigation}
                goBackCallback={() => {
                    callOneByOne(
                        //() => { _mainCversionGetter=undefined; _subCversionGetter=undefined;},
                        resetSelectorStates,
                    );
                    /*callOneByOne(clearStore, resetSelectorStates)*/  // 返回前清空mmkv存储因为临时数据不再需要, 重置状态使得页面切换后不会使用前面的数据
                }}
                menuItemArray={formType === 0 ? [{ title: "删除表单", action: deleteAlert }, { title: "清空缓存", action: clearCache }] : []}
            />

            <ScreenWrapper contentContainerStyle={styles.container}>

                <View style={styles.formEntry}>
                    <FormListNodesMapper
                        fieldsConfig={FieldsConfig}
                        subCversionRef={subCversionRef}
                        control={control}
                        setStore={setStore}
                        resetField={resetField}
                        errors={errors}
                        setSnackBarMessage={setSnackBarMessage}
                        setShowSnackbar={setShowSnackbar}
                        //formDisabledGlobal={formDisabledGlobal}
                        editable={formEditableGlobal}
                        rowStyle={styles.rowContainer}
                    />
                    <Divider bold={true}/>
                    <View style={{alignItems: "center"}}>
                        {quotaRefImage && <StaticImageView
                            source={quotaRefImage}
                            reservedMargin={formMargin}
                            imageStyles={styles.imageStyles}
                        />}
                    </View>

                </View>


                {formType === 0 && <View style={styles.button}>
                    <Button mode="contained"
                        disabled={okButtonDisabled}
                        loading={okButtonLoading}
                        icon={saveButtonIcon}
                        onPress={handleSubmit(onHandleSubmit, (formErr) => onPreSubmitError(formErr, getStore, "ClientInfoRecordsUpdating"))}
                    >{bottomBarLabels.ok}</Button>
                </View>}
            </ScreenWrapper>

            <LoadingIndicator
                title={""}
                message={"加载中....."}
                visible={loadingIndicatorVisible}
                onClose={()=>setLoadingIndicatorVisible(false)}
            />

            <Snackbar
                style={styles.snackBar}
                visible={showSnackbar}
                onDismiss={() => setShowSnackbar(false)}
                onIconPress={() => setShowSnackbar(false)}
                duration={Snackbar.DURATION_LONG}
            >
                {snackBarMessage}
            </Snackbar>

            <DialogToConfirm
                visible={showConfirm}
                title={confimDialogConfig.title ? confimDialogConfig.title : ""}
                text={confimDialogConfig.text ? confimDialogConfig.text : ""}
                onOK={confimDialogConfig.onOK ? confimDialogConfig.onOK : () => setShowConfirm(false)}
                okBtnLabel={confimDialogConfig.okLabel ? confimDialogConfig.okLabel : "确认"}
            />
        </View>
    );
};

const formMargin = 8;

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    rowContainer:{
        firstColumn: {
            flex: 2,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 3,
        // /borderWidth: 1,
        // height: 40,
        },
    },
    rowContainerLongText: {
        firstColumn: {
            flex: 1,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 4,
            // /borderWidth: 1,
            // height: 40,
        },
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        width: "80%",
        alignSelf: "center",
    },
    formEntry: {
        margin: formMargin,
    },
    snackBar: {
        flexDirection: "column",
    },
    imageStyles: {
        container: {marginTop: 10},
    }
});

export default RecordsUpdating;
