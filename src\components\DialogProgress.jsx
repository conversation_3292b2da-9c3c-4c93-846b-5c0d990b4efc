import React from "react";
import { View } from "react-native";
import { Button, Dialog, Portal, ProgressBar, Text, useTheme } from "react-native-paper";
import { isNumber } from "../utils";


/**
 *
 * @param {object} args
 * @param {number | undefined | false} args.progress 下载进度, 0~1
 * @returns
 */
export const DialogProgress = ({ title, onProgressText, onFinishedText, progress, visible, setVisible, onFinished, onCancel = false, okBtnLabel = "确定", cancelBtnLabel = "取消"}) => {
    //const [visible, setVisible] = useState(true);
    const theme = useTheme();
    return (
        <Portal>
            <Dialog onDismiss={()=>{onCancel ? onCancel() : undefined;}} visible={visible} dismissable={false}>
                {title && <Dialog.Title>{title}</Dialog.Title>}
                <Dialog.Content>
                    <View flexDirection="column">
                        <ProgressBar
                            indeterminate={isNumber(progress) ? false : true}
                            progress={isNumber(progress) ? progress : undefined}
                            visible={true}
                            style={{marginTop: 10, marginBottom: 20}}
                        />
                        <Text variant="bodyLarge" style={{ color: theme.colors.onSurfaceVariant}}>{progress >= 100 ? onFinishedText : onProgressText}</Text>
                    </View>
                </Dialog.Content>
                {(onFinished || onCancel) && <Dialog.Actions>
                    {onCancel && <Button onPress={()=>{
                        setVisible(false);
                        onCancel();
                    }}>{cancelBtnLabel}</Button>}
                    {onFinished && <Button onPress={()=>{
                        if(onFinished) {
                            setVisible(false);
                            onFinished();
                        } else {
                            setVisible(false);
                        }
                    }}>{okBtnLabel}</Button>}
                </Dialog.Actions>}
            </Dialog>
        </Portal>
    );
};


/**
 *
 * @param {object} args
 * @param {number | undefined | false} args.progress 下载进度, 0~1
 * @param {bool | undefined} args.visible 界面是否可见, 通过外部控制
 * @param {function} args.setVisible 界面是否可见, 通过外部控制
 * @returns
 */
export const DownloadUpdateProgress = ({progress, visible, setVisible}) => {
    return (
        <DialogProgress
            title="客户端更新"
            onProgressText={"下载中..."}
            onFinishedText={"下载完成"}
            progress={progress}
            visible={visible}
            setVisible={setVisible}
            onFinished={undefined}
            onCancel={()=>console.log("Switch downloading in background!")}
            okBtnLabel="安装"
            cancelBtnLabel="后台下载"
        />
    );
};
