import React from "react";
import { StyleSheet } from "react-native";
import { Card, Chip, Divider, Icon, Text, TouchableRipple, useTheme } from "react-native-paper";
import { makeConfirmMessageQuery } from "../api/confirmMessage";
import { isInteger } from "../utils";
import <PERSON><PERSON>ontainer from "./RowContainer";


// 参考: CREATE TABLE PLATFORM.TASKMESSAGES
// 0: 无, 1: 已完结, 2: 新建, 3: 有更新
/*
const chipStates = {
    0: { icon: "circle-medium",      text: "" },
    1: { icon: "check",              text: "完成" },
    2: { icon: "email-plus-outline", text: "新"},
    3: { icon: "update",             text: "状态更新"},
    4: { icon: "alert-outline",      text: "反馈"},
    default: { icon: "circle-medium", text: "" },
};
*/

const chipStates = {
    0: { icon: "circle-medium",       text: "" },
    1: { icon: "check",               text: "" },
    2: { icon: "circle-medium",       text: ""},
    3: { icon: "update",              text: ""},
    4: { icon: "alert-outline",       text: ""},
    default: { icon: "circle-medium", text: "" },
};

// https://callstack.github.io/react-native-paper/docs/components/Card/
/**
 * 消息定义见服务端: src/model/query/projects.lisp, %get-user-project-messages
 * 以下几个是`task-messages`中的原始字段, 生成消息时定义:
 * id: 消息的pubid
 * title: 消息标题
 * subtitle: 消息子标题
 * description: 消息描述
 * state: 消息状态
 * confirmed: 消息是否已确认
 *
 * 以下3个是根据type计算出的文本信息, 显示在MessageCard上:
 * schedule: 截至日期
 * ps: 消息ps
 * signature: 消息签名
 *
 * type, subtype都是`task-messages`中的原始字段, 生成消息时定义; contentId根据type去其他地方索引:
 * type: 消息类型, 1流程消息(对应allflows数据), 目前只有类型1.
 * subtype: 在客户端用于判断该消息路由到那个屏.
 * contentId: 如果type=1, contentId表示allprojects中的pubid.
 *
 * 以下4个是根据type计算出的, 目前只有type=1时有效, 对应于allprojects中的三个字段:
 * class: type=1时, 对应all-projects的class, 即项目类型. 1表示水平衡, 11表示零碳诊断.
 * subclass: type=1时, 对应all-projects的subclass, 即项目子类型. 对于水平衡项目, 1表, 2书, 零碳诊断无子类型.
 * industry: type=1时, 对应all-projects的industry, 即行业类型. 水平衡项目, 1: 工业, 2: 服务业; 零碳诊断, 1: 工业, 3公共机构.
 * principalPubid: type=1时, 对应all-projects的pubid.
 *
 * @param {Object} args
 * @param {string} args.msgId 消息的pubid
 * @param {string} args.title 消息标题
 * @param {string} args.subtitle 消息子标题
 * @param {string} args.description 消息描述
 * @param {string} args.postscript 消息附言
 * @param {int} args.schedule 当前结点截止时间UT
 * @param {int} args.state 消息状态编号
 * @param {boolean} args.confirmed 消息是否已确认
 * @param {()=>{}} args.onPress 点击事件
 * @returns
 */
const MessageCard = ({ msgId, title, subtitle, description, postscript, schedule, signature, state, confirmed, onPress, ...props}) => {
    const theme = useTheme();
    const { isSuccess, refetch } = makeConfirmMessageQuery(msgId); // isSuccess initialized to false
    // 由于服务端沿用了原先的字符串类型, 而不是json类型, 因此这里需要转换为json对象
    const { createDate, expectedDate} = JSON.parse(description);
    //console.log("confirmed:.................", title, confirmed);
    return (
        <Card
            mode="elevated"
            elevation={1}
            onPress={() => console.log("Card Pressed")} // will not trigged as the event is processed in the TouchableRipple component
            style={{ backgroundColor: theme.colors.background, ...styles.card, }}
            {...props}
        >
            <TouchableRipple
                onPress={()=> {
                    confirmed || refetch();
                    onPress && onPress();
                    isSuccess && console.log("Message confirmed accepted by server!");
                }}
                //rippleColor="rgba(0, 0, 0, .32)"
            >
                <>
                    <Card.Title
                        title={title || ""}
                        subtitle={subtitle || ""}
                        titleVariant="titleMedium"
                        titleStyle={{ color: theme.colors.primary, fontWeight: "bold", marginBottom: 0, paddingTop: 5}} // marginBottom属性参考rn-paper中Card的源码
                        subtitleVariant="titleSmall"
                        titleNumberOfLines={5}
                        style={styles.title}
                        //left={(props) => title && <Avatar.Text {...props} label={title.substring(0, 1)} />}
                        right={(props) => {
                            if (chipStates[state]?.text) {
                                return <Chip
                                    icon={() => <Icon source={chipStates[state]?.icon} size={props.size} color={confirmed ? theme.colors.primary : theme.colors.error} />}
                                >
                                    {chipStates[state]?.text}
                                </Chip>;
                            } else if (chipStates[state]) {
                                return confirmed ? null : <Icon source={chipStates[state]?.icon} size={props.size} color={theme.colors.error}/>;
                            } else if (isInteger(state)) {
                                return confirmed ? null : <Icon source={chipStates.default.icon} size={props.size} color={theme.colors.error} />;
                            } else {
                                return null;
                            }
                        }}
                    />
                    <Card.Content style={styles.content}>

                        {createDate   && <Text variant="bodyLarge" style={styles.desc}>{`创建日期: ${createDate}`}</Text>}
                        {expectedDate && <Text variant="bodyLarge" style={styles.desc}>{`交付日期: ${expectedDate}`}</Text>}
                        <RowContainer style={styles.psContainer}>
                            {postscript && <Text variant="bodyLarge" style={styles.postscript}>{postscript}</Text>}
                            {signature  && <Text variant="bodyLarge" style={styles.signature}>{signature}</Text>}
                        </RowContainer>
                    </Card.Content>
                    <Divider/>
                </>

            </TouchableRipple>
        </Card>
    );
};

const styles = StyleSheet.create({
    card: {
        // /backgroundColor: "#FFFFFF",
        borderTopLeftRadius: 0,
        borderTopRightRadius: 0,
        borderBottomLeftRadius: 0,
        borderBottomRightRadius: 0,
        paddingRight: 0,
        //marginVertical:10,
    },
    title: {
        marginTop: 10,
        minHeight: 30, // style属性要参考rn-paper中Card的源码
        //borderWidth: 1,
        paddingRight: 10,
    },
    content: {
        //borderWidth: 1,
        //borderColor: "red",
        //paddingRight: 5,
    },
    desc: {
        //borderWidth: 1,
    },
    psContainer: {
        //borderWidth: 1,
    },
    postscript: {
        //borderWidth: 1,
        flexGrow:1,     // 剩余空间全部分配给这部分
        flexShrink:1,   // 如果溢出也只针对这部分
        textAlign: "left"
    },
    schedule: {
        //borderWidth: 1,
        flexGrow: 0,    // 剩余空间不分配给这部分
        flexShrink: 0,  // 如果移除也不针对这部分
        textAlign: "right",
        marginRight: 0
    },
    signature: {
        //borderWidth: 1,
        flexGrow: 0,    // 剩余空间不分配给这部分
        flexShrink: 0,  // 如果移除也不针对这部分
        textAlign: "right",
        marginRight: 0
    },
});

export default MessageCard;
