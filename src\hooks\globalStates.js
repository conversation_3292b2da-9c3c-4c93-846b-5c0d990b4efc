import * as keys from "../config/keysConfig";
import { createZustandState, createZustandStoredState } from "../services/state-management";
import { isFunction } from "../utils";

const defineStoredState = (storeKey) => {
    const statDefinition = (set, get) => ({
        data: {},
        setDate: (dataArray) => set({ data: dataArray }),
        resetData: () => set({ data: {} }),
    });
    return createZustandStoredState(storeKey, statDefinition);
};

const defineState = () => {
    const statDefinition = (set, get) => ({
        data: [],
        setData: (dataArray) => set({ data: dataArray }),
        resetData: (defaultData) => set({ data: defaultData ? defaultData : [] }),
    });
    return createZustandState(statDefinition);
};

const snackbarStateDefinition = (set, get) => ({
    visible: false,
    message: "Some error occured!",
    onOpen:  (msg) => set({ visible: true,  message: msg ||  "遇到错误, 请联系管理员, 谢谢!"}),
    onClose: ()    => set({ visible: false, message: "遇到错误, 请联系管理员, 谢谢!"}),
});

const loadingIndicatorStateDefinition = (set, get) => ({
    visible: false,
    title:   "",
    message: "加载中.....",
    onOpen:  (title, msg) => set({ visible: true,  title: title || "", message: msg || "加载中....." }),
    onClose: ()           => set({ visible: false, title: "",          message: "加载中....." }),
    initIndicator: (title, msg) => {
        const oldVals = get();
        // 这个if的目的是阻止重复渲染时反复设置状态
        if (oldVals.visible === false) {
            set({visible: true, title: title || "", message: msg || "加载中....."});
        }
    }
});

const confirmDialogStateDefinition = (set, get) => ({
    visible: false,
    title:   "",
    message: "", // 内容消息可以是字符串, 也可以是字符串数组, 数组内容会一段一段列出来
    confirmCB: undefined,
    cancelCB: undefined,
    initDialog:  (title, msg, confirmCB, cancelCB) => { // cancelCB为假就不会显示取消按钮
        const oldVals = get();
        // 这个if的目的是阻止重复渲染时反复设置状态
        if (title !== oldVals.title || msg !== oldVals.message) {
            set({ visible: true,  title: title || "", message: msg || "", confirmCB: confirmCB,  cancelCB: cancelCB});
        }
    }, // 初始化对话框, 并且让对话框显示出来
    onConfirm: async () => {
        const cb = get()?.confirmCB;
        if(isFunction(cb)) {
            await cb();
        }
        set({visible: false, title: "", message: "", confirmCB: undefined, cancelCB: undefined,});
    },  // 确认按钮调用
    onCancel: (get()?.cancelCB) ? (async () => {
        const cb = get().cancelCB;
        if (isFunction(cb)) {
            await cb();
        }
        set({visible: false, title: "", message: "", confirmCB: undefined, cancelCB: undefined,});
    }) : undefined, // 取消按钮调用, 如果为假就不会显示按钮
});

const tabBarStateDefinition = (set, get) => ({
    msgTab:              { hasNew: false, newNum: 0 },
    workbenchTab:        { hasNew: false, newNum: 0 },
    knowdedgeBaseTab:    { hasNew: false, newNum: 0 },
    setMsgTab:           (hasNew, newNum) => set({ msgTab: { hasNew: hasNew, newNum: newNum } }),
    setWorkbenchTab:     (hasNew, newNum) => set({ msgTab: { hasNew: hasNew, newNum: newNum } }),
    setKnowdedgeBaseTab: (hasNew, newNum) => set({ msgTab: { hasNew: hasNew, newNum: newNum } }),
});

const updateProgressDefinition = (set, get) => ({
    visible: false,
    progress: 0,
    setVisible:  (visible)  => get().visible  !== visible  && set({ visible: visible }),
    setProgress: (progress) => get().progress !== progress && set({ progress: progress }),
});

export const allUsersState       = defineState();
export const allDepartmentsState = defineState();
export const allPositionsState   = defineState();
export const allRolesState       = defineState();
export const allClientsState     = defineState();
export const orgProjBaseState    = defineState();
export const userProjMessages    = defineState();

export const tmpUserState        = defineStoredState(keys.ORG_USER_ADD.store);
export const tmpDepartmentState  = defineStoredState(keys.ORG_DEPARTMENT_ADD.store);
export const tmpPositionState    = defineStoredState(keys.ORG_POSITION_ADD.store);
export const tmpRoleState        = defineStoredState(keys.ORG_ROLE_ADD.store);
export const tmpClientState      = defineStoredState(keys.ORG_CLIENT_ADD.store);

// 分别用于全局定义的snack, loading, confirm界面, 界面定义在HomeScreen.jsx
export const snackbarState         = createZustandState(snackbarStateDefinition);
export const loadingIndicatorState = createZustandState(loadingIndicatorStateDefinition);
export const confirmDialogState    = createZustandState(confirmDialogStateDefinition);

// 用于tab导航栏图标的右上方消息数量的提示
export const tabBarBadgedIconState = createZustandState(tabBarStateDefinition);

// 用于显示更新下载进度的界面
export const updateProgressState = createZustandState(updateProgressDefinition);
