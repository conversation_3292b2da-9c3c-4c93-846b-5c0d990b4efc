import * as React from "react";
import { ActivityIndicator, Platform, StyleSheet, View } from "react-native";
import { Dialog, MD2Colors, MD3Colors, Portal, useTheme } from "react-native-paper";
import { DialogTextComponent as TextComponent} from "./DialogTextComponent";
import { useShallow } from "zustand/shallow";

const isIOS = Platform.OS === "ios";

/**
 * https://github.com/callstack/react-native-paper/tree/main/example/src/Examples/Dialogs/DialogWithLoadingIndicator.tsx
 * @param {object} arg
 * @param {string} arg.title
 * @param {string} arg.message
 * @param {boolean} arg.visible
 * @param {()=>{}} arg.onClose
 * @returns
 */
export const LoadingIndicator = ({ title, message, visible, onClose }) => {
    const { isV3 } = useTheme();
    return (
        <Portal>
            <Dialog onDismiss={onClose} visible={visible}>
                {title ? <Dialog.Title>{title || ""}</Dialog.Title> : <View/>}
                <Dialog.Content>
                    <View style={styles.flexing}>
                        <ActivityIndicator
                            color={isV3 ? MD3Colors.tertiary30 : MD2Colors.indigo500}
                            size={isIOS ? "large" : 48}
                            style={styles.marginRight}
                        />
                        <TextComponent>{message || "加载中....."}</TextComponent>
                    </View>
                </Dialog.Content>
            </Dialog>
        </Portal>
    );
};

/**
 * @param {object} args
 * @param {} args.globalStates something like `loadingIndicatorState`
 */
export const LoadingIndicatorGlobal = ({ globalStates }) => {
    const { isV3 } = useTheme();

    const [
        visible,
        title,
        message,
        //onOpen,
        onClose,
    ] = globalStates(useShallow(state => [
        state.visible,
        state.title,
        state.message,
        //state.onOpen,
        state.onClose,
    ]));

    return (
        <Portal>
            <Dialog onDismiss={onClose} visible={visible}>
                {title ? <Dialog.Title>{title || ""}</Dialog.Title> : <View/>}
                <Dialog.Content>
                    <View style={styles.flexing}>
                        <ActivityIndicator
                            color={isV3 ? MD3Colors.tertiary30 : MD2Colors.indigo500}
                            size={isIOS ? "large" : 48}
                            style={styles.marginRight}
                        />
                        <TextComponent>{message || "加载中....."}</TextComponent>
                    </View>
                </Dialog.Content>
            </Dialog>
        </Portal>
    );
};

const styles = StyleSheet.create({
    flexing: {
        flexDirection: "row",
        alignItems: "center",
    },
    marginRight: {
        marginRight: 16,
    },
});
