import { createZustandState } from "../services/state-management";

// 本文件用于配置和定义所有selecter组件的全局状态管理.
// 由于selector的标签和值属于不同属性,
// 在上层组件点击重置按钮时需要同时改变标签和值,
// 而selector的操作也会改变标签和值,
// 因此需要使用全局状态管理.


/**
 * 用于选择器组件的状态定义.
 * 用法: const selectorState = createZustandState(selectorStateDefinition);
 * @param {*} set
 * @param {*} get
 * @returns
 */
export const selectorStateDefinition = (set, get) => ({
    value:      {},
    setValue:   (obj) => set({ value: obj }),
    resetState: ()    => set({ value: {}, }),
});


// 用于OrgInserting组件
const orgInsertStateDefinition = (set, get) => ({
    expired:          0,
    subscriptions:    [],
    setExpired:       (val) => set({ expired: val }),
    setSubscriptions: (arr) => set({ subscriptions: arr }),
    resetStates:      ()    => set({ subscriptions: [], expired: 0 }),
});
export const orgInsertStates = createZustandState(orgInsertStateDefinition);

// 用于OrgUpdating组件
const orgUpdateStateDefinition = (set, get) => ({
    expired:          0,
    subscriptions:    [],
    setExpired:       (val) => set({ expired: val }),
    setSubscriptions: (arr) => set({ subscriptions: arr }),
    resetStates:      ()    => set({ subscriptions: [], expired: 0 }),
});
export const orgUpdateStates = createZustandState(orgUpdateStateDefinition);


// 用于DepartmentsInserting组件
const deptInsertStateDefinition = (set, get) => ({
    superior:   {},
    members:    [],
    leader:     {},
    viceLeader: {},
    setSuperior:   (obj) => set({ superior:   obj }), // {id, name}
    setMembers:    (arr) => set({ members:    arr }), // [pid1, pid2, ...]
    setLeader:     (obj) => set({ leader:     obj }), // {id, name}
    setViceLeader: (obj) => set({ viceLeader: obj }), // {id, name}
    resetStates: ()    => set({ superior: {}, members: [], leader: {}, viceLeader: {} }),
});
export const deptInsertState = createZustandState(deptInsertStateDefinition);


// 用于DepartmentsUdating组件
const deptUpdateStateDefinition = (set, get) => ({
    superior:   {},
    members:    [],
    leader:     {},
    viceLeader: {},
    setSuperior:   (obj) => set({ superior:   obj }), // {id, name}
    setMembers:    (arr) => set({ members:    arr }), // [pid1, pid2, ...]
    setLeader:     (obj) => set({ leader:     obj }), // {id, name}
    setViceLeader: (obj) => set({ viceLeader: obj }), // {id, name}
    resetStates: ()    => set({ superior: {}, members: [], leader: {}, viceLeader: {} }),
});
export const deptUpdateState = createZustandState(deptUpdateStateDefinition);


// 用于UserInserting组件
const userInsertStateDefinition = (set, get) => ({
    //state: {},
    //setState: (obj) => set({ state: obj }), // {id, name}
    rolesCheck: [],
    setRolesCheck: (arr) => set({ rolesCheck: arr }),
    resetStates: () => set({ state: {}, rolesCheck: [] }),
});
export const userInsertStates = createZustandState(userInsertStateDefinition);

// 用于UserUpdating组件
const userUpdateStateDefinition = (set, get) => ({
    //state: {},
    //setState: (obj) => set({ state: obj }), // {id, name}
    rolesCheck: [],
    setRolesCheck: (arr) => set({ rolesCheck: arr }),
    resetStates: () => set({ state: {} }),
});
export const userUpdateStates = createZustandState(userUpdateStateDefinition);


// 用于PositionsInserting组件
const positionInsertStateDefinition = (set, get) => ({
    state: {},
    setState: (obj) => set({ state: obj }), // {id, name}
    resetStates: () => set({ state: {} }),
});
export const positionInsertStates = createZustandState(positionInsertStateDefinition);

// 用于PositionsUpdating组件
const positionUpdateStateDefinition = (set, get) => ({
    state: {},
    setState: (obj) => set({ state: obj }), // {id, name}
    resetStates: () => set({ state: {} }),
});
export const positionUpdateStates = createZustandState(positionUpdateStateDefinition);


// 用于ClientsInserting组件
const clientInsertStateDefinition = (set, get) => ({
    state: {},
    setState: (obj) => set({ state: obj }), // {id, name}
    resetStates: () => set({ state: {} }),
});
export const clientInsertStates = createZustandState(clientInsertStateDefinition);

// 用于ClientsUpdating组件
const clientUpdateStateDefinition = (set, get) => ({
    state: {},
    setState: (obj) => set({ state: obj }), // {id, name}
    resetStates: () => set({ state: {} }),
});
export const clientUpdateStates = createZustandState(clientUpdateStateDefinition);


// 用于RolesInserting组件
const roleInsertStateDefinition = (set, get) => ({
    stdRoleEnum:    [],
    setStdRoleEnum: (arr) => set({ stdRoleEnum: arr }), // [role1, role2, ...]
    resetStates:    ()    => set({ stdRoleEnum: [] }),
});
export const roleInsertStates = createZustandState(roleInsertStateDefinition);

// 用于RolesUpdating组件
const roleUpdateStateDefinition = (set, get) => ({
    stdRoleEnum:    [],
    setStdRoleEnum: (arr) => set({ stdRoleEnum: arr }), // [role1, role2, ...]
    resetStates:    ()    => set({ stdRoleEnum: [] }),
});
export const roleUpdateStates = createZustandState(roleUpdateStateDefinition);


// 用于ProjectBaseInserting组件
const projbaseInsertStateDefinition = (set, get) => ({
    classRadio:         {},
    subClassRadio:      {},
    industryRadio:      {},
    difficultyRadio:    {},
    setClassRadio:      (obj) => set({ classRadio: obj }),
    setSubClassRadio:   (obj) => set({ subClassRadio: obj }),
    setIndustryRadio:   (obj) => set({ industryRadio: obj }),
    setDifficultyRadio: (obj) => set({ difficultyRadio: obj }),
    resetStates:        () => set({ classRadio: {}, subClassRadio: {}, industryRadio: {}, difficultyRadio: {} }),
});
export const projbaseInsertStates = createZustandState(projbaseInsertStateDefinition);

// 用于ProjectBaseUpdating组件
const projbaseUpdateStateDefinition = (set, get) => ({
    classRadio:         {},
    subClassRadio:      {},
    industryRadio:      {},
    difficultyRadio:    {},
    setClassRadio:      (obj) => set({ classRadio: obj }),
    setSubClassRadio:   (obj) => set({ subClassRadio: obj }),
    setIndustryRadio:   (obj) => set({ industryRadio: obj }),
    setDifficultyRadio: (obj) => set({ difficultyRadio: obj }),
    resetStates:        () => set({ classRadio: {}, subClassRadio: {}, industryRadio: {}, difficultyRadio: {} }),
});
export const projbaseUpdateStates = createZustandState(projbaseUpdateStateDefinition);


// 用于WaterBalanceInserting组件
const waterBalanceInsertStateDefinition = (set, get) => ({
    clientRadio:         {},
    projbaseRadio:       {},
    difficultyRadio:     {},
    expectedTimePicker:  0,
    salesmanRadio:       {},
    managerRadio:        {},
    managerLevelRadio:   {},
    areaPrincipalRadio:  {},
    courtesyCopiesCheck: [],
    templateRadio:       {},
    templateOptsCheck:   [],
    startDatePicker:     0,
    endDatePicker:       0,
    setClientRadio:         (obj) => set({ clientRadio: obj }),
    setProjbaseRadio:       (obj) => set({ projbaseRadio: obj }),
    setDifficultyRadio:     (obj) => set({ difficultyRadio: obj }),
    setExpectedTimePicker:  (val) => set({ expectedTimePicker: val }),
    setSalesmanRadio:       (obj) => set({ salesmanRadio: obj }),
    setManagerRadio:        (obj) => set({ managerRadio: obj }),
    setManagerLevelRadio:   (obj) => set({ managerLevelRadio: obj }),
    setAreaPrincipalRadio:  (obj) => set({ areaPrincipalRadio: obj }),
    setCourtesyCopiesCheck: (arr) => set({ courtesyCopiesCheck: arr }),
    setTemplateRadio:       (obj) => set({ templateRadio: obj }),
    setTemplateOptsCheck:   (arr) => set({ templateOptsCheck: arr }),
    setStartDatePicker:     (val) => set({ startDatePicker: val }),
    setEndDatePicker:       (val) => set({ endDatePicker: val }),
    resetStates: () => set({
        clientRadio:         {},
        projbaseRadio:       {},
        difficultyRadio:     {},
        expectedTimePicker:  0,
        salesmanRadio:       {},
        managerRadio:        {},
        managerLevelRadio:   {},
        areaPrincipalRadio:  {},
        courtesyCopiesCheck: [],
        templateRadio:       {},
        templateOptsCheck:   [],
        startDatePicker:     0,
        endDatePicker:       0,
    }),
});
export const waterBalanceInsertStates = createZustandState(waterBalanceInsertStateDefinition);


// 用于WaterBalanceUpdating组件
const waterBalanceUpdateStateDefinition = (set, get) => ({
    clientRadio:         {},
    projbaseRadio:       {},
    difficultyRadio:     {},
    expectedTimePicker:  0,
    salesmanRadio:       {},
    managerRadio:        {},
    managerLevelRadio:   {},
    areaPrincipalRadio:  {},
    courtesyCopiesCheck: [],
    setClientRadio:         (obj) => set({ clientRadio: obj }),
    setProjbaseRadio:       (obj) => set({ projbaseRadio: obj }),
    setDifficultyRadio:     (obj) => set({ difficultyRadio: obj }),
    setExpectedTimePicker:  (val) => set({ expectedTimePicker: val }),
    setSalesmanRadio:       (obj) => set({ salesmanRadio: obj }),
    setManagerRadio:        (obj) => set({ managerRadio: obj }),
    setManagerLevelRadio:   (obj) => set({ managerLevelRadio: obj }),
    setAreaPrincipalRadio:  (obj) => set({ areaPrincipalRadio: obj }),
    setCourtesyCopiesCheck: (arr) => set({ courtesyCopiesCheck: arr }),
    resetStates: () => set({
        clientRadio:         {},
        projbaseRadio:       {},
        difficultyRadio:     {},
        expectedTimePicker:  0,
        salesmanRadio:       {},
        managerRadio:        {},
        managerLevelRadio:   {},
        areaPrincipalRadio:  {},
        courtesyCopiesCheck: [], }),
});
export const waterBalanceUpdateStates = createZustandState(waterBalanceUpdateStateDefinition);


// 用于ZeroCarbonInserting组件
const zeroCarbonInsertStateDefinition = (set, get) => ({
    clientRadio:         {},
    projbaseRadio:       {},
    difficultyRadio:     {},
    expectedTimePicker:  0,
    salesmanRadio:       {},
    managerRadio:        {},
    managerLevelRadio:   {},
    areaPrincipalRadio:  {},
    courtesyCopiesCheck: [],
    setClientRadio:         (obj) => set({ clientRadio: obj }),
    setProjbaseRadio:       (obj) => set({ projbaseRadio: obj }),
    setDifficultyRadio:     (obj) => set({ difficultyRadio: obj }),
    setExpectedTimePicker:  (val) => set({ expectedTimePicker: val }),
    setSalesmanRadio:       (obj) => set({ salesmanRadio: obj }),
    setManagerRadio:        (obj) => set({ managerRadio: obj }),
    setManagerLevelRadio:   (obj) => set({ managerLevelRadio: obj }),
    setAreaPrincipalRadio:  (obj) => set({ areaPrincipalRadio: obj }),
    setCourtesyCopiesCheck: (arr) => set({ courtesyCopiesCheck: arr }),
    resetStates: () => set({
        clientRadio:         {},
        projbaseRadio:       {},
        difficultyRadio:     {},
        expectedTimePicker:  0,
        salesmanRadio:       {},
        managerRadio:        {},
        managerLevelRadio:   {},
        areaPrincipalRadio:  {},
        courtesyCopiesCheck: [], }),
});
export const zeroCarbonInsertStates = createZustandState(zeroCarbonInsertStateDefinition);

// 用于ZeroCarbonUpdating组件
const zeroCarbonUpdateStateDefinition = (set, get) => ({
    clientRadio:         {},
    projbaseRadio:       {},
    difficultyRadio:     {},
    expectedTimePicker:  0,
    salesmanRadio:       {},
    managerRadio:        {},
    managerLevelRadio:   {},
    areaPrincipalRadio:  {},
    courtesyCopiesCheck: [],
    setClientRadio:         (obj) => set({ clientRadio: obj }),
    setProjbaseRadio:       (obj) => set({ projbaseRadio: obj }),
    setDifficultyRadio:     (obj) => set({ difficultyRadio: obj }),
    setExpectedTimePicker:  (val) => set({ expectedTimePicker: val }),
    setSalesmanRadio:       (obj) => set({ salesmanRadio: obj }),
    setManagerRadio:        (obj) => set({ managerRadio: obj }),
    setManagerLevelRadio:   (obj) => set({ managerLevelRadio: obj }),
    setAreaPrincipalRadio:  (obj) => set({ areaPrincipalRadio: obj }),
    setCourtesyCopiesCheck: (arr) => set({ courtesyCopiesCheck: arr }),
    resetStates: () => set({
        clientRadio:         {},
        projbaseRadio:       {},
        difficultyRadio:     {},
        expectedTimePicker:  0,
        salesmanRadio:       {},
        managerRadio:        {},
        managerLevelRadio:   {},
        areaPrincipalRadio:  {},
        courtesyCopiesCheck: [], }),
});
export const zeroCarbonUpdateStates = createZustandState(zeroCarbonUpdateStateDefinition);

// 用于ProjectCopying组件
const projectCopyingStateDefinition = (set, get) => ({
    projClassRadio:      {},
    fatherRadio:         {},
    managerRadio:        {},
    setProjClassRadio:   (obj) => set({ projClassRadio: obj }),
    setFatherRadio:      (obj) => set({ fatherRadio:    obj }),
    setManagerRadio:     (obj) => set({ managerRadio:   obj }),
    resetStates: () => set({
        projClassRadio:      {},
        fatherRadio:         {},
        managerRadio:        {},
    }),
});
export const projectCopyingStates = createZustandState(projectCopyingStateDefinition);

// 用于WaterSavingEquipsRecordsUpdating组件
const wbWaterSavingEquipsUpdateStatesDefinition = (set, get) => ({
    waterClassRadio: {},
    unitNameRadio:   {},
    equipNameRadio:  {},
    waterSavePRadio: {},
    setWaterClassRadio: (obj) => set({ waterClassRadio: obj }),
    setUnitNameRadio:   (obj) => set({ unitNameRadio:   obj }),
    setEquipNameRadio:  (obj) => set({ equipNameRadio:  obj }),
    setWaterSavePRadio: (obj) => set({ waterSavePRadio: obj }),
    resetStates: () => set({
        waterClassRadio: {},
        unitNameRadio:   {},
        equipNameRadio:  {},
        waterSavePRadio: {},
    }),
});
export const wbWaterSavingEquipsUpdateStates = createZustandState(wbWaterSavingEquipsUpdateStatesDefinition);

// 用于WaterUsingEquipsRecordsUpdating组件
const wbWaterUsingEquipsUpdateStatesDefinition = (set, get) => ({
    workStatusRadio: {},
    usingTypeRadio: {},
    setWorkStatusRadio: (obj) => set({ workStatusRadio: obj }),
    setUsingTypeRadio: (obj) => set({ usingTypeRadio: obj }),
    resetStates: () => set({
        workStatusRadio: {},
        usingTypeRadio: {},
    }),
});
export const wbWaterUsingEquipsUpdateStates = createZustandState(wbWaterUsingEquipsUpdateStatesDefinition);

// 用于WaterSourceRecordsUpdating组件
const wbWaterSourceUpdateStatesDefinition = (set, get) => ({
    waterUnitScale: {},
    waterTypeRadio: {},
    mainUseCheck:   [],
    turbidity:      "",
    setWaterUnitScale: (obj) => set({ waterUnitScale: obj }),
    setWaterTypeRadio: (obj) => set({ waterTypeRadio: obj }),
    setMainUseCheck:   (arr) => set({ mainUseCheck:   arr }),
    setTurbidity:      (str) => set({ turbidity:      str }),
    resetStates: () => set({
        waterUnitScale: {},
        waterTypeRadio: {},
        mainUseCheck:   [],
        turbidity:      "",
    }),
});
export const wbWaterSourceUpdateStates = createZustandState(wbWaterSourceUpdateStatesDefinition);

// 用于WaterUsageRecordsUpdating组件
const wbWaterUsageUpdateStatesDefinition = (set, get) => ({
    waterUnitScale: {},
    waterTypeRadio: {},
    setWaterUnitScale: (obj) => set({ waterUnitScale: obj }),
    setWaterTypeRadio: (obj) => set({ waterTypeRadio: obj }),
    resetStates: () => set({
        waterUnitScale: {},
        waterTypeRadio: {},
    }),
});
export const wbWaterUsageUpdateStates = createZustandState(wbWaterUsageUpdateStatesDefinition);

// 用于WaterUsageAnnualRecordsUpdating组件
const wbWaterUsageAnnualUpdateStatesDefinition = (set, get) => ({
    serviceTypesCheck: [],
    waterUnitScale:    {},
    //waterTypeRadio:  {},
    setServiceTypesCheck: (arr) => set({ serviceTypesCheck: arr }),
    setWaterUnitScale:   (obj) => set({ waterUnitScale: obj }),
    //setWaterTypeRadio: (obj) => set({ waterTypeRadio: obj }),
    resetStates: () => set({
        serviceTypesCheck: [],
        waterUnitScale:    {},
        //waterTypeRadio:  {},
    }),
});
export const wbWaterUsageAnnualUpdateStates = createZustandState(wbWaterUsageAnnualUpdateStatesDefinition);

// 表8  企业近三年生产情况统计表
const wbComProdStatStatesDefinition = (set, get) => ({
    year:           0,
    waterUnitScale: {},
    setYear:           (num) => set({ year: num }),
    setWaterUnitScale: (obj) => set({ waterUnitScale: obj }),
    resetStates: () => set({
        year: 0,
        waterUnitScale: {},
    }),
});
export const wbComProdStatStates = createZustandState(wbComProdStatStatesDefinition);

// 服务业表： 生产情况统计表
const wbComOpStatStatesDefinition = (set, get) => ({
    serviceTypeRadio: {},
    testDatePicker:   0,
    beginTimePicker:  0,
    //endTimePicker:  0,
    waterUnitScale:   {},
    setServiceTypeRadio: (obj) => set({ serviceTypeRadio: obj }),
    setTestDatePicker:   (num) => set({ testDatePicker:  num }),
    setBeginTimePicker:  (num) => set({ beginTimePicker: num }),
    //setEndTimePicker:  (num) => set({ endTimePicker:   num}),
    setWaterUnitScale:   (obj) => set({ waterUnitScale: obj }),
    resetStates: () => set({
        serviceTypeRadio: {},
        testDatePicker:   0,
        beginTimePicker:  0,
        //endTimePicker:  0,
        waterUnitScale:   {},
    }),
});
export const wbComOpStatStates = createZustandState(wbComOpStatStatesDefinition);

// 表9 用水单位水计量器具配备统计表
const wbWaterMeterEquipStatStatesDefinition = (set, get) => ({
    deviceType:    {},
    setDeviceType: (obj) => set({ deviceType: obj }),
    resetStates:   () => set({
        deviceType: {},
    }),
});
export const wbWaterMeterEquipStatStates = createZustandState(wbWaterMeterEquipStatStatesDefinition);

// 表10用水单位水计量器具配备情况
const wbComWaterMeterEquipStatesDefinition = (set, get) => ({
    waterUnitScale: {},
    waterTypeRadio:     {},
    accuracyLevelRadio: {},
    calibrateUnit:      {},
    equipStateRadio:    {},
    monitoredRadio:     {},
    setWaterUnitScale:     (obj) => set({ waterUnitScale:     obj }),
    setWaterTypeRadio:     (obj) => set({ waterTypeRadio:     obj }),
    setAccuracyLevelRadio: (obj) => set({ accuracyLevelRadio: obj }),
    setCalibrateUnit:      (obj) => set({ calibrateUnit:      obj }),
    setEquipStateRadio:    (obj) => set({ equipStateRadio:    obj }),
    setMonitoredRadio:     (obj) => set({ monitoredRadio:     obj }),
    resetStates: () => set({
        waterUnitScale:     {},
        waterTypeRadio:     {},
        accuracyLevelRadio: {},
        calibrateUnit:      {},
        equipStateRadio:    {},
        monitoredRadio:     {},
    }),
});
export const wbComWaterMeterEquipStates = createZustandState(wbComWaterMeterEquipStatesDefinition);

// 表11水表原始数据记录表
const wbWaterMeterSampleStateDefinition = (set, get) => ({
    waterUnitScale: {},
    pipeLevel:      {},
    startDatePicker: 0,
    datePicker1:     0,
    datePicker2:     0,
    datePicker3:     0,
    datePicker4:     0,
    datePicker5:     0,
    datePicker6:     0,
    datePicker7:     0,
    setWaterUnitScale:  (obj) => set({ waterUnitScale:  obj }),
    setPipeLevel:       (obj) => set({ pipeLevel:       obj }),
    setStartDatePicker: (val) => set({ startDatePicker: val }),
    setDatePicker1:     (val) => set({ datePicker1:     val }),
    setDatePicker2:     (val) => set({ datePicker2:     val }),
    setDatePicker3:     (val) => set({ datePicker3:     val }),
    setDatePicker4:     (val) => set({ datePicker4:     val }),
    setDatePicker5:     (val) => set({ datePicker5:     val }),
    setDatePicker6:     (val) => set({ datePicker6:     val }),
    setDatePicker7:     (val) => set({ datePicker7:     val }),
    resetStates: () => set({
        waterUnitScale:  {},
        pipeLevel:       {},
        startDatePicker: 0,
        datePicker1:     0,
        datePicker2:     0,
        datePicker3:     0,
        datePicker4:     0,
        datePicker5:     0,
        datePicker6:     0,
        datePicker7:     0,
    }),
});
export const wbWaterMeterSampleStates = createZustandState(wbWaterMeterSampleStateDefinition);


// 添加主要/次要/辅助/非生产用水共同数据, 用于批量创建多天记录
const wbWaterUsageUnitsRecordsAddingStatesDefinition = (set, get) => ({
    waterUnitScale:  {},
    startDatePicker: 0,
    endDatePicker:   0,
    equipRumtimeRadio: {},
    testMethodCheck:   [], // 根据反馈, 测试方法需要复选
    intakePurposeRadio: {},
    setWaterUnitScale:  (obj) => set({ waterUnitScale:  obj }),
    setStartDatePicker: (val) => set({ startDatePicker: val }),
    setEndDatePicker:   (val) => set({ endDatePicker:   val }),
    setEquipRumtimeRadio: (obj) => set({ equipRumtimeRadio: obj }),
    setTestMethodCheck:   (arr) => set({ testMethodCheck:  arr }),
    setIntakePurposeRadio: (obj) => set({ intakePurposeRadio: obj }),
    resetStates: () => set({
        waterUnitScale:  {},
        startDatePicker: 0,
        endDatePicker:   0,
        equipRumtimeRadio: {},
        testMethodCheck:   [],
        intakePurposeRadio: {},
    }),
});
export const wbWaterUsageUnitsRecordsAddingStates = createZustandState(wbWaterUsageUnitsRecordsAddingStatesDefinition);

// 主要/次要/辅助/非生产用水
const wbWaterUsageUnitsRecordingsStatesDefinition = (set, get) => ({
    waterUnitScale:  {},
    testDatePicker:  0,
    startDatePicker: 0,
    endDatePicker:   0,
    equipRumtimeRadio: {},
    //testMethodRadio:   {},
    testMethodCheck:   [], // 根据反馈, 测试方法需要复选
    intakePurposeRadio: {},
    setWaterUnitScale:  (obj) => set({ waterUnitScale:  obj }),
    setTestDatePicker:  (val) => set({ testDatePicker:  val }),
    setStartDatePicker: (val) => set({ startDatePicker: val }),
    setEndDatePicker:   (val) => set({ endDatePicker:   val }),
    setEquipRumtimeRadio: (obj) => set({ equipRumtimeRadio: obj }),
    //setTestMethodRadio:   (obj) => set({ testMethodRadio:  obj }),
    setTestMethodCheck:   (arr) => set({ testMethodCheck:  arr }),
    setIntakePurposeRadio: (obj) => set({ intakePurposeRadio: obj }),
    resetStates: () => set({
        waterUnitScale:  {},
        testDatePicker:  0,
        startDatePicker: 0,
        endDatePicker:   0,
        equipRumtimeRadio: {},
        //testMethodRadio:   {},
        testMethodCheck:   [],
        intakePurposeRadio: {},
    }),
});
export const wbWaterUsageUnitsRecordingsStates = createZustandState(wbWaterUsageUnitsRecordingsStatesDefinition);

// 用水单位水平衡测试总统计表
const wbWaterBalanceInspectStatsStatesDefinition = (set, get) => ({
    waterClassRadio:        {},
    convWaterSourceRadio:   {},
    unconvWaterSourceRadio: {},
    setWaterClassRadio:        (obj) => set({ waterClassRadio:        obj }),
    setConvWaterSourceRadio:   (obj) => set({ convWaterSourceRadio:   obj }),
    setUnconvWaterSourceRadio: (obj) => set({ unconvWaterSourceRadio: obj }),
    resetStates: () => set({
        waterClassRadio:        {},
        convWaterSourceRadio:   {},
        unconvWaterSourceRadio: {},
    }),
});
export const wbWaterBalanceInspectStatsStates = createZustandState(wbWaterBalanceInspectStatsStatesDefinition);

// 现状用水测试结果
const wbWaterUsageInspectResultsStatesDefinition = (set, get) => ({
    waterClassRadio: {},
    setWaterClassRadio: (obj) => set({ waterClassRadio: obj }),
    resetStates: () => set({
        waterClassRadio: {},
    }),
});
export const wbWaterUsageInspectResultsStates = createZustandState(wbWaterUsageInspectResultsStatesDefinition);

// 用水单位用水分析表
const wbWaterUsageInstAnalyzeStatesDefinition = (set, get) => ({
    waterClassRadio:    {},
    setWaterClassRadio: (obj) => set({ waterClassRadio:        obj }),
    resetStates: () => set({
        waterClassRadio: {},
    }),
});
export const wbWaterUsageInstAnalyzeStates = createZustandState(wbWaterUsageInstAnalyzeStatesDefinition);

// 客户资料清单
const wbClientInfoStatesDefinition = (set, get) => ({
    prodSystemRadio:       {},
    exptProdDatePicker:    0,
    setProdSystemRadio:    (obj) => set({ prodSystemRadio:    obj }),
    setExptProdDatePicker: (val) => set({ exptProdDatePicker: val }),
    resetStates: () => set({
        prodSystemRadio:    {},
        exptProdDatePicker: 0,
    }),
});
export const wbClientInfoStates = createZustandState(wbClientInfoStatesDefinition);

// 产品项目规模
const wbClientProductScaleStatesDefinition = (set, get) => ({
    unitScaleRadio:       {},
    setUnitScaleRadio:    (obj) => set({ unitScaleRadio:    obj }),
    resetStates: () => set({
        unitScaleRadio:    {},
    }),
});
export const wbClientProductScaleStates = createZustandState(wbClientProductScaleStatesDefinition);


// 用水定额参考表
const wbWaterQuotaInfoStatesDefinition = (set, get) => ({
    subindustryRadio:      {},
    hospGradeRadio:    {},
    instHasCanteenRadio:   {},
    instHasDormitoryRadio: {},
    instHasBathroomRadio:  {},
    commonBinaryRadio1: {},
    commonBinaryRadio2: {},
    commonBinaryRadio3: {},
    commonBinaryRadio4: {},
    commonBinaryRadio5: {},
    setSubindustryRadio:      (obj) => set({ subindustryRadio:      obj }),
    setHospGradeRadio:        (obj) => set({ hospGradeRadio:        obj }),
    setInstHasCanteenRadio:   (obj) => set({ instHasCanteenRadio:   obj }),
    setInstHasDormitoryRadio: (obj) => set({ instHasDormitoryRadio: obj }),
    setInstHasBathroomRadio:  (obj) => set({ instHasBathroomRadio:  obj }),
    setCommonBinaryRadio1:    (obj) => set({ commonBinaryRadio1:    obj }),
    setCommonBinaryRadio2:    (obj) => set({ commonBinaryRadio2:    obj }),
    setCommonBinaryRadio3:    (obj) => set({ commonBinaryRadio3:    obj }),
    setCommonBinaryRadio4:    (obj) => set({ commonBinaryRadio4:    obj }),
    setCommonBinaryRadio5:    (obj) => set({ commonBinaryRadio5:    obj }),

    resetStates: () => set({
        subindustryRadio:      {},
        hospGradeRadio:        {},
        instHasCanteenRadio:   {},
        instHasDormitoryRadio: {},
        instHasBathroomRadio:  {},
        commonBinaryRadio1:    {},
        commonBinaryRadio2:    {},
        commonBinaryRadio3:    {},
        commonBinaryRadio4:    {},
        commonBinaryRadio5:    {},
    }),
});
export const wbWaterQuotaInfoStates = createZustandState(wbWaterQuotaInfoStatesDefinition);


// 用水单位基本情况表
const wbBasicInfoStatesDefinition = (set, get) => ({
    intakeCoordsLocations:       [],
    setIntakeCoordsLocations:    (arr) => set({ intakeCoordsLocations:    arr }),
    resetStates: () => set({
        intakeCoordsLocations: [],
    }),
});
export const wbBasicInfoStatesDefinitionStates = createZustandState(wbBasicInfoStatesDefinition);






// zero carbon

// 基本情况表
const zcBasicInfoStatesDefinition = (set, get) => ({
    cityRadio:         {},
    industryTypeRadio: {},
    eQuotaTypeRadio:   {},
    startYearRadio:    {},
    durationRadio:     {},
    energyListCheck:   [],
    setCityRadio:         (obj) => set({ cityRadio:         obj }),
    setIndustryTypeRadio: (obj) => set({ industryTypeRadio: obj }),
    setEQuotaTypeRadio:   (obj) => set({ eQuotaTypeRadio:   obj }),
    setStartYearRadio:    (obj) => set({ startYearRadio:    obj }),
    setDurationRadio:     (obj) => set({ durationRadio:     obj }),
    setEnergyListCheck:   (arr) => set({ energyListCheck:  arr }),
    resetStates: () => set({
        cityRadio:         {},
        industryTypeRadio: {},
        eQuotaTypeRadio:   {},
        startYearRadio:    {},
        durationRadio:     {},
        energyListCheck:   [],
    }),
});
export const zcBasicInfoStates = createZustandState(zcBasicInfoStatesDefinition);

// 用能人数统计表
const zcEnergyComsumersStatesDefinition = (set, get) => ({
    yearRadio:    {},
    setYearRadio:    (obj) => set({ yearRadio:    obj }),
    resetStates: () => set({
        yearRadio:    {},
    }),
});
export const zcEnergyComsumersStates = createZustandState(zcEnergyComsumersStatesDefinition);

// 能源账单表
const zcEnergyBillsStatesDefinition = (set, get) => ({
    yearRadio:       {},
    energyTypeRadio: {},
    setYearRadio:       (obj) => set({ yearRadio:    obj }),
    setEnergyTypeRadio: (obj) => set({ energyTypeRadio: obj }),
    resetStates: () => set({
        yearRadio:       {},
        energyTypeRadio: {},
    }),
});
export const zcEnergyBillsStates = createZustandState(zcEnergyBillsStatesDefinition);

// 采暖费用信息
const zcHeatingBillsStatesDefinition = (set, get) => ({
    yearRadio:       {},
    setYearRadio:       (obj) => set({ yearRadio:    obj }),
    resetStates: () => set({
        yearRadio:       {},
    }),
});
export const zcHeatingBillsStates = createZustandState(zcHeatingBillsStatesDefinition);


// 用能系统基本情况
const zcEnergySystemInfoStatesDefinition = (set, get) => ({
    hsTypeRadio: {},
    hsNameRadio: {},
    hsHeatStationRadio: {},
    hwsUseFormRadio: {},
    liftFreqConvUsedRadio: {},
    liftTractionRadio: {},
    liftHydraulicRadio: {},
    psdsClassRadio: {},
    wusWaterTypeRadio: {},
    wusBathroomFixtureRadio: {},
    wusDrinkWaterRadio: {},
    wusShowerFixtureRadio: {},
    wusUrinalRadio: {},
    wusSquatToiletRadio: {},
    wusPressPumpUsedRadio: {},
    wusRoadPermeableRadio: {},
    wusGreenWaterTypeRadio: {},
    wusWasteReuseRadio: {},
    wusReuseRctRadio: {},
    acsTypeRadio: {},
    acsNameRadio: {},
    skEnergySaveRadio: {},
    lsIndoorRadio: {},
    lsSwitchTypeRadio: {},
    lsOutdoorRadio: {},
    beWallInsulationRadio: {},
    beWindowRadio: {},
    beRootInsulationRadio: {},
    vtsOfficeVehicleRadio: {},
    msWmeterFullInstallRadio: {},
    msEmeterFullInstallRadio: {},
    ecpConstrStatusRadio: {},
    ecpOpStatusRadio: {},

    setHsTypeRadio:    (obj) => set({ hsTypeRadio: obj }),
    setHsNameRadio:    (obj) => set({ hsNameRadio: obj }),
    setHsHeatStationRadio:    (obj) => set({ hsHeatStationRadio: obj }),
    setHwsUseFormRadio:    (obj) => set({ hwsUseFormRadio: obj }),
    setLiftFreqConvUsedRadio:    (obj) => set({ liftFreqConvUsedRadio: obj }),
    setLiftTractionRadio:    (obj) => set({ liftTractionRadio: obj }),
    setLiftHydraulicRadio:    (obj) => set({ liftHydraulicRadio: obj }),
    setPsdsClassRadio:    (obj) => set({ psdsClassRadio: obj }),
    setWusWaterTypeRadio:    (obj) => set({ wusWaterTypeRadio: obj }),
    setWusBathroomFixtureRadio:    (obj) => set({ wusBathroomFixtureRadio: obj }),
    setWusDrinkWaterRadio:    (obj) => set({ wusDrinkWaterRadio: obj }),
    setWusShowerFixtureRadio:    (obj) => set({ wusShowerFixtureRadio: obj }),
    setWusUrinalRadio:    (obj) => set({ wusUrinalRadio: obj }),
    setWusSquatToiletRadio:    (obj) => set({ wusSquatToiletRadio: obj }),
    setWusPressPumpUsedRadio:    (obj) => set({ wusPressPumpUsedRadio: obj }),
    setWusRoadPermeableRadio:    (obj) => set({ wusRoadPermeableRadio: obj }),
    setWusGreenWaterTypeRadio:    (obj) => set({ wusGreenWaterTypeRadio: obj }),
    setWusWasteReuseRadio:    (obj) => set({ wusWasteReuseRadio: obj }),
    setWusReuseRctRadio:    (obj) => set({ wusReuseRctRadio: obj }),
    setAcsTypeRadio:    (obj) => set({ acsTypeRadio: obj }),
    setAcsNameRadio:    (obj) => set({ acsNameRadio: obj }),
    setSkEnergySaveRadio:    (obj) => set({ skEnergySaveRadio: obj }),
    setLsIndoorRadio:    (obj) => set({ lsIndoorRadio: obj }),
    setLsSwitchTypeRadio:    (obj) => set({ lsSwitchTypeRadio: obj }),
    setLsOutdoorRadio:    (obj) => set({ lsOutdoorRadio: obj }),
    setBeWallInsulationRadio:    (obj) => set({ beWallInsulationRadio: obj }),
    setBeWindowRadio:    (obj) => set({ beWindowRadio: obj }),
    setBeRootInsulationRadio:    (obj) => set({ beRootInsulationRadio: obj }),
    setVtsOfficeVehicleRadio:    (obj) => set({ vtsOfficeVehicleRadio: obj }),
    setMsWmeterFullInstallRadio:    (obj) => set({ msWmeterFullInstallRadio: obj }),
    setMsEmeterFullInstallRadio:    (obj) => set({ msEmeterFullInstallRadio: obj }),
    setEcpConstrStatusRadio:    (obj) => set({ ecpConstrStatusRadio: obj }),
    setEcpOpStatusRadio:    (obj) => set({ ecpOpStatusRadio: obj }),

    resetStates: () => set({
        hsTypeRadio: {},
        hsNameRadio: {},
        hsHeatStationRadio: {},
        hwsUseFormRadio: {},
        liftFreqConvUsedRadio: {},
        liftTractionRadio: {},
        liftHydraulicRadio: {},
        psdsClassRadio: {},
        wusWaterTypeRadio: {},
        wusBathroomFixtureRadio: {},
        wusDrinkWaterRadio: {},
        wusShowerFixtureRadio: {},
        wusUrinalRadio: {},
        wusSquatToiletRadio: {},
        wusPressPumpUsedRadio: {},
        wusRoadPermeableRadio: {},
        wusGreenWaterTypeRadio: {},
        wusWasteReuseRadio: {},
        wusReuseRctRadio: {},
        acsTypeRadio: {},
        acsNameRadio: {},
        skEnergySaveRadio: {},
        lsIndoorRadio: {},
        lsSwitchTypeRadio: {},
        lsOutdoorRadio: {},
        beWallInsulationRadio: {},
        beWindowRadio: {},
        beRootInsulationRadio: {},
        vtsOfficeVehicleRadio: {},
        msWmeterFullInstallRadio: {},
        msEmeterFullInstallRadio: {},
        ecpConstrStatusRadio: {},
        ecpOpStatusRadio: {},
    }),
});
export const zcEnergySystemInfoStates = createZustandState(zcEnergySystemInfoStatesDefinition);
