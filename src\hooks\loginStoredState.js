import { createZustandStoredState } from "../services/state-management";
import { STORE_LOGIN_INFO as storeKey } from "../config/keysConfig";


/*
import { create } from "zustand";
const userStoredState = create((set, get) => ({
    name: { value: "", error: "" },
    account: { value: "", error: "" },
    password: { value: "", error: "" },
    setName: (name, errMsg) => set({ name: { value: name, error: errMsg } }),
    setAccount: (acc, errMsg) => set({ account: { value: acc, error: errMsg } }),
    setPassword: (pwd, errMsg) => set({ password: { value: pwd, error: errMsg } }),
    clearUserState: () => set({
        name: { value: "", error: "" },
        account: { value: "", error: "" },
        password: { value: "", error: "" },
    }),
}));
*/

/* not store password anymore
const StatDefinition = (set, get) => ({
    name:        { value: "", error: "" },
    account:     { value: "", error: "" },
    password:    { value: "", error: "" },
    setName:     (name, errMsg)  => set({ name:     { value: name,  error: errMsg }}),
    setAccount:  (acc,  errMsg)  => set({ account:  { value: acc,   error: errMsg }}),
    setPassword: (pwd,  errMsg)  => set({ password: { value: pwd,   error: errMsg }}),
});
*/

const statDefinition = (set, get) => ({
    token: "",
    pubid: "",
    name: "",
    mobile: "",
    roles: [],
    permits: [],
    department: 0,
    position: 0,
    userType: 0,
    latestVersion: "",
    setToken:         (newToken)   => set({ token: newToken }),
    setPubid:         (newPubid)   => set({ pubid: newPubid }),
    setName:          (newName)    => set({ name: newName }),
    setMobile:        (newMobile)  => set({ mobile: newMobile}),
    setRoles:         (arr)        => set({ roles: arr }),
    setPermits:       (arr)        => set({ permits: arr}),
    setDepartment:    (deptId)     => set({ department: deptId }),
    setPosition:      (positionId) => set({ position: positionId }),
    setUserType:      (userType)   => set({ userType: userType }),
    setLatestVersion: (version)    => set({ latestVersion: version }),
    resetStates: () => set({
        token: "",
        pubid: "",
        name: "",
        mobile: "",
        roles: [],
        permits: [],
        department: 0,
        position: 0,
        userType: 0,
        latestVersion: "",
    }),
});

//import { createZustandState } from "./zustand";
//const userStoredState = createZustandState(userStatDefinition);

const userLoginState = createZustandStoredState(storeKey, statDefinition);

export { userLoginState };
