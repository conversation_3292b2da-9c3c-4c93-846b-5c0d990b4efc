import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import * as cn from "./cn";
//import * as en from "./en";

const ns = Object.keys(cn);
export const defaultNS = ns[0];
void i18n.use(initReactI18next).init({
    ns,
    defaultNS,
    resources: {
        cn,
        //en,
    },
    lng: "cn",
    fallbackLng: "cn",
    interpolation: {
        escapeValue: false, // not needed for react as it escapes by default
    },
    compatibilityJSON: "v3",
});
export default i18n;
