import dayjs from "dayjs";

// https://day.js.org/docs/en/display/format

/**
 * Milliseconds between 1900-01-01 and 1970-01-01.
 * Server defined in `soft-time::+unix-epoch+`
 */
const UNIX_EPOCH = 2208988800000;

/**
 * Milliseconds in a day.
 */
export const MS_A_DAY = 1000 * 24 * 3600;

export const getCurrentTime = (fmt="YYYY-MM-DD HH:mm:ss") => dayjs(new Date()).format(fmt);

/**
 * 将给定的时间转换为指定格式的字符串
 *
 * @param {string|number|Date} timeDesignator - 表示时间的字符串、数字或Date对象，用于指定要格式化的时间, 如果是数字, 必需是1970年起的毫秒数.
 * @param {string} fmt - 可选参数，表示时间格式的字符串，默认为"YYYY-MM-DD HH:mm:ss"，用于指定输出的时间字符串格式
 * @returns {string} 格式化后的时间字符串
 */
export const toTimeString = (timeDesignator, fmt="YYYY-MM-DD HH:mm:ss") => dayjs(timeDesignator).format(fmt);

/**
 * Get current universal time since 1900-01-01 in milliseconds
 * @param {integer | undefined} msSince1970
 * @return {integer}
 */
export const getUniversalTime = (msSince1970) => {
    return UNIX_EPOCH + (msSince1970 ? msSince1970 : new Date().getTime());
};

/**
 * Milliseconds since 1900-01-01 to milliseconds since 1970-01-01
 * @param {integer} msSince1900
 * @returns integer
 */
export const UTC1900To1970 = (msSince1900) => msSince1900 - UNIX_EPOCH;

/**
 * Milliseconds since 1970-01-01 to milliseconds since 1900-01-01
 * @param {integer} msSince1970
 * @returns integer
 */
export const UTC1970To1900 = (msSince1970) => msSince1970 + UNIX_EPOCH;

export const utc1900Format = (utc1900, fmt="YYYY-MM-DD HH:mm:ss") => toTimeString(UTC1900To1970(utc1900), fmt);
export const utc1970Format = (utc1970, fmt="YYYY-MM-DD HH:mm:ss") => toTimeString(utc1970, fmt);

/**
 * Get current year.
 */
export const thisYear = () => new Date().getFullYear();

