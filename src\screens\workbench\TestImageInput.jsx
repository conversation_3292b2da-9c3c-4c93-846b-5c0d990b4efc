import React, { useEffect, useMemo, useRef, } from "react";
import { useTheme } from "react-native-paper";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { StyleSheet, View } from "react-native";
import ScreenWrapper from "../ScreenWrapper";
//import { httpClient } from "../../services/http";
import ControlledImageInput from "../../components/ControlledImageInput";
import <PERSON><PERSON> from "joi";
import { validatorBase } from "../../utils/validatorBase";
import { useForm } from "react-hook-form";
import { joiResolver } from "@hookform/resolvers/joi";
import { creatMMKVStore, makeStoredValueToFormValue } from "../../services/local-storage";
import { DEFAULT_IMAGE_URI, DEFAULT_IMAGES_PICK } from "../../config";


/**
 * 测试获取图像并上传到服务器.
 * 关于图像获取, 压缩, 传输: https://dev.to/ajmal_hasan/react-native-fileimage-picker-1o2j
 * Expo图像获取基本用法: https://docs.expo.dev/versions/latest/sdk/imagepicker/#usage
 * 关于权限: https://github.com/sanchit0496/react_native_scaffolding/blob/main/screens/ImagePickerScreen.js
 * @param {Object} args
 * @returns
 */
const TestImageInput = ({ navigation }) => {
    const { bottom, } = useSafeAreaInsets();
    const theme = useTheme();
    const height = theme.isV3 ? 80 : 56;

    const pickNum = DEFAULT_IMAGES_PICK;

    //const [imageUri, setImageUri] = useState([]);
    //const imagePickResult = useRef(null);

    const STORE_KEY = "TestImageInputScreen";
    const { setStore, getStore, /*clearStore, setStoreObject*/ } =  useMemo(() => {
        return creatMMKVStore(STORE_KEY);
    }, [STORE_KEY]);

    const storedValueToFormValue = useRef(makeStoredValueToFormValue(getStore));

    const schema = Joi.object({
        name:        validatorBase.waterBalance.commons.textField,
        pickedImage: Joi.array().items(Joi.string()).min(1).max(pickNum).messages({"*": "请选择图像"}),
    });

    const {
        //handleSubmit,
        control,
        resetField,
        reset,
        formState: { errors },
        //setValue,
        //getValues,
    } = useForm({
        resolver: joiResolver(schema),
        //reValidateMode: "onBlur", // https://github.com/orgs/react-hook-form/discussions/10327
        defaultValues: { // 默认值在组件载入时会被更新, 这里的初始值正常情况下没有用处
            // 注意, 默认为0的是允许默认为0的值, 或者通过计算得到的值
            name:     "",
            pickedImage: [], // 表示一个路径或者url的字符串数组
        },
    });

    useEffect(() => {
        const restoreLocalData = () => {
            const formObject = { // 默认值用户表单的数据显示
                name:        storedValueToFormValue.current("name"),
                pickedImage: storedValueToFormValue.current("pickedImage"),
            };
            reset(formObject); // 重置react-form数据
        };

        restoreLocalData();
    }, []);

    return (
        <View style={{ flex: 1 }}>

            <ScreenWrapper contentContainerStyle={styles.container} style={{marginBottom: height+ bottom}}>
                <View style={styles.formEntry}>

                    <ControlledImageInput
                        name="pickedImage"
                        rowLabel="图片拾取"
                        toolTip={"点击上传"}
                        control={ control }
                        placeholder={DEFAULT_IMAGE_URI}
                        onPickImageCB={(path) => {
                            const pickedImages = getStore("pickedImage");
                            if(pickedImages.length < pickNum){
                                setStore("pickedImage", [...getStore("pickedImage"), path]);
                            } else {
                                console.warn(`最多只能选择${pickNum}张图片`);
                            }
                        }}
                        onDeleteImageCB={() => {
                            setStore("pickedImage", []); // reset  mmkv
                            resetField("pickedImage", { defaultValue: [] }); // reset hook form
                        }}
                        pickNum={pickNum}
                        //width={null}
                        //height={150}
                        crop={false}
                        compress={1}
                        editable={true}
                        formError={errors}
                        required={false}
                        multiline={true}
                    />
                </View>

            </ScreenWrapper>


        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        //flex: 1,
        //alignItems: "center",
        //justifyContent: "center",
    },
    formEntry: {
        // borderWidth: 1,
        margin: 8,
    },
    Button: {
        marginTop: 20,
        marginBottom: 20,
    },
    image: {
        width: 200,
        height: 150,
        marginTop: 20,
        marginBottom: 20,
    },
});

export default TestImageInput;
