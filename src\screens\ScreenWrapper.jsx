import * as React from "react";
import { ScrollView, StyleSheet, View } from "react-native";

import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useTheme } from "react-native-paper";


const useExampleTheme = () => useTheme();
/*
type Props = ScrollViewProps & {
  children: React.ReactNode;
  withScrollView?: boolean;
  style?: StyleProp<ViewStyle>;
  contentContainerStyle?: StyleProp<ViewStyle>;
};
*/

const ScreenWrapper = ({ children, withScrollView = true, style, contentContainerStyle, ...rest }) => {
    const theme = useExampleTheme();

    const insets = useSafeAreaInsets();

    const containerStyle = [
        styles.container,
        {
            backgroundColor: theme.colors.background,
            paddingBottom: insets.bottom,
            paddingLeft: insets.left,
            paddingRight: insets.left,
        },
    ];

    return (
        <>
            {withScrollView ? (
                <ScrollView
                    {...rest}
                    contentContainerStyle={contentContainerStyle}
                    keyboardShouldPersistTaps="always"
                    alwaysBounceVertical={false}
                    showsVerticalScrollIndicator={false}
                    style={[containerStyle, style]}
                >
                    {children}
                </ScrollView>
            ) : (
                <View style={[containerStyle, style]}>{children}</View>
            )}
        </>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
});

export default ScreenWrapper;
