import { joi<PERSON>esolver } from "@hookform/resolvers/joi";
import "fast-text-encoding";
import <PERSON><PERSON> from "joi";
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { Alert, StyleSheet, View } from "react-native";
import { Snackbar } from "react-native-paper";
import Button from "../../../components/Button";
import { DialogToConfirm } from "../../../components/DialogToConfirm";
import { FormListNodesMapper } from "../../../components/FormListNodesMapper";
import HeaderBar from "../../../components/HeaderBar";
import { LoadingIndicator } from "../../../components/LoadingIndicator";
import { creatMMKVStore, makeStoredValueToFormValue } from "../../../services/local-storage";
import { callOneByOne, ifTruthLet, isNullness, makeDataFeeder } from "../../../utils";
import { checkPermits } from "../../../utils/permits";
import { checkIdsToObject, isServerDataNewer, makeGetClientCversion, radioIdToObject } from "../../../utils/records";
import { validatorBase } from "../../../utils/validatorBase";
import ScreenWrapper from "../../ScreenWrapper";


// 新组件需要重新!!
import { useShallow } from "zustand/shallow";
import {
    makeReqDelZeroCarbonRecords as recordDeleteQueryMaker,
    makeReqGetZeroCarbonRecords as recordSelectQueryMaker,
    makeReqUpdZeroCarbonRecords as recordUpdateQueryMaker,
} from "../../../api/projectRecordsQueries";
import { UPD_ZERO_CARBON_RECORDS as pageMainKey } from "../../../config/keysConfig";
import { durationEnum, energyListEnum, eQuotaTypeEnum, hebeiRegionEnum, industryTypeEnum, startYearEnum } from "../../../config/zeroCarbon";
import { zcBasicInfoStates as selectorStates } from "../../../hooks/selectorStates";
import { onPreSubmitError } from "../../../utils/screens";


const dataFeeder = makeDataFeeder();

/**
 * 单位基本情况表
 * @param {object} args
 * @param {object} args.navigation
 * @param {object} args.route
 * @returns
 */
const RecordsUpdating = ({ navigation, route }) => {
    console.log("RecordsUpdating pageMeta from nav:", route.params.pageMeta);
    console.log("RecordsUpdating projMeta from nav:", route.params.projMeta);

    const { title: screenTitle, } = route.params.projMeta;
    //const projClass    = route.params.projMeta.class;     // 项目class, 1水平衡, 11零碳诊断
    //const industryType = route.params.projMeta.industry;  // 行业类型, 1工业, 2服务业
    //const projIndustry = useRef(parseIndustryCode(industryType, projClass)); // "publicInst"

    // formType对应着服务端的form-type, 为0时表示表单是用户填写, 为1表示服务端生成, 为1时只读
    const { queryKwd, id: recordPubid, name: recordName, formType } = route.params.pageMeta; // recordPubid is the alias of id
    const saveButtonIcon = "content-save-all-outline";
    const formEditableGlobal = checkPermits() ? (formType === 0 ? true : false) : false;

    // 使用memoize来优化
    const { setStore, getStore, clearStore, setStoreObject } = useMemo(() => {
        console.log("creatMMKVStore memo:", pageMainKey.store, queryKwd, recordPubid);
        return creatMMKVStore(`${pageMainKey.store}/${queryKwd}/${recordPubid}`);
    }, [queryKwd, recordPubid]);

    const storedValueToFormValue = useRef(makeStoredValueToFormValue(getStore));
    const getClientCversion = useRef(makeGetClientCversion(() => getStore("mainCversion"), () => getStore("subCversion")));

    // 客户端cversion = `${mainCversion}.${subCversion}`
    // 这两行没出重复渲染都要获取, 不放到useEffect中
    (getStore("mainCversion") === undefined) && setStore("mainCversion", 0); // 客户端主版本号
    (getStore("subCversion")  === undefined) && setStore("subCversion",  0); // 客户端次版本号, 修改一次自增一次
    //let mainCversion = getStore("mainCversion"); // 主版本号, 与服务端cversion对应
    // 次版本号, 表示本地更改次数, 用于程序上对子版本号的增加, 用于判断与服务端版本的差异, 同时可用于判断避免重复从MMKV获取数据
    const subCversionRef = useRef(getStore("subCversion"));

    // 新组件需要修改!!
    const bottomBarLabels = { ok: "提交" };                    // 底部按钮显示文字
    const [snackBarMessage, setSnackBarMessage] = useState("更新表单遇到错误"); // 下方错误提示文字内容
    const [confimDialogConfig, setConfirmDialogConfig] = useState({});       // 对话框信息, {title, text, okLabel, onOK}, 但显示对话框的变量在showConfirm
    const [okButtonLoading,  setOkButtonLoading]  = useState(false);            // 右下方按钮图标是否旋转
    const [okButtonDisabled, setOkButtonDisabled] = useState(!checkPermits());  // 右下方按钮是否禁用
    const [showSnackbar,     setShowSnackbar]     = useState(false);            // 下方是否显示错误通知
    const [showConfirm,      setShowConfirm]      = useState(false);            // 是否显示确认对话框
    const [loadingIndicatorVisible, setLoadingIndicatorVisible] = useState(true); // 是否显示loading, 还负责是否渲染ScreenWrapper组件, 为false时渲染.
    const delQueryInfo = useRef({ id: recordPubid, name: recordName });         // 用于删除本记录的查询变量

    const [
        cityRadioState,
        industryTypeRadioState,
        eQuotaTypeRadioState,
        startYearRadioState,
        durationRadioState,
        energyListCheckState,
        setCityRadioState,
        setIndustryTypeRadioState,
        setEQuotaTypeRadioState,
        setStartYearRadioState,
        setDurationRadioState,
        setEnergyListCheckState,
        resetSelectorStates,
    ] = selectorStates(useShallow(state => [
        state.cityRadio,
        state.industryTypeRadio,
        state.eQuotaTypeRadio,
        state.startYearRadio,
        state.durationRadio,
        state.energyListCheck,
        state.setCityRadio,
        state.setIndustryTypeRadio,
        state.setEQuotaTypeRadio,
        state.setStartYearRadio,
        state.setDurationRadio,
        state.setEnergyListCheck,
        state.resetStates,
    ])); // radio组件状态

    const defaultProvince = useRef(13); // 河北
    const cityRadioStateDataProviderRef         = useRef(hebeiRegionEnum);
    const industryTypeRadioStateDataProviderRef = useRef(industryTypeEnum);
    const eQuotaTypeRadioStateDataProviderRef   = useRef(eQuotaTypeEnum);
    const startYearRadioStateDataProviderRef    = useRef(startYearEnum);
    const durationRadioStateDataProviderRef     = useRef(durationEnum);
    const energyListCheckStateDataProviderRef   = useRef(energyListEnum);


    // Define form
    // 新组件需要重新定义!!
    const schema = Joi.object({
        clientName:   validatorBase.zeroCarbon.commons.name,
        property:     validatorBase.zeroCarbon.commons.textField,
        address:      validatorBase.zeroCarbon.commons.textField,
        busNum:       validatorBase.zeroCarbon.commons.intField,
        residentNum:  validatorBase.zeroCarbon.commons.intField,
        headcount:    validatorBase.zeroCarbon.commons.intField,
        esContact:    validatorBase.zeroCarbon.commons.name,
        esPhone:      validatorBase.zeroCarbon.commons.textField,
        landArea:     validatorBase.zeroCarbon.commons.floatField,
        buildArea:    validatorBase.zeroCarbon.commons.floatField,
        heatingArea:  validatorBase.zeroCarbon.commons.floatField,
        greenArea:    validatorBase.zeroCarbon.commons.floatField,
        buildUseTime: validatorBase.zeroCarbon.commons.textField,
        //province:     validatorBase.zeroCarbon.commons.intField,
        city:         validatorBase.zeroCarbon.commons.intField,
        industryType: validatorBase.zeroCarbon.commons.intField,
        eQuotaType:   validatorBase.zeroCarbon.commons.intField,
        startYear:    validatorBase.zeroCarbon.commons.yearField,
        duration:     validatorBase.zeroCarbon.basicInfo.duration,
        energyList:   validatorBase.zeroCarbon.commons.simpleArray,
        others:       validatorBase.waterBalance.waterUsageUnits.textField,
        remarks:      validatorBase.waterBalance.waterUsageUnits.longTextField,
    });

    const defaultFormValues = useMemo(() => ({
        // 注意, 默认为0的是允许默认为0的值, 或者通过计算得到的值
        clientName:   "",
        property:     "",
        address:      "",
        busNum:       "",
        residentNum:  "",
        headcount:    "",
        esContact:    "",
        esPhone:      "",
        landArea:     "",
        buildArea:    "",
        heatingArea:  "",
        greenArea:    "",
        buildUseTime: "",
        //province:     "",
        city:         "",
        industryType: "",
        eQuotaType:   "",
        startYear:    "",
        duration:     "",
        energyList:   "",
        others:       "",
        remarks:      "",
    }), []);

    // 新组件需要重新定义defaultValues!!
    const {
        handleSubmit,
        control,
        resetField,
        reset,
        formState: { errors },
        //setValue,
        //getValues,
    } = useForm({
        resolver: joiResolver(schema),
        //reValidateMode: "onBlur", // https://github.com/orgs/react-hook-form/discussions/10327
        // 默认值在组件载入时会被更新, 这里的初始值正常情况下没有用处
        defaultValues: defaultFormValues
    });

    // Query: select record
    const onRecordSelectSuccess = useCallback((data) => { // data: {"id": 3, "leader": 0, "name": "DEPT-03", "superior": 0}
        // 注意, 进入onSuccess之前就意味着服务端版本比客户端版本新, 因此要更新客户端主版本号
        console.log("onRecordSelectSuccess:", data);
        if (data.STATUS === 0) {
            setStore("mainCversion", data.DATA.cversion);
            setStore("subCversion", 0);

            const formObject = {
                clientName:   String(data.DATA.clientName  ),
                property:     String(data.DATA.property    ),
                address:      String(data.DATA.address     ),
                busNum:       String(data.DATA.busNum      ),
                residentNum:  String(data.DATA.residentNum ),
                headcount:    String(data.DATA.headcount   ),
                esContact:    String(data.DATA.esContact   ),
                esPhone:      String(data.DATA.esPhone     ),
                landArea:     String(data.DATA.landArea    ),
                buildArea:    String(data.DATA.buildArea   ),
                heatingArea:  String(data.DATA.heatingArea ),
                greenArea:    String(data.DATA.greenArea   ),
                buildUseTime: String(data.DATA.buildUseTime),
                //province:     String(defaultProvince.current), // 暂时固定使用默认值
                city:         String(data.DATA.city        ),
                industryType: String(data.DATA.industryType),
                eQuotaType:   String(data.DATA.eQuotaType  ),
                startYear:    String(data.DATA.startYear   ),
                duration:     String(data.DATA.duration    ),
                energyList:   data.DATA.energyList,
                others:       String(data.DATA.others),
                remarks:      String(data.DATA.remarks),
            };
            reset(formObject);           // 重置react-form

            const storeObjects = checkPermits() && (formType === 0) && {
                ...formObject,
                city:         radioIdToObject(cityRadioStateDataProviderRef.current,         data.DATA.city),
                industryType: radioIdToObject(industryTypeRadioStateDataProviderRef.current, data.DATA.industryType),
                eQuotaType:   radioIdToObject(eQuotaTypeRadioStateDataProviderRef.current,   data.DATA.eQuotaType),
                startYear:    radioIdToObject(startYearRadioStateDataProviderRef.current,    data.DATA.startYear),
                duration:     radioIdToObject(durationRadioStateDataProviderRef.current,     data.DATA.duration),
                energyList:   checkIdsToObject(energyListCheckStateDataProviderRef.current,  data.DATA.energyList),
            };
            storeObjects && setStoreObject(storeObjects, false); // 判断用户权限, 把服务端数据写到mmkv存储, 只有可写用户才需要本地存储, 不会首先清空老数据

            // 设置selector菜单状态
            setCityRadioState(storeObjects.city);
            setIndustryTypeRadioState(storeObjects.industryType);
            setEQuotaTypeRadioState(storeObjects.eQuotaType);
            setStartYearRadioState(storeObjects.startYear);
            setDurationRadioState(storeObjects.duration);
            setEnergyListCheckState(storeObjects.energyList);
        } else {
            console.log("STATUS !== 0", data.STATUS);
            setSnackBarMessage(`表单数据拉取发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        }
    }, [setStore, setStoreObject, reset, setCityRadioState, setIndustryTypeRadioState, setEQuotaTypeRadioState, cityRadioStateDataProviderRef, industryTypeRadioStateDataProviderRef, eQuotaTypeRadioStateDataProviderRef, formType]);

    const onRecordSelectError = useCallback((err) => {
        console.log("Record selecting error:", err);
    }, []);

    const onRecordSelectSettled = useCallback((data, err) => {
        //console.log("Record selecting settled...............:", data, err);
        const loadingDuration = 100;
        const currentTime = Date.now();
        const mutationTime = data?.mutationTime || currentTime;

        //console.log("record selecting time left: ", currentTime - data.mutationTime);
        if (currentTime - mutationTime < loadingDuration) {
            setTimeout(() => {
                loadingIndicatorVisible && setLoadingIndicatorVisible(false);
            }, currentTime - data.mutationTime);
        } else {
            loadingIndicatorVisible && setLoadingIndicatorVisible(false);
        }
    }, [loadingIndicatorVisible]); // 必需要依赖, 不然不会关闭loading

    const getPath = formType === 0 ? "get" : "getsum";
    const recordSelectQuery = recordSelectQueryMaker([queryKwd, getPath, recordPubid], onRecordSelectSuccess, onRecordSelectError, onRecordSelectSettled);

    // Query: update record
    const onHandleSubmit = useCallback((data) => {
        setOkButtonDisabled(true);
        setOkButtonLoading(true);
        dataFeeder({ ...data,
            cversion: getClientCversion.current(),
            name:       data.clientName,
            province:   defaultProvince.current,
            energyList: JSON.stringify(data.energyList), // 对象类型和复合数组必需转换为字符串, 否则发送消息时客户端会报错: Network request failed!
        }); // append client cversion
        recordUpdateQuery.mutate();
    }, [getClientCversion, recordUpdateQuery]);

    // 新组件不需改动
    const onRecordUpdateSuccess = useCallback((data) => {
        console.log(" Record update success response data: ", data);
        if(data.STATUS !== 0) {
            setSnackBarMessage(`表单更新发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        } else {
            // 设置确认框数据
            setConfirmDialogConfig({
                title: "更新表单",
                text: "表单信息更新成功!",
                okLabel: "确定",
                onOK: () => {
                    setShowConfirm(false);
                    recordSelectQuery.mutate();
                }});
            setShowConfirm(true);
        }
    }, [recordSelectQuery]);

    const onRecordUpdateError = useCallback((error) => {
        setSnackBarMessage(`表单更新发生错误, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    }, []);

    const onRecordUpdateSettled = useCallback((data, error) => {
        setOkButtonDisabled(false);
        setOkButtonLoading(false);
    }, []);

    const recordUpdateQuery = recordUpdateQueryMaker(dataFeeder, [queryKwd, "upd", recordPubid], onRecordUpdateSuccess, onRecordUpdateError, onRecordUpdateSettled);

    // Query: delete record
    const recordDeleteOnSuccess = useCallback(() => {
        clearStore();
        resetSelectorStates();
        navigation.goBack();
    }, [clearStore, resetSelectorStates, navigation]);
    const recordDeleteOnError = useCallback((error) => {
        setSnackBarMessage(`表单删除失败, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    }, []);
    const recordDeleteQuery = recordDeleteQueryMaker([queryKwd, "del", recordPubid], recordDeleteOnSuccess, recordDeleteOnError);

    const deleteAlert = useCallback(() => {
        return (
            Alert.alert("删除表单", `删除后不可撤销, 请确认是否删除: ${delQueryInfo.current?.name}?`, [
                { text: "取消" },
                { text: "确定", onPress: () => recordDeleteQuery.mutate() },
            ])
        );
    }, [recordDeleteQuery]);

    const clearCache = useCallback(() => {
        return (
            Alert.alert("清空本地缓存", "1. 用于未知原因导致数据无法提交的情况\r\n2. 未提交的内容将会丢失, 请做好数据截图备份\r\n3. 已提交到服务端的数据不受影响\r\n4. 确认清空请点击“确认”按钮", [
                { text: "取消" },
                { text: "确定", onPress: () => {
                    clearStore();
                    resetSelectorStates();
                    navigation.goBack();
                }},
            ])
        );
    }, [clearStore, resetSelectorStates, navigation]);

    // Hook: onMount fetch data
    useEffect(() => {
        // 优化方案: 根据用户权限进行mutate, 只有可写用户才需要本地存储
        const localCversion = getClientCversion.current();
        const remoteCversion = route.params.pageMeta.cversion;
        console.log("localCversion:", localCversion, "remoteSversion:", remoteCversion, "subCversionRef:", subCversionRef.current);

        const restoreLocalData = () => {
            const formObject = { // 默认值用户表单的数据显示
                clientName:   storedValueToFormValue.current("clientName"),
                property:     storedValueToFormValue.current("property"),
                address:      storedValueToFormValue.current("address"),
                busNum:       storedValueToFormValue.current("busNum"),
                residentNum:  storedValueToFormValue.current("residentNum"),
                headcount:    storedValueToFormValue.current("headcount"),
                esContact:    storedValueToFormValue.current("esContact"),
                esPhone:      storedValueToFormValue.current("esPhone"),
                landArea:     storedValueToFormValue.current("landArea"),
                buildArea:    storedValueToFormValue.current("buildArea"),
                heatingArea:  storedValueToFormValue.current("heatingArea"),
                greenArea:    storedValueToFormValue.current("greenArea"),
                buildUseTime: storedValueToFormValue.current("buildUseTime"),
                //province:     storedValueToFormValue.current("province"),
                city:         storedValueToFormValue.current("city"),
                industryType: storedValueToFormValue.current("industryType"),
                eQuotaType:   storedValueToFormValue.current("eQuotaType"),
                startYear:    storedValueToFormValue.current("startYear"),
                duration:     storedValueToFormValue.current("duration"),
                energyList:   storedValueToFormValue.current("energyList"),
                others:       storedValueToFormValue.current("others"),
                remarks:      storedValueToFormValue.current("remarks"),
            };
            reset(formObject); // 重置react-form数据

            // 设置selector数据
            setCityRadioState(ifTruthLet(getStore("city"),                 isNullness, () => {return {id: 0, name: ""};}, value => value));
            setIndustryTypeRadioState(ifTruthLet(getStore("industryType"), isNullness, () => {return {id: 0, name: ""};}, value => value));
            setEQuotaTypeRadioState(ifTruthLet(getStore("eQuotaType"),     isNullness, () => {return {id: 0, name: ""};}, value => value));
            setStartYearRadioState(ifTruthLet(getStore("startYear"),       isNullness, () => {return {id: 0, name: ""};}, value => value));
            setDurationRadioState(ifTruthLet(getStore("duration"),         isNullness, () => {return {id: 0, name: ""};}, value => value));
            setEnergyListCheckState(ifTruthLet(getStore("energyList"),     isNullness, () => [], value => value));
        };

        // 根据反馈, 要求默认显示0
        /*if (localCversion === 0 && remoteCversion === 0) {
            setLoadingIndicatorVisible(false);
            //recordSelectQuery.mutate();
            return;
        }*/

        if(formType === 1) {
            recordSelectQuery.mutate();
        } else if (localCversion === 0 && remoteCversion === 0) { // 初始版本不同步服务器更合理, 更方便输入
            setTimeout(() => {
                setLoadingIndicatorVisible(false);
            }, 10);
            //recordSelectQuery.mutate();
            return;
        } else if (localCversion === 0 && remoteCversion > 0) {
            console.log("Local cversion === 0, serve may have something nontrivial initialized, query mutate!");
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        } else if (isServerDataNewer(localCversion, remoteCversion) && subCversionRef.current > 0) { // 服务端有更新数据, 但本地有修改
            console.log("Client cversion fell behind the server cversion, someone might modified the database, choose to use the data version!");
            Alert.alert("注意", "由于其他人向服务器提交了数据, 服务端数据比本地数据新, 请选择使用哪份数据?", [
                { text: "使用本地数据", onPress: () => { setStore("mainCversion", remoteCversion); restoreLocalData(); } },
                { text: "使用服务端数据", onPress: () => { setLoadingIndicatorVisible(true); recordSelectQuery.mutate(); } },
            ]);
        } else if (isServerDataNewer(localCversion, remoteCversion)) { // localCversion==0时要mutate是因为服务端可能在创建记录时生成了必要数据
            console.log("Server cversion may be newer, query mutate:", localCversion, remoteCversion);
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        }
        else {
            console.log("Local cversion >= server cversion, query will not mutate!");
            setTimeout(() =>{
                console.log("Use local data, delay rendering!");
                restoreLocalData();
                setLoadingIndicatorVisible(false);
            }, 50);
        }
    }, []); // 这里只要求载入时运行一次, 添加依赖项会导致太多重复渲染

    const fieldsConfig = [
        {
            inputs: [
                { name: "clientName",   label: "名称",         unit: "",    type: "PLAIN", editable: true, placeholder: "", },
                { name: "property",     label: "性质",         unit: "",    type: "PLAIN", editable: true, placeholder: "", },
                { name: "address",      label: "地址",         unit: "",    type: "PLAIN", editable: true, placeholder: "", },
                { name: "busNum",       label: "公车数量",      unit: "",    type: "PLAIN", editable: true, placeholder: "", },
                { name: "residentNum",  label: "常驻实有人数",   unit: "",    type: "PLAIN", editable: true, placeholder: "", },
                { name: "headcount",    label: "在编人数",      unit: "",    type: "PLAIN", editable: true, placeholder: "", },
                { name: "esContact",    label: "节能工作联系人", unit: "",    type: "PLAIN", editable: true, placeholder: "", },
                { name: "esPhone",      label: "联系电话",      unit: "",    type: "PLAIN", editable: true, placeholder: "", },
                { name: "landArea",     label: "占地面积",      unit: "m²",  type: "PLAIN", editable: true, placeholder: "", },
                { name: "buildArea",    label: "建筑面积",      unit: "m²",  type: "PLAIN", editable: true, placeholder: "", },
                { name: "heatingArea",  label: "空调/供暖面积",  unit: "m²",  type: "PLAIN", editable: true, placeholder: "", },
                { name: "greenArea",    label: "绿化面积",       unit: "m²", type: "PLAIN", editable: true, placeholder: "", },
                { name: "buildUseTime", label: "建筑投入使用时间", unit: "",   type: "PLAIN", editable: true, placeholder: "", },
                //{ name: "province",     label: "", unit: "", type: "PLAIN", editable: true, placeholder: "", },
                { name: "city",         label: "地域",          unit: "",    type: "RADIO", editable: true, placeholder: "", selectorState: cityRadioState,         setSelectorState: setCityRadioState,         dataProvider: hebeiRegionEnum, },
                { name: "industryType", label: "行业类型",       unit: "",    type: "RADIO", editable: true, placeholder: "", selectorState: industryTypeRadioState, setSelectorState: setIndustryTypeRadioState, dataProvider: industryTypeEnum, },
                { name: "eQuotaType",   label: "分类",          unit: "",    type: "RADIO", editable: true, placeholder: "", selectorState: eQuotaTypeRadioState,   setSelectorState: setEQuotaTypeRadioState  , dataProvider: eQuotaTypeEnum, },

            ],
        },
        {
            nodeType: "Section", title: "以下三字段用于控制自动生成表单", nodes: [
                {
                    inputs: [
                        { name: "duration",     label: "统计周期",      unit: "年",  type: "RADIO", editable: true, placeholder: "", selectorState: durationRadioState,     setSelectorState: setDurationRadioState,     dataProvider: durationEnum, },
                        { name: "startYear",    label: "开始年份",      unit: "",    type: "RADIO", editable: true, placeholder: "", selectorState: startYearRadioState,    setSelectorState: setStartYearRadioState,    dataProvider: startYearEnum, },
                        { name: "energyList",   label: "能源能耗类型",   unit: "",    type: "CHECK", editable: true, placeholder: "", selectorState: energyListCheckState,   setSelectorState: setEnergyListCheckState,   dataProvider: energyListEnum},
                    ]
                },
            ]
        },
    ];


    /**
     * 注释掉的理由见EnergyConsumersRecordsUpdating.jsx
     */
    // const MemoizedFormListNodesMapper = React.memo(FormListNodesMapper);

    return (
        <View style={{ flex: 1 }}>

            <HeaderBar
                title={screenTitle}
                navigation={navigation}
                goBackCallback={() => {
                    callOneByOne(
                        //() => { _mainCversionGetter=undefined; _subCversionGetter=undefined;},
                        resetSelectorStates,
                    );
                    /*callOneByOne(clearStore, resetSelectorStates)*/  // 返回前清空mmkv存储因为临时数据不再需要, 重置状态使得页面切换后不会使用前面的数据
                }}
                menuItemArray={formType === 0 ? [{ title: "删除表单", action: deleteAlert }, { title: "清空缓存", action: clearCache }] : []}
            />

            {!loadingIndicatorVisible && <ScreenWrapper contentContainerStyle={styles.container}>

                <View style={styles.formEntry}>
                    <FormListNodesMapper
                        fieldsConfig={fieldsConfig}
                        subCversionRef={subCversionRef}
                        control={control}
                        setStore={setStore}
                        resetField={resetField}
                        errors={errors}
                        setSnackBarMessage={setSnackBarMessage}
                        setShowSnackbar={setShowSnackbar}
                        //formDisabledGlobal={formDisabledGlobal}
                        editable={formEditableGlobal}
                        rowStyle={styles.rowContainer}
                    />

                    {/*formType === 0 && <ControlledTextInput
                        name="remarks"
                        rowLabel="备注"
                        control={control}
                        placeholder=""
                        onChangeText={(text) => {
                            subCversionRef.current++;
                            setStore("subCversion", subCversionRef.current);
                            setStore("remarks", text);
                        }}
                        onClearText={() => {
                            subCversionRef.current++;
                            setStore("subCversion", subCversionRef.current);
                            setStore("remarks", "");
                            resetField("remarks", { defaultValue: "" });
                        }}
                        editable={formEditableGlobal}
                        mode="flat"
                        multiline={true}
                        formError={errors}
                        style={styles.rowContainerLongText}
                        //disabled={formDisabledGlobal}
                        required={false}
                    />*/}
                </View>

                {formType === 0 && <View style={styles.button}>
                    <Button mode="contained"
                        disabled={okButtonDisabled}
                        loading={okButtonLoading}
                        icon={saveButtonIcon}
                        onPress={handleSubmit(onHandleSubmit, (formErr) => onPreSubmitError(formErr, getStore, "WaterUsageUnitsFirstRecordsUpdating"))}
                    >{bottomBarLabels.ok}</Button>
                </View>}
            </ScreenWrapper>}

            <LoadingIndicator
                title={""}
                message={"加载中....."}
                visible={loadingIndicatorVisible}
                onClose={()=>setLoadingIndicatorVisible(false)}
            />

            <Snackbar
                style={styles.snackBar}
                visible={showSnackbar}
                onDismiss={() => setShowSnackbar(false)}
                onIconPress={() => setShowSnackbar(false)}
                duration={Snackbar.DURATION_LONG}
            >
                {snackBarMessage}
            </Snackbar>

            <DialogToConfirm
                visible={showConfirm}
                title={confimDialogConfig.title ? confimDialogConfig.title : ""}
                text={confimDialogConfig.text ? confimDialogConfig.text : ""}
                onOK={confimDialogConfig.onOK ? confimDialogConfig.onOK : () => setShowConfirm(false)}
                okBtnLabel={confimDialogConfig.okLabel ? confimDialogConfig.okLabel : "确认"}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    rowContainer:{
        firstColumn: {
            flex: 2,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 3,
        // /borderWidth: 1,
        // height: 40,
        },
    },
    rowContainerLongText: {
        firstColumn: {
            flex: 1,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 4,
            // /borderWidth: 1,
            // height: 40,
        },
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        width: "80%",
        alignSelf: "center",
    },
    formEntry: {
        margin: 8,
    },
    snackBar: {
        flexDirection: "column",
    },
});

export default RecordsUpdating;
