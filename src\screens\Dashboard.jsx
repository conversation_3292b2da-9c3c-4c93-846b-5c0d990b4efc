import React from "react";
import Background from "../components/Background";
import Logo from "../components/Logo";
import Header from "../components/Header";
import Paragraph from "../components/Paragraph";
import Button from "../components/Button";
import { useTranslation } from "react-i18next";
import { userLoginState } from "../hooks/loginStoredState";
import { useShallow } from "zustand/shallow";

export default function Dashboard({ navigation }) {
    const userToken = userLoginState(useShallow((state) => state.token));

    const { t } = useTranslation(["screenDashboard"]);
    return (
        <Background>
            <Logo />
            <Header>{t("screenDashboard:header")}</Header>
            <Paragraph>
                {`${t("screenDashboard:paragraph")}:${userToken}`}
            </Paragraph>
            <Button
                mode="outlined"
                onPress={() =>
                    navigation.reset({
                        index: 0,
                        routes: [{ name: "StartScreen" }],
                    })
                }
            >
                {t("screenDashboard:signButton")}
            </Button>
        </Background>
    );
}
