import { createZustandStoredState } from "../services/state-management";
import LocalStorage from "../services/local-storage";


const  STORE_TEST_REACT_HOOK_FORM = "TEST-HOOK-FORM-STORE";

const statDefinition = (set, get) => ({
    name: "n-a-m-e",
    age: 100,
    setName: (newName) => set({ name: newName }),
    setAge: (newAge) => set({ age: newAge }),
});

const useTestReactHookFormState = createZustandStoredState(STORE_TEST_REACT_HOOK_FORM, statDefinition);


const TEST_REACT_HOOK_FORM_MMKV = "TEST_REACT_HOOK_FORM_MMKV";

LocalStorage.set(TEST_REACT_HOOK_FORM_MMKV, JSON.stringify({name: "DDDDDD"}));

export { useTestReactHookFormState };
