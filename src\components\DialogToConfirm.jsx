import * as React from "react";
import { View } from "react-native";
import { Button, Portal, Dialog, Text, useTheme } from "react-native-paper";
//import { DialogTextComponent } from "./DialogTextComponent";
import { isArray, isString } from "../utils";
import { useShallow } from "zustand/shallow";


// UndismissableDialog.tsx in paper examples
/**
 * texta可以是字符串或者字符串数组
 * @returns
 */
export const DialogToConfirm = ({ title, text, visible, onOK, onCancel, okBtnLabel = "确定", cancelBtnLabel = "取消"}) => {
    const theme = useTheme();
    return (
        <Portal>
            <Dialog onDismiss={()=>{onCancel ? onCancel() : undefined;}} visible={visible} dismissable={false}>
                <Dialog.Title>{title}</Dialog.Title>
                <Dialog.Content>
                    <View flexDirection="column">
                        {isString(text) ? <Text variant="bodyLarge" style={{ color: theme.colors.onSurfaceVariant}}>{text}</Text> : text}
                    </View>
                </Dialog.Content>
                <Dialog.Actions>
                    {onCancel && <Button onPress={()=>{onCancel();}}>{cancelBtnLabel}</Button>}
                    <Button onPress={()=>{onOK ? onOK() : undefined;}}>{okBtnLabel}</Button>
                </Dialog.Actions>
            </Dialog>
        </Portal>
    );
};

/**
 *
 * @param {object} args
 * @param {} args.globalStatus something like `confirmDialogState`
 * @returns
 */
export const DialogToConfirmGlobal = ({globalStatus, okBtnLabel = "确定", cancelBtnLabel = "取消"}) => {
    const theme = useTheme();
    const [
        confirmDialogVisible,
        confirmDialogTitle,
        confirmDialogMessage,
        //initDialog,
        confirmDialog,
        cancelDialog
    ] = globalStatus(useShallow(state => [
        state.visible,
        state.title,
        state.message,
        //state.initDialog,
        state.onConfirm,
        state.onCancel
    ]));

    const dialogContents = isString(confirmDialogMessage) ? <Text variant="bodyLarge" style={{ color: theme.colors.onSurfaceVariant}}>{confirmDialogMessage}</Text> :
        (isArray(confirmDialogMessage) ? <>
            {confirmDialogMessage.map((msg, index) => <Text key={index}
                variant="bodyLarge" style={{ color: theme.colors.onSurfaceVariant}}>{msg}</Text> )}
        </> : "");
    return (
        <DialogToConfirm
            visible={confirmDialogVisible}
            title={confirmDialogTitle || ""}
            text={dialogContents}
            onOK={confirmDialog}
            onCancel={cancelDialog}
            okBtnLabel={okBtnLabel}
            cancelBtnLabel={cancelBtnLabel}
        />
    );
};
