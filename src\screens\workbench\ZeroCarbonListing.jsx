import React from "react";
import { makeReqListZCByCreater as queryClient } from "../../api/listingQueries";
import { orgProjBaseState as pageDataState } from "../../hooks/globalStates";
import ListingTemplate from "./ListingTemplate";

/**
 * Ther view of list-org-project-base of the server.
 * @param {*} param0
 * @returns
 */
const ZeroCarbonListing = ({ navigation }) => {
    return (
        <ListingTemplate
            bottomBarLabel={"创建新项目"}
            emptyScreenText={"请点击下方按钮创建新项目"}
            snackBarDefaultText={"创建新项目遇到错误"}
            saveButtonIcon={"card-plus-outline"}
            queryClient={queryClient}
            pageDataState={pageDataState}
            listItemNavigateTo={"WorkbenchZeroCarbonUpdating"}
            addingButtonNavigateTo={"WorkbenchZeroCarbonInserting"}
            navigation={navigation}
        />
    );
};

export default ZeroCarbonListing;
