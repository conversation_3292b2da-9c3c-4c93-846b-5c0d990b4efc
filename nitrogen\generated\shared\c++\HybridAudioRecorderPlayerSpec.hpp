///
/// HybridAudioRecorderPlayerSpec.hpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc Rousavy @ Margelo
///

#pragma once

#if __has_include(<NitroModules/HybridObject.hpp>)
#include <NitroModules/HybridObject.hpp>
#else
#error NitroModules cannot be found! Are you sure you installed NitroModules properly?
#endif

// Forward declaration of `AudioSet` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { struct AudioSet; }
// Forward declaration of `RecordBackType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { struct RecordBackType; }
// Forward declaration of `PlayBackType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { struct PlayBackType; }

#include <NitroModules/Promise.hpp>
#include <string>
#include <optional>
#include "AudioSet.hpp"
#include <unordered_map>
#include <functional>
#include "RecordBackType.hpp"
#include "PlayBackType.hpp"

namespace margelo::nitro::react_native_audio_recorder_player {

  using namespace margelo::nitro;

  /**
   * An abstract base class for `AudioRecorderPlayer`
   * Inherit this class to create instances of `HybridAudioRecorderPlayerSpec` in C++.
   * You must explicitly call `HybridObject`'s constructor yourself, because it is virtual.
   * @example
   * ```cpp
   * class HybridAudioRecorderPlayer: public HybridAudioRecorderPlayerSpec {
   * public:
   *   HybridAudioRecorderPlayer(...): HybridObject(TAG) { ... }
   *   // ...
   * };
   * ```
   */
  class HybridAudioRecorderPlayerSpec: public virtual HybridObject {
    public:
      // Constructor
      explicit HybridAudioRecorderPlayerSpec(): HybridObject(TAG) { }

      // Destructor
      ~HybridAudioRecorderPlayerSpec() override = default;

    public:
      // Properties
      

    public:
      // Methods
      virtual std::shared_ptr<Promise<std::string>> startRecorder(const std::optional<std::string>& uri, const std::optional<AudioSet>& audioSets, std::optional<bool> meteringEnabled) = 0;
      virtual std::shared_ptr<Promise<std::string>> pauseRecorder() = 0;
      virtual std::shared_ptr<Promise<std::string>> resumeRecorder() = 0;
      virtual std::shared_ptr<Promise<std::string>> stopRecorder() = 0;
      virtual std::shared_ptr<Promise<std::string>> startPlayer(const std::optional<std::string>& uri, const std::optional<std::unordered_map<std::string, std::string>>& httpHeaders) = 0;
      virtual std::shared_ptr<Promise<std::string>> stopPlayer() = 0;
      virtual std::shared_ptr<Promise<std::string>> pausePlayer() = 0;
      virtual std::shared_ptr<Promise<std::string>> resumePlayer() = 0;
      virtual std::shared_ptr<Promise<std::string>> seekToPlayer(double time) = 0;
      virtual std::shared_ptr<Promise<std::string>> setVolume(double volume) = 0;
      virtual std::shared_ptr<Promise<std::string>> setPlaybackSpeed(double playbackSpeed) = 0;
      virtual void setSubscriptionDuration(double sec) = 0;
      virtual void addRecordBackListener(const std::function<void(const RecordBackType& /* recordingMeta */)>& callback) = 0;
      virtual void removeRecordBackListener() = 0;
      virtual void addPlayBackListener(const std::function<void(const PlayBackType& /* playbackMeta */)>& callback) = 0;
      virtual void removePlayBackListener() = 0;
      virtual std::string mmss(double secs) = 0;
      virtual std::string mmssss(double milisecs) = 0;

    protected:
      // Hybrid Setup
      void loadHybridMethods() override;

    protected:
      // Tag for logging
      static constexpr auto TAG = "AudioRecorderPlayer";
  };

} // namespace margelo::nitro::react_native_audio_recorder_player
