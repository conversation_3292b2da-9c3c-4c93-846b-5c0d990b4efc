///
/// JAudioEncoderAndroidType.hpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc R<PERSON>avy @ Margelo
///

#pragma once

#include <fbjni/fbjni.h>
#include "AudioEncoderAndroidType.hpp"

namespace margelo::nitro::react_native_audio_recorder_player {

  using namespace facebook;

  /**
   * The C++ JNI bridge between the C++ enum "AudioEncoderAndroidType" and the the Kotlin enum "AudioEncoderAndroidType".
   */
  struct JAudioEncoderAndroidType final: public jni::JavaClass<JAudioEncoderAndroidType> {
  public:
    static auto constexpr kJavaDescriptor = "Lcom/margelo/nitro/reactnativeaudiorecorderplayer/AudioEncoderAndroidType;";

  public:
    /**
     * Convert this Java/Kotlin-based enum to the C++ enum AudioEncoderAndroidType.
     */
    [[maybe_unused]]
    [[nodiscard]]
    AudioEncoderAndroidType toCpp() const {
      static const auto clazz = javaClassStatic();
      static const auto fieldOrdinal = clazz->getField<int>("_ordinal");
      int ordinal = this->getFieldValue(fieldOrdinal);
      return static_cast<AudioEncoderAndroidType>(ordinal);
    }

  public:
    /**
     * Create a Java/Kotlin-based enum with the given C++ enum's value.
     */
    [[maybe_unused]]
    static jni::alias_ref<JAudioEncoderAndroidType> fromCpp(AudioEncoderAndroidType value) {
      static const auto clazz = javaClassStatic();
      static const auto fieldDEFAULT = clazz->getStaticField<JAudioEncoderAndroidType>("DEFAULT");
      static const auto fieldAMR_NB = clazz->getStaticField<JAudioEncoderAndroidType>("AMR_NB");
      static const auto fieldAMR_WB = clazz->getStaticField<JAudioEncoderAndroidType>("AMR_WB");
      static const auto fieldAAC = clazz->getStaticField<JAudioEncoderAndroidType>("AAC");
      static const auto fieldHE_AAC = clazz->getStaticField<JAudioEncoderAndroidType>("HE_AAC");
      static const auto fieldAAC_ELD = clazz->getStaticField<JAudioEncoderAndroidType>("AAC_ELD");
      static const auto fieldVORBIS = clazz->getStaticField<JAudioEncoderAndroidType>("VORBIS");
      
      switch (value) {
        case AudioEncoderAndroidType::DEFAULT:
          return clazz->getStaticFieldValue(fieldDEFAULT);
        case AudioEncoderAndroidType::AMR_NB:
          return clazz->getStaticFieldValue(fieldAMR_NB);
        case AudioEncoderAndroidType::AMR_WB:
          return clazz->getStaticFieldValue(fieldAMR_WB);
        case AudioEncoderAndroidType::AAC:
          return clazz->getStaticFieldValue(fieldAAC);
        case AudioEncoderAndroidType::HE_AAC:
          return clazz->getStaticFieldValue(fieldHE_AAC);
        case AudioEncoderAndroidType::AAC_ELD:
          return clazz->getStaticFieldValue(fieldAAC_ELD);
        case AudioEncoderAndroidType::VORBIS:
          return clazz->getStaticFieldValue(fieldVORBIS);
        default:
          std::string stringValue = std::to_string(static_cast<int>(value));
          throw std::invalid_argument("Invalid enum value (" + stringValue + "!");
      }
    }
  };

} // namespace margelo::nitro::react_native_audio_recorder_player
