import { useQuery } from "@tanstack/react-query";
import { QUERY_PASSWORD_LOGIN, QUERY_TOKEN_LOGIN, URL_LOGIN as url } from "../config/keysConfig";
import { authHttpClient as httpClient } from "../services/http";
import { APP_VERSION, CLIENT_OS, CLIENT_OS_VERSION } from "../services/system";
import { getToken } from "../utils/user";


/**
 * Login to the server with POST method and fetch the token of this user
 * 注意: enabled参数是false, 查询不能自动运行, 而必须是用户点击后才能运行, 查询也不retry, 用户可以自己去点(但ky需要retry).
 * HTTPError: https://github.com/sindresorhus/ky?tab=readme-ov-file#httperror
 * Disable Query: https://tanstack.com/query/latest/docs/react/guides/disabling-queries
 * @param {string} account user account
 * @param {string} password user password
 * @param {(token:string)=>{}} action callback function
 */
const usePasswordLogin = (account, password, action) => {
    const formData = new FormData();
    formData.append("acc", account);
    formData.append("pass", password);
    formData.append("os", CLIENT_OS);
    formData.append("osVersion", CLIENT_OS_VERSION);
    formData.append("appVersion", APP_VERSION);
    // no retry for both ky and query, user will retry by himself
    return useQuery({
        queryKey: [QUERY_PASSWORD_LOGIN],
        queryFn: async () => {
            console.debug(`Make a post request to ${url} with data ${account}:${password}`);
            const response = await httpClient.post("login", { body: formData, retry: { limit: 0 } });
            const json = await response.json();
            console.log("token login response json:", json);
            console.debug(`Fetching token from server: ${json.token}.`);
            action(json);
            return json;
        },
        enabled: false,
        retry: false,
    });
};

/**
 * Login to the server with POST method and fetch the token of this user
 * 注意: 用于身份验证的retry要放在ky上, 不能放在query上, 否则key处的token交换会出现问题
 * HTTPError: https://github.com/sindresorhus/ky?tab=readme-ov-file#httperror
 * @param {string} token token string
 * @param {(token:string)=>{}} action callback function
 */
const useTokenLogin = (action) => {
    return useQuery({
        queryKey: [QUERY_TOKEN_LOGIN],
        queryFn: async () => {
            const token = getToken();
            if (!token) {
                return Promise.reject(new Error("No token stored, reject before fetching."));
            }
            const formData = new FormData();
            formData.append("token", token);
            formData.append("os", CLIENT_OS);
            formData.append("osVersion", CLIENT_OS_VERSION);
            formData.append("appVersion", APP_VERSION);
            const response = await httpClient.post("login", { body: formData });
            const json = await response.json();
            if (json.status === "success") {
                console.log("token login response json:", json);
                action(json);
            } else {
                return Promise.reject(new Error(`The server rejected this token with status "${json.status}".`));
            }
            return json;
        },
        retry: false,
    });
};

export { usePasswordLogin, useTokenLogin };

