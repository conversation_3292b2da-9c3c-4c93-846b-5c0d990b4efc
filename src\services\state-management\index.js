import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import LocalStorage from "../local-storage";


// https://github.com/mrousavy/react-native-mmkv/blob/master/docs/WRAPPER_ZUSTAND_PERSIST_MIDDLEWARE.md
const ZustandMMKVStorage = {
    /**
     * @param {string} name
     * @param {string} value
     */
    setItem: (name, value) => {
        return LocalStorage.set(name, value);
    },
    /**
     * @param {string} name
     */
    getItem: (name) => {
        const value = LocalStorage.getString(name);
        return value ?? null;
    },
    /**
     * @param {string} name
     */
    removeItem: (name) => {
        return LocalStorage.delete(name);
    },
};


/**
 * 创建一个Zustand状态管理, 并且存储到MMKV
 * @param {string} storeName mmkv存储名称
 * @param {(get:function, set:function)=>({})[]} stateDefinitions 状态定义函数数组
 */
const createZustandStoredState = (storeName, ...stateDefinitions) => {
    //console.log("CreateZustandStoredState: ", storeName, stateDefinitions.map(stateDef => stateDef(console.log, console.log)));
    return create(
        persist((set, get) => stateDefinitions.map(stateDef => stateDef(set, get)).reduce(((r, c) => Object.assign(r, c)), {}),
            {
                name: storeName,
                storage: createJSONStorage(() => ZustandMMKVStorage),
            },
        ),);
};


/**
 * 创建一个Zustand状态管理
 * @param {(get:function, set:function)=>({})[]} stateDefinitions 状态定义函数数组
 */
const createZustandState = (...stateDefinitions) => {
    //console.log("CreateZustandState: ", stateDefinitions.map(stateDef => stateDef(console.log, console.log)));
    return create((set, get) => stateDefinitions.map(stateDef => stateDef(set, get)).reduce(((r, c) => Object.assign(r, c)), {}),);
};


// a radio option is an object of shape { id, name }.
// the radio option can be created globally and used in a component chain
const createRadioOptionState = () => {
    const radioStatDefine = (set, get) => ({
        id: 0,
        name: "",
        setId: (id) => set({ id: id }),
        setName: (name) => set({ name: name }),
        resetRadioState: () => set({ id: 0, name: "" }),
    });
    return createZustandState(radioStatDefine);
};


/*
import { useShallow } from "zustand/shallow";

// 定义状态
let statDefine = (set, get) => ({
    userData: null,
    setUserData: (user) => set({ userData: user }),
    resetUserData: () => set({ userData: null }),
});

// 创建Zustand+MMKV状态存储
const useStore = createZustandStoredState("userStore", statDefine);

// 获取状态指针
// 注意在Zustand5中, 不使用useShallow会报错: Maximum update depth exceeded.
const userData = useStore(useShallow(state => state.userData));
const setUserData = useStore(useShallow(state => state.setUserData));

// 访问状态值
userData?.userData;
setUserData({ name: "John Doe", email: "<EMAIL>" });
*/


export { createZustandStoredState, createZustandState, createRadioOptionState };
