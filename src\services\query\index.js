import { QueryClient, useQuery, useMutation, useQueryClient } from "@tanstack/react-query";


export const queryClient = new QueryClient();

// https://medium.com/@amavictor/how-to-use-react-query-axios-and-a-custom-request-processor-to-transform-your-data-2a9f0c9f5bf0
/**
 * 似乎没用, 直接使用useQuery和useMutation更加直截了当, 暂且放在这里供参考.
 * @returns
 */
export const useRequestProcessor = () => {
    const queryClient = useQueryClient();

    const query = (key, queryFunction, options = {}) => {
        return useQuery({
            queryKey: key,
            queryFn: queryFunction,
            ...options,
        });
    };

    const mutate = (key, mutationFunction, options = {}) => {
        return useMutation({
            mutationKey: key,
            mutationFn: mutationFunction,
            onSettled: () => queryClient.invalidateQueries(key),
            ...options,
        });
    };

    return { query, mutate };
};
