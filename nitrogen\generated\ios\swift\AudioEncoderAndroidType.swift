///
/// AudioEncoderAndroidType.swift
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON> @ Margelo
///

/**
 * Represents the JS enum `AudioEncoderAndroidType`, backed by a C++ enum.
 */
public typealias AudioEncoderAndroidType = margelo.nitro.react_native_audio_recorder_player.AudioEncoderAndroidType

public extension AudioEncoderAndroidType {
  /**
   * Get a AudioEncoderAndroidType for the given String value, or
   * return `nil` if the given value was invalid/unknown.
   */
  init?(fromString string: String) {
    switch string {
      case "DEFAULT":
        self = .default
      case "AMR_NB":
        self = .amrNb
      case "AMR_WB":
        self = .amrWb
      case "AAC":
        self = .aac
      case "HE_AAC":
        self = .heAac
      case "AAC_ELD":
        self = .aacEld
      case "VORBIS":
        self = .vorbis
      default:
        return nil
    }
  }

  /**
   * Get the String value this AudioEncoderAndroidType represents.
   */
  var stringValue: String {
    switch self {
      case .default:
        return "DEFAULT"
      case .amrNb:
        return "AMR_NB"
      case .amrWb:
        return "AMR_WB"
      case .aac:
        return "AAC"
      case .heAac:
        return "HE_AAC"
      case .aacEld:
        return "AAC_ELD"
      case .vorbis:
        return "VORBIS"
    }
  }
}
