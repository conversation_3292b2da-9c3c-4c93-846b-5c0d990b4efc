import { joiResolver } from "@hookform/resolvers/joi";
import "fast-text-encoding";
import <PERSON><PERSON> from "joi";
import debounce from "lodash/debounce";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { useForm, useWatch } from "react-hook-form";
import { Alert, StyleSheet, View } from "react-native";
import { Divider, Snackbar } from "react-native-paper";
import Button from "../../../components/Button";
import ControlledTextInput from "../../../components/ControlledTextInput";
import { DialogToConfirm } from "../../../components/DialogToConfirm";
import HeaderBar from "../../../components/HeaderBar";
import { LoadingIndicator } from "../../../components/LoadingIndicator";
import { creatMMKVStore, makeStoredValueToFormValue } from "../../../services/local-storage";
import { callOneByOne, ifTruthLet, isNullness, makeDataFeeder, parseIndustryCode } from "../../../utils";
import { checkPermits } from "../../../utils/permits";
import { checkIdsToObject, isServerDataNewer, makeGetClientCversion, radioIdToObject } from "../../../utils/records";
import { validatorBase } from "../../../utils/validatorBase";
import ScreenWrapper from "../../ScreenWrapper";

// 新组件需要重新!!
import { useShallow } from "zustand/shallow";
import {
    makeReqDelWaterBalanceRecords as recordDeleteQueryMaker,
    makeReqGetWaterBalanceRecords as recordSelectQueryMaker,
    makeReqUpdWaterBalanceRecords as recordUpdateQueryMaker,
} from "../../../api/projectRecordsQueries";
import { FormListNodesMapper } from "../../../components/FormListNodesMapper";
import { UPD_WATER_BALANCE_RECORDS as pageMainKey } from "../../../config/keysConfig";
import { serviceTypeEnum, waterUnitScaleEnum } from "../../../config/waterBalance";
import { wbWaterUsageAnnualUpdateStates as selectorStates } from "../../../hooks/selectorStates";
import { roundNearest, roundNearestTo, roundPercent } from "../../../utils/numeric";
import { onPreSubmitError } from "../../../utils/screens";
//import log from "../../../services/logging";


const dataFeeder = makeDataFeeder();

/**
 * 表: 近三年用水情况表, 书: 用水单位年用水情况表
 * 其中涉及的各种率都是以百分比显示的, 因此计算中需要除以100, 当时在数据库中都是以率来计算, 即服务端数据 = 客户端数据 / 100, 通信时需要相互.
 * 注意, 项目数据表单与工作台管理表单的数据有一个显著的区别:
 * 项目数据不需要像用户编号那样脱敏, 两边传输的数据是直接的, 不需要做{id, name}这种映射, 部分需要映射的可以在客户端本地完成,
 * @param {object} args
 * @param {object} args.navigation
 * @param {object} args.route
 * @returns
 */
const RecordsUpdating = ({ navigation, route }) => {
    console.log("WaterUsageAnnualRecordsUpdating pageMeta from nav:", route.params.pageMeta);
    console.log("WaterUsageAnnualRecordsUpdating projMeta from nav:", route.params.projMeta);
    const projSubclass = route.params.projMeta.subclass; // 1表, 2书
    const projIndustry = parseIndustryCode(route.params.projMeta.industry); // 1 -> industry, 2 -> service

    const { queryKwd, id: recordPubid, name: recordName, formType } = route.params.pageMeta; // recordPubid is the alias of id
    const saveButtonIcon = "content-save-all-outline";
    const formEditableGlobal = checkPermits() ? (formType === 0 ? true : false) : false;

    // 使用memoize来优化
    const { setStore, getStore, clearStore, setStoreObject } = useMemo(() => {
        console.log("creatMMKVStore memo:", pageMainKey.store, queryKwd, recordPubid);
        return creatMMKVStore(`${pageMainKey.store}/${queryKwd}/${recordPubid}`);
    }, [queryKwd, recordPubid]);

    const storedValueToFormValue = useRef(makeStoredValueToFormValue(getStore));
    const getClientCversion = useRef(makeGetClientCversion(() => getStore("mainCversion"), () => getStore("subCversion")));

    // 客户端cversion = `${mainCversion}.${subCversion}`
    (getStore("mainCversion") === undefined) ? setStore("mainCversion", 0) : undefined; // 客户端主版本号
    (getStore("subCversion")  === undefined) ? setStore("subCversion",  0) : undefined; // 客户端次版本号, 修改一次自增一次
    //let mainCversion = getStore("mainCversion"); // 主版本号, 与服务端cversion对应
    // 次版本号, 表示本地更改次数, 用于程序上对子版本号的增加, 用于判断与服务端版本的差异, 同时可用于判断避免重复从MMKV获取数据
    const subCversionRef = useRef(getStore("subCversion"));

    // 新组件需要修改!!
    const bottomBarLabels = { ok: "提交" };                    // 底部按钮显示文字
    const [snackBarMessage, setSnackBarMessage] = useState("更新表单遇到错误"); // 下方错误提示文字内容
    const [
        serviceTypesCheckState,
        waterUnitScaleState,
        setServiceTypesCheckState,
        setWaterUnitScaleState,
        resetSelectorStates
    ] = selectorStates(useShallow(state => [
        state.serviceTypesCheck,
        state.waterUnitScale,
        state.setServiceTypesCheck,
        state.setWaterUnitScale,
        state.resetStates,
    ])); // radio组件状态

    const [confimDialogConfig, setConfirmDialogConfig] = useState({});       // 对话框信息, {title, text, okLabel, onOK}, 但显示对话框的变量在showConfirm

    // 新组件不需改动
    const [screenTitle,      setScreenTitle]      = useState(route.params.pageMeta.name); // 屏幕上方导航栏标题
    const [okButtonLoading,  setOkButtonLoading]  = useState(false);            // 右下方按钮图标是否旋转
    const [okButtonDisabled, setOkButtonDisabled] = useState(!checkPermits());  // 右下方按钮是否禁用
    const [showSnackbar,     setShowSnackbar]     = useState(false);            // 下方是否显示错误通知
    const [showConfirm,      setShowConfirm]      = useState(false);            // 是否显示确认对话框
    const [loadingIndicatorVisible, setLoadingIndicatorVisible] = useState(false); // 是否显示loading
    const [unitToggleState, setUnitToggleState]   = useState(false);            // 用于控制切换单位后刷新数据
    const delQueryInfo = useRef({ id: recordPubid, name: recordName });         // 用于删除本记录的查询变量

    //const waterTypeStateDataProviderRef = useRef(waterSourceTypeEnum);
    const waterUnitScaleStateDataProviderRef = useRef(waterUnitScaleEnum);
    const serviceTypesStateDataProviderRef    = useRef(serviceTypeEnum);
    const waterUnitScaleRef = useRef(0); // 用于存储waterScale的值, 当unitToggleState发生改变时需要通过该值更新表单字段

    const fieldByServiceType = useRef({
        101: [ "rjqsY", "avgBuildWater", ],  // 机关
        102: [ "dwffSmmj", ],                // 机关(附加)-绿化
        201: [ "dwffCwY", ],                 // 酒店-住宿业
        301: [ "rjqsY", "avgBuildWater", ],  // 学校类
        401: [ "dwffCwD", ],                 // 医院类-住院
        402: [ "dwffRs", ],                  // 医院类-门诊
        501: [ "rjqsD", ],                   // 写字楼
        601: [ "avgBuildWater", ],           // 餐饮
        701: [ "avgBuildWater", ],           // 商场/超市
        801: [ "avgBuildWater", ],           // 文化娱乐
    });

    /**
         * 返回“真值”时对应配置的表单不会显示出来
         * @param {string} fieldName
         * @returns
         */
    const inputFilterIf = (fieldName) =>  {
        console.log("inputFilterIf: ", fieldName, serviceTypesCheckState, !((serviceTypesCheckState.map(item => fieldByServiceType.current[item]).flat(1))?.includes(fieldName)));
        return !((serviceTypesCheckState.map(item => fieldByServiceType.current[item.id]).flat(1))?.includes(fieldName));
    };

    // Define form
    // 新组件需要重新定义!!
    const schema = Joi.object({
        name:            validatorBase.waterBalance.commons.name,
        serviceTypes:    validatorBase.waterBalance.waterUsageAnnual.serviceTypes,
        year:            validatorBase.waterBalance.waterUsageAnnual.year,
        intakeTotal:     validatorBase.waterBalance.commons.floatFieldUnrequired,     // 总取水量
        intakeRegular:   validatorBase.waterBalance.commons.floatFieldUnrequired,     // 常规水源取水量
        intakeIrregular: validatorBase.waterBalance.commons.floatFieldUnrequired,     // 非常规水源取水量
        intakeTapWater:  validatorBase.waterBalance.commons.floatFieldUnrequired,     // 自来水
        intakeSurface:   validatorBase.waterBalance.commons.floatFieldUnrequired,     // 地表水
        intakeGround:    validatorBase.waterBalance.commons.floatFieldUnrequired,     // 地下水
        intakeSoft:      validatorBase.waterBalance.commons.floatFieldUnrequired,     // 外购软化水
        intakeSteam:     validatorBase.waterBalance.commons.floatFieldUnrequired,     // 外购蒸汽
        intakeBought:    validatorBase.waterBalance.commons.floatFieldUnrequired,     // 外购水
        //intakeExt1:      validatorBase.waterBalance.commons.floatFieldUnrequired,   // 预留常规1
        //intakeExt2:      validatorBase.waterBalance.commons.floatFieldUnrequired,   // 预留常规2
        //intakeExt3:      validatorBase.waterBalance.commons.floatFieldUnrequired,   // 预留常规3
        //intakeExt4:      validatorBase.waterBalance.commons.floatFieldUnrequired,   // 预留常规4
        intakeSea:       validatorBase.waterBalance.commons.floatFieldUnrequired,     // 海水
        intakeBrackish:  validatorBase.waterBalance.commons.floatFieldUnrequired,     // 苦咸水
        intakeRecycled:  validatorBase.waterBalance.commons.floatFieldUnrequired,     // 城镇污水再生水
        intakeMine:      validatorBase.waterBalance.commons.floatFieldUnrequired,     // 矿井水
        intakeRain:      validatorBase.waterBalance.commons.floatFieldUnrequired,     // 雨水
        //intakeExt11:     validatorBase.waterBalance.commons.floatFieldUnrequired,   // 预留非常规1
        //intakeExt12:     validatorBase.waterBalance.commons.floatFieldUnrequired,   // 预留非常规2
        //intakeExt13:     validatorBase.waterBalance.commons.floatFieldUnrequired,   // 预留非常规3
        //intakeExt14:     validatorBase.waterBalance.commons.floatFieldUnrequired,   // 预留非常规4
        rdccwv:          validatorBase.waterBalance.commons.floatFieldUnrequired,     // 补水(直接冷却循环水)量
        riccwv:          validatorBase.waterBalance.commons.floatFieldUnrequired,     // 补水(间接冷却循环水)量
        rilwv:           validatorBase.waterBalance.commons.floatFieldUnrequired,     // 补水(生活)量
        ripwv:           validatorBase.waterBalance.commons.floatFieldUnrequired,     // 补水(进入产品)量
        riowv:           validatorBase.waterBalance.commons.floatFieldUnrequired,     // 补水(其他)量
        scrv:            validatorBase.waterBalance.commons.floatFieldUnrequired,     // 蒸汽冷凝水回用量
        rwv:             validatorBase.waterBalance.commons.floatFieldUnrequired,     // 回用水量
        oswv:            validatorBase.waterBalance.commons.floatFieldUnrequired,     // 其他串联水量
        spv:             validatorBase.waterBalance.commons.floatFieldUnrequired,     // 蒸汽产汽量
        dccwv:           validatorBase.waterBalance.commons.floatFieldUnrequired,     // 直接冷却循环水量
        iccwv:           validatorBase.waterBalance.commons.floatFieldUnrequired,     // 间接冷却循环水量
        ocwv:            validatorBase.waterBalance.commons.floatFieldUnrequired,     // 其它循环水量
        //cllr:          validatorBase.waterBalance.commons.percentField,   // 常规漏损率%
        wcvf:            validatorBase.waterBalance.commons.percentField,   // 耗水量计算系数%
        inputWater:      validatorBase.waterBalance.commons.floatFieldUnrequired,     // 输入水量
        edv:             validatorBase.waterBalance.commons.floatFieldUnrequired,     // 外排水量
        lwv:             validatorBase.waterBalance.commons.floatFieldUnrequired,     // 漏损水量
        wcv:             validatorBase.waterBalance.commons.floatFieldUnrequired,     // 耗水量
        llr:             validatorBase.waterBalance.commons.percentField,   // 漏损率%
        //llrt:          validatorBase.waterBalance.commons.floatField,     // 漏损率超标(＜4%)
        wcpup:           validatorBase.waterBalance.commons.largeTextField, // 单位产品取水量, 字符串
        //wcpub:           validatorBase.waterBalance.commons.largeTextField, // 单位建筑面积取水量, 字符串
        //wcpus:           validatorBase.waterBalance.commons.largeTextField, // 服务业书独有字段, 单位服务用水量(取代excel中的单位出租床位用水量), 字符串
        pcdwc:           validatorBase.waterBalance.commons.floatFieldUnrequired, // 人均生活用水量(m³/人·a), Per capita domestic water consumption
        reuseRate:       validatorBase.waterBalance.commons.percentField,   // 重复利用率%
        dcwcr:           validatorBase.waterBalance.commons.percentField,   // 直接冷却水循环率%
        icwcr:           validatorBase.waterBalance.commons.percentField,   // 间接冷却水循环率%
        scrr:            validatorBase.waterBalance.commons.percentField,   // 蒸汽冷凝水回用率%
        wwrr:            validatorBase.waterBalance.commons.percentField,   // 废水回用率%
        cer:             validatorBase.waterBalance.commons.percentField,   // 达标排放率%
        uwrr:            validatorBase.waterBalance.commons.percentField,   // 非常规水替代率%
        //rcmd:          validatorBase.waterBalance.commons.largeTextField,
        // 服务业对应于运营情况统计表(WB_COMPANY_OPERATION_STATS)使用的考核指标
        rjqsY:           validatorBase.waterBalance.commons.floatFieldUnrequired, // 人均取水量: 年度(m³/人·a)
        rjqsD:           validatorBase.waterBalance.commons.floatFieldUnrequired, // 人均取水量: 每天(L/人·d)
        dwffSmmj:        validatorBase.waterBalance.commons.floatFieldUnrequired, //  单位服务取水量: 单位水面面积(m³/m².a)
        dwffCwY:         validatorBase.waterBalance.commons.floatFieldUnrequired, //  单位服务取水量: 单位床位取水量(m³/床·a)
        dwffCwD:         validatorBase.waterBalance.commons.floatFieldUnrequired, //  单位服务取水量: 单位床位取水量(L/床·d)
        dwffRs:          validatorBase.waterBalance.commons.floatFieldUnrequired, //  单位服务取水量: 单位人数取水量L/(人·次)
        avgWaterUsage:   validatorBase.waterBalance.commons.floatFieldUnrequired, //  人均用水量(L/人·d)
        avgBuildWater:   validatorBase.waterBalance.commons.floatFieldUnrequired, //  单位建筑面积用水(m³/m²·a)
        unitWaterUsage:  validatorBase.waterBalance.commons.floatFieldUnrequired, //  单位用水量(m³)
        others:      validatorBase.waterBalance.waterUsage.textField,
        remarks:     validatorBase.waterBalance.waterUsage.longTextField,
        waterScale:  validatorBase.waterBalance.commons.waterScale,
    });

    // 新组件需要重新定义defaultValues!!
    const {
        handleSubmit,
        control,
        resetField,
        reset,
        formState: { errors },
        setValue,
        getValues,
    } = useForm({
        resolver: joiResolver(schema),
        //reValidateMode: "onBlur", // https://github.com/orgs/react-hook-form/discussions/10327
        defaultValues: { // 默认值在组件载入时会被更新, 这里的初始值正常情况下没有用处
            name:            "",
            serviceTypes:    "[]",
            year:            "",
            intakeTotal:     "0", // 总取水量
            intakeRegular:   "0", // 常规水源取水量
            intakeIrregular: "0", // 非常规水源取水量
            intakeTapWater:  "0", // 自来水
            intakeSurface:   "0", // 地表水
            intakeGround:    "0", // 地下水
            intakeSoft:      "0", // 外购软化水
            intakeSteam:     "0", // 外购蒸汽
            intakeBought:    "0", // 外购水
            //intakeExt1:    "0", // 预留常规1
            //intakeExt2:    "0", // 预留常规2
            //intakeExt3:    "0", // 预留常规3
            //intakeExt4:    "0", // 预留常规4
            intakeSea:       "0", // 海水
            intakeBrackish:  "0", // 苦咸水
            intakeRecycled:  "0", // 城镇污水再生水
            intakeMine:      "0", // 矿井水
            intakeRain:      "0", // 雨水
            //intakeExt11:   "0", // 预留非常规1
            //intakeExt12:   "0", // 预留非常规2
            //intakeExt13:   "0", // 预留非常规3
            //intakeExt14:   "0", // 预留非常规4
            rdccwv:          "0", // 补水(直接冷却循环水)量
            riccwv:          "0", // 补水(间接冷却循环水)量
            rilwv:           "0", // 补水(生活)量
            ripwv:           "0", // 补水(进入产品)量
            riowv:           "0", // 补水(其他)量
            scrv:            "0", // 蒸汽冷凝水回用量
            rwv:             "0", // 回用水量
            oswv:            "0", // 其他串联水量
            spv:             "0", // 蒸汽产汽量
            dccwv:           "0", // 直接冷却循环水量
            iccwv:           "0", // 间接冷却循环水量
            ocwv:            "0", // 其它循环水量
            //cllr:          "0", // 常规漏损率%
            wcvf:            "0", // 耗水量计算系数%
            inputWater:      "0", // 输入水量
            edv:             "0", // 外排水量
            lwv:             "0", // 漏损水量
            wcv:             "0", // 耗水量
            llr:             "0", // 漏损率%
            //llrt:          "0", // 漏损率超标(＜4%)
            wcpup:           "",  // 单位产品取水量, 字符串
            //wcpub:           "",  // 单位建筑面积取水量, 字符串
            //wcpus:           "",  // 服务业书独有字段, 单位服务用水量(取代excel中的单位出租床位用水量), 字符串
            pcdwc:           "",  // 人均生活用水量(m³/人·a)
            reuseRate:       "0", // 重复利用率%
            dcwcr:           "0", // 直接冷却水循环率%
            icwcr:           "0", // 间接冷却水循环率%
            scrr:            "0", // 蒸汽冷凝水回用率%
            wwrr:            "0", // 废水回用率%
            cer:             "0", // 达标排放率%
            uwrr:            "0", // 非常规水替代率%
            rjqsY:           "", // 人均取水量: 年度(m³/人·a)
            rjqsD:           "", // 人均取水量: 每天(L/人·d)
            dwffSmmj:        "", //  单位服务取水量: 单位水面面积(m³/m².a)
            dwffCwY:         "", //  单位服务取水量: 单位床位取水量(m³/床·a)
            dwffCwD:         "", //  单位服务取水量: 单位床位取水量(L/床·d)
            dwffRs:          "", //  单位服务取水量: 单位人数取水量L/(人·次)
            avgWaterUsage:   "", //  人均用水量(L/人·d)
            avgBuildWater:   "", //  单位建筑面积用水(m³/m²·a)
            unitWaterUsage:  "", //  单位用水量(m³)
            //rcmd:            "",
            others:          "",
            remarks:         "",
            waterScale:      "0",
        },
    });

    // Query: select record
    const onRecordSelectSuccess = (data) => { // data: {"id": 3, "leader": 0, "name": "DEPT-03", "superior": 0}
        // 注意, 进入onSuccess之前就意味着服务端版本比客户端版本新, 因此要更新客户端主版本号
        console.log("onRecordSelectSuccess:", data);
        if (data.STATUS === 0) {
            setStore("mainCversion", data.DATA.cversion);
            setStore("subCversion", 0);
            const factor = 10 ** (data.DATA.waterScale || 0);
            // 水量数据需要转换计量单位, 百分比数据需要放大100倍
            const formObjects  = {
                name:        String(data.DATA.name),
                year:            String(data.DATA.year),
                serviceTypes:    data.DATA.serviceTypes || [],
                intakeTotal:     String(roundNearest(data.DATA.intakeTotal     / factor)),  // 总取水量
                intakeRegular:   String(roundNearest(data.DATA.intakeRegular   / factor)),  // 常规水源取水量
                intakeIrregular: String(roundNearest(data.DATA.intakeIrregular / factor)),  // 非常规水源取水量
                intakeTapWater:  String(roundNearest(data.DATA.intakeTapWater  / factor)),  // 自来水
                intakeSurface:   String(roundNearest(data.DATA.intakeSurface   / factor)),  // 地表水
                intakeGround:    String(roundNearest(data.DATA.intakeGround    / factor)),  // 地下水
                intakeSoft:      String(roundNearest(data.DATA.intakeSoft      / factor)),  // 外购软化水
                intakeSteam:     String(roundNearest(data.DATA.intakeSteam     / factor)),  // 外购蒸汽
                intakeBought:     String(roundNearest(data.DATA.intakeBought   / factor)),  // 外购水
                //intakeExt1:    String(roundNearest(data.DATA.intakeExt1      / factor)),  // 预留常规1
                //intakeExt2:    String(roundNearest(data.DATA.intakeExt2      / factor)),  // 预留常规2
                //intakeExt3:    String(roundNearest(data.DATA.intakeExt3      / factor)),  // 预留常规3
                //intakeExt4:    String(roundNearest(data.DATA.intakeExt4      / factor)),  // 预留常规4
                intakeSea:       String(roundNearest(data.DATA.intakeSea       / factor)),  // 海水
                intakeBrackish:  String(roundNearest(data.DATA.intakeBrackish  / factor)),  // 苦咸水
                intakeRecycled:  String(roundNearest(data.DATA.intakeRecycled  / factor)),  // 城镇污水再生水
                intakeMine:      String(roundNearest(data.DATA.intakeMine      / factor)),  // 矿井水
                intakeRain:      String(roundNearest(data.DATA.intakeRain      / factor)),  // 雨水
                //intakeExt11:   String(roundNearest(data.DATA.intakeExt11     / factor)),  // 预留非常规1
                //intakeExt12:   String(roundNearest(data.DATA.intakeExt12     / factor)),  // 预留非常规2
                //intakeExt13:   String(roundNearest(data.DATA.intakeExt13     / factor)),  // 预留非常规3
                //intakeExt14:   String(roundNearest(data.DATA.intakeExt14     / factor)),  // 预留非常规4
                rdccwv:          String(roundNearest(data.DATA.rdccwv          / factor)),  // 补水(直接冷却循环水)量
                riccwv:          String(roundNearest(data.DATA.riccwv          / factor)),  // 补水(间接冷却循环水)量
                rilwv:           String(roundNearest(data.DATA.rilwv           / factor)),  // 补水(生活)量
                ripwv:           String(roundNearest(data.DATA.ripwv           / factor)),  // 补水(进入产品)量
                riowv:           String(roundNearest(data.DATA.riowv           / factor)),  // 补水(其他)量
                scrv:            String(roundNearest(data.DATA.scrv            / factor)),  // 蒸汽冷凝水回用量
                rwv:             String(roundNearest(data.DATA.rwv             / factor)),  // 回用水量
                oswv:            String(roundNearest(data.DATA.oswv            / factor)),  // 其他串联水量
                spv:             String(roundNearest(data.DATA.spv             / factor)),  // 蒸汽产汽量
                dccwv:           String(roundNearest(data.DATA.dccwv           / factor)),  // 直接冷却循环水量
                iccwv:           String(roundNearest(data.DATA.iccwv           / factor)),  // 间接冷却循环水量
                ocwv:            String(roundNearest(data.DATA.ocwv            / factor)),  // 其它循环水量
                //cllr:          String(roundNearest(data.DATA.cllr            * 100   )),  // 常规漏损率%
                wcvf:            String(roundNearest(data.DATA.wcvf            * 100   )),  // 耗水量计算系数%
                inputWater:      String(roundNearest(data.DATA.inputWater      / factor)),  // 输入水量
                edv:             String(roundNearest(data.DATA.edv             / factor)),  // 外排水量
                lwv:             String(roundNearest(data.DATA.lwv             / factor)),  // 漏损水量
                wcv:             String(roundNearest(data.DATA.wcv             / factor)),  // 耗水量
                llr:             String(roundNearest(data.DATA.llr             * 100   )),  // 漏损率%
                //llrt:          String(roundNearest(data.DATA.llrt            * 100   )),  // 漏损率超标(＜4%)
                wcpup:           String(data.DATA.wcpup),                                   // 单位产品取水量, 字符串
                //wcpub:           String(data.DATA.wcpub),                                   // 单位建筑面积用水量, 字符串
                //wcpus:           String(data.DATA.wcpus),                                   // 服务业书独有字段, 单位服务用水量(取代excel中的单位出租床位用水量)
                pcdwc:           String(roundNearest(data.DATA.pcdwc)),                     // 人均生活用水量(m³/人·a), 单位不放缩
                reuseRate:       String(roundNearest(data.DATA.reuseRate       * 100   )),  // 重复利用率%
                dcwcr:           String(roundNearest(data.DATA.dcwcr           * 100   )),  // 直接冷却水循环率%
                icwcr:           String(roundNearest(data.DATA.icwcr           * 100   )),  // 间接冷却水循环率%
                scrr:            String(roundNearest(data.DATA.scrr            * 100   )),  // 蒸汽冷凝水回用率%
                wwrr:            String(roundNearest(data.DATA.wwrr            * 100   )),  // 废水回用率%
                cer:             String(roundNearest(data.DATA.cer             * 100   )),  // 达标排放率%
                uwrr:            String(roundNearest(data.DATA.uwrr            * 100   )),  // 非常规水替代率%
                //unitWaterUsage:  String(roundNearest(data.DATA.unitWaterUsage  / factor)),  // 服务业: 单位用水量
                //rcmd:      String(data.DATA.rcmd),
                // 服务业对应于运营情况统计表(WB_COMPANY_OPERATION_STATS)使用的考核指标
                rjqsY:           String(data.DATA.rjqsY),          // 人均取水量: 年度(m³/人·a)
                rjqsD:           String(data.DATA.rjqsD),          // 人均取水量: 每天(L/人·d)
                dwffSmmj:        String(data.DATA.dwffSmmj),       //  单位服务取水量: 单位水面面积(m³/m².a)
                dwffCwY:         String(data.DATA.dwffCwY),        //  单位服务取水量: 单位床位取水量(m³/床·a)
                dwffCwD:         String(data.DATA.dwffCwD),        //  单位服务取水量: 单位床位取水量(L/床·d)
                dwffRs:          String(data.DATA.dwffRs),         //  单位服务取水量: 单位人数取水量L/(人·次)
                avgWaterUsage:   String(data.DATA.avgWaterUsage),  //  人均用水量(L/人·d)
                avgBuildWater:   String(data.DATA.avgBuildWater),  //  单位建筑面积用水(m³/m²·a)
                unitWaterUsage:  String(data.DATA.unitWaterUsage), //  单位用水量(m³)
                others:      String(data.DATA.others),
                remarks:     String(data.DATA.remarks),
                waterScale:  String(data.DATA.waterScale || 0),
            };
            reset(formObjects);           // 重置react-form

            const storeObjects = checkPermits() && {
                ...formObjects,
                //waterType:  radioIdToObject(waterTypeStateDataProviderRef.current,      data.DATA.waterType),
                serviceTypes: checkIdsToObject(serviceTypesStateDataProviderRef.current,  data.DATA.serviceTypes),
                waterScale:   radioIdToObject(waterUnitScaleStateDataProviderRef.current, data.DATA.waterScale),
            };
            storeObjects && setStoreObject(storeObjects, false); // 判断用户权限, 把服务端数据写到mmkv存储, 只有可写用户才需要本地存储, 不会首先清空老数据

            // 设置selector菜单状态
            setServiceTypesCheckState(storeObjects.serviceTypes);
            setWaterUnitScaleState(storeObjects.waterScale);
            //setWaterTypeRadioState(radioIdToObject(waterTypeStateDataProviderRef.current, data.DATA.waterType));
            //setMainUseRadioState(radioIdToObject(mainUseStateDataProviderRef.current, data.DATA.mainUse));

            waterUnitScaleRef.current = data.DATA.waterScale || 0; // 根据服务端数据更新
            // 设置屏幕标题
            (screenTitle !== formObjects.name) && setScreenTitle(formObjects.name);
        } else  {
            console.log("STATUS !== 0", data.STATUS);
            setSnackBarMessage(`表单数据拉取发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        }
    };
    const onRecordSelectError = (err) => {
        console.log("Record selecting error:", err);
    };
    const onRecordSelectSettled = (err) => {
        loadingIndicatorVisible && setLoadingIndicatorVisible(false);
    };
    const recordSelectQuery = recordSelectQueryMaker([queryKwd, "get", recordPubid], onRecordSelectSuccess, onRecordSelectError, onRecordSelectSettled);


    // 新组件需要重新定义setDiagText!!
    const onHandleSubmit = (data) => {
        const factor = 10 ** Number(waterUnitScaleRef.current);
        setOkButtonDisabled(true);
        setOkButtonLoading(true);
        dataFeeder({
            ...data,
            name: data.year,
            cversion: getClientCversion.current(),

            intakeTotal:     roundNearest(data.intakeTotal     * factor),  // 总取水量
            intakeRegular:   roundNearest(data.intakeRegular   * factor),  // 常规水源取水量
            intakeIrregular: roundNearest(data.intakeIrregular * factor),  // 非常规水源取水量
            intakeTapWater:  roundNearest(data.intakeTapWater  * factor),  // 自来水
            intakeSurface:   roundNearest(data.intakeSurface   * factor),  // 地表水
            intakeGround:    roundNearest(data.intakeGround    * factor),  // 地下水
            intakeSoft:      roundNearest(data.intakeSoft      * factor),  // 外购软化水
            intakeSteam:     roundNearest(data.intakeSteam     * factor),  // 外购蒸汽
            intakeBought:    roundNearest(data.intakeBought    * factor),  // 外购水
            //intakeExt1:    roundNearest(data.intakeExt1      * factor),  // 预留常规1
            //intakeExt2:    roundNearest(data.intakeExt2      * factor),  // 预留常规2
            //intakeExt3:    roundNearest(data.intakeExt3      * factor),  // 预留常规3
            //intakeExt4:    roundNearest(data.intakeExt4      * factor),  // 预留常规4
            intakeSea:       roundNearest(data.intakeSea       * factor),  // 海水
            intakeBrackish:  roundNearest(data.intakeBrackish  * factor),  // 苦咸水
            intakeRecycled:  roundNearest(data.intakeRecycled  * factor),  // 城镇污水再生水
            intakeMine:      roundNearest(data.intakeMine      * factor),  // 矿井水
            intakeRain:      roundNearest(data.intakeRain      * factor),  // 雨水
            //intakeExt11:   roundNearest(data.intakeExt11     * factor),  // 预留非常规1
            //intakeExt12:   roundNearest(data.intakeExt12     * factor),  // 预留非常规2
            //intakeExt13:   roundNearest(data.intakeExt13     * factor),  // 预留非常规3
            //intakeExt14:   roundNearest(data.intakeExt14     * factor),  // 预留非常规4
            rdccwv:          roundNearest(data.rdccwv          * factor),  // 补水(直接冷却循环水)量
            riccwv:          roundNearest(data.riccwv          * factor),  // 补水(间接冷却循环水)量
            rilwv:           roundNearest(data.rilwv           * factor),  // 补水(生活)量
            ripwv:           roundNearest(data.ripwv           * factor),  // 补水(进入产品)量
            riowv:           roundNearest(data.riowv           * factor),  // 补水(其他)量
            scrv:            roundNearest(data.scrv            * factor),  // 蒸汽冷凝水回用量
            rwv:             roundNearest(data.rwv             * factor),  // 回用水量
            oswv:            roundNearest(data.oswv            * factor),  // 其他串联水量
            spv:             roundNearest(data.spv             * factor),  // 蒸汽产汽量
            dccwv:           roundNearest(data.dccwv           * factor),  // 直接冷却循环水量
            iccwv:           roundNearest(data.iccwv           * factor),  // 间接冷却循环水量
            ocwv:            roundNearest(data.ocwv            * factor),  // 其它循环水量
            //cllr:          roundNearest(data.cllr            / 100   ),  // 常规漏损率%
            wcvf:            roundNearest(data.wcvf            / 100   ),  // 耗水量计算系数%
            inputWater:      roundNearest(data.inputWater      * factor),  // 输入水量
            edv:             roundNearest(data.edv             * factor),  // 外排水量
            lwv:             roundNearest(data.lwv             * factor),  // 漏损水量
            wcv:             roundNearest(data.wcv             * factor),  // 耗水量
            llr:             roundNearest(data.llr             / 100   ),  // 漏损率%
            //llrt:          roundNearest(data.llrt            / 100   ),  // 漏损率超标(＜4%)
            //wcpup:         roundNearest(data.wcpup           / 100   ),  // 单位产品取水量, 字符串
            //wcpub:         roundNearest(data.wcpub           / 100   ),  // 单位建筑面积取水量, 字符串
            //pcdwc:         roundNearest(data.pcdwc),                     // 人均生活用水量(m³/人·a)
            reuseRate:       roundNearest(data.reuseRate       / 100   ),  // 重复利用率%
            dcwcr:           roundNearest(data.dcwcr           / 100   ),  // 直接冷却水循环率%
            icwcr:           roundNearest(data.icwcr           / 100   ),  // 间接冷却水循环率%
            scrr:            roundNearest(data.scrr            / 100   ),  // 蒸汽冷凝水回用率%
            wwrr:            roundNearest(data.wwrr            / 100   ),  // 废水回用率%
            cer:             roundNearest(data.cer             / 100   ),  // 达标排放率%
            uwrr:            roundNearest(data.uwrr            / 100   ),  // 非常规水替代率%
            //unitWaterUsage:  roundNearest(data.unitWaterUsage  * factor),  // 服务业: 单位用水量
        }); // append client cversion
        recordUpdateQuery.mutate();
    };

    // 新组件不需改动
    const onRecordUpdateSuccess = (data) => {
        console.log("response success, data: ", data);
        if(data.STATUS !== 0) {
            setSnackBarMessage(`表单更新发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        } else {
            // 设置确认框数据
            setConfirmDialogConfig({
                title: "更新表单",
                text: "表单信息更新成功!",
                okLabel: "确定",
                onOK: () => {
                    setShowConfirm(false);
                    recordSelectQuery.mutate();
                }});
            setShowConfirm(true);
        }
    };
    const onRecordUpdateError = (error) => {
        setSnackBarMessage(`表单更新发生错误, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const onRecordUpdateSettled = (data, error) => {
        setOkButtonDisabled(false);
        setOkButtonLoading(false);
    };
    const recordUpdateQuery = recordUpdateQueryMaker(dataFeeder, [queryKwd, "upd", recordPubid], onRecordUpdateSuccess, onRecordUpdateError, onRecordUpdateSettled);

    const recordDeleteOnSuccess = () => {
        clearStore();
        navigation.goBack();
    };
    const recordDeleteOnError = (error) => {
        setSnackBarMessage(`表单删除失败, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const recordDeleteQuery = recordDeleteQueryMaker([queryKwd, "del", recordPubid], recordDeleteOnSuccess, recordDeleteOnError);

    const deleteAlert = () => {
        return (
            Alert.alert("删除表单", `删除后不可撤销, 请确认是否删除: ${delQueryInfo.current?.name}?`, [
                { text: "取消" },
                { text: "确定", onPress: () => recordDeleteQuery.mutate() },
            ])
        );
    };

    const clearCache = () => {
        return (
            Alert.alert("清空本地缓存", "1. 用于未知原因导致数据无法提交的情况\r\n2. 未提交的内容将会丢失, 请做好数据截图备份\r\n3. 已提交到服务端的数据不受影响\r\n4. 确认清空请点击“确认”按钮", [
                { text: "取消" },
                { text: "确定", onPress: () => { clearStore(); navigation.goBack(); } },
            ])
        );
    };


    // Hook: Auto calc water volumn sumups
    const intakeTapWater = Number(useWatch({ control, name: "intakeTapWater"}));  // 自来水
    const intakeSurface  = Number(useWatch({ control, name: "intakeSurface" }));  // 地表水
    const intakeGround   = Number(useWatch({ control, name: "intakeGround"  }));  // 地下水
    const intakeSoft     = Number(useWatch({ control, name: "intakeSoft"    }));  // 外购软化水
    const intakeSteam    = Number(useWatch({ control, name: "intakeSteam"   }));  // 外购蒸汽
    const intakeBought   = Number(useWatch({ control, name: "intakeBought"  }));  // 外购水
    //const intakeExt1   = Number(useWatch({ control, name: "intakeExt1"    }));  // 预留常规1
    //const intakeExt2   = Number(useWatch({ control, name: "intakeExt2"    }));  // 预留常规2
    //const intakeExt3   = Number(useWatch({ control, name: "intakeExt3"    }));  // 预留常规3
    //const intakeExt4   = Number(useWatch({ control, name: "intakeExt4"    }));  // 预留常规4
    const intakeSea      = Number(useWatch({ control, name: "intakeSea"     }));  // 海水
    const intakeBrackish = Number(useWatch({ control, name: "intakeBrackish"}));  // 苦咸水
    const intakeRecycled = Number(useWatch({ control, name: "intakeRecycled"}));  // 城镇污水再生水
    const intakeMine     = Number(useWatch({ control, name: "intakeMine"    }));  // 矿井水
    const intakeRain     = Number(useWatch({ control, name: "intakeRain"    }));  // 雨水
    //const intakeExt11  = Number(useWatch({ control, name: "intakeExt11"   }));  // 预留非常规1
    //const intakeExt12  = Number(useWatch({ control, name: "intakeExt12"   }));  // 预留非常规2
    //const intakeExt13  = Number(useWatch({ control, name: "intakeExt13"   }));  // 预留非常规3
    //const intakeExt14  = Number(useWatch({ control, name: "intakeExt14"   }));  // 预留非常规4
    const rdccwv         = Number(useWatch({ control, name: "rdccwv"        }));  // 补水(直接冷却循环水)量
    const riccwv         = Number(useWatch({ control, name: "riccwv"        }));  // 补水(间接冷却循环水)量
    const rilwv          = Number(useWatch({ control, name: "rilwv"         }));  // 补水(生活)量
    const ripwv          = Number(useWatch({ control, name: "ripwv"         }));  // 补水(进入产品)量
    const riowv          = Number(useWatch({ control, name: "riowv"         }));  // 补水(其他)量
    const scrv           = Number(useWatch({ control, name: "scrv"          }));  // 蒸汽冷凝水回用量
    const rwv            = Number(useWatch({ control, name: "rwv"           }));  // 回用水量
    const oswv           = Number(useWatch({ control, name: "oswv"          }));  // 其他串联水量
    const spv            = Number(useWatch({ control, name: "spv"           }));  // 蒸汽产汽量
    const dccwv          = Number(useWatch({ control, name: "dccwv"         }));  // 直接冷却循环水量
    const iccwv          = Number(useWatch({ control, name: "iccwv"         }));  // 间接冷却循环水量
    const ocwv           = Number(useWatch({ control, name: "ocwv"          }));  // 其它循环水量
    //const cllr         = Number(useWatch({ control, name: "cllr"          }));  // 常规漏损率%
    const llr            = Number(useWatch({ control, name: "llr"           }));  // 漏损率%
    const wcvf           = Number(useWatch({ control, name: "wcvf"          }));  // 耗水量计算系数%

    // 注意：各种率还未转换，需要参考其它屏幕，在数据库是是否存放百分比
    const updateGlobalState = debounce((intakeTapWater, intakeSurface, intakeGround, intakeSoft, intakeSteam, intakeBought, intakeSea, intakeBrackish, intakeRecycled, intakeMine, intakeRain, rdccwv, riccwv, rilwv, ripwv, riowv, scrv, rwv, oswv, spv, dccwv, iccwv, ocwv, llr, wcvf) => {
        const intakeRegular   = intakeTapWater + intakeSurface + intakeGround + intakeSoft + intakeSteam + intakeBought; // 常规水源取水量
        const intakeIrregular = intakeSea + intakeBrackish + intakeRecycled + intakeMine + intakeRain;    // 非常规水源取水量
        const intakeTotal     = intakeRegular + intakeIrregular;                                          // 总取水量
        const inputWater      = intakeTotal + scrv + rwv + oswv;                                          // 输入水量
        // 耗水量 = 取水量 * 耗水量计算系数
        const wcv             = intakeTotal * wcvf / 100;          // 耗水量
        //const llr             = cllr;                            // 漏损率
        const lwv             = intakeRegular * (llr / 100) ;      // 漏损水量
        // 外排水量 = 取水量 - 耗水量 - 漏损水量
        const edv             = intakeRegular + intakeIrregular - wcv - lwv;         // 外排水量
        //const llrt            = (llr - cllr) / 100;              // 漏损率超标(＜4%)
        // 非常规水替代率 = 非常规水取水量 / (常规水取水量 + 非常规取水量)
        const uwrr            = (intakeRegular + intakeIrregular) === 0 ? 0 : intakeIrregular / (intakeRegular + intakeIrregular); // 非常规水替代率
        const reuseRate       = (intakeTotal + dccwv + iccwv + ocwv + scrv + rwv + oswv) === 0 ? 0 : (dccwv + iccwv + ocwv + scrv + rwv + oswv) / (intakeTotal + dccwv + iccwv + ocwv + scrv + rwv + oswv); // 重复利用率
        const dcwcr           = (dccwv + rdccwv) === 0 ? 0 : dccwv / (dccwv + rdccwv); // 直接冷却水循环率
        const icwcr           = (iccwv + riccwv) === 0 ? 0 : iccwv / (iccwv + riccwv); // 间接冷却水循环率
        const scrr            = spv === 0 ? 0 : scrv  / spv;                           // 蒸汽冷凝水回用率
        const wwrr            = (rwv + edv) === 0 ? 0 : rwv   / (rwv + edv);           // 废水回用率
        const cer             = 1;                                                     // 达标排放率, 固定100%

        setValue("intakeRegular",   `${roundNearestTo(intakeRegular,   4)}`);
        setValue("intakeIrregular", `${roundNearestTo(intakeIrregular, 4)}`);
        setValue("intakeTotal",     `${roundNearestTo(intakeTotal,     4)}`);
        setValue("inputWater",      `${roundNearestTo(inputWater,      4)}`);
        setValue("wcv",             `${roundNearestTo(wcv,             4)}`);
        //setValue("llr",           `${roundNearestTo(llr,             4)}`);
        setValue("lwv",             `${roundNearestTo(lwv,             4)}`);
        setValue("edv",             `${roundNearestTo(edv,             4)}`);
        //setValue("llrt",          `${roundPercent(llrt,              2)}`);
        setValue("uwrr",            `${roundPercent(uwrr,              2)}`);
        setValue("reuseRate",       `${roundPercent(reuseRate,         2)}`);
        setValue("dcwcr",           `${roundPercent(dcwcr,             2)}`);
        setValue("icwcr",           `${roundPercent(icwcr,             2)}`);
        setValue("scrr",            `${roundPercent(scrr,              2)}`);
        setValue("wwrr",            `${roundPercent(wwrr,              2)}`);
        setValue("cer",             `${roundPercent(cer,               2)}`);
        setStore("intakeRegular",   getValues("intakeRegular"));
        setStore("intakeIrregular", getValues("intakeIrregular"));
        setStore("intakeTotal",     getValues("intakeTotal"));
        setStore("inputWater",      getValues("inputWater"));
        setStore("wcv",             getValues("wcv",));
        //setStore("llr",           getValues("llr",));
        setStore("lwv",             getValues("lwv",));
        setStore("edv",             getValues("edv",));
        //setStore("llrt",          getValues("llrt",));
        setStore("uwrr",            getValues("uwrr",));
        setStore("reuseRate",       getValues("reuseRate"));
        setStore("dcwcr",           getValues("dcwcr",));
        setStore("icwcr",           getValues("icwcr",));
        setStore("scrr",            getValues("scrr",));
        setStore("wwrr",            getValues("wwrr",));
        setStore("cer",             getValues("cer",));

        subCversionRef.current++;
    }, 100);

    // 注意, 这个useEffect必需在恢复本地存储之前运行, 否则初始化时不会触发
    useEffect(() => {
        updateGlobalState(intakeTapWater, intakeSurface, intakeGround, intakeSoft, intakeSteam, intakeBought, intakeSea, intakeBrackish, intakeRecycled, intakeMine, intakeRain, rdccwv, riccwv, rilwv, ripwv, riowv, scrv, rwv, oswv, spv, dccwv, iccwv, ocwv, llr, wcvf);
    }, [intakeTapWater, intakeSurface, intakeGround, intakeSoft, intakeSteam, intakeBought, intakeSea, intakeBrackish, intakeRecycled, intakeMine, intakeRain, rdccwv, riccwv, rilwv, ripwv, riowv, scrv, rwv, oswv, spv, dccwv, iccwv, ocwv, llr, wcvf]);

    // Hook: onMount fetch data
    useEffect(() => {
        // 优化方案: 根据用户权限进行mutate, 只有可写用户才需要本地存储
        const localCversion = getClientCversion.current();
        const remoteCversion = route.params.pageMeta.cversion;
        console.log("localCversion:", localCversion, "remoteSversion:", remoteCversion, "subCversionRef:", subCversionRef.current);
        // 还起着新表单记录的初始化作用
        const restoreLocalData = () => {
            const formObjects = { // 默认值用户表单的数据显示
                name:            storedValueToFormValue.current("name"),
                serviceTypes:    storedValueToFormValue.current("serviceTypes",    ""),   // 这里需要默认为空串, 让Joi验证器去填充默认值, 否则可能报错喊拉取数据
                year:            storedValueToFormValue.current("year"),
                intakeTotal:     storedValueToFormValue.current("intakeTotal",     "0"),  // 总取水量
                intakeRegular:   storedValueToFormValue.current("intakeRegular",   "0"),  // 常规水源取水量
                intakeIrregular: storedValueToFormValue.current("intakeIrregular", "0"),  // 非常规水源取水量
                intakeTapWater:  storedValueToFormValue.current("intakeTapWater",  "0"),  // 自来水
                intakeSurface:   storedValueToFormValue.current("intakeSurface",   "0"),  // 地表水
                intakeGround:    storedValueToFormValue.current("intakeGround",    "0"),  // 地下水
                intakeSoft:      storedValueToFormValue.current("intakeSoft",      "0"),  // 外购软化水
                intakeSteam:     storedValueToFormValue.current("intakeSteam",     "0"),  // 外购蒸汽
                intakeBought:    storedValueToFormValue.current("intakeBought",    "0"),  // 外购水
                //intakeExt1:      storedValueToFormValue.current("intakeExt1",    "0"),  // 预留常规1
                //intakeExt2:      storedValueToFormValue.current("intakeExt2",    "0"),  // 预留常规2
                //intakeExt3:      storedValueToFormValue.current("intakeExt3",    "0"),  // 预留常规3
                //intakeExt4:      storedValueToFormValue.current("intakeExt4",    "0"),  // 预留常规4
                intakeSea:       storedValueToFormValue.current("intakeSea",       "0"),  // 海水
                intakeBrackish:  storedValueToFormValue.current("intakeBrackish",  "0"),  // 苦咸水
                intakeRecycled:  storedValueToFormValue.current("intakeRecycled",  "0"),  // 城镇污水再生水
                intakeMine:      storedValueToFormValue.current("intakeMine",      "0"),  // 矿井水
                intakeRain:      storedValueToFormValue.current("intakeRain",      "0"),  // 雨水
                //intakeExt11:     storedValueToFormValue.current("intakeExt11",   "0"),  // 预留非常规1
                //intakeExt12:     storedValueToFormValue.current("intakeExt12",   "0"),  // 预留非常规2
                //intakeExt13:     storedValueToFormValue.current("intakeExt13",   "0"),  // 预留非常规3
                //intakeExt14:     storedValueToFormValue.current("intakeExt14",   "0"),  // 预留非常规4
                rdccwv:          storedValueToFormValue.current("rdccwv",          "0"),  // 补水(直接冷却循环水)量
                riccwv:          storedValueToFormValue.current("riccwv",          "0"),  // 补水(间接冷却循环水)量
                rilwv:           storedValueToFormValue.current("rilwv",           "0"),  // 补水(生活)量
                ripwv:           storedValueToFormValue.current("ripwv",           "0"),  // 补水(进入产品)量
                riowv:           storedValueToFormValue.current("riowv",           "0"),  // 补水(其他)量
                scrv:            storedValueToFormValue.current("scrv",            "0"),  // 蒸汽冷凝水回用量
                rwv:             storedValueToFormValue.current("rwv",             "0"),  // 回用水量
                oswv:            storedValueToFormValue.current("oswv",            "0"),  // 其他串联水量
                spv:             storedValueToFormValue.current("spv",             "0"),  // 蒸汽产汽量
                dccwv:           storedValueToFormValue.current("dccwv",           "0"),  // 直接冷却循环水量
                iccwv:           storedValueToFormValue.current("iccwv",           "0"),  // 间接冷却循环水量
                ocwv:            storedValueToFormValue.current("ocwv",            "0"),  // 其它循环水量
                //cllr:          storedValueToFormValue.current("cllr",            "0"),  // 常规漏损率%
                wcvf:            storedValueToFormValue.current("wcvf",            "0"),  // 耗水量计算系数%
                inputWater:      storedValueToFormValue.current("inputWater",      "0"),  // 输入水量
                edv:             storedValueToFormValue.current("edv",             "0"),  // 外排水量
                lwv:             storedValueToFormValue.current("lwv",             "0"),  // 漏损水量
                wcv:             storedValueToFormValue.current("wcv",             "0"),  // 耗水量
                llr:             storedValueToFormValue.current("llr",             "0"),  // 漏损率%
                //llrt:          storedValueToFormValue.current("llrt",            "0"),  // 漏损率超标(＜4%)
                wcpup:           storedValueToFormValue.current("wcpup",           ""),   // 单位产品取水量, 字符串
                //wcpub:           storedValueToFormValue.current("wcpub",           ""),   // 单位建筑面积用水量, 字符串
                //wcpus:           storedValueToFormValue.current("wcpus",           ""),   // 服务业书独有字段, 单位服务用水量(取代excel中的单位出租床位用水量), 字符串
                pcdwc:           storedValueToFormValue.current("pcdwc",           "0"),  // 人均生活用水量(m³/人·a)
                reuseRate:       storedValueToFormValue.current("reuseRate",       "0"),  // 重复利用率%
                dcwcr:           storedValueToFormValue.current("dcwcr",           "0"),  // 直接冷却水循环率%
                icwcr:           storedValueToFormValue.current("icwcr",           "0"),  // 间接冷却水循环率%
                scrr:            storedValueToFormValue.current("scrr",            "0"),  // 蒸汽冷凝水回用率%
                wwrr:            storedValueToFormValue.current("wwrr",            "0"),  // 废水回用率%
                cer:             storedValueToFormValue.current("cer",             "0"),  // 达标排放率%
                uwrr:            storedValueToFormValue.current("uwrr",            "0"),  // 非常规水替代率%
                // 服务业对应于运营情况统计表(WB_COMPANY_OPERATION_STATS)使用的考核指标
                rjqsY:           storedValueToFormValue.current("rjqsY",          "0"), // 人均取水量: 年度(m³/人·a)
                rjqsD:           storedValueToFormValue.current("rjqsD",          "0"), // 人均取水量: 每天(L/人·d)
                dwffSmmj:        storedValueToFormValue.current("dwffSmmj",       "0"), //  单位服务取水量: 单位水面面积(m³/m².a)
                dwffCwY:         storedValueToFormValue.current("dwffCwY",        "0"), //  单位服务取水量: 单位床位取水量(m³/床·a)
                dwffCwD:         storedValueToFormValue.current("dwffCwD",        "0"), //  单位服务取水量: 单位床位取水量(L/床·d)
                dwffRs:          storedValueToFormValue.current("dwffRs",         "0"), //  单位服务取水量: 单位人数取水量L/(人·次)
                avgWaterUsage:   storedValueToFormValue.current("avgWaterUsage",  "0"), //  人均用水量(L/人·d)
                avgBuildWater:   storedValueToFormValue.current("avgBuildWater",  "0"), //  单位建筑面积用水(m³/m²·a)
                unitWaterUsage:  storedValueToFormValue.current("unitWaterUsage", "0"), //  单位用水量(m³)
                //rcmd:          storedValueToFormValue.current("rcmd"),
                others:          storedValueToFormValue.current("others"),
                remarks:         storedValueToFormValue.current("remarks"),
                waterScale:      storedValueToFormValue.current("waterScale", "0"),
            };
            reset(formObjects); // 重置react-form数据

            // 设置selector数据
            const defaultWaterUnitScale = 0;
            console.log("stored service types:", getStore("serviceTypes"));
            setWaterUnitScaleState(ifTruthLet(getStore("waterScale"),      isNullness, () => {return {id: 0, name: ""};}, value => value));
            setServiceTypesCheckState(ifTruthLet(getStore("serviceTypes"), isNullness, () => {return [];},                value => value));
            //setWaterTypeRadioState(getStore("waterType"));
            //setMainUseRadioState(getStore("mainUse"));

            waterUnitScaleRef.current = getStore("waterScale")?.id || defaultWaterUnitScale; // 根据本地存储更新
        };

        if (formType === 1) {
            recordSelectQuery.mutate();
        } else if (localCversion === 0 && remoteCversion > 0) {
            console.log("Local cversion === 0, serve may have something nontrivial initialized, query mutate!");
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        } else if (isServerDataNewer(localCversion, remoteCversion) && subCversionRef.current > 0) { // 服务端有更新数据, 但本地有修改
            console.log("Client cversion fell behind the server cversion, someone might modified the database, choose to use the data version!");
            Alert.alert("注意", "由于其他人向服务器提交了数据, 服务端数据比本地数据新, 请选择使用哪份数据?", [
                { text: "使用本地数据", onPress: () => { setStore("mainCversion", remoteCversion); restoreLocalData(); } },
                { text: "使用服务端数据", onPress: () => { setLoadingIndicatorVisible(true); recordSelectQuery.mutate(); } },
            ]);
        } else if (isServerDataNewer(localCversion, remoteCversion)) { // localCversion==0时要mutate是因为服务端可能在创建记录时生成了必要数据
            console.log("Server cversion may be newer, query mutate:", localCversion, remoteCversion);
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        }
        else {
            console.log("Local cversion >= server cversion, query will not mutate!");
            restoreLocalData();
        }
    }, []);

    // Hook: refresh data by unit changing
    useEffect(() => {
        //if (waterUnitScaleState.id !== undefined && waterUnitScaleRef.current !== waterUnitScaleState.id) { // waterUnitScaleState may not be updated in realtime
        if (unitToggleState === true ) {
            const oldFactor = 10 ** Number(waterUnitScaleRef.current);
            const newFactor = 10 ** Number(waterUnitScaleState.id);
            const changedFields = { // 默认值用户表单的数据显示

                intakeTotal:     String(roundNearest(getValues("intakeTotal")     * oldFactor / newFactor)), // 总取水量
                intakeRegular:   String(roundNearest(getValues("intakeRegular")   * oldFactor / newFactor)), // 常规水源取水量
                intakeIrregular: String(roundNearest(getValues("intakeIrregular") * oldFactor / newFactor)), // 非常规水源取水
                intakeTapWater:  String(roundNearest(getValues("intakeTapWater")  * oldFactor / newFactor)), // 自来水
                intakeSurface:   String(roundNearest(getValues("intakeSurface")   * oldFactor / newFactor)), // 地表水
                intakeGround:    String(roundNearest(getValues("intakeGround")    * oldFactor / newFactor)), // 地下水
                intakeSoft:      String(roundNearest(getValues("intakeSoft")      * oldFactor / newFactor)), // 外购软化水
                intakeSteam:     String(roundNearest(getValues("intakeSteam")     * oldFactor / newFactor)), // 外购蒸汽
                intakeBought:    String(roundNearest(getValues("intakeBought")    * oldFactor / newFactor)), // 外购水
                //intakeExt1:    String(roundNearest(getValues("intakeExt1")      * oldFactor / newFactor)), // 预留常规1
                //intakeExt2:    String(roundNearest(getValues("intakeExt2")      * oldFactor / newFactor)), // 预留常规2
                //intakeExt3:    String(roundNearest(getValues("intakeExt3")      * oldFactor / newFactor)), // 预留常规3
                //intakeExt4:    String(roundNearest(getValues("intakeExt4")      * oldFactor / newFactor)), // 预留常规4
                intakeSea:       String(roundNearest(getValues("intakeSea")       * oldFactor / newFactor)), // 海水
                intakeBrackish:  String(roundNearest(getValues("intakeBrackish")  * oldFactor / newFactor)), // 苦咸水
                intakeRecycled:  String(roundNearest(getValues("intakeRecycled")  * oldFactor / newFactor)), // 城镇污水再生水
                intakeMine:      String(roundNearest(getValues("intakeMine")      * oldFactor / newFactor)), // 矿井水
                intakeRain:      String(roundNearest(getValues("intakeRain")      * oldFactor / newFactor)), // 雨水
                //intakeExt11:   String(roundNearest(getValues("intakeExt11")     * oldFactor / newFactor)), // 预留非常规1
                //intakeExt12:   String(roundNearest(getValues("intakeExt12")     * oldFactor / newFactor)), // 预留非常规2
                //intakeExt13:   String(roundNearest(getValues("intakeExt13")     * oldFactor / newFactor)), // 预留非常规3
                //intakeExt14:   String(roundNearest(getValues("intakeExt14")     * oldFactor / newFactor)), // 预留非常规4
                rdccwv:          String(roundNearest(getValues("rdccwv")          * oldFactor / newFactor)), // 补水(直接冷却循环水)量
                riccwv:          String(roundNearest(getValues("riccwv")          * oldFactor / newFactor)), // 补水(间接冷却循环水)量
                rilwv:           String(roundNearest(getValues("rilwv")           * oldFactor / newFactor)), // 补水(生活)量
                ripwv:           String(roundNearest(getValues("ripwv")           * oldFactor / newFactor)), // 补水(进入产品)量
                riowv:           String(roundNearest(getValues("riowv")           * oldFactor / newFactor)), // 补水(其他)量
                scrv:            String(roundNearest(getValues("scrv")            * oldFactor / newFactor)), // 蒸汽冷凝水回用量
                rwv:             String(roundNearest(getValues("rwv")             * oldFactor / newFactor)), // 回用水量
                oswv:            String(roundNearest(getValues("oswv")            * oldFactor / newFactor)), // 其他串联水量
                spv:             String(roundNearest(getValues("spv")             * oldFactor / newFactor)), // 蒸汽产汽量
                dccwv:           String(roundNearest(getValues("dccwv")           * oldFactor / newFactor)), // 直接冷却循环水量
                iccwv:           String(roundNearest(getValues("iccwv")           * oldFactor / newFactor)), // 间接冷却循环水量
                ocwv:            String(roundNearest(getValues("ocwv")            * oldFactor / newFactor)), // 其它循环水量
                inputWater:      String(roundNearest(getValues("inputWater")      * oldFactor / newFactor)), // 输入水量
                edv:             String(roundNearest(getValues("edv")             * oldFactor / newFactor)), // 外排水量
                lwv:             String(roundNearest(getValues("lwv")             * oldFactor / newFactor)), // 漏损水量
                wcv:             String(roundNearest(getValues("wcv")             * oldFactor / newFactor)), // 耗水量

            };
            for (const [key, val] of Object.entries(changedFields)) {
                setValue(key, val); // set react hook form
                setStore(key, val); // set mmkv
            }

            waterUnitScaleRef.current = waterUnitScaleState.id || 0;
            setUnitToggleState(false);
        }
    }, [unitToggleState]);

    const refreshUnits = () => { setUnitToggleState(true); };
    const calcUnit = (unit, unitStatObj = waterUnitScaleState) => {
        if (unitStatObj.id === 0) { return unit; }
        else if (unitStatObj.id === 4) { return "万" + unit; }
        else if (unitStatObj.id === 3) { return "千" + unit; }
        else if (unitStatObj.id === 2) { return "百" + unit; }
        else if (unitStatObj.id === 1) { return "十" + unit; }
        else { /*log.warn("Invalid unit scale: %s, id: %s", unit, unitStatObj.id);*/ return unit; }
    };
    const FieldsConfig_1_book = {
        industry: [
            {
                inputs: [
                //{ name: "name",        label: "表单名称",       unit: "",               type: "PLAIN", editable: true, multiline: true, },
                    { name: "year",        label: "年份",    unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "waterScale",  label: "水量单位", unit: "",  type: "RADIO", editable: true, placeholder: "", selectorState: waterUnitScaleState,  setSelectorState: setWaterUnitScaleState, dataProvider: waterUnitScaleEnum, cb: refreshUnits },
                ]
            },
        ],
        service: [
            {
                inputs: [
                //{ name: "name",        label: "表单名称",       unit: "",               type: "PLAIN", editable: true, multiline: true, },
                    { name: "year",        label: "年份",    unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "serviceTypes", label: "服务类型",    unit: "",  type: "CHECK", editable: true, placeholder: "", selectorState: serviceTypesCheckState,  setSelectorState: setServiceTypesCheckState, dataProvider: serviceTypesStateDataProviderRef, },
                    { name: "waterScale",  label: "水量单位", unit: "",  type: "RADIO", editable: true, placeholder: "", selectorState: waterUnitScaleState,  setSelectorState: setWaterUnitScaleState, dataProvider: waterUnitScaleEnum, cb: refreshUnits },
                ]
            },
        ],
    };
    const FieldsConfig_1_table = FieldsConfig_1_book;

    const FieldsConfig_2_book = {
        industry: [
            {
                nodeType: "Section", title: "取水量", nodes: [
                    {
                        inputs: [
                            { name: "intakeRegular",   label: "常规水源取水量合计",   unit: calcUnit("m³"), type: "PLAIN", editable: false, placeholder: "", },
                            { name: "intakeIrregular", label: "非常规水源取水量合计", unit: calcUnit("m³"), type: "PLAIN", editable: false, placeholder: "", },
                        ]
                    },
                    {
                        nodeType: "Accordion", title: "常规水源取水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "intakeTapWater", label: "自来水",    unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeSurface",  label: "地表水",    unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeGround",   label: "地下水",    unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeSoft",     label: "外购软化水", unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeSteam",    label: "外购蒸汽",   unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeBought",   label: "外购水",    unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },
                    {
                        nodeType: "Accordion", title: "非常规水源取水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "intakeSea",      label: "海水",         unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeBrackish", label: "苦咸水",        unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeRecycled", label: "城镇污水再生水", unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeMine",     label: "矿井水",        unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeRain",     label: "雨水",         unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },
                ],
            },
            {
                inputs: [
                    { name: "rdccwv",     label: "补水(直接冷却循环水)量", unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "riccwv",     label: "补水(间接冷却循环水)量", unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "rilwv",      label: "补水(生活)量",         unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "ripwv",      label: "补水(进入产品)量",      unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "riowv",      label: "补水(其他)量",         unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "scrv",       label: "蒸汽冷凝水回用量",      unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "rwv",        label: "回用水量",             unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "oswv",       label: "其他串联水量",          unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "spv",        label: "蒸汽产汽量",           unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "dccwv",      label: "直接冷却循环水量",      unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "iccwv",      label: "间接冷却循环水量",      unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "ocwv",       label: "其它循环水量",          unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "llr",        label: "漏损率",              unit: "%",              type: "PLAIN", editable: true, placeholder: "", },
                    //{ name: "cllr",     label: "常规漏损率",           unit: "%",             type: "PLAIN", editable: true, placeholder: "", },
                    { name: "wcvf",       label: "耗水量计算系数",       unit: "%",             type: "PLAIN", editable: true, placeholder: "", },
                    // 以下字段的结果自动生成
                    { name: "wcpup",      label: "单位产品取水量",       unit: "m³",             type: "PLAIN", editable: false, placeholder: "提交后自动计算", props: {multiline: true}, },
                    { name: "inputWater", label: "输入水量",            unit: calcUnit("m³"),   type: "PLAIN", editable: false, placeholder: "", },
                    { name: "edv",        label: "外排水量",            unit: calcUnit("m³"),   type: "PLAIN", editable: false, placeholder: "", },
                    { name: "lwv",        label: "漏损水量",            unit: calcUnit("m³"),   type: "PLAIN", editable: false, placeholder: "", },
                    { name: "wcv",        label: "耗水量",              unit: calcUnit("m³"),   type: "PLAIN", editable: false, placeholder: "", },
                    //{ name: "llrt",     label: "漏损率超标(<4%)",     unit: "%",              type: "PLAIN", editable: false, placeholder: "", },
                    { name: "reuseRate",  label: "重复利用率",          unit: "%",              type: "PLAIN", editable: false, placeholder: "", },
                    { name: "dcwcr",      label: "直接冷却水循环率",    unit: "%",               type: "PLAIN", editable: false, placeholder: "", },
                    { name: "icwcr",      label: "间接冷却水循环率",    unit: "%",               type: "PLAIN", editable: false, placeholder: "", },
                    { name: "scrr",       label: "蒸汽冷凝水回用率",    unit: "%",               type: "PLAIN", editable: false, placeholder: "", },
                    { name: "wwrr",       label: "废水回用率",          unit: "%",              type: "PLAIN", editable: false, placeholder: "", },
                    { name: "cer",        label: "达标排放率",          unit: "%",              type: "PLAIN", editable: false, placeholder: "", },
                    { name: "uwrr",       label: "非常规水替代率",      unit: "%",               type: "PLAIN", editable: false, placeholder: "", },

                ]
            },
        ],
        service: [
            {
                nodeType: "Section", title: "取水量", nodes: [
                    {
                        inputs: [
                            { name: "intakeRegular",   label: "常规水源取水量合计",   unit: calcUnit("m³"), type: "PLAIN", editable: false, placeholder: "", },
                            { name: "intakeIrregular", label: "非常规水源取水量合计", unit: calcUnit("m³"), type: "PLAIN", editable: false, placeholder: "", },
                        ]
                    },
                    {
                        nodeType: "Accordion", title: "常规水源取水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "intakeTapWater", label: "自来水",    unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeSurface",  label: "地表水",    unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeGround",   label: "地下水",    unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeSoft",     label: "外购软化水", unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeSteam",    label: "外购蒸汽",   unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeBought",   label: "外购水",    unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },
                    {
                        nodeType: "Accordion", title: "非常规水源取水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "intakeSea",      label: "海水",         unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeBrackish", label: "苦咸水",        unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeRecycled", label: "城镇污水再生水", unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeMine",     label: "矿井水",        unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeRain",     label: "雨水",         unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },
                ],
            },
            {
                inputs: [
                    { name: "rdccwv",     label: "补水(直接冷却循环水)量", unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "riccwv",     label: "补水(间接冷却循环水)量", unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "rilwv",      label: "补水(生活)量",         unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "ripwv",      label: "补水(进入产品)量",      unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "riowv",      label: "补水(其他)量",         unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "scrv",       label: "蒸汽冷凝水回用量",      unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "rwv",        label: "回用水量",             unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "oswv",       label: "其他串联水量",          unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "spv",        label: "蒸汽产汽量",           unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "dccwv",      label: "直接冷却循环水量",      unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "iccwv",      label: "间接冷却循环水量",      unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "ocwv",       label: "其它循环水量",          unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    //{ name: "unitWaterUsage",label: "单位用水量",      unit: calcUnit("m³"),   type: "PLAIN", editable: true, placeholder: "", },
                    { name: "llr",        label: "漏失率",              unit: "%",              type: "PLAIN", editable: true, placeholder: "", },
                    //{ name: "cllr",     label: "常规漏损率",           unit: "%",             type: "PLAIN", editable: true, placeholder: "", },
                    { name: "wcvf",       label: "耗水量计算系数",       unit: "%",             type: "PLAIN", editable: true, placeholder: "", },
                    //{ name: "wcpus",      label: "单位服务用水量",       unit: "",              type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                    //{ name: "pcdwc",      label: "人均用水量",          unit: "m³/人·a",         type: "PLAIN", editable: true,  placeholder: "", },
                    // 服务业对应于运营情况统计表(WB_COMPANY_OPERATION_STATS)使用的考核指标
                    { name: "rjqsY",           label: "年度人均取水量(机关, 学校)", unit: "m³/人·a",   type: "PLAIN", editable: true, placeholder: "",  filterIf: inputFilterIf, },
                    { name: "rjqsD",           label: "每天人均取水量(写字楼)",     unit: "L/人·d",   type: "PLAIN", editable: true, placeholder: "",  filterIf: inputFilterIf, },
                    { name: "dwffSmmj",        label: "单位水面面积取水量(绿化)",   unit: "m³/m²·a",  type: "PLAIN", editable: true, placeholder: "",  filterIf: inputFilterIf, },
                    { name: "dwffCwY",         label: "单位床位取水量(酒店)",      unit: "m³/床·a",   type: "PLAIN", editable: true, placeholder: "",  filterIf: inputFilterIf, },
                    { name: "dwffCwD",         label: "单位床位取水量(住院)",      unit: "L/床·d",    type: "PLAIN", editable: true, placeholder: "",  filterIf: inputFilterIf, },
                    { name: "dwffRs",          label: "单位人数取水量次(门诊)",    unit: "L/(人·次)", type: "PLAIN", editable: true, placeholder: "",  filterIf: inputFilterIf, },
                    { name: "avgBuildWater",   label: "单位建筑面积用水量",        unit: "m³/m²·a",  type: "PLAIN", editable: true, placeholder: "",  filterIf: inputFilterIf,  },
                    { name: "avgWaterUsage",   label: "人均用水量",              unit: "L/人·d",    type: "PLAIN", editable: true, placeholder: "",   filterIf: inputFilterIf,  },
                    { name: "unitWaterUsage",  label: "单位用水量",              unit: "m³",        type: "PLAIN", editable: true, placeholder: "",   filterIf: inputFilterIf,   },

                    // 以下字段的结果自动生成
                    { name: "inputWater", label: "输入水量",            unit: calcUnit("m³"),   type: "PLAIN", editable: false, placeholder: "", },
                    { name: "edv",        label: "外排水量",            unit: calcUnit("m³"),   type: "PLAIN", editable: false, placeholder: "", },
                    { name: "lwv",        label: "漏损水量",            unit: calcUnit("m³"),   type: "PLAIN", editable: false, placeholder: "", },
                    { name: "wcv",        label: "耗水量",              unit: calcUnit("m³"),   type: "PLAIN", editable: false, placeholder: "", },
                    //{ name: "llrt",     label: "漏损率超标(<4%)",     unit: "%",              type: "PLAIN", editable: false, placeholder: "", },
                    //{ name: "reuseRate",  label: "重复利用率",          unit: "%",              type: "PLAIN", editable: false, placeholder: "", },
                    //{ name: "dcwcr",      label: "直接冷却水循环率",    unit: "%",               type: "PLAIN", editable: false, placeholder: "", },
                    //{ name: "icwcr",      label: "间接冷却水循环率",    unit: "%",               type: "PLAIN", editable: false, placeholder: "", },
                    //{ name: "scrr",       label: "蒸汽冷凝水回用率",    unit: "%",               type: "PLAIN", editable: false, placeholder: "", },
                    //{ name: "wwrr",       label: "废水回用率",          unit: "%",              type: "PLAIN", editable: false, placeholder: "", },
                    { name: "cer",        label: "达标排放率",          unit: "%",              type: "PLAIN", editable: false, placeholder: "", },
                    { name: "uwrr",       label: "非常规水替代率",      unit: "%",               type: "PLAIN", editable: false, placeholder: "", },
                ]
            },
        ],
    };
    // FieldsConfig_2_table比FieldsConfig_2_book多了一个pcdwc字段, 表示单位产品取水量.
    const FieldsConfig_2_table = {
        industry: [
            {
                nodeType: "Section", title: "取水量", nodes: [
                    {
                        inputs: [
                            { name: "intakeRegular",   label: "常规水源取水量合计",   unit: calcUnit("m³"), type: "PLAIN", editable: false, placeholder: "", },
                            { name: "intakeIrregular", label: "非常规水源取水量合计", unit: calcUnit("m³"), type: "PLAIN", editable: false, placeholder: "", },
                        ]
                    },
                    {
                        nodeType: "Accordion", title: "常规水源取水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "intakeTapWater", label: "自来水",    unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeSurface",  label: "地表水",    unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeGround",   label: "地下水",    unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeSoft",     label: "外购软化水", unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeSteam",    label: "外购蒸汽",   unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeBought",   label: "外购水",    unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },
                    {
                        nodeType: "Accordion", title: "非常规水源取水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "intakeSea",      label: "海水",         unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeBrackish", label: "苦咸水",        unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeRecycled", label: "城镇污水再生水", unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeMine",     label: "矿井水",        unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeRain",     label: "雨水",         unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },
                ],
            },
            {
                inputs: [
                    { name: "rdccwv",     label: "补水(直接冷却循环水)量", unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "riccwv",     label: "补水(间接冷却循环水)量", unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "rilwv",      label: "补水(生活)量",         unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "ripwv",      label: "补水(进入产品)量",      unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "riowv",      label: "补水(其他)量",         unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "scrv",       label: "蒸汽冷凝水回用量",      unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "rwv",        label: "回用水量",             unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "oswv",       label: "其他串联水量",          unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "spv",        label: "蒸汽产汽量",           unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "dccwv",      label: "直接冷却循环水量",      unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "iccwv",      label: "间接冷却循环水量",      unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "ocwv",       label: "其它循环水量",          unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "llr",        label: "漏失率",              unit: "%",              type: "PLAIN", editable: true, placeholder: "", },
                    //{ name: "cllr",     label: "常规漏损率",           unit: "%",             type: "PLAIN", editable: true, placeholder: "", },
                    { name: "wcvf",       label: "耗水量计算系数",       unit: "%",             type: "PLAIN", editable: true, placeholder: "", },
                    { name: "wcpup",      label: "单位产品取水量",      unit: "m³",              type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                    { name: "pcdwc",      label: "人均生活用水量",      unit: "m³/人·a",         type: "PLAIN", editable: true,  placeholder: "", },
                    // 以下字段的结果自动生成
                    { name: "inputWater", label: "输入水量",            unit: calcUnit("m³"),   type: "PLAIN", editable: false, placeholder: "", },
                    { name: "edv",        label: "外排水量",            unit: calcUnit("m³"),   type: "PLAIN", editable: false, placeholder: "", },
                    { name: "lwv",        label: "漏损水量",            unit: calcUnit("m³"),   type: "PLAIN", editable: false, placeholder: "", },
                    { name: "wcv",        label: "耗水量",              unit: calcUnit("m³"),   type: "PLAIN", editable: false, placeholder: "", },
                    //{ name: "llrt",     label: "漏损率超标(<4%)",     unit: "%",              type: "PLAIN", editable: false, placeholder: "", },
                    { name: "reuseRate",  label: "重复利用率",          unit: "%",              type: "PLAIN", editable: false, placeholder: "", },
                    { name: "dcwcr",      label: "直接冷却水循环率",    unit: "%",               type: "PLAIN", editable: false, placeholder: "", },
                    { name: "icwcr",      label: "间接冷却水循环率",    unit: "%",               type: "PLAIN", editable: false, placeholder: "", },
                    { name: "scrr",       label: "蒸汽冷凝水回用率",    unit: "%",               type: "PLAIN", editable: false, placeholder: "", },
                    { name: "wwrr",       label: "废水回用率",          unit: "%",              type: "PLAIN", editable: false, placeholder: "", },
                    { name: "cer",        label: "达标排放率",          unit: "%",              type: "PLAIN", editable: false, placeholder: "", },
                    { name: "uwrr",       label: "非常规水替代率",      unit: "%",               type: "PLAIN", editable: false, placeholder: "", },

                ]
            },
        ],
        service: [
            {
                nodeType: "Section", title: "取水量", nodes: [
                    {
                        inputs: [
                            { name: "intakeRegular",   label: "常规水源取水量合计",   unit: calcUnit("m³"), type: "PLAIN", editable: false, placeholder: "", },
                            { name: "intakeIrregular", label: "非常规水源取水量合计", unit: calcUnit("m³"), type: "PLAIN", editable: false, placeholder: "", },
                        ]
                    },
                    {
                        nodeType: "Accordion", title: "常规水源取水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "intakeTapWater", label: "自来水",    unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeSurface",  label: "地表水",    unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeGround",   label: "地下水",    unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeSoft",     label: "外购软化水", unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeSteam",    label: "外购蒸汽",   unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeBought",   label: "外购水",    unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },
                    {
                        nodeType: "Accordion", title: "非常规水源取水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "intakeSea",      label: "海水",         unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeBrackish", label: "苦咸水",        unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeRecycled", label: "城镇污水再生水", unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeMine",     label: "矿井水",        unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeRain",     label: "雨水",         unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },
                ],
            },
            {
                inputs: [
                    { name: "rdccwv",     label: "补水(直接冷却循环水)量", unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "riccwv",     label: "补水(间接冷却循环水)量", unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "rilwv",      label: "补水(生活)量",         unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "ripwv",      label: "补水(进入产品)量",      unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "riowv",      label: "补水(其他)量",         unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "scrv",       label: "蒸汽冷凝水回用量",      unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "rwv",        label: "回用水量",             unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "oswv",       label: "其他串联水量",          unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "spv",        label: "蒸汽产汽量",           unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "dccwv",      label: "直接冷却循环水量",      unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "iccwv",      label: "间接冷却循环水量",      unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "ocwv",       label: "其它循环水量",          unit: calcUnit("m³"), type: "PLAIN", editable: true, placeholder: "", },
                    { name: "llr",        label: "漏失率",              unit: "%",              type: "PLAIN", editable: true, placeholder: "", },
                    //{ name: "cllr",     label: "常规漏损率",           unit: "%",             type: "PLAIN", editable: true, placeholder: "", },
                    { name: "wcvf",       label: "耗水量计算系数",       unit: "%",             type: "PLAIN", editable: true, placeholder: "", },
                    // 服务业对应于运营情况统计表(WB_COMPANY_OPERATION_STATS)使用的考核指标
                    { name: "rjqsY",           label: "年度人均取水量(机关, 学校)", unit: "m³/人·a",   type: "PLAIN", editable: true, placeholder: "",  filterIf: inputFilterIf, },
                    { name: "rjqsD",           label: "每天人均取水量(写字楼)",     unit: "L/人·d",   type: "PLAIN", editable: true, placeholder: "",  filterIf: inputFilterIf, },
                    { name: "dwffSmmj",        label: "单位水面面积取水量(绿化)",   unit: "m³/m²·a",  type: "PLAIN", editable: true, placeholder: "",  filterIf: inputFilterIf, },
                    { name: "dwffCwY",         label: "单位床位取水量(酒店)",      unit: "m³/床·a",   type: "PLAIN", editable: true, placeholder: "",  filterIf: inputFilterIf, },
                    { name: "dwffCwD",         label: "单位床位取水量(住院)",      unit: "L/床·d",    type: "PLAIN", editable: true, placeholder: "",  filterIf: inputFilterIf, },
                    { name: "dwffRs",          label: "单位人数取水量次(门诊)",    unit: "L/(人·次)", type: "PLAIN", editable: true, placeholder: "",  filterIf: inputFilterIf, },
                    { name: "avgBuildWater",   label: "单位建筑面积用水量",        unit: "m³/m²·a",  type: "PLAIN", editable: true, placeholder: "",  filterIf: inputFilterIf,  },
                    { name: "avgWaterUsage",   label: "人均用水量",              unit: "L/人·d",    type: "PLAIN", editable: true, placeholder: "",   filterIf: inputFilterIf,  },
                    { name: "unitWaterUsage",  label: "单位用水量",              unit: "m³",        type: "PLAIN", editable: true, placeholder: "",   filterIf: inputFilterIf,   },

                    // 以下字段的结果自动生成
                    { name: "inputWater", label: "输入水量",            unit: calcUnit("m³"),   type: "PLAIN", editable: false, placeholder: "", },
                    { name: "edv",        label: "外排水量",            unit: calcUnit("m³"),   type: "PLAIN", editable: false, placeholder: "", },
                    { name: "lwv",        label: "漏损水量",            unit: calcUnit("m³"),   type: "PLAIN", editable: false, placeholder: "", },
                    { name: "wcv",        label: "耗水量",              unit: calcUnit("m³"),   type: "PLAIN", editable: false, placeholder: "", },

                    //{ name: "llrt",     label: "漏损率超标(<4%)",     unit: "%",              type: "PLAIN", editable: false, placeholder: "", },
                    //{ name: "reuseRate",  label: "重复利用率",          unit: "%",              type: "PLAIN", editable: false, placeholder: "", },
                    //{ name: "dcwcr",      label: "直接冷却水循环率",    unit: "%",               type: "PLAIN", editable: false, placeholder: "", },
                    //{ name: "icwcr",      label: "间接冷却水循环率",    unit: "%",               type: "PLAIN", editable: false, placeholder: "", },
                    //{ name: "scrr",       label: "蒸汽冷凝水回用率",    unit: "%",               type: "PLAIN", editable: false, placeholder: "", },
                    //{ name: "wwrr",       label: "废水回用率",          unit: "%",              type: "PLAIN", editable: false, placeholder: "", },
                    { name: "cer",        label: "达标排放率",          unit: "%",              type: "PLAIN", editable: false, placeholder: "", },
                    { name: "uwrr",       label: "非常规水替代率",      unit: "%",               type: "PLAIN", editable: false, placeholder: "", },

                ]
            },
        ],
    };

    const FieldsConfig_3_book = {
        industry: [
        // 其它部分
            {
                inputs: [
                //{ name: "others",  label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "", },
                ]
            },
        ],
        service: [
            // 其它部分
            {
                inputs: [
                    //{ name: "others",  label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "", },
                ]
            },
        ],
    };
    const FieldsConfig_3_table = FieldsConfig_3_book;

    //const FieldsConfig = [...FieldsConfig_1, ...FieldsConfig_2, ...FieldsConfig_3];
    let FieldsConfig = [];
    switch(projSubclass) {
        case 1: // 表
            FieldsConfig = [...FieldsConfig_1_table[projIndustry], ...FieldsConfig_2_table[projIndustry], ...FieldsConfig_3_table[projIndustry]];
            break;
        case 2: // 书
            FieldsConfig = [...FieldsConfig_1_book[projIndustry], ...FieldsConfig_2_book[projIndustry], ...FieldsConfig_3_book[projIndustry]];
            break;
        default:
            console.warn("Unknown subclass:", projSubclass);
            FieldsConfig = [];
            break;
    }

    return (
        <View style={{ flex: 1 }}>

            <HeaderBar
                //title={screenTitle}
                navigation={navigation}
                goBackCallback={() => {
                    callOneByOne(
                        //() => { _mainCversionGetter=undefined; _subCversionGetter=undefined;},
                        resetSelectorStates);
                    /*callOneByOne(clearStore, resetSelectorStates)*/  // 返回前清空mmkv存储因为临时数据不再需要, 重置状态使得页面切换后不会使用前面的数据
                }}
                menuItemArray={[{ title: "删除表单", action: deleteAlert }, { title: "清空缓存", action: clearCache }]}
            />

            <ScreenWrapper contentContainerStyle={styles.container}>

                <View style={styles.formEntry}>

                    <FormListNodesMapper
                        fieldsConfig={FieldsConfig}
                        subCversionRef={subCversionRef}
                        control={control}
                        setStore={setStore}
                        resetField={resetField}
                        errors={errors}
                        setSnackBarMessage={setSnackBarMessage}
                        setShowSnackbar={setShowSnackbar}
                        editable={formEditableGlobal}
                        rowStyle={styles.rowContainer}
                    />

                    <Divider/>

                    {/*formType === 0 && <ControlledTextInput
                        name="remarks"
                        rowLabel="备注"
                        control={control}
                        placeholder=""
                        onChangeText={(text) => {
                            subCversionRef.current++;
                            setStore("subCversion", subCversionRef.current);
                            setStore("remarks", text);
                        }}
                        onClearText={() => {
                            subCversionRef.current++;
                            setStore("subCversion", subCversionRef.current);
                            setStore("remarks", "");
                            resetField("remarks", { defaultValue: "" });
                        }}
                        editable={formEditableGlobal}
                        mode="flat"
                        multiline={true}
                        formError={errors}
                        style={styles.rowContainerLongText}
                        //disabled={formDisabledGlobal}
                        required={false}
                    />*/}

                    <ControlledTextInput
                        name="others"
                        rowLabel="其它"
                        control={control}
                        placeholder=""
                        onChangeText={(text) => {
                            subCversionRef.current++;
                            setStore("subCversion", subCversionRef.current);
                            setStore("others", text);
                        }}
                        onClearText={() => {
                            subCversionRef.current++;
                            setStore("subCversion", subCversionRef.current);
                            setStore("others", "");
                            resetField("others", { defaultValue: "" });
                        }}
                        editable={formEditableGlobal}
                        mode="flat"
                        multiline={true}
                        formError={errors}
                        style={styles.rowContainerLongText}
                        //disabled={formDisabledGlobal}
                        required={false}
                    />

                    {/*<ControlledTextInput
                        name="rcmd"
                        rowLabel="建议"
                        control={control}
                        placeholder=""
                        onChangeText={(text) => {
                            subCversionRef.current++;
                            setStore("subCversion", subCversionRef.current);
                            setStore("rcmd", text);
                        }}
                        onClearText={() => {
                            subCversionRef.current++;
                            setStore("subCversion", subCversionRef.current);
                            setStore("rcmd", "");
                            resetField("rcmd", { defaultValue: "" });
                        }}
                        editable={formEditableGlobal}
                        mode="flat"
                        multiline={true}
                        formError={errors}
                        style={styles.rowContainerLongText}
                        //disabled={formDisabledGlobal}
                        required={false}
                    />*/}

                    <Divider bold={true}/>

                </View>

                <View style={styles.button}>
                    <Button mode="contained"
                        disabled={okButtonDisabled}
                        loading={okButtonLoading}
                        icon={saveButtonIcon}
                        onPress={handleSubmit(onHandleSubmit, (formErr) => onPreSubmitError(formErr, getStore, "WaterUsageAnnualRecordsUpdating"))}
                    >{bottomBarLabels.ok}</Button>
                </View>
            </ScreenWrapper>

            <LoadingIndicator
                title={""}
                message={"加载中....."}
                visible={loadingIndicatorVisible}
                onClose={()=>setLoadingIndicatorVisible(false)}
            />

            <Snackbar
                style={styles.snackBar}
                visible={showSnackbar}
                onDismiss={() => setShowSnackbar(false)}
                onIconPress={() => setShowSnackbar(false)}
                duration={Snackbar.DURATION_LONG}
            >
                {snackBarMessage}
            </Snackbar>

            <DialogToConfirm
                visible={showConfirm}
                title={confimDialogConfig.title ? confimDialogConfig.title : ""}
                text={confimDialogConfig.text ? confimDialogConfig.text : ""}
                onOK={confimDialogConfig.onOK ? confimDialogConfig.onOK : () => setShowConfirm(false)}
                okBtnLabel={confimDialogConfig.okLabel ? confimDialogConfig.okLabel : "确认"}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    rowContainer:{
        firstColumn: {
            flex: 1,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 1,
        // /borderWidth: 1,
        // height: 40,
        },
    },
    rowContainerLongText: {
        firstColumn: {
            flex: 1,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 4,
            // /borderWidth: 1,
            // height: 40,
        },
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        width: "80%",
        alignSelf: "center",
    },
    formEntry: {
        margin: 8,
    },
    snackBar: {
        flexDirection: "column",
    },
});

export default RecordsUpdating;
