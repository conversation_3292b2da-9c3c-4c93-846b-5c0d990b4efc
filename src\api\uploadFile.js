import { Platform } from "react-native";
import { lookup } from "react-native-mime-types";
import { noTimeoutHttpClient as httpClient } from "../services/http";
import { getFileExtension } from "../services/system/file";

/**
 * 将文件上传到服务器, 一次上传一个文件.
 * https://dev.to/ajmal_hasan/react-native-fileimage-picker-1o2j
 * @param {string} filePath 本地的完整文件路径.
 * @param {string} uploadName 提交到服务端的文件名, 不包含扩展名, 扩展名从文件路径获取.
 * @param {string} uploadURL 服务端接受此请求的api路径, 例如"upload-image".
 */
export const uploadFile = async (filePath, uploadName, uploadURL) => {
    const mimeType = lookup(filePath);
    const uploadFullName = `${uploadName}.${getFileExtension(filePath)}`;
    //console.log(`Upload file: ${filePath} with name ${uploadFullName}.`);

    try {
        const formData = new FormData();
        formData.append("file", {
            uri:  Platform.OS === "ios" ? filePath.replace("file://", "") : filePath,
            type: mimeType,
            name: uploadFullName, // Adjust the filename as needed
        });

        const response = await httpClient.post(uploadURL, {
            method:  "POST",
            body:    formData,
            headers: {
                "Content-Type": "multipart/form-data",
                // You may need to include additional headers depending on your API requirements
            },
        });

        const responseData = await response.json();
        console.log("File upload response:", responseData);
        return responseData;
    } catch (error) {
        console.log(`Upload file: ${filePath} with name ${uploadFullName} met error ${error}.`);
        return {};
    }
};
