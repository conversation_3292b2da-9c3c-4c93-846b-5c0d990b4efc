import React from "react";
import RecordsListingTemplate from "../RecordListingTemplate";

/**
 * 逻辑: 从上层页面传入项目url关键字和pubid, 点击进来后, 首先查询数据库有没有记录,
 * 有记录就拉取记录并显示记录列表, 没有记录就显示空屏幕并提示添加.
 * 点击添加后通过添加查询在数据库创建一条记录, 并且刷新页面拉取新记录.
 * 根据拉取到的数据数组创建一个列表, 每个列表项目都有各自的状态存储, 拉取查询和更新查询.
 * 点击列表项导航到表单填写/更新屏幕进行下一步操作.
 * @param {object} args
 * @param {string} args.addButtonLabel
 * @param {string} args.emptyScreenText
 * @param {function} args.recordAddQuery 添加表格记录的查询
 * @param {function} args.recordFetchQuery 拉取表格记录的查询
 * @param {function} args.recordUpdateQuery 更新表格记录的查询
 * @param {string} args.listItemNavigateTo 导航到哪个屏幕
 * @param {object} args.navigation 导航对象
 * @returns
 */
const RecordsListing = ({ addButtonLabel, emptyScreenText, listItemNavigateTo, navigation, route }) => {
    console.log("Record listing screen, pageMeta:", route.params.pageMeta);

    const queryKwd = route.params.pageMeta.queryKwd;
    const projectPubid = route.params.pageMeta.contentId;
    const recordListNaviTo = route.params.pageMeta.recordListNaviTo;

    return (
        <RecordsListingTemplate
            title={route.params.pageMeta.title}
            queryKwd={queryKwd}
            pubid={projectPubid}
            listItemNavigateTo={recordListNaviTo}
            bottomBarLabel={addButtonLabel || "添加新记录"}
            emptyScreenText={emptyScreenText || "请点击下方按钮添加新数据记录"}
            snackBarDefaultText={"添加表格记录遇到错误"}
            listLeftIcon={"water-outline"}
            addButtonIcon={"water-plus-outline"}
            addButtonLabel={"添加新记录"}
            navigation={navigation}
            projMeta={route.params.pageMeta}
        />
    );
};

export default RecordsListing;
