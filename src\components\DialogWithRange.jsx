import React, { useEffect, useState } from "react";
import { StyleSheet, View } from "react-native";
import {
    Button,
    Dialog,
    Portal,
    Switch,
    Text,
    TextInput,
} from "react-native-paper";
import { isRange, isString, parseRangeVals } from "../utils";

/**
 * 根据区间两边括号即最小最大值, 组成一个完整的区间字符串.
 * 如果min_和max_都为空串, 则直接返回一个空串""."
 * 如果组成的区间字符串不满足区间条件(非数字, min>max), 则返回undefined.
 * 返回的区间字符串, 无穷大用-Inf, +Inf表示.
 * @param {string} left 区间左边括号
 * @param {string} right 区间右边括号
 * @param {string} min_ 区间最小值
 * @param {string} max_ 区间最大值
 * @returns
 */
const craftRange = (left, right, min_, max_) => {
    const min = min_.replace(/\s/g, "");
    const max = max_.replace(/\s/g, "");
    if ((min === "" && max === "")) {
        return "";
    } else {
        const leftSide = (min === "" || min.toUpperCase === "-INF") ? "(" : left;
        const rightSide = (max === "" || max.toUpperCase === "INF" || max.toUpperCase === "+INF") ? ")" : right;
        const minVal = (min === "") ? "-Inf" : min;
        const maxVal = (max === "") ? "+Inf" : max;
        const res = `${leftSide} ${minVal},  ${maxVal} ${rightSide}`;
        return isRange(res) ? res : undefined;
    }
};

/**
 * 注意, 这里不要返回"empty", 否则表单提交会报错
 * @param {object} args
 * @returns
 */
export const DialogWithRange = ({ title, visible, onOK, onCancel, okBtnLabel = "确定", cancelBtnLabel = "取消", dialogState, setDialogState, ...props }) => {
    const [leftBracket, setLeftBracket] = useState("[");  // true 表示使用bracket, 即括号, false使用方括号
    const [rightBracket, setRightBracket] = useState("]");
    const [min, setMin] = useState("");
    const [max, setMax] = useState("");
    const [range, setRange] = useState(dialogState);

    // 监控区间四元数, 实时生成区间字符串
    useEffect(() => {
        const craftedVal = craftRange(leftBracket, rightBracket, min, max);
        if (craftedVal) {
            setRange(craftedVal);
        } else if (craftedVal === "") {
            setRange("");
        } else {
            setRange("非法区间值");
        }
    }, [leftBracket, rightBracket, min, max]);

    // 载入时从负组件恢复区间字符串
    useEffect(()=> {
        const trimedRange = isString(dialogState) ? dialogState.trim() : "";
        const parsedRange = parseRangeVals(trimedRange);
        if (parsedRange) {
            setMin(parsedRange[0]);
            setMax(parsedRange[1]);
            (trimedRange !== "empty") ? setLeftBracket(trimedRange[0]) : setLeftBracket("(");
            (trimedRange !== "empty") ? setRightBracket(trimedRange[trimedRange.length - 1]) : setRightBracket(")");
        }
    }, [dialogState]);

    return (
        <Portal>
            <Dialog onDismiss={() => {onCancel();}} visible={visible}>
                <Dialog.Title>{title}</Dialog.Title>
                <Dialog.ScrollArea style={styles.container}>
                    <View style={styles.view}>
                        <Switch
                            value={leftBracket==="("}
                            onValueChange={() => {
                                setLeftBracket(leftBracket==="(" ? "[" : "(");
                            }} />
                        <Text variant="titleMedium">{leftBracket}</Text>
                        <TextInput
                            value={min}
                            style={{ fontSize: 18 }}
                            onChangeText={text => {
                                setMin(text);
                            }}
                            keyboardType="numeric"/>
                        <Text>{", "}</Text>

                        <TextInput
                            value={max}
                            style={{ fontSize: 18 }}
                            onChangeText={text => {
                                setMax(text);
                            }}
                            keyboardType="numeric"/>
                        <Text variant="titleMedium">{rightBracket}</Text>
                        <Switch
                            value={rightBracket === ")"}
                            onValueChange={() => {
                                setRightBracket(rightBracket === ")" ? "]" : ")");
                            }} />
                    </View>

                    <View style={{ alignItems: "center", marginTop: 20}}>
                        <Text variant="titleLarge">{range}</Text>
                    </View>

                </Dialog.ScrollArea>
                <Dialog.Actions>
                    <Button onPress={() => {
                        // 与useEffect中逻辑一样
                        const trimedRange = isString(dialogState) ? dialogState.trim() : "";
                        const parsedRange = parseRangeVals(trimedRange);
                        if (parsedRange) {
                            setMin(parsedRange[0]);
                            setMax(parsedRange[1]);
                            (trimedRange !== "empty") ? setLeftBracket(trimedRange[0]) : setLeftBracket("(");
                            (trimedRange !== "empty") ? setRightBracket(trimedRange[trimedRange.length - 1]) : setRightBracket(")");
                        }
                        onCancel();
                    }}>{cancelBtnLabel}</Button>
                    <Button onPress={() => {
                        const rangeVal = range.replace(/\s/g, "");
                        console.log("rangeVal", rangeVal);
                        if (isRange(rangeVal)){
                            setDialogState(rangeVal);
                            onOK(rangeVal);
                        } else if(rangeVal==="empty") {
                            setDialogState(rangeVal);
                            onOK(rangeVal);
                        } else {
                            console.log("Invalid range data passed to onOK: ", rangeVal);
                            onOK(rangeVal);
                        }
                    }}>{okBtnLabel}</Button>
                </Dialog.Actions>
            </Dialog>
        </Portal>
    );
};

const styles = StyleSheet.create({
    container: {
        //maxHeight: 270,
        paddingHorizontal: 0,
    },
    view: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
        paddingVertical: 8,
        paddingHorizontal: 16,
    },
});
