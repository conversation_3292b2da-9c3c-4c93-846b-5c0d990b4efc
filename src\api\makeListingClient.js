import { useMutation } from "@tanstack/react-query";
import { httpClient } from "../services/http";
import log from "../services/logging";


const RETRY_CODES = [401, 501, 502, 503, 504];
const RETRY_METHODS = ["GET", "POST", "PUT", "HEAD", "DELETE", "OPTIONS", "TRACE"];
const RETRY_INTERVALS = [2000, 3000, 5000];
const RETRY_LIMIT = RETRY_INTERVALS.length;

/**
 * Return a `useMutation` query object. The query result will be stored in MMKV
 * @param {Object} arg
 * @param {string} arg.query the query key for react-query
 * @param {string} arg.url the http request path which will be responed by the server
 * @returns
 */
const makeListingClient = ({ query, url }) => {
    /**
     * 返回一个函数, 这个返回的函数创建一个useMutation查询, 用于发起list请求.
     * 注意: 这个版本的ky客户端含有重连,
     * 并且在客户端中将token设置到标头: request.headers.set("Authorization", token)
     * selectingClient与listingClient的区别在于, selectingClient的查询需要传递变量, 而listingClient不需要
     * @param {()=>{}} onSuccess token string
     * @param {()=>{}} onError callback function
     * @param {()=>{}} onSettled callback function
     */
    return (onSuccess, onError, onSettled) => {
        return useMutation({
            mutationKey: [query],
            mutationFn: async () => {
                const response = await httpClient.get(url);
                const json = await response.json();
                //log.debug("Query %s on %s receive data: %s", query, url, json);
                return json; // 注意不要return await!
            },

            onSuccess: (data, variables, context) => {
                //log.debug("makeListingClient onSuccess, query: %s, url: %s, data: %s, vars, %s, context: %s", query, url, data, variables, context);
                onSuccess?.(data);
            },
            onError: (error, variables, context) => {
                log.debug("makeListingClient onError, error: %s, query: %s, url: %s, vars, %s, context: %s", error, query, url, variables, context);
                onError?.(error);
            },
            onSettled: (data, error, variables, context) => {
                //log.debug("makeListingClient onSettled, query: %s, url: %s, var: %s, context: %s", query, url, variables, context);
                onSettled?.(data, error);
            },

            //enabled: true, // useMutation does not have enabled

            // 逻辑上, 首次失败后, 立即检测retryDelay, 经延迟后再检测retry触发重试
            // 注意, 有Ky的retry有bug, 这里使用React Query的retry!!!!!!
            retry: (failureCount, error) => { // failureCount from 0
                log.debug("failureCount: %s", failureCount);
                if (failureCount < RETRY_LIMIT
                    && error.name === "HTTPError"
                    && RETRY_CODES.includes(error.response.status)
                    && RETRY_METHODS.includes(error.request.method)) {
                    return true;
                } else {
                    return false;
                }
            },
            retryDelay: (attemptIndex) => RETRY_INTERVALS[attemptIndex], // attemptIndex from 0
        });
    };
};

export { makeListingClient };
