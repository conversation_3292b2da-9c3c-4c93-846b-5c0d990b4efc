import { Linking, Alert } from "react-native";
import { Platform } from "react-native";
import { check, request, PERMISSIONS, RESULTS } from "react-native-permissions";

export const callPhone = async (phoneNumber) => {
    const permission = Platform.OS === "android" ? PERMISSIONS.ANDROID.CALL_PHONE : PERMISSIONS.IOS.PHONE;
    // 检查权限
    const result = await check(permission);
    if (result === RESULTS.GRANTED) {
        // 权限已授予
        Linking.openURL(`tel:${phoneNumber}`);
    } else {
        // 请求权限
        const requestResult = await request(permission);
        if (requestResult === RESULTS.GRANTED) {
        // 权限被授予，拨打电话
            Linking.openURL(`tel:${phoneNumber}`);
        } else {
            Alert.alert("权限被拒绝", "无法拨打电话，请在设置中授予权限");
        }
    }
};
