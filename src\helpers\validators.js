import i18n from "../translations";

export function emailValidator(email) {
    const re = /\S+@\S+\.\S+/;
    if (!email) return "Email can't be empty.";
    if (!re.test(email)) return "Ooops! We need a valid email address.";
    return "";
}

export function mobileValidator(mobile) {
    const t = i18n.t;

    const re = /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/;
    if (!mobile) return t("validators:mobile.nullInputErrorHint");
    if (!re.test(mobile)) return t("validators:mobile.formatErrorHint");
    return "";
}

export function nameValidator(name) {
    const t = i18n.t;
    if (!name) return t("validators:name.nullInputErrorHint");
    return "";
}

export function instNameValidator(name) {
    const t = i18n.t;
    if (!name) return t("validators:instName.nullInputErrorHint");
    return "";
}

export function passwordValidator(password) {
    const t = i18n.t;

    if (!password) return t("validators:password.nullInputErrorHint");
    if (password.length < 6) return t("validators:password.formatErrorHint");
    return "";
}

export function promteCodeValidator(promteCode) {
    const t = i18n.t;
    if (!promteCode) return t("validators:promteCode.nullInputErrorHint");
    if (promteCode.length !== 4) return t("validators:promteCode.formatErrorHint");
    return "";
}
