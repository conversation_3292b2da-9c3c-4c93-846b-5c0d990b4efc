import React, { useEffect, useState } from "react";
import { Divider, Appbar, useTheme, Text, TextInput } from "react-native-paper";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { StyleSheet, View } from "react-native";
import { joiResolver } from "@hookform/resolvers/joi";
import "fast-text-encoding";
import <PERSON><PERSON> from "joi";
import ScreenWrapper from "../ScreenWrapper";
import BottomBarButton from "../../components/BottomBarButton";
import { useForm } from "react-hook-form";
import { creatMMKVStore } from "../../services/local-storage";
import { whenLet, identity } from "../../utils";
import ControlledTextInput from "../../components/ControlledTextInput";
import { validatorBase } from "../../utils/validatorBase";
import ControlledRadioInput from "../../components/ControlledRadioInput";


const MMKV_KEY = "TEST_REACT_HOOK_FORM_MMKV";
let render = 0;
const { setStore, getStore, getStoreObject, getStoreKeys, clearStore } = creatMMKVStore(MMKV_KEY);

/**
 * 测试react-hook-form + mmkv的屏幕!
 * @param {Object} args
 * @returns
 */
const TestReactHookFormMMKV = ({ navigation }) => {
    const { bottom, left, right } = useSafeAreaInsets();
    const theme = useTheme();
    const height = theme.isV3 ? 80 : 56;

    const [saveButtonLoading] = useState(false);
    const [saveButtonDisabled] = useState(false);
    const [saveButtonIcon] = useState("content-save-all-outline");

    render++;

    const schema = Joi.object({
        name: validatorBase.name,
        age: validatorBase.age,
        mobile: validatorBase.mobile,
        dept: validatorBase.deptName,
    });

    const {
        handleSubmit,
        control,
        resetField,
        reset,
        formState: { errors },
    } = useForm({
        resolver: joiResolver(schema),
        //reValidateMode: "onBlur", // https://github.com/orgs/react-hook-form/discussions/10327
        defaultValues: {
            name: whenLet(getStore("name"), identity, ""),
            age: whenLet(getStore("age"), identity, ""),
            mobile: whenLet(getStore("mobile"), identity, ""),
            dept: whenLet(getStore("dept"), identity, ""),
        },
    });

    useEffect(() => {
    }, []);

    console.log("errors:", errors);
    console.log("errors.age:", errors?.age);

    return (
        <>
            <ScreenWrapper contentContainerStyle={styles.container}>
                <Text>Render: {render}</Text>

                <View style={styles.formEntry}>
                    <ControlledTextInput
                        rowLabel="姓名"
                        control={control}
                        name="name"
                        placeholder="张三"
                        onChangeText={(text)=>setStore("name", text)}
                        onClearText={() => {
                            setStore("name", "");
                            resetField("name", { defaultValue: "" });
                        }}
                        editable={true}
                        mode="outlined"
                        formError={errors}
                        //error={!!errors?.name} // 不需要传递error, 传递后遇到错误后文本框显示红色, 但是这里的错误消息通过ErrorMessage组件显示出来了
                        //errorText={errors.name?.message}
                    />

                    <ControlledTextInput
                        rowLabel="年龄"
                        control={control}
                        name="age"
                        placeholder="年龄"
                        onChangeText={(text) => setStore("age", text)}
                        onClearText={() => {
                            setStore("age", "");
                            resetField("age", { defaultValue: "" });
                        }}
                        editable={true}
                        mode="outlined"
                        formError={errors}
                    />

                    <ControlledTextInput
                        rowLabel="手机"
                        control={control}
                        name="mobile"
                        placeholder="18888888888"
                        onChangeText={(text) => setStore("mobile", text)}
                        onClearText={() => {
                            setStore("mobile", "");
                            resetField("mobile", { defaultValue: "" });
                        }}
                        editable={true}
                        mode="outlined"
                        formError={errors}
                    />

                    <ControlledRadioInput
                        name="dept"
                        rowLabel="部门"
                        control={control}
                        placeholder="请拉选右边图标"
                        onRadioConfirm={(text) => setStore("dept", text)}
                        defaultRadioCheck={() => getStore("dept")}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        radioItemArray={[{ name: "Option1", id: 1 }, { name: "Option2", id: 2 }]}
                    />

                </View>

            </ScreenWrapper>

            <Appbar
                style={[
                    styles.bottom,
                    {
                        height: height + bottom,
                    },
                    theme.isV3 && {
                        backgroundColor: theme.colors.elevation.level2,
                    },
                ]}
                safeAreaInsets={{ bottom, left, right }}
                theme={{ mode: "adaptive" }}
            //mode='center-aligned'
            >

                <View style={styles.bottomBarContainer}>
                    <Divider />
                    <View style={styles.bottomBarComponents}>
                        <BottomBarButton
                            label={"取消"}
                            disabled={false}
                            onPress={() => { }}
                        />
                        <BottomBarButton
                            label={"保存"}
                            loading={saveButtonLoading}
                            disabled={saveButtonDisabled}
                            icon={saveButtonIcon}
                            onPress={handleSubmit(data => console.log(data, data.age))} // {"age": 88, "mobile": "18888888888", "name": "1sgest"}
                        />
                    </View>
                </View>
            </Appbar>
        </>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    firstColumn: {
        flex: 2,
        //borderWidth: 1,
        //height: 40,
        content: {
            fontSize: 18,
        }
    },
    secondColumn: {
        flex: 3,
        // /borderWidth: 1,
        // height: 40,
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        flex: 1,
        marginHorizontal: 6
    },
    formEntry: {
        margin: 8,
    },
});

export default TestReactHookFormMMKV;
