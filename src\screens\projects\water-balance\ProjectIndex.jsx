import React, { useState } from "react";
import { View } from "react-native";
import { Divider, List } from "react-native-paper";
import { makeReqGenWaterBalanceDoc as genDocQueryMaker, } from "../../../api/projectRecordsQueries";
import { CardProjectMessage } from "../../../components/CardProjectMessage";
import { DialogToConfirm } from "../../../components/DialogToConfirm";
import HeaderBar from "../../../components/HeaderBar";
import { PromptDialog } from "../../../components/PromptDialog";
import { UserTypeList, verifySomeUserTypes } from "../../../utils/permits";
import ScreenWrapper from "./../../ScreenWrapper";


// 权限模拟函数, 暂时全部都可写, 但要根据服务端数据进行判断
const canIWrite = () => {
    return true;
};
/**
 * 在"任务"主页点击项目卡片后进入的屏幕
 * route.params.pageMeta从MessageScreen传递过来
 * pageMeta.class=1表示水平衡, pageMeta.subclass=1表示表, 2表示书
 * @param {*} param0
 * @returns
 */
export const ProjectIndex = ({ navigation, route }) => {
    console.log("ProjectIndex screen, Page info passed from nav:", route.params.pageMeta);
    const pageMeta = route.params.pageMeta;
    const { contentId: projPubid  } = pageMeta;
    const genDocQueryKwd = "all";

    //const { bottom, left, right } = useSafeAreaInsets();
    //const theme = useTheme();
    //const height = theme.isV3 ? 80 : 56;

    //const editableIcon     = "folder-edit-outline";
    //const readOnlyIcon     = "folder-outline";
    //const infoSectionIcon  = canIWrite() ? "folder-edit-outline" : "folder-eye-outline";
    const tableSectionIcon = canIWrite() ? "folder-edit-outline" : "folder-eye-outline";

    //const projOverViewNaviTo = (pageMeta.type === 1 && pageMeta.class === 1) ? "WaterBalanceOverview" : undefined;
    //console.log("projOverViewNaviTo:", projOverViewNaviTo);

    // Query: generate all docs of this project
    const projGenDocOnSuccess = () => {
        console.log("genDoc success!");
    };
    const projGenDocOnError = (error) => {
        console.log("genDoc error:", error);
    };
    const payload = {prompts: promptsText};
    const projGenDocQuery = genDocQueryMaker(payload, [genDocQueryKwd, projPubid], projGenDocOnSuccess, projGenDocOnError);

    const [promptsVisible, setPromptsVisible] = useState(false);
    const [promptsText, setPromptsText] = useState("根据水平衡资讯行业的知识总结所有相关表格");
    const [dialogConfirmVisible, setDialogConfirmVisible] = useState(false);

    return (
        <View style={{ flex: 1 }}>

            <HeaderBar
                title={route.params.pageMeta.title}
                navigation={navigation}
                //goBackCallback={() => {}}
                // 获得外链需要根据权限开启
                menuItemArray={[
                    //{ title: "获得外链", action: ()=>{} },
                    { title: "生成文档(AI)", action: ()=>{setPromptsVisible(true);} }
                ]}
            />
            <ScreenWrapper>
                <Divider bold={true} />

                {/*
                <List.Section title={"项目信息"}>
                    <CardProjectMessage title="客户资料清单"    naviTo="ClientInfoRecordsListing"             icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "cinfo",     title: "客户资料清单",     recordListNaviTo: "ClientInfoRecordsUpdating" }}/>
                    <CardProjectMessage title="公用工程"       naviTo="ClientPublicUtilityRecordsListing"    icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "cputil",     title: "公用工程",        recordListNaviTo: "ClientPublicUtilityRecordsUpdating" }}/>
                    <CardProjectMessage title="项目产品规模"    naviTo="ClientProductScaleRecordsListing"     icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "cpscale",    title: "项目产品规模",     recordListNaviTo: "ClientProductScaleRecordsUpdating" }}/>
                    <CardProjectMessage title="项目原辅材料消耗" naviTo="ClientMaterialConsumeRecordsListing"  icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "cmconsume",  title: "项目原辅材料消耗",  recordListNaviTo: "ClientMaterialConsumeRecordsUpdating" }}/>
                    <CardProjectMessage title="项目所需设备清单" naviTo="ClientRequiredDeviceRecordsListing"   icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "crdevice",   title: "项目所需设备清单",  recordListNaviTo: "ClientRequiredDeviceRecordsUpdating" }}/>
                </List.Section>
                <Divider bold={true} />
 */}

                <List.Section>
                    {pageMeta.industry === 1 && pageMeta.subclass === 1 && // 工业, 表
                        <>
                            <CardProjectMessage title="用水单位基本情况表"    naviTo="BasicInfoRecordsListing"               icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "binfo",        title: "用水单位基本情况表",        recordListNaviTo: "BasicInfoRecordsUpdating" }}/>
                            <CardProjectMessage title="用水量计算表"         naviTo="WaterUsageCalcListing"                 icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "wucalc",        title: "用水量计算表",            recordListNaviTo: "WaterUsageCalcUpdating" }}/>
                            {/*<CardProjectMessage title="用水定额参考表"        naviTo="WaterQuotaInfoRecordsListing"          icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "wqinfo",        title: "用水定额参考表",          recordListNaviTo: "WaterQuotaInfoRecordsUpdating" }}/>*/}
                            <CardProjectMessage title="取水水源情况表"        naviTo="WaterSourceRecordsListing"             icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "watersource",  title: "用水单位取水水源情况表",     recordListNaviTo: "WaterSourceRecordsUpdating" }}/>
                            <CardProjectMessage title="近三年用水情况表"      naviTo="WaterUsageAnnualRecordsListing"         icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "wua",         title: "近三年用水情况表",          recordListNaviTo: "WaterUsageAnnualRecordsUpdating" }} />
                            <CardProjectMessage title="生产情况统计表"        naviTo="ComProdStatsRecordsListing"            icon={tableSectionIcon} navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "comprodstats", title: "生产情况统计表",            recordListNaviTo: "ComProdStatsRecordsUpdating" }} />
                            <CardProjectMessage title="计量仪表配置情况表"     naviTo="ComWaterMeterEquipStatsRecordsListing" icon={tableSectionIcon} navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "comwmeqst",    title: "计量仪表配置情况表",         recordListNaviTo: "ComWaterMeterEquipStatsRecordsUpdating" }} />
                            <CardProjectMessage title="水表安装情况一览表"     naviTo="ComWaterMeterEquipRecordsListing"       icon={tableSectionIcon} navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "comwmequip",   title: "水表安装情况一览表",        recordListNaviTo: "ComWaterMeterEquipRecordsUpdating" }} />
                            {/*<CardProjectMessage title="水表原始数据记录"   naviTo="WaterMeterSampleRecordsListing"         icon={tableSectionIcon} navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "wmsamps",      title: "水表原始数据记录",          recordListNaviTo: "WaterMeterSampleRecordsUpdating" }} />*/}
                            <CardProjectMessage title="水平衡测试/主要生产用水" naviTo="WaterUsageUnitsFirstRecordsListing"     icon={tableSectionIcon} navigation={navigation}  pageMeta={{ ...pageMeta, queryKwd: "wwuur/1",      title: "主要生产用水",             recordListNaviTo: "WaterUsageUnitsFirstRecordsUpdating" ,  batchAddNaviTo: "WaterUsageUnitsRecordsAdding", waterClass: 1 }} />
                            <CardProjectMessage title="水平衡测试/辅助生产用水" naviTo="WaterUsageUnitsSecondRecordsListing"    icon={tableSectionIcon} navigation={navigation}  pageMeta={{ ...pageMeta, queryKwd: "wwuur/2",      title: "辅助生产用水",             recordListNaviTo: "WaterUsageUnitsSecondRecordsUpdating" , batchAddNaviTo: "WaterUsageUnitsRecordsAdding", waterClass: 2 }} />
                            <CardProjectMessage title="水平衡测试/附属生产用水" naviTo="WaterUsageUnitsThirdRecordsListing"     icon={tableSectionIcon} navigation={navigation}  pageMeta={{ ...pageMeta, queryKwd: "wwuur/3",      title: "附属生产用水",             recordListNaviTo: "WaterUsageUnitsThirdRecordsUpdating" ,  batchAddNaviTo: "WaterUsageUnitsRecordsAdding", waterClass: 3 }} />
                            <CardProjectMessage title="水平衡测试/非生产用水"   naviTo="WaterUsageUnitsFourthRecordsListing"    icon={tableSectionIcon} navigation={navigation}  pageMeta={{ ...pageMeta, queryKwd: "wwuur/4",      title: "非生产用水",              recordListNaviTo: "WaterUsageUnitsFourthRecordsUpdating" , batchAddNaviTo: "WaterUsageUnitsRecordsAdding", waterClass: 4 }} />

                            {/** More tables here */}
                        </>}
                    {pageMeta.industry === 1 && pageMeta.subclass === 2 && // 工业, 书
                        <>
                            <CardProjectMessage title="用水单位基本情况表"       naviTo="BasicInfoRecordsListing"               icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "binfo",        title: "用水单位基本情况表",        recordListNaviTo: "BasicInfoRecordsUpdating" }}/>
                            <CardProjectMessage title="用水单位取水水源情况"      naviTo="WaterSourceRecordsListing"             icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "watersource",  title: "用水单位取水水源情况表",     recordListNaviTo: "WaterSourceRecordsUpdating" }}/>
                            <CardProjectMessage title="用水单位年用水情况"       naviTo="WaterUsageAnnualRecordsListing"         icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "wua",         title: "用水单位年用水情况表",       recordListNaviTo: "WaterUsageAnnualRecordsUpdating" }} />
                            <CardProjectMessage title="企业近三年生产情况统计"    naviTo="ComProdStatsRecordsListing"             icon={tableSectionIcon} navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "comprodstats", title: "企业近三年生产情况统计",     recordListNaviTo: "ComProdStatsRecordsUpdating" }} />
                            <CardProjectMessage title="主要用水设备、设施一览表"  naviTo="WaterUsingEquipsRecordsListing"        icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "wuequip",       title: "主要用水设备、设施一览表", recordListNaviTo: "WaterUsingEquipsRecordsUpdating" }}/>
                            <CardProjectMessage title="用水单位水计量器具配备统计" naviTo="ComWaterMeterEquipStatsRecordsListing" icon={tableSectionIcon} navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "comwmeqst",    title: "用水单位水计量器具配备统计",  recordListNaviTo: "ComWaterMeterEquipStatsRecordsUpdating" }} />
                            <CardProjectMessage title="用水单位水计量器具配备情况" naviTo="ComWaterMeterEquipRecordsListing"       icon={tableSectionIcon} navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "comwmequip",   title: "用水单位水计量器具配备情况", recordListNaviTo: "ComWaterMeterEquipRecordsUpdating" }} />
                            <CardProjectMessage title="水表原始数据记录"         naviTo="WaterMeterSampleRecordsListing"         icon={tableSectionIcon} navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "wmsamps",      title: "水表原始数据记录",          recordListNaviTo: "WaterMeterSampleRecordsUpdating" }} />
                            <CardProjectMessage title="水平衡测试/主要生产用水"   naviTo="WaterUsageUnitsFirstRecordsListing"     icon={tableSectionIcon} navigation={navigation}  pageMeta={{ ...pageMeta, queryKwd: "wwuur/1",      title: "主要生产用水",             recordListNaviTo: "WaterUsageUnitsFirstRecordsUpdating",  batchAddNaviTo: "WaterUsageUnitsRecordsAdding", waterClass: 1 }} />
                            <CardProjectMessage title="水平衡测试/辅助生产车间或工序" naviTo="WaterUsageUnitsSecondRecordsListing"  icon={tableSectionIcon} navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "wwuur/2",      title: "辅助生产用水",             recordListNaviTo: "WaterUsageUnitsSecondRecordsUpdating", batchAddNaviTo: "WaterUsageUnitsRecordsAdding", waterClass: 2 }} />
                            <CardProjectMessage title="水平衡测试/附属生产办公、生活等用水" naviTo="WaterUsageUnitsThirdRecordsListing" icon={tableSectionIcon} navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "wwuur/3",   title: "附属生产用水",             recordListNaviTo: "WaterUsageUnitsThirdRecordsUpdating",  batchAddNaviTo: "WaterUsageUnitsRecordsAdding", waterClass: 3 }} />
                            {/** More tables here */}
                        </>}

                    {pageMeta.industry === 2 && pageMeta.subclass === 1 && // 服务业, 表
                        <>
                            <CardProjectMessage title="用水单位基本情况表"    naviTo="BasicInfoRecordsListing"               icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "binfo",        title: "用水单位基本情况表",        recordListNaviTo: "BasicInfoRecordsUpdating" }}/>
                            <CardProjectMessage title="用水量计算表"         naviTo="WaterUsageCalcListing"                 icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "wucalc",        title: "用水量计算表",            recordListNaviTo: "WaterUsageCalcUpdating" }}/>
                            <CardProjectMessage title="用水定额参考表"        naviTo="WaterQuotaInfoRecordsListing"          icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "wqinfo",        title: "用水定额参考表",          recordListNaviTo: "WaterQuotaInfoRecordsUpdating" }}/>
                            <CardProjectMessage title="取水水源情况表"        naviTo="WaterSourceRecordsListing"             icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "watersource",  title: "用水单位取水水源情况表",     recordListNaviTo: "WaterSourceRecordsUpdating" }}/>
                            <CardProjectMessage title="近三年用水情况表"      naviTo="WaterUsageAnnualRecordsListing"         icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "wua",         title: "近三年用水情况表",          recordListNaviTo: "WaterUsageAnnualRecordsUpdating" }} />
                            <CardProjectMessage title="运营情况统计表"        naviTo="ComOpStatsRecordsListing"              icon={tableSectionIcon} navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "comopstats",   title: "运营情况统计表",            recordListNaviTo: "ComOpStatsRecordsUpdating" }} />
                            <CardProjectMessage title="计量仪表配置情况表"     naviTo="ComWaterMeterEquipStatsRecordsListing" icon={tableSectionIcon} navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "comwmeqst",    title: "计量仪表配置情况表",         recordListNaviTo: "ComWaterMeterEquipStatsRecordsUpdating" }} />
                            <CardProjectMessage title="水表安装情况一览表"     naviTo="ComWaterMeterEquipRecordsListing"       icon={tableSectionIcon} navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "comwmequip",   title: "水表安装情况一览表",        recordListNaviTo: "ComWaterMeterEquipRecordsUpdating" }} />
                            <CardProjectMessage title="节水器具配备情况"      naviTo="WaterSavingEquipsRecordsListing"       icon={tableSectionIcon} navigation={navigation}  pageMeta={{ ...pageMeta, queryKwd: "wsequip",       title: "节水器具配备情况",         recordListNaviTo: "WaterSavingEquipsRecordsUpdating" }} />

                            {/*<CardProjectMessage title="水表原始数据记录"   naviTo="WaterMeterSampleRecordsListing"         icon={tableSectionIcon} navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "wmsamps",      title: "水表原始数据记录",          recordListNaviTo: "WaterMeterSampleRecordsUpdating" }} />*/}
                            <CardProjectMessage title="水平衡测试/主要功能用水" naviTo="WaterUsageUnitsFirstRecordsListing"     icon={tableSectionIcon} navigation={navigation}  pageMeta={{ ...pageMeta, queryKwd: "wwuur/1",      title: "主要功能用水",             recordListNaviTo: "WaterUsageUnitsFirstRecordsUpdating",  batchAddNaviTo: "WaterUsageUnitsRecordsAdding", waterClass: 1 }} />
                            <CardProjectMessage title="水平衡测试/辅助功能用水" naviTo="WaterUsageUnitsSecondRecordsListing"    icon={tableSectionIcon} navigation={navigation}  pageMeta={{ ...pageMeta, queryKwd: "wwuur/2",      title: "辅助功能用水",             recordListNaviTo: "WaterUsageUnitsSecondRecordsUpdating", batchAddNaviTo: "WaterUsageUnitsRecordsAdding", waterClass: 2 }} />
                            <CardProjectMessage title="水平衡测试/附属功能用水" naviTo="WaterUsageUnitsThirdRecordsListing"     icon={tableSectionIcon} navigation={navigation}  pageMeta={{ ...pageMeta, queryKwd: "wwuur/3",      title: "附属功能用水",             recordListNaviTo: "WaterUsageUnitsThirdRecordsUpdating",  batchAddNaviTo: "WaterUsageUnitsRecordsAdding", waterClass: 3 }} />
                            {/*<CardProjectMessage title="水平衡测试/非功能用水"   naviTo="WaterUsageUnitsFourthRecordsListing"    icon={tableSectionIcon} navigation={navigation}  pageMeta={{ ...pageMeta, queryKwd: "wwuur/4",      title: "非功能用水",              recordListNaviTo: "WaterUsageUnitsFourthRecordsUpdating", batchAddNaviTo: "WaterUsageUnitsRecordsAdding", waterClass: 4 }} />*/}

                            {/** More tables here */}
                        </>}
                    {pageMeta.industry === 2 && pageMeta.subclass === 2 && // 服务业, 书
                        <>
                            <CardProjectMessage title="用水单位基本情况表"       naviTo="BasicInfoRecordsListing"               icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "binfo",        title: "用水单位基本情况表",      recordListNaviTo: "BasicInfoRecordsUpdating" }}/>
                            <CardProjectMessage title="用水定额参考表"          naviTo="WaterQuotaInfoRecordsListing"          icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "wqinfo",        title: "用水定额参考表",          recordListNaviTo: "WaterQuotaInfoRecordsUpdating" }}/>
                            <CardProjectMessage title="水源基本情况表"          naviTo="WaterSourceRecordsListing"             icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "watersource",   title: "水源基本情况表",          recordListNaviTo: "WaterSourceRecordsUpdating" }}/>
                            <CardProjectMessage title="主要用水设备、设施一览表"  naviTo="WaterUsingEquipsRecordsListing"        icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "wuequip",       title: "主要用水设备、设施一览表", recordListNaviTo: "WaterUsingEquipsRecordsUpdating" }}/>
                            <CardProjectMessage title="用水器具统计表"          naviTo="WaterSavingEquipsRecordsListing"       icon={tableSectionIcon} navigation={navigation}  pageMeta={{ ...pageMeta, queryKwd: "wsequip",       title: "用水器具统计表",          recordListNaviTo: "WaterSavingEquipsRecordsUpdating" }} />
                            <CardProjectMessage title="用水单位年用水情况"       naviTo="WaterUsageAnnualRecordsListing"         icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "wua",         title: "用水单位年用水情况表",       recordListNaviTo: "WaterUsageAnnualRecordsUpdating" }} />
                            <CardProjectMessage title="单位运营情况统计表"       naviTo="ComOpStatsRecordsListing"             icon={tableSectionIcon} navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "comopstats",    title: "单位运营情况统计表",         recordListNaviTo: "ComOpStatsRecordsUpdating" }} />
                            <CardProjectMessage title="用水单位水计量器具配备统计" naviTo="ComWaterMeterEquipStatsRecordsListing" icon={tableSectionIcon} navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "comwmeqst",    title: "用水单位水计量器具配备统计",  recordListNaviTo: "ComWaterMeterEquipStatsRecordsUpdating" }} />
                            <CardProjectMessage title="用水单位水计量器具配备情况" naviTo="ComWaterMeterEquipRecordsListing"       icon={tableSectionIcon} navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "comwmequip",   title: "用水单位水计量器具配备情况", recordListNaviTo: "ComWaterMeterEquipRecordsUpdating" }} />
                            {/*<CardProjectMessage title="水表原始数据记录"         naviTo="WaterMeterSampleRecordsListing"         icon={tableSectionIcon} navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "wmsamps",      title: "水表原始数据记录",          recordListNaviTo: "WaterMeterSampleRecordsUpdating" }} />*/}
                            <CardProjectMessage title="水平衡测试/主要功能用水"   naviTo="WaterUsageUnitsFirstRecordsListing"     icon={tableSectionIcon} navigation={navigation}  pageMeta={{ ...pageMeta, queryKwd: "wwuur/1",      title: "主要功能用水",             recordListNaviTo: "WaterUsageUnitsFirstRecordsUpdating",  batchAddNaviTo: "WaterUsageUnitsRecordsAdding", waterClass: 1 }} />
                            <CardProjectMessage title="水平衡测试/辅助功能用水"   naviTo="WaterUsageUnitsSecondRecordsListing"    icon={tableSectionIcon} navigation={navigation}  pageMeta={{ ...pageMeta, queryKwd: "wwuur/2",      title: "辅助功能用水",             recordListNaviTo: "WaterUsageUnitsSecondRecordsUpdating", batchAddNaviTo: "WaterUsageUnitsRecordsAdding", waterClass: 2 }} />
                            <CardProjectMessage title="水平衡测试/附属功能用水"   naviTo="WaterUsageUnitsThirdRecordsListing"     icon={tableSectionIcon} navigation={navigation}  pageMeta={{ ...pageMeta, queryKwd: "wwuur/3",      title: "附属功能用水",             recordListNaviTo: "WaterUsageUnitsThirdRecordsUpdating",  batchAddNaviTo: "WaterUsageUnitsRecordsAdding", waterClass: 3 }} />
                            {/** More tables here */}
                        </>}
                </List.Section>
                <Divider bold={true} />

                <List.Section title={"附件"}>
                    <CardProjectMessage title="画图板" naviTo="SketchesListing" icon={"draw-pen"} navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "sketch",  title: "画图板",recordListNaviTo: "SketchingScreen" }} />
                </List.Section>
                <Divider bold={true} />

                {pageMeta.subclass === 1 && //表
                    <></>}

                {/*pageMeta.subclass === 2 && // 书
                    <>
                        <List.Section title={"计算结果"}>
                            <CardProjectMessage title="用水单位水平衡测试总统计表" naviTo="WaterBalanceInspectStatsRecordsListing" icon={tableSectionIcon} navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "wbistat", title: "用水单位水平衡测试总统计表", recordListNaviTo: "WaterBalanceInspectStatsRecordsUpdating" }} />
                            <CardProjectMessage title="现状用水测试结果"         naviTo="WaterUsageInspectResultsRecordsListing" icon={tableSectionIcon} navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "wuires",  title: "现状用水测试结果",          recordListNaviTo: "WaterUsageInspectResultsRecordsUpdating" }} />
                            <CardProjectMessage title="用水单位用水分析"         naviTo="WaterUsageInstAnalyzeRecordsListing"     icon={tableSectionIcon} navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "wuianlz", title: "用水单位用水分析",         recordListNaviTo: "WaterUsageInstAnalyzeRecordsUpdating" }} />
                            <CardProjectMessage title="用水单位用水分析"         naviTo="WaterUsageInstAnalyzeRecordsWhole"      icon={tableSectionIcon} navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "wuianlz", title: "用水单位用水分析",         recordListNaviTo: "WaterUsageInstAnalyzeRecordsWhole" }} />
                        </List.Section>
                        <Divider bold={true} />
                    </>*/}

                {verifySomeUserTypes(UserTypeList.sysAdmin, UserTypeList.qa_0)  && <>
                    <List.Section title={"功能测试"}>
                        <CardProjectMessage title="用水器具统计表(new)" naviTo="WaterSavingEquipsRecordsListing2" icon={tableSectionIcon} navigation={navigation}  pageMeta={{ ...pageMeta, queryKwd: "wsequip2", title: "用水器具统计表(new)", recordListNaviTo: "WaterSavingEquipsRecordsUpdating2" }} />

                    </List.Section>
                    <Divider bold={true} />
                    <List.Section title={"项目信息(测试用)"}>
                        <CardProjectMessage title="客户资料清单"    naviTo="ClientInfoRecordsListing"             icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "cinfo",     title: "客户资料清单",     recordListNaviTo: "ClientInfoRecordsUpdating" }}/>
                        <CardProjectMessage title="公用工程"       naviTo="ClientPublicUtilityRecordsListing"    icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "cputil",     title: "公用工程",        recordListNaviTo: "ClientPublicUtilityRecordsUpdating" }}/>
                        <CardProjectMessage title="项目产品规模"    naviTo="ClientProductScaleRecordsListing"     icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "cpscale",    title: "项目产品规模",     recordListNaviTo: "ClientProductScaleRecordsUpdating" }}/>
                        <CardProjectMessage title="项目原辅材料消耗" naviTo="ClientMaterialConsumeRecordsListing"  icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "cmconsume",  title: "项目原辅材料消耗",  recordListNaviTo: "ClientMaterialConsumeRecordsUpdating" }}/>
                        <CardProjectMessage title="项目所需设备清单" naviTo="ClientRequiredDeviceRecordsListing"   icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "crdevice",   title: "项目所需设备清单",  recordListNaviTo: "ClientRequiredDeviceRecordsUpdating" }}/>
                    </List.Section>
                    <Divider bold={true} />
                </>}

            </ScreenWrapper>

            <PromptDialog
                visible={promptsVisible}
                setVisible={setPromptsVisible}
                promptsText={promptsText}
                onOKCallback={(prompts) => {
                    setPromptsText(prompts);
                    setTimeout(() => {
                        projGenDocQuery.mutate();
                    }, 200);
                    setDialogConfirmVisible(true);
                }}
                onCancelCallback={() => {}}
                dialogTitle={"请填写AI提示词"}
                okBtnLabel={"确定"}
                cancelBtnLabel={"取消"}
            />

            <DialogToConfirm
                visible={dialogConfirmVisible}
                title={"生成文档"}
                text={"已将请求发送给AI, 稍后请在Web端查看结果!"}
                onOK={() => {setDialogConfirmVisible(false);}}
                okBtnLabel={"确认"}
            />

        </View>
    );
};

/*
const styles = StyleSheet.create({
    container: {
        flex: 1,
        marginTop: 4,
        marginBottom: 4,
        marginHorizontal: 10,
        //marginVertical: 10,
    },
    card: {
        borderWidth: 1,
        marginVertical: 2,
    }
});
*/

export default ProjectIndex;
