///
/// ReactNativeAudioRecorderPlayerOnLoad.cpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc R<PERSON>avy @ Margelo
///

#ifndef BUILDING_REACTNATIVEAUDIORECORDERPLAYER_WITH_GENERATED_CMAKE_PROJECT
#error ReactNativeAudioRecorderPlayerOnLoad.cpp is not being built with the autogenerated CMakeLists.txt project. Is a different CMakeLists.txt building this?
#endif

#include "ReactNativeAudioRecorderPlayerOnLoad.hpp"

#include <jni.h>
#include <fbjni/fbjni.h>
#include <NitroModules/HybridObjectRegistry.hpp>

#include "JHybridAudioRecorderPlayerSpec.hpp"
#include "JFunc_void_RecordBackType.hpp"
#include "JFunc_void_PlayBackType.hpp"

namespace margelo::nitro::react_native_audio_recorder_player {

int initialize(JavaVM* vm) {
  using namespace margelo::nitro;
  using namespace margelo::nitro::react_native_audio_recorder_player;
  using namespace facebook;

  return facebook::jni::initialize(vm, [] {
    // Register native JNI methods
    margelo::nitro::react_native_audio_recorder_player::JHybridAudioRecorderPlayerSpec::registerNatives();
    margelo::nitro::react_native_audio_recorder_player::JFunc_void_RecordBackType_cxx::registerNatives();
    margelo::nitro::react_native_audio_recorder_player::JFunc_void_PlayBackType_cxx::registerNatives();

    // Register Nitro Hybrid Objects
    
  });
}

} // namespace margelo::nitro::react_native_audio_recorder_player
