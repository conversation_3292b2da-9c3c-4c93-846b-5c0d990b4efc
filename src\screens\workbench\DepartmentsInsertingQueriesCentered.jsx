import { joi<PERSON><PERSON><PERSON>ver } from "@hookform/resolvers/joi";
import "fast-text-encoding";
import <PERSON><PERSON> from "joi";
import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { StyleSheet, View } from "react-native";
import { Appbar, Divider, Snackbar, useTheme } from "react-native-paper";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import BottomBarButton from "../../components/BottomBarButton";
import ControlledRadioInput from "../../components/ControlledRadioInput";
import ControlledTextInput from "../../components/ControlledTextInput";
import { DialogToConfirm } from "../../components/DialogToConfirm";
import { creatMMKVStore } from "../../services/local-storage";
import { identity, makeDataFeeder, whenLet } from "../../utils";
import { parseServerState } from "../../utils/messages";
import { validatorBase } from "../../utils/validatorBase";
import ScreenWrapper from "../ScreenWrapper";

// 新组件需要重新!!
import { makeRequestInsertDepartment as insertQueryClient } from "../../api/insertingQueries";
import { makeRequestListingDepartments as allDeptsQueryClient } from "../../api/listingQueries";
import { ORG_DEPARTMENT_ADD as key } from "../../config/keysConfig";
import { onPreSubmitError } from "../../utils/screens";


// 新组件需要重新设置!!
const { setStore, getStore, clearStore } = creatMMKVStore(key.store);
const dataFeeder = makeDataFeeder();
// 数据规范要求服务端数据包含id为0的默认值, 同时mmkv存储的是一个对象, 因此以下几行不再需要
//const superiorReplacer = makeReplacer("superior", "name", "id"); // 数据中superior对应的值, 将它到对象数组中去匹配name键, 若成功, superior的值替换成name所在对象的id的值
//const deptDefaults = { id: 0, name: "无上级部门" };                // 需要添加的默认部门占位
//const deptLookUpKey = "id";                                      // radio按钮根据值方向查找的键值
//const threadFunctions = [superiorReplacer.replace];
//let render = 0;

/**
 * 所有控件数据随着屏幕加载, 目前已改用各控件使用自己的查询, 本屏幕已废弃!
 * @param {Object} args
 * @returns
 */
const DepartmentsInserting = ({ navigation }) => {
    // render++;
    const { bottom, left, right } = useSafeAreaInsets();
    const theme = useTheme();
    const height = theme.isV3 ? 80 : 56;

    // 新组件需要修改!!
    const bottomBarLabels = { ok: "添加部门", cancel: "取消" };                // 底部按钮显示文字
    const confirmDiagText = { title: "添加部门", okBtn: "确定" };              // 确认对话框标题和按钮文字
    const [snackBarMessage, setSnackBarMessage] = useState("添加部门遇到错误"); // 下方错误提示文字内容
    const [deptDataArray, setDeptDataArray] = useState([]);                  // 用作radio选项的数据, 添加了默认项
    const [superiorDisplayText, setSuperiorDisplayText] = useState(getStore("superior")?.name); // 上级部门显示文本

    // 新组件不需改动
    const saveButtonIcon = "content-save-all-outline";
    const [okButtonLoading, setOkButtonLoading] = useState(false);           // 右下方按钮图标是否旋转
    const [okButtonDisabled, setOkButtonDisabled] = useState(false);         // 右下方按钮是否禁用
    const [cancelButtonDisabled, setCancelButtonDisabled] = useState(false); // 左下方按钮是否禁用
    const [showSnackbar, setShowSnackbar] = useState(false);                 // 下方是否显示错误通知
    const [showConfirm, setShowConfirm] = useState(false);                   // 是否显示确认对话框
    const [diagText, setDiagText] = useState("");                            // 对话框文本

    // 新组件需要修改!!
    const onDeptQuerySuccess = (data) => {
        if (data.STATUS === 0) {
            // /const fullDataArray = radioItemMakeSureDefault(data.DATA, deptDefaults, deptLookUpKey);
            //setDeptDataArray(fullDataArray);
            console.log("data.DATA", data.DATA);
            //console.log("fullDataArray", fullDataArray);
            //superiorReplacer.setLookup(fullDataArray);
            setDeptDataArray(data.DATA);
        } else {
            setSnackBarMessage(parseServerState(data.STATUS || data.status, data.DATA.info));
            setShowSnackbar(true);
        }
    };
    const radioDataQuery = allDeptsQueryClient(onDeptQuerySuccess);

    // 新组件需要重新定义!!
    const schema = Joi.object({
        name: validatorBase.deptName,
        superior: validatorBase.deptId,
        //leader:     validatorBase.userPubid,
        //viceLeader: validatorBase.userPubid,
    });

    // 新组件需要重新定义defaultValues!!
    const {
        handleSubmit,
        control,
        resetField,
        reset,
        formState: { errors },
    } = useForm({
        resolver: joiResolver(schema),
        //reValidateMode: "onBlur", // https://github.com/orgs/react-hook-form/discussions/10327
        defaultValues: {
            name: whenLet(getStore("name"), identity, ""),
            superior: whenLet(getStore("superior"), identity, 0),
            //leader:     whenLet(getStore("leader"),     identity, ""),
            //viceLeader: whenLet(getStore("viceLeader"), identity, ""),
        },
    });
    const resetDefaultValues = { // 用于还原时替换已存储的默认值
        name: "",
        superior: 0,
    };

    // 新组件需要重新定义setDiagText!!
    const onHandleSubmit = (data) => { // data: {"name": "mmm", "superior": 0}
        // 由于这里的data的版本可能先于mmkv的版本, 因此必须在此将data转储到一个引用地址, 以供query使用, 这就是dataFeeder的用处, 另外dataFeeder可以做一些数据预处理
        setCancelButtonDisabled(true);
        setOkButtonDisabled(true);
        setOkButtonLoading(true);
        setDiagText("新部门添加成功: " + data.name);
        //const replacedData = callThreads(data, threadFunctions);
        dataFeeder(data);
        submitQuery.mutate();
    };
    // 新组件不需改动
    const onCancel = () => {
        clearStore();
        reset(resetDefaultValues);
        setSuperiorDisplayText("");
    };

    // 新组件不需改动
    const commitOnSuccess = () => { setShowConfirm(true); };
    const commitOnError = () => {};
    const commitOnSettled = () => {
        setCancelButtonDisabled(false);
        setOkButtonDisabled(false);
        setOkButtonLoading(false);
    };
    const submitQuery = insertQueryClient(dataFeeder, commitOnSuccess, commitOnError, commitOnSettled);

    // 新组件不需改动
    const onDiagConfirm = () => {
        clearStore();
        reset(resetDefaultValues);
        setShowConfirm(false);
        setSuperiorDisplayText("");
        radioDataQuery.mutate();
    };

    useEffect(() => {
        radioDataQuery.mutate();
    }, []);

    return (
        <>
            <ScreenWrapper contentContainerStyle={styles.container} style={{marginBottom: height+ bottom}}>
                {/*<Text>Render: {render}</Text>*/}

                <View style={styles.formEntry}>
                    <ControlledTextInput
                        rowLabel="部门名称"
                        control={control}
                        name="name"
                        placeholder="请填写部门名称"
                        onChangeText={(text) => setStore("name", text)}
                        onClearText={() => {
                            setStore("name", "");
                            resetField("name", { defaultValue: "" });
                        }}
                        editable={true}
                        mode="outlined"
                        formError={errors}
                        multiline={true}
                    />

                    <ControlledRadioInput
                        name="superior"
                        rowLabel="上级部门"
                        control={control}
                        placeholder="请拉选上级部门"
                        //getStoredVal={() => getStore("superior")}
                        resultText={superiorDisplayText}
                        setResultText={setSuperiorDisplayText}
                        onRadioConfirm={(obj) => setStore("superior", obj)}
                        defaultRadioCheck={() => getStore("superior")}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        radioItemArray={deptDataArray}
                    />

                </View>

            </ScreenWrapper>

            <Appbar
                style={[
                    styles.bottom,
                    {
                        height: height + bottom,
                    },
                    theme.isV3 && {
                        backgroundColor: theme.colors.elevation.level2,
                    },
                ]}
                safeAreaInsets={{ bottom, left, right }}
                theme={{ mode: "adaptive" }}
            //mode='center-aligned'
            >

                <View style={styles.bottomBarContainer}>
                    <Divider />
                    <View style={styles.bottomBarComponents}>
                        <BottomBarButton
                            label={bottomBarLabels.cancel}
                            disabled={cancelButtonDisabled}
                            onPress={onCancel}
                        />
                        <BottomBarButton
                            label={bottomBarLabels.ok}
                            loading={okButtonLoading}
                            disabled={okButtonDisabled}
                            icon={saveButtonIcon}
                            onPress={handleSubmit(onHandleSubmit, (formErr) => onPreSubmitError(formErr, getStore, "DepartmentsInsertingQueriesCentered"))}
                        />
                    </View>
                </View>
            </Appbar>

            <Snackbar
                style={styles.snackBar}
                visible={showSnackbar}
                onDismiss={() => {
                    setShowSnackbar(false);
                }}
                onIconPress={() => {
                    setShowSnackbar(false);
                }}
                duration={Snackbar.DURATION_LONG}
            >
                {snackBarMessage}
            </Snackbar>

            <DialogToConfirm
                title={confirmDiagText.title}
                text={diagText}
                visible={showConfirm}
                onOK={onDiagConfirm}
                okBtnLabel={confirmDiagText.okBtn}
            />
        </>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    firstColumn: {
        flex: 2,
        //borderWidth: 1,
        //height: 40,
        content: {
            fontSize: 18,
        }
    },
    secondColumn: {
        flex: 3,
        // /borderWidth: 1,
        // height: 40,
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        flex: 1,
        marginHorizontal: 6
    },
    formEntry: {
        margin: 8,
    },
    snackBar: {
        flexDirection: "column",
    },
});

export default DepartmentsInserting;
