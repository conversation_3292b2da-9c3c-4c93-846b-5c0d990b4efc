import React, { useRef, useState } from "react";
import { Divider, Appbar, useTheme, Button } from "react-native-paper";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Alert, Image, PermissionsAndroid, Platform, StyleSheet, View } from "react-native";
import ScreenWrapper from "../ScreenWrapper";
import BottomBarButton from "../../components/BottomBarButton";
import { httpClient } from "../../services/http";

import ImagePicker from "react-native-image-crop-picker";
import { check, request, PERMISSIONS, RESULTS } from "react-native-permissions";
import RNFS from "react-native-fs";
import { getSystemVersion } from "react-native-device-info";
import { Image as CompressImage} from "react-native-compressor";


const FILE_MAX_SIZE = 20 * 1024 * 1024;

/**
 * 测试获取图像并上传到服务器.
 * 关于图像获取, 压缩, 传输: https://dev.to/ajmal_hasan/react-native-fileimage-picker-1o2j
 * Expo图像获取基本用法: https://docs.expo.dev/versions/latest/sdk/imagepicker/#usage
 * 关于权限: https://github.com/sanchit0496/react_native_scaffolding/blob/main/screens/ImagePickerScreen.js
 * @param {Object} args
 * @returns
 */
const TestUploadFiles = ({ navigation }) => {
    const { bottom, left, right } = useSafeAreaInsets();
    const theme = useTheme();
    const height = theme.isV3 ? 80 : 56;

    const [saveButtonLoading] = useState(false);
    const [saveButtonDisabled] = useState(false);
    const [saveButtonIcon] = useState("content-save-all-outline");

    const [image, setImage] = useState(null);
    const imagePickResult = useRef();


    console.log("getSystemVersion:", getSystemVersion());


    function getFileExtension(uri) {
        const lastDotIndex = uri.lastIndexOf(".");
        if (lastDotIndex !== -1) {
            return uri.slice(lastDotIndex + 1);
        }
        return null; // or an appropriate default value
    }

    const requestCameraWithPermission = async () => {
        try {
            const cameraPermission = await check(PERMISSIONS.ANDROID.CAMERA);

            if (cameraPermission === RESULTS.GRANTED) {
                console.log("Camera permission already granted");
                return pickImageFromCamera();
            }
            const cameraPermissionResult = await request(PERMISSIONS.ANDROID.CAMERA);

            if (cameraPermissionResult === RESULTS.GRANTED) {
                console.log("Camera permission granted");
                return pickImageFromCamera();
            }
            console.log("Camera permission denied");
        } catch (error) {
            console.log("Error checking/requesting camera permission:", error);
            return null;
        }
    };

    const requestGalleryWithPermission = async () => {
        try {
            if (Platform.OS === "android") {
                const deviceVersion = getSystemVersion();
                let granted = PermissionsAndroid.RESULTS.DENIED;
                if (deviceVersion >= 13) {
                    granted = PermissionsAndroid.RESULTS.GRANTED;
                } else {
                    granted = await PermissionsAndroid.request(
                        PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE
                    );
                }
                if (granted) {
                    return pickImageFromGallery();
                }
            } else {
            // On iOS, permissions are typically not required for accessing the photo library
                console.log("iOS platform: No specific permissions required for media library");
                return pickImageFromGallery();
            }
        } catch (error) {
            console.log("Error checking/requesting storage permission:", error);
            return null;
        }
    };

    const pickImageFromCamera = async () => {
        try {
            const image = await ImagePicker.openCamera({
            // width: 300,
            // height: 400,
                cropping: true,
                multiple: false,
                mediaType: "photo",
            });

            const rawSize = await RNFS.stat(image.path);
            console.log("Image raw size: ", rawSize.size);

            if (image) {
                const pathCompressed = await CompressImage.compress(image?.path, {
                    maxWidth: 1500,
                    maxHeight: 1000,
                    quality: 0.8,
                });
                const imageCompressed = await RNFS.stat(pathCompressed);

                console.log("pickImageFromCamera, raw image:", image);
                console.log("pickImageFromCamera, compressed image:", imageCompressed);
                if (imageCompressed.size > FILE_MAX_SIZE) {
                    Alert.alert("File Size Limit Exceeded", "Please select a file up to 20 MB.");
                } else {
                    // The picked document is available in the 'result' object
                    setImage(imageCompressed.path);
                    imagePickResult.current = imageCompressed;
                    const pickedImage = {
                        name: image?.filename || `image_${Date.now()}.${getFileExtension(imageCompressed?.path)}`,
                        type: image?.mime,
                        uri: imageCompressed?.path,
                        size: imageCompressed?.size,
                    };
                    imagePickResult.current = pickedImage;
                    return pickedImage;
                }
            }
        } catch (error) {
            console.log("Error picking image from camera:", error);
            return null;
        }
    };

    const pickImageFromGallery = async () => {
        try {
            const image = await ImagePicker.openPicker({
            // width: 300,
            // height: 400,
                cropping: true,
                multiple: false,
                mediaType: "photo",
            });

            const rawSize = await RNFS.stat(image.path);
            console.log("Image raw size: ", rawSize.size);

            if (image) {
                const pathCompressed = await CompressImage.compress(image?.path, {
                    maxWidth: 2000,
                    maxHeight: 1500,
                    quality: 0.8,
                });
                const imageCompressed = await RNFS.stat(pathCompressed);

                console.log("pickImageFromGallery, raw image:", image);
                console.log("pickImageFromGallery, compressed image:", imageCompressed);
                if (imageCompressed.size > FILE_MAX_SIZE) {
                    Alert.alert("File Size Limit Exceeded", "Please select a file up to 2 MB.");
                } else {
                    // The picked document is available in the 'result' object
                    setImage(imageCompressed.path);
                    imagePickResult.current = imageCompressed;
                    const pickedImage = {
                        name: image?.filename || `image_${Date.now()}.${getFileExtension(imageCompressed?.path)}`,
                        type: image?.mime,
                        uri: imageCompressed?.path,
                        size: imageCompressed?.size,
                    };
                    imagePickResult.current = pickedImage;
                    return pickedImage;
                }
            }
        } catch (error) {
            console.log("Error picking image from gallery:", error);
            return null;
        }
    };

    const uploadImage = async (pickResult) => {
        //
        console.log("uploadImage:", pickResult);
        try {
            const formData = new FormData();
            formData.append("file", {
                uri: pickResult.uri,
                type: pickResult.type,
                name: pickResult.name, // Adjust the filename as needed
            });

            const response = await httpClient.post("upload-image", {
                method: "POST",
                body: formData,
                headers: {
                    "Content-Type": "multipart/form-data",
                    // You may need to include additional headers depending on your API requirements
                },
            });

            const responseData = await response.json();
            console.log("File upload response:", responseData);
        } catch (error) {
            console.log("File upload error:", error);
        }
    };

    /*
    //https://juejin.cn/post/7067108052075282469
    const pickPicture = () => {
        ImagePicker.openPicker({
            width: 300,
            height: 400,
            cropping: true,
        }).then(image => {
            console.log("picked a picture: ", image);
            setImage(image.path);
        });
    };
    */

    /*
    useEffect(() => {
        (async () => {
            if (Platform.OS !== "web") {
                const libraryStatus = await ImagePicker.requestMediaLibraryPermissionsAsync();
                if (libraryStatus.status !== "granted") {
                    Alert("Sorry, we need camera roll permissions to make this work!");
                }

                const cameraStatus = await ImagePicker.requestCameraPermissionsAsync();
                if (cameraStatus.status !== "granted") {
                    Alert("Sorry, we need camera permissions to make this work!");
                }
            }
        })();
    }, []);
*/

    /* with expo image picker
    const pickImage = async () => {
        // No permissions request is necessary for launching the image library
        let result = await ImagePicker.launchImageLibraryAsync({
            mediaTypes: ImagePicker.MediaTypeOptions.All,
            allowsEditing: true,
            aspect: [4, 3],
            quality: 0.5,
        });

        imagePickResult.current = result;
        console.log(result);

        if (!result.canceled) {
            setImage(result.assets[0].uri);
        }
    };

    const uploadImage = async (pickResult) => {
        // /{"assets": [{"assetId": null, "base64": null, "duration": null, "exif": null, "fileName": "43b69a33-b591-4229-8fc3-c7962d770896.jpeg", "fileSize": 169928, "height": 1080, "mimeType": "image/jpeg", "rotation": null, "type": "image", "uri": "file:///data/user/0/com.lowcarbonmanagement/cache/ImagePicker/43b69a33-b591-4229-8fc3-c7962d770896.jpeg", "width": 1440}], "canceled": false}
        console.log("uploadImage:", pickResult);
        try {
            const formData = new FormData();
            formData.append("file", {
                uri: pickResult.assets[0].uri,
                type: pickResult.assets[0].mimeType,
                name: pickResult.assets[0].fileName, // Adjust the filename as needed
            });

            const response = await httpClient.post("upload-image", {
                method: "POST",
                body: formData,
                headers: {
                    "Content-Type": "multipart/form-data",
                    // You may need to include additional headers depending on your API requirements
                },
            },
            );

            const responseData = await response.json();
            console.log("File upload response:", responseData);
        } catch (error) {
            console.log("File upload error:", error);
        }
    };
*/

    return (
        <>
            <ScreenWrapper contentContainerStyle={styles.container}>
                <View>
                    {image && <Image source={{uri: image}} style={styles.image} />}
                    <Button mode="contained" style={styles.Button} onPress={requestGalleryWithPermission}>
                    从图库获取图片
                    </Button>
                    <Button mode="contained" style={styles.Button} onPress={requestCameraWithPermission}>
                    从摄像头获取图片
                    </Button>
                </View>

                <Button
                    icon="camera"
                    mode="contained"
                    style={styles.Button}
                    onPress={() => uploadImage(imagePickResult.current)}>
                    上传图片
                </Button>
            </ScreenWrapper>

            <Appbar
                style={[
                    styles.bottom,
                    {
                        height: height + bottom,
                    },
                    theme.isV3 && {
                        backgroundColor: theme.colors.elevation.level2,
                    },
                ]}
                safeAreaInsets={{bottom, left, right}}
                theme={{mode: "adaptive"}}
                //mode='center-aligned'
            >
                <View style={styles.bottomBarContainer}>
                    <Divider />
                    <View style={styles.bottomBarComponents}>
                        <BottomBarButton
                            label={"取消"}
                            disabled={false}
                            onPress={() => {}}
                        />
                        <BottomBarButton
                            label={"保存"}
                            loading={saveButtonLoading}
                            disabled={saveButtonDisabled}
                            icon={saveButtonIcon}
                            onPress={() => {
                                uploadImage(imagePickResult.current);
                            }}
                        />
                    </View>
                </View>
            </Appbar>
        </>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        alignItems: "center",
        justifyContent: "center",
    },
    Button: {
        marginTop: 20,
        marginBottom: 20,
    },
    image: {
        width: 200,
        height: 150,
        marginTop: 20,
        marginBottom: 20,
    },
});

export default TestUploadFiles;
