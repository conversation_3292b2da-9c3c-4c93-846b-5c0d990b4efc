import React from "react";
import { DataTable, Text, useTheme } from "react-native-paper";
import { Controller } from "react-hook-form";
import { Pressable, StyleSheet, View } from "react-native";
import { ErrorMessage } from "@hookform/error-message";
import ControlledCellImageInput from "./ControlledCellImageInput";
import { isFunction, isObject } from "../utils";
//import PropTypes from "prop-types";

/**
 * 废弃prop: pickResult: 用于存储图像pick结果的useRef数据, pickResult.current不为null表示获取到了图像, 可以由此上传到服务器. pickResult将在调用屏幕通过文件路径重建, 减少调用传参!
 * DataTable中一个2列的记录行, 左边是提示文字, 右边是图像展示
 * 注意: props中同时包含style和rowStyle的原因是:
 * 在FormListNodesMapper中会有别的样式加入并组合成一个数组后通过style传到本组件, 使得在这里展开后不能覆盖
 * style array: [{"paddingLeft": 40}, {"firstColumn": {"content": [Object], "flex": 1}, "secondColumn": {"flex": 1}}]
 * This array has a padding for List.Accordion with an indent efx and which has been ignored here.
 * @param {Object} arg
 * @param {string} arg.rowLabel 左边的字段名称标签
 * @param {function} arg.onPickImageCB
 * @param {function} arg.onDeleteImageCB
 * @param {boolean} arg.disabled
 * @param {function} arg.setImageUri 设置图像Uri的函数, 参数为图像路径, 调用本函数设置图像的本地路径, 显示到输入处.
 * @param {number} arg.pickNum 默认为1, 表示最多选择1张图片, 2表示最多选择2张图片, 依此类推, 0表示无限制(由全局上限限制).
 * @param {int | null} arg.width 界面上图像框的宽度, 默认null, 表示使用最大宽度.
 * @param {int} arg.height 图像高度, 默认150.
 * @param {boolean} arg.crop 是否剪裁, false不剪裁, 使用原图
 * @param {number | false | undefined | null} arg.compress 压缩程度, 0~1, 为假值时(包括0)表示不压缩
 */
const ControlledImageInput = ({ name, rowLabel, toolTip, control, placeholder, onPickImageCB, onDeleteImageCB, setImageUri, pickNum = 1, width = null, height = 150, crop = false, compress = 0.8, editable = true, style={}, rowStyle={}, ...props }) => {
    const theme = useTheme();
    const styles = StyleSheet.create({
        container: {
            paddingLeft: 4,
            paddingRight: 0,
            marginLeft: 0,
            marginRight: -8,
            //borderWidth: 1,
            //height: 60,
            //borderColor: "black",
        },
        firstColumn: {
            flex: 1,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 2,
            //borderWidth: 1,
            // height: 40,
        },
        inputBox: {
            width: "100%"
        },
        errMsg: {
            fontSize: 16,
            color: theme.colors.error,
        },
        ...(isObject(style) ? style : {}),
        ...rowStyle,
    });

    return (
        <DataTable.Row style={styles.container}>
            <DataTable.Cell style={styles.firstColumn}>
                {toolTip ?
                    <Pressable onPress={()=>{
                        isFunction(props.setSnackbarMessage) && props.setSnackbarMessage(toolTip);
                        isFunction(props.setDisplaySnackbar) && props.setDisplaySnackbar(true);
                    }}>
                        <Text style={styles.firstColumn.content}>
                            {(props.required ? rowLabel + " *" : rowLabel) + " ℹ️"}
                        </Text>
                    </Pressable> :
                    <Text style={styles.firstColumn.content}>
                        {props.required ? rowLabel + " *" : rowLabel}
                    </Text>}
            </DataTable.Cell>
            <DataTable.Cell style={styles.secondColumn}>
                <Controller
                    control={control}
                    render={({ field: { onChange, onBlur, value } }) => (
                        <View style={styles.inputBox}>
                            <ControlledCellImageInput
                                onBlur={onBlur}
                                onPickImageCB={path => {
                                    onChange([...value, path]);     // react-hook-form修改内部状态
                                    onPickImageCB(path); // 调用mmkv保存当前输入
                                }}
                                onDeleteImageCB={onDeleteImageCB}
                                pickNum={pickNum}
                                uriArray={value}
                                width={width}
                                height={height}
                                crop={crop}
                                compress={compress}
                                returnKeyType="next"
                                placeholder={placeholder}
                                editable={editable}
                                {...props}
                            />
                            {props?.formError &&
                                <ErrorMessage
                                    errors={props.formError}
                                    name={name}
                                    render={({ message }) => {
                                        return (
                                            <Text style={styles.errMsg}>
                                                {"⚠ " + message}
                                            </Text>);
                                    }}
                                />
                            }
                        </View>
                    )}
                    name={name}
                    rules={{ required: true }}
                />
            </DataTable.Cell>
        </DataTable.Row>
    );
};


export default ControlledImageInput;
