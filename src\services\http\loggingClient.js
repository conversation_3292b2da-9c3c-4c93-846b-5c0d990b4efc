import ky from "ky";
import { BASE_LOGGING_URL } from "../../config";
import { getToken } from "../../utils/user";
import { APP_VERSION, CLIENT_OS } from "../system";

// https://github.com/sindresorhus/ky

// no retries
const RETRY_CODES = [];
const RETRY_INTERVALS = [0, 3000, 10000, 30000];
const RETRY_METHODS = ["get", "post", "put", "head", "delete", "options", "trace"];
const RETRY_LIMIT = 0;
const TIMEOUT = 5000;

/**
 * ky的自定义实例, 用于向远程服务器输出日志的HTTP通信.
 */
export const loggingHttpClient = ky.extend({
    prefixUrl: BASE_LOGGING_URL,
    timeout: TIMEOUT,
    retry: {
        limit: RETRY_LIMIT,
        methods: RETRY_METHODS,
        statusCodes: RETRY_CODES,
        delay: attemptCount => RETRY_INTERVALS[attemptCount], // attemptCount starts at 1
    },
    headers: {
        "content-type": "application/json",
    },
    hooks: {
        beforeRequest: [
            (request) => {
                request.headers.set("Authorization", getToken());
                request.headers.set("APPOS",         CLIENT_OS);
                request.headers.set("AppVersion",    APP_VERSION);
            },
        ],
    },
});
