import React from "react";
import ListingTemplate from "./ListingTemplate";
import { makeRequestListingRoles as queryClient } from "../../api/listingQueries";
import { allRolesState as pageDataState } from "../../hooks/globalStates";


const RolesListing = ({ navigation }) => {
    return (
        <ListingTemplate
            bottomBarLabel={"添加角色"}
            emptyScreenText={"请点击下方按钮添加角色"}
            snackBarDefaultText={"添加角色遇到错误"}
            saveButtonIcon={"account-plus-outline"}
            queryClient={queryClient}
            pageDataState={pageDataState}
            listItemNavigateTo={"WorkbenchRolesUpdating"}
            addingButtonNavigateTo={"WorkbenchRolesInserting"}
            navigation={navigation}
        />
    );
};

export default RolesListing;
