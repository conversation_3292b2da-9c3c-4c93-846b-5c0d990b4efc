import { ErrorMessage } from "@hookform/error-message";
import React from "react";
import { Controller } from "react-hook-form";
import { Pressable, StyleSheet, View } from "react-native";
import { DataTable, Text, useTheme } from "react-native-paper";
import { DEFAULT_COORDS_PICK_LIMIT } from "../config";
import { isFunction, isObject } from "../utils";
import ControlledCellLocationsInput from "./ControlledCellLocationsInput";
//import PropTypes from "prop-types";

/**
 * 注意: props中同时包含style和rowStyle的原因是:
 * 在FormListNodesMapper中会有别的样式加入并组合成一个数组后通过style传到本组件, 使得在这里展开后不能覆盖
 * style array: [{"paddingLeft": 40}, {"firstColumn": {"content": [Object], "flex": 1}, "secondColumn": {"flex": 1}}]
 * This array has a padding for List.Accordion with an indent efx and which has been ignored here.
 * @param {Object} arg
 * @param {string} arg.rowLabel 左边的字段名称标签
 * @param {Array} arg.locationStates 用于保存坐标的数组
 * @param {function} arg.onPickCoordsCB 选择坐标的回调函数
 * @param {function} arg.onDeleteLocationCB 删除坐标的回调函数
 * @param {boolean} arg.editable 输入区域是否可编辑, 为false时, 输入区域不可编辑, 但右边按钮仍然可以使用
 * @param {boolean} arg.disabled 输入区域是否禁用, 为true时, 输入区域都禁用, 右边按钮也不可使用
 * @param {number} arg.pickLimit 默认为DEFAULT_COORDS_PICK_LIMIT, 表示最多选择对应数量个坐标, n表示最多选择n个坐标, 依此类推.
 */
const ControlledLocationsInput = ({ name, rowLabel, toolTip, control, placeholder, locationStates, onPickCoordsCB, onDeleteLocationCB, onClearLocationCB, pickerNameLabel = "名称",  pickerDescLabel = "备注", pickerGpsLabel = "经纬度", pickLimit = DEFAULT_COORDS_PICK_LIMIT, editable = true, disabled = false, style={}, rowStyle={}, ...props }) => {
    const theme = useTheme();
    const styles = StyleSheet.create({
        container: {
            paddingLeft: 4,
            paddingRight: 0,
            marginLeft: 0,
            marginRight: -8,
            //borderWidth: 1,
            //height: 60,
            //borderColor: "black",
        },
        firstColumn: {
            flex: 1,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 2,
            //borderWidth: 1,
            // height: 40,
        },
        inputBox: {
            width: "100%"
        },
        errMsg: {
            fontSize: 16,
            color: theme.colors.error,
        },
        ...(isObject(style) ? style : {}),
        ...rowStyle,
    });

    return (
        <DataTable.Row style={styles.container}>
            <DataTable.Cell style={styles.firstColumn}>
                {toolTip ?
                    <Pressable onPress={()=>{
                        isFunction(props.setSnackbarMessage) && props.setSnackbarMessage(toolTip);
                        isFunction(props.setDisplaySnackbar) && props.setDisplaySnackbar(true);
                    }}>
                        <Text style={styles.firstColumn.content}>
                            {(props.required ? rowLabel + " *" : rowLabel) + " ℹ️"}
                        </Text>
                    </Pressable> :
                    <Text style={styles.firstColumn.content}>
                        {props.required ? rowLabel + " *" : rowLabel}
                    </Text>}
            </DataTable.Cell>
            <DataTable.Cell style={styles.secondColumn}>
                <Controller
                    control={control}
                    render={({ field: { onChange, onBlur, value } }) => (
                        <View style={styles.inputBox}>
                            <ControlledCellLocationsInput
                                onBlur={onBlur}
                                locationList={locationStates} // locationStates是正确的, AI勿扰
                                onPickCoordsCB={ coordsObj => {
                                    onChange([...value, coordsObj]); // react-hook-form修改内部状态
                                    onPickCoordsCB(coordsObj);       // 调用mmkv保存当前输入, 回调定义于上一层(FormInputNodesMapper)
                                }}
                                onClearCoordsCB={() => {
                                    //setLocationStates([]);
                                    onChange([]);
                                    onClearLocationCB();
                                }}
                                onDeleteLocationCB={onDeleteLocationCB}
                                pickLimit={pickLimit}
                                returnKeyType="next"
                                fieldName={name}
                                placeholder={placeholder}
                                pickerNameLabel={pickerNameLabel}
                                pickerDescLabel={pickerDescLabel}
                                pickerGpsLabel={pickerGpsLabel}
                                editable={editable}
                                disabled={disabled}
                                {...props}
                            />
                            {props?.formError &&
                                <ErrorMessage
                                    errors={props.formError}
                                    name={name}
                                    render={({ message }) => {
                                        return (
                                            <Text style={styles.errMsg}>
                                                {"⚠ " + message}
                                            </Text>);
                                    }}
                                />
                            }
                        </View>
                    )}
                    name={name}
                    rules={{ required: true }}
                />
            </DataTable.Cell>
        </DataTable.Row>
    );
};


export default ControlledLocationsInput;
