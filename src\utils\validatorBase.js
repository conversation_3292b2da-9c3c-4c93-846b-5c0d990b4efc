import <PERSON><PERSON> from "joi";
import { RangeRegex } from "./regex";

// Joi表单验证
// https://stackoverflow.com/questions/48720942/node-js-joi-how-to-display-a-custom-error-messages


/* templates, https://github.com/hapijs/joi/tree/master/lib/types
string: {
    'string.alphanum': '{{#label}} must only contain alpha-numeric characters',
    'string.base': '{{#label}} must be a string',
    'string.base64': '{{#label}} must be a valid base64 string',
    'string.creditCard': '{{#label}} must be a credit card',
    'string.dataUri': '{{#label}} must be a valid dataUri string',
    'string.domain': '{{#label}} must contain a valid domain name',
    'string.email': '{{#label}} must be a valid email',
    'string.empty': '{{#label}} is not allowed to be empty',
    'string.guid': '{{#label}} must be a valid GUID',
    'string.hex': '{{#label}} must only contain hexadecimal characters',
    'string.hexAlign': '{{#label}} hex decoded representation must be byte aligned',
    'string.hostname': '{{#label}} must be a valid hostname',
    'string.ip': '{{#label}} must be a valid ip address with a {{#cidr}} CIDR',
    'string.ipVersion': '{{#label}} must be a valid ip address of one of the following versions {{#version}} with a {{#cidr}} CIDR',
    'string.isoDate': '{{#label}} must be in iso format',
    'string.isoDuration': '{{#label}} must be a valid ISO 8601 duration',
    'string.length': '{{#label}} length must be {{#limit}} characters long',
    'string.lowercase': '{{#label}} must only contain lowercase characters',
    'string.max': '{{#label}} length must be less than or equal to {{#limit}} characters long',
    'string.min': '{{#label}} length must be at least {{#limit}} characters long',
    'string.normalize': '{{#label}} must be unicode normalized in the {{#form}} form',
    'string.token': '{{#label}} must only contain alpha-numeric and underscore characters',
    'string.pattern.base': '{{#label}} with value {:[.]} fails to match the required pattern: {{#regex}}',
    'string.pattern.name': '{{#label}} with value {:[.]} fails to match the {{#name}} pattern',
    'string.pattern.invert.base': '{{#label}} with value {:[.]} matches the inverted pattern: {{#regex}}',
    'string.pattern.invert.name': '{{#label}} with value {:[.]} matches the inverted {{#name}} pattern',
    'string.trim': '{{#label}} must not have leading or trailing whitespace',
    'string.uri': '{{#label}} must be a valid uri',
    'string.uriCustomScheme': '{{#label}} must be a valid uri with a scheme matching the {{#scheme}} pattern',
    'string.uriRelativeOnly': '{{#label}} must be a valid relative uri',
    'string.uppercase': '{{#label}} must only contain uppercase characters'
}

number: {
    'number.base': '{{#label}} must be a number',
    'number.greater': '{{#label}} must be greater than {{#limit}}',
    'number.infinity': '{{#label}} cannot be infinity',
    'number.integer': '{{#label}} must be an integer',
    'number.less': '{{#label}} must be less than {{#limit}}',
    'number.max': '{{#label}} must be less than or equal to {{#limit}}',
    'number.min': '{{#label}} must be greater than or equal to {{#limit}}',
    'number.multiple': '{{#label}} must be a multiple of {{#multiple}}',
    'number.negative': '{{#label}} must be a negative number',
    'number.port': '{{#label}} must be a valid port',
    'number.positive': '{{#label}} must be a positive number',
    'number.precision': '{{#label}} must have no more than {{#limit}} decimal places',
    'number.unsafe': '{{#label}} must be a safe number'
}
*/

// empty values: https://github.com/hapijs/joi/issues/1597, 举例: universalTime
// prefs配置: https://joi.dev/api/?v=17.13.3#anyprefsoptions---aliases-preferences-options, 举例: 取两位小数

const mobileRegex = /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/;
// const emailRegex = /\S+@\S+\.\S+/;

// doc: https://github.com/hapijs/joi/blob/v17.12.1/API.md
// sandbox: https://joi.dev/tester/


export const genRadioValidatorRule = (allow = 0, msg = "", ...validNums) => {
    return Joi.number().integer().valid(...validNums).allow(allow).required().messages({
        "*": `请拉选${msg}数据项`,
    });
};

/*
// Gemini recommended impl.
export const genRadioValidatorRule = (allow = 0, msg = "", validNumsLabels) => {
    return Joi.number().integer().valid(...validNumsLabels.map(item => item.value)).allow(allow).required().messages({
        "*": `请拉选${msg}数据项, 合法值为:${validNumsLabels.map(item => item.label).join(',')}`,
    });
};
*/


export const validatorBase = {
    name: Joi.string().trim().min(2).max(10).required().messages({
        "string.base": "姓名必须是文本格式",
        "string.empty": "请填写姓名",
        "string.min": "姓名长度不能少于{#limit}个字符",
        "string.max": "姓名长度不能超过{#limit}个字符",
        "any.required": "姓名必须填写"
    }),
    age: Joi.number().integer().min(1).max(999).required().messages({
        "number.base": "请填写整数年龄",
        "number.integer": "请填写整数年龄",
        "number.min": "年龄不能小于{#limit}",
        "number.max": "年龄不能超过{#limit}",
    }),
    mobile: {
        required: Joi.string().length(11).regex(mobileRegex).required().messages({
            "string.base": "手机号码包含11个数字",
            "string.empty": "请填写手机号码",
            "string.length": "手机号包含11个数字",
            "string.pattern.base": "手机号格式错误",
        }),
        unrequired: Joi.string().length(11).regex(mobileRegex).allow("").messages({
            "string.base": "手机号码包含11个数字",
            "string.length": "手机号包含11个数字",
            "string.pattern.base": "手机号格式错误",
        }),
    },

    /*
    email: {
        required: Joi.string().regex(emailRegex).required().messages({
            "string.base": "邮箱必须是文本格式",
            "string.empty": "请填写电子邮箱",
            "string.pattern.base": "电子邮箱格式错误",
        }),
        unrequired: Joi.string().regex(emailRegex).allow("").messages({
            "string.base": "邮箱必须是文本格式",
            "string.pattern.base": "电子邮箱格式错误",
        }),
    },
    */

    email: {
        required: Joi.string().email({ tlds: { allow: false } }).required().messages({
            "string.base": "邮箱必须是文本格式",
            "string.empty": "请填写电子邮箱",
            "string.email": "电子邮箱格式错误", // Changed here
        }),
        unrequired: Joi.string().email({ tlds: { allow: false } }).allow("").messages({
            "string.base": "邮箱必须是文本格式",
            "string.email": "电子邮箱格式错误", // Changed here
        }),
    },

    universalTime: {
        required: Joi.number().integer().min(1).required().messages({
            "*": "请选择日期",
        }),
        unrequired: Joi.number().integer().min(0).empty("").default(0).messages({
            "number.base": "请选择日期",
            "number.min": "不能小于{#limit}",
            "number.max": "不能超过{#limit}",
        }),
    },

    // 用户
    userName: {
        required: Joi.string().trim().min(2).max(20).required().messages({
            "string.base": "姓名必须是文本格式",
            "string.empty": "请填写姓名",
            "string.min": "姓名长度不能少于{#limit}个字符",
            "string.max": "姓名长度不能超过{#limit}个字符",
            "any.required": "姓名必须填写"
        }),
    },
    userMobile: {
        required: Joi.string().trim().length(11).regex(mobileRegex).required().messages({
            "string.base": "手机号码包含11个数字",
            "string.empty": "请填写手机号码",
            "string.length": "手机号包含11个数字",
            "string.pattern.base": "手机号格式错误",
        }),
        unrequired: Joi.string().trim().length(11).regex(mobileRegex).allow("").messages({
            "string.base": "手机号码包含11个数字",
            "string.empty": "请填写手机号码",
            "string.length": "手机号包含11个数字",
            "string.pattern.base": "手机号格式错误",
        }),
    },
    userRoles: {
        required: Joi.array().items(Joi.number().integer().min(0).max(60)).min(1).messages({
            "*": "请勾选用户角色",
        }),
        unrequired: Joi.array().items(Joi.number().integer().min(0).max(60)),
    },
    userState: {
        required: Joi.number().integer().min(0).max(5).required().messages({
            "number.base": "请填写整数值",
            "number.integer": "请填写整数值",
            "number.min": "状态不能小于{#limit}",
            "number.max": "状态不能超过{#limit}",
        }),
    },
    userPassword: {
        required: Joi.string().trim().min(6).required().messages({
            "string.base": "密码必须是文本格式",
            "string.empty": "请填写初始密码",
            "string.min": "初始密码长度不能少于{#limit}个字符",
        }),
        unrequired: Joi.string().trim().min(6).allow("").messages({
            "string.base": "密码必须是文本格式",
            "string.min": "初始密码长度不能少于{#limit}个字符",
        }),
    },
    resetUserPassword: Joi.string().trim().min(6).required().messages({
        "string.base": "密码必须是文本格式",
        "string.empty": "请填写初始密码",
        "string.min": "初始密码长度不能少于{#limit}个字符",
    }),
    confirmResetUserPassword: {
        required: Joi.string().required().valid(Joi.ref("resetUserPassword")),

    },

    userPubid: {
        required: Joi.string().min(16).max(64).required().messages({
            "any.required": "请选取",
        }),
        unrequired: Joi.string().min(16).max(64).allow("").messages({
            "any.required": "请选取",
        }),
    },

    //部门
    deptName: Joi.string().trim().min(1).max(20).required().messages({
        "string.base": "部门必须是文本格式",
        "string.empty": "请填写部门名称",
        "string.min": "名称长度不能少于{#limit}个字符",
        "string.max": "名称长度不能超过{#limit}个字符",
        "any.required": "名称必须填写",
    }),
    deptId: Joi.number().integer().min(0).max(999).required().messages({
        "any.required": "请选取部门",
    }),
    deptMembers: {
        required: Joi.array().items(Joi.string()).min(1).messages({
            "array.min": "请勾选部门成员",
        }),
        unrequired: Joi.array().items(Joi.string()),
    },

    // 岗位
    positionName: {
        required: Joi.string().trim().min(2).max(20).required().messages({
            "string.base": "名称必须是文本格式",
            "string.empty": "请填写部门名称",
            "string.min": "姓名长度不能少于{#limit}个字符",
            "string.max": "姓名长度不能超过{#limit}个字符",
            "any.required": "名称必须填写"
        }),
    },
    positionState: {
        required: Joi.number().integer().min(0).max(5).required().messages({
            "number.base": "请填写整数值",
            "number.integer": "请填写整数值",
            "number.min": "状态不能小于{#limit}",
            "number.max": "状态不能超过{#limit}",
        }),
    },

    // 客户
    clientName: {
        required: Joi.string().trim().max(64).required().messages({
            "string.base": "客户名称必须是文本格式",
            "string.empty": "请填写客户名称",
            "string.max": "长度不能超过{#limit}个字符",
            "any.required": "客户名称必须填写",
        }),
    },
    clientAddr: {
        required: Joi.string().trim().max(128).required().messages({
            "string.base": "地址必须是文本格式",
            "string.empty": "请填写客户地址",
            "string.max": "长度不能超过{#limit}个字符",
            "any.required": "地址必须填写",
        }),
        unrequired: Joi.string().trim().max(128).allow("").messages({
            "string.base": "地址必须是文本格式",
            "string.max": "长度不能超过{#limit}个字符",
        }),
    },

    // 角色
    roleName: {
        required: Joi.string().trim().max(64).required().messages({
            "string.base": "角色名称必须是文本格式",
            "string.empty": "请填写角色名称",
            "string.max": "长度不能超过{#limit}个字符",
            "any.required": "角色名称必须填写",
        }),
    },
    roleStdRoles: {
        required: Joi.array().items(Joi.number().integer()).min(1).messages({
            "*": "请勾选角色权限",
        }),
        unrequired: Joi.array().items(Joi.number().integer()),
    },

    // 项目库
    projbaseName: {
        required: Joi.string().trim().max(64).required().messages({
            "string.base": "名称必须是文本格式",
            "string.empty": "请填写项目库名称",
            "string.max": "长度不能超过{#limit}个字符",
            "any.required": "名称必须填写",
        }),
    },
    projbaseClass: {
        required: Joi.number().integer().min(0).max(100).required().messages({
            "*": "请拉选项目类型",
        }),
        unrequired: Joi.number().integer().min(0).max(100).empty("").default(0).messages({
            "*": "请拉选项目类型",
        }),
    },
    projbaseSubClass: {
        required: Joi.number().integer().min(0).max(100).required().messages({
            "*": "请拉选项目子类型",
        }),
        unrequired: Joi.number().integer().min(0).max(100).empty("").default(0).messages({
            "*": "请拉选项目子类型",
        }),
    },
    projbaseIndustry: {
        required: Joi.number().integer().min(0).max(100).required().messages({
            "*": "请拉选行业类型",
        }),
        unrequired: Joi.number().integer().min(0).max(100).empty("").default(0).messages({
            "*": "请拉选行业类型",
        }),
    },
    projbaseDifficulty: {
        required: Joi.number().integer().valid(1, 2, 3).required().messages({
            "*": "请拉选项目难度",
        }),
        unrequired: Joi.number().integer().valid(0, 1, 2, 3).empty("").default(0).messages({
            "*": "请拉选项目难度",
        }),
    },

    // water balance
    wbProjectName: {
        required: Joi.string().trim().max(64).required().messages({
            "string.base": "名称必须是文本格式",
            "string.empty": "请填写项目名称",
            "string.max": "长度不能超过{#limit}个字符",
            "any.required": "名称必须填写",
        }),
    },
    wbProductName: {
        required: Joi.string().trim().max(64).required().messages({
            "string.base": "名称必须是文本格式",
            "string.empty": "请填写产品名称",
            "string.max": "长度不能超过{#limit}个字符",
            "any.required": "名称必须填写",
        }),
        unrequired: Joi.string().trim().max(64).allow("").messages({
            "string.base": "名称必须是文本格式",
            "string.empty": "请填写产品名称",
            "string.max": "长度不能超过{#limit}个字符",
        }),
    },
    wbClient: {
        required: Joi.string().trim().min(1).max(64).required().messages({
            "*": "请拉选服务客户",
        }),
        unrequired: Joi.string().trim().min(1).max(64).allow("").messages({
            "*": "请拉选服务客户",
        }),
    },
    wbProjbase: {
        required: Joi.string().trim().min(16).max(64).required().messages({
            "*": "请拉选项目类型",
        }),
        unrequired: Joi.string().trim().min(16).max(64).allow("").messages({
            "*": "请拉选项目类型",
        }),
    },
    wbDifficulty: {
        required: Joi.number().integer().min(0).max(100).required().messages({
            "*": "请拉选项目难度",
        }),
        unrequired: Joi.number().integer().min(0).max(100).allow(0).messages({
            "*": "请拉选项目难度",
        }),
    },
    wbSalesman: {
        required: Joi.string().trim().min(16).max(64).required().messages({
            "*": "请选取",
        }),
        unrequired: Joi.string().trim().min(16).max(64).allow("").messages({
            "*": "请选取",
        }),
    },
    wbManager: {
        required: Joi.string().trim().min(16).max(64).required().messages({
            "*": "请选取",
        }),
        unrequired: Joi.string().trim().min(16).max(64).allow("").messages({
            "*": "请选取",
        }),
    },
    wbManagerLevel: {
        required: Joi.number().integer().valid(1, 2, 3).required().messages({
            "*": "请拉选项目难度",
        }),
        unrequired: Joi.number().integer().valid(1, 2, 3).allow(0),
    },
    wbAreaPrincipal: {
        required: Joi.string().trim().min(16).max(64).required().messages({
            "any.required": "请选取",
        }),
        unrequired: Joi.string().trim().min(16).max(64).allow("").messages({
            "any.required": "请选取",
        }),
    },
    wbCourtesyCopies: {
        required: Joi.array().items(Joi.string()).min(1).messages({
            "array.min": "请勾选部门成员",
        }),
        unrequired: Joi.array().items(Joi.string()),
    },
    wbTemplate: {
        required: Joi.number().integer().valid(1, 2, 3).required().messages({
            "*": "请拉选行业类型",
        }),
        unrequired: Joi.number().integer().valid(1, 2, 3).allow(0),
    },
    wbTemplateOpts: {
        required: Joi.array().items(Joi.string()).min(1).messages({
            "array.min": "请勾选用水单元",
        }),
        unrequired: Joi.array().items(Joi.number().integer()),
    },

    // project copying
    projectName: {
        required: Joi.string().trim().max(64).required().messages({
            "string.base": "名称必须是文本格式",
            "string.empty": "请填写项目名称",
            "string.max": "长度不能超过{#limit}个字符",
            "any.required": "名称必须填写",
        }),
    },
    projectClass: {
        required: Joi.number().integer().min(0).max(100).required().messages({
            "*": "请拉选项目类型",
        }),
        unrequired: Joi.number().integer().min(0).max(100).empty("").default(0).messages({
            "*": "请拉选项目类型",
        }),
    },
    projectFather: {
        required: Joi.string().trim().min(16).max(64).required().messages({
            "*": "请选取",
        }),
        unrequired: Joi.string().trim().min(16).max(64).allow("").messages({
            "*": "请选取",
        }),
    },

    // app客户
    orgName: {
        required: Joi.string().trim().max(64).required().messages({
            "string.base": "角色名称必须是文本格式",
            "string.empty": "请填写客户名称",
            "string.max": "长度不能超过{#limit}个字符",
            "any.required": "客户名称必须填写",
        }),
    },
    orgAddress: {
        required: Joi.string().trim().max(64).required().messages({
            "string.base": "地址必须是文本格式",
            "string.empty": "请填写客户地址",
            "string.max": "长度不能超过{#limit}个字符",
            "any.required": "地址必须填写",
        }),
        unrequired: Joi.string().trim().max(64).allow("").messages({
            "string.base": "地址必须是文本格式",
            "string.max": "长度不能超过{#limit}个字符",
        }),
    },
    orgSubscriptions: {
        required: Joi.array().items(Joi.number().integer()).min(1).messages({
            "*": "请勾选功能",
        }),
        unrequired: Joi.array().items(Joi.number().integer()),
    },

    waterBalance: {
        commons: {
            waterScale: Joi.number().integer().valid(0, 1, 2, 3, 4).allow(0).required().messages({
                "*": "请拉选水量单位",
            }),
            cversion: Joi.number().min(0).required(),
            name: Joi.string().trim().max(64).allow("").messages({
                "string.base": "名称必须是文本格式",
                "string.empty": "请填写记录名称",
                "string.max": "长度不能超过{#limit}个字符",
                "any.required": "名称必须填写",
            }),
            boolField: Joi.number().integer().valid(0, 1).empty("").default(0).messages({
                "*": "请拉选数据",
            }),
            // 扩展版布尔, -1表示未填写的状态
            boolFieldExt: Joi.number().integer().valid(-1, 0, 1).empty("").default(-1).messages({
                "*": "请拉选数据",
            }),
            intField: Joi.number().integer().min(0).max(2147483647).required().messages({
                "number.base": "请填写数值",
                "number.integer": "请填写整数",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            intFieldUnrequired: Joi.number().integer().min(0).max(2147483647).empty("").default(0).messages({
                "number.base": "请填写数值",
                "number.integer": "请填写整数",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
            }),
            percentField: Joi.number().min(0).max(100).empty("").default(0).messages({
                "number.base": "请填写数值",
                "number.min": "百分比不能小于{#limit}",
                "number.max": "百分比不能超过{#limit}",
            }),
            floatField: Joi.number().min(0).max(2147483647).required().messages({
                "number.base": "请填写数值",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            floatFieldUnrequired: Joi.number().min(0).max(2147483647).empty("").default(0).messages({
                "number.base": "请填写数值",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            dateField: Joi.number().integer().min(0).required().messages({
                "number.base": "请填写数值",
                "number.integer": "请填写整数",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            dateFieldUnrequired: Joi.number().integer().min(0).empty("").default(0).messages({
                "number.base": "请填写数值",
                "number.integer": "请填写整数",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
            }),
            yearField: Joi.number().integer().min(1900).max(2099).required().messages({
                "number.base": "请填写四位数年份",
                "number.integer": "请填写整数",
                "number.min": "年份不能小于{#limit}",
                "number.max": "年份不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            yearFieldUnrequired: Joi.number().integer().min(1900).max(2099).empty("").default(0).messages({
                "number.base": "请填写四位数年份",
                "number.integer": "请填写整数",
                "number.min": "年份不能小于{#limit}",
                "number.max": "年份不能超过{#limit}",
            }),
            /**  注: Joi会尝试将数据转换成对应的类型, 因此下面会对更长的小数舍入到两位,
                 但如果配置prefs({ convert: false }), 则其它number规则会失效,
                 综合考虑后使用precision(2)配置
             */
            moneyField: Joi.number().min(0).precision(2).required().messages({
                "number.base": "请填写数值",
                "number.min": "不能小于{#limit}",
                "number.precision": "最多{#limit}位小数",
                "any.required": "此项必须填写",
            }),
            shortTextField: Joi.string().trim().max(100).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            textField: Joi.string().trim().max(500).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            longTextField: Joi.string().trim().max(1000).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            largeTextField: Joi.string().trim().max(10000).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            waterClass: Joi.number().integer().valid(1, 2, 3, 4).allow(0).required().messages({
                "*": "请拉选用水类别",
            }),
            locationRequired: Joi.array().ordered(
                Joi.number().min(-180).max(180).required().messages({
                    "number.base": "经度必须是数字",
                    "number.min": "经度超出范围(-180°~180°)",
                    "number.max": "经度超出范围(-180°~180°)",
                    "*": "请点击右边图标获取经度"
                }),
                Joi.number().min(-90).max(90).required().messages({
                    "number.base": "纬度必须是数字",
                    "number.min": "纬度超出范围(-90°~90°)",
                    "number.max": "纬度超出范围(-90°~90°)",
                    "*": "请点击右侧图标获取纬度"
                })
            ).required().messages({
                "array.base": "请点击右侧图标获取纬度",
                "array.length": "经纬度坐标格式不正确",
                "array.ordered": "经纬度坐标格式不正确",
                "*": "请点击右侧图标获取纬度"
            }),
            locationUnrequired: Joi.array().ordered(
                Joi.number().min(-180).max(180).required().messages({
                    "number.base": "经度必须是数字",
                    "number.min": "经度超出范围(-180°~180°)",
                    "number.max": "经度超出范围(-180°~180°)",
                    "*": "请点击右边图标获取经度"
                }),
                Joi.number().min(-90).max(90).required().messages({
                    "number.base": "纬度必须是数字",
                    "number.min": "纬度超出范围(-90°~90°)",
                    "number.max": "纬度超出范围(-90°~90°)",
                    "*": "请点击右侧图标获取纬度"
                })
            ).length(2).allow("").default([0, 0]).messages({
                "array.base": "请点击右边图标获取经纬度",
                "array.length": "经纬度坐标格式不正确",
                "array.ordered": "经纬度坐标格式不正确",
                "*": "请点击右边图标获取经纬度"
            }),
            simpleArray: Joi.array().items(Joi.any()).empty("").default([]).messages({
                "*": "请输入数据",
            }),
            simpleObject: Joi.object().empty("").default({}).messages({
                "*": "请输入数据",
            }),
        },

        factBase: {
            waterClass: Joi.number().integer().valid(1, 2, 3, 4).required().messages({
                "*": "请拉选用水类别",
            }),
            waterClassUnrequired: Joi.number().integer().valid(1, 2, 3, 4).allow(0).required().messages({
                "*": "请拉选用水类别",
            }),
            industry: Joi.number().integer().valid(1, 2, 3, 4).required().messages({
                "*": "请拉选行业类型",
            }),
            industryUnrequired: Joi.number().integer().valid(0, 1, 2, 3, 4).empty("").default(0).messages({
                "*": "请拉选行业类型",
            }),
            hospitalLevel: Joi.number().integer().valid(11, 12, 13, 21, 22, 23, 30, 31, 32, 33).allow(0).required().messages({
                "*": "请拉选医院级别",
            }),
        },
        waterQuotaInfo: {
            subindustry: Joi.number().integer().valid(-1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9).empty("").default(-1).messages({
                "*": "请拉选项目类型",
            }),
        },
        waterSource: {
            cversion: Joi.number().min(0).required(),
            name: Joi.string().trim().max(64).allow("").messages({
                "string.base": "名称必须是文本格式",
                "string.empty": "请填写记录名称",
                "string.max": "长度不能超过{#limit}个字符",
                "any.required": "名称必须填写",
            }),
            waterType: Joi.number().integer().valid(1, 2, 3, 4, 5, 6, 101, 102, 103, 104, 105).allow(0).required().messages({
                "*": "请拉选水源类型",
            }),
            designAmount: Joi.number().min(0).max(2147483647).required().messages({
                "number.base": "请填写整数",
                "number.min": "设计用水量不能小于{#limit}立方米/天",
                "number.max": "设计用水量不能超过{#limit}立方米/天",
                "any.required": "此项必须填写",
            }),
            actualAmount: Joi.number().min(0).max(2147483647).required().messages({
                "number.base": "请填写整数",
                "number.min": "实际用水量不能小于{#limit}立方米/天",
                "number.max": "实际用水量不能超过{#limit}立方米/天",
                "any.required": "此项必须填写",
            }),
            pipeDiameter: {
                required: Joi.number().integer().min(0).max(2147483647).required().messages({
                    "number.base": "请填写整数",
                    "number.integer": "请填写整数",
                    "number.min": "管径不能小于{#limit}mm",
                    "number.max": "管径不能超过{#limit}mm",
                    "any.required": "此项必须填写",
                }),
                unrequired: Joi.number().integer().min(0).max(2147483647).empty("").default(0).messages({
                    "number.base": "请填写整数",
                    "number.integer": "请填写整数",
                    "number.min": "管径不能小于{#limit}mm",
                    "number.max": "管径不能超过{#limit}mm",
                }),
            },
            pipeNum: {
                required: Joi.number().integer().min(0).max(2147483647).required().messages({
                    "number.base": "请填写整数",
                    "number.integer": "请填写整数",
                    "number.min": "输水管道数量不能小于{#limit}",
                    "number.max": "输水管道数量不能超过{#limit}",
                    "any.required": "此项必须填写",
                }),
                unrequired: Joi.number().integer().min(0).max(2147483647).empty("").default(0).messages({
                    "number.base": "请填写整数",
                    "number.integer": "请填写整数",
                    "number.min": "输水管道数量不能小于{#limit}",
                    "number.max": "输水管道数量不能超过{#limit}",
                }),
            },
            ph: {
                required: Joi.number().min(0).max(14).required().messages({
                    "number.base": "请填写数值",
                    "number.min": "pH值不能小于{#limit}",
                    "number.max": "pH值不能超过{#limit}",
                    "any.required": "此项必须填写",
                }),
                unrequired: Joi.number().min(0).max(14).empty("").default(0).messages({
                    "number.base": "请填写数值",
                    "number.min": "pH值不能小于{#limit}",
                    "number.max": "pH值不能小于{#limit}",
                }),
            },
            tempLow: Joi.number().min(-273).max(1000).precision(2).required().messages({
                "number.base": "请填写整数",
                "number.min": "最低水温不能小于{#limit}°C",
                "number.max": "最低水温不能超过{#limit}°C",
                "number.precision": "最多{#limit}位小数",
                "any.required": "此项必须填写",
            }),
            tempHigh: Joi.number().min(-273).max(1000).precision(2).required().messages({
                "number.base": "请填写整数",
                "number.min": "最高水温不能小于{#limit}°C",
                "number.max": "最高水温不能超过{#limit}°C",
                "number.precision": "最多{#limit}位小数",
                "any.required": "此项必须填写",
            }),
            turbidity: {
                required: Joi.string().regex(RangeRegex).required().messages({
                    "string.base": "浑浊度是一个表示区间的字符串",
                    "string.empty": "请填写浑浊度NTU",
                    "string.pattern.base": "浑浊度格式错误, 格式: [min, max)",
                }),
                unrequired: Joi.string().regex(RangeRegex).empty("").default("(0, 0)").messages({
                    "string.base": "浑浊度是一个表示区间的字符串",
                    "string.empty": "请填写浑浊度NTU",
                    "string.pattern.base": "浑浊度格式错误, 格式: [min, max)",
                }),
            },
            hardness: Joi.number().min(0).max(1000).required().messages({
                "number.base": "请填写数值",
                "number.min": "硬度值不能小于{#limit}mg/L",
                "number.max": "硬度值不能超过{#limit}mg/L",
                "any.required": "此项必须填写",
            }),
            others: Joi.string().trim().max(64).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            mainUseRadio: Joi.number().integer().valid(1, 2, 3).allow(0).required().messages({
                "*": "请拉选数据",
            }),
            mainUseCheck: Joi.array().items(Joi.number().integer().min(0).max(1000)).messages({
                "*": "请拉选数据",
            }),
            plannedUse: Joi.number().min(0).max(2147483647).empty("").default(0).messages({
                "number.base": "请填写整数",
                "number.min": "计划用水指标不能小于{#limit}m³/a",
                "number.max": "计划用水指标不能超过{#limit}m³/a",
            }),
            intField:{
                unrequired: Joi.number().integer().min(0).max(2147483647).empty("").default(0).messages({
                    "number.base": "请填写数值",
                    "number.min": "不能小于{#limit}",
                    "number.max": "不能超过{#limit}",
                }),
                required: Joi.number().integer().min(0).max(2147483647).required().messages({
                    "number.base": "请填写数值",
                    "number.min": "不能小于{#limit}",
                    "number.max": "不能超过{#limit}",
                    "any.required": "此项必须填写",
                })
            },
            float: {
                unrequired: Joi.number().min(0).max(2147483647).empty("").default(0).messages({
                    "number.base": "请填写数值",
                    "number.min": "不能小于{#limit}",
                    "number.max": "不能超过{#limit}",
                }),
                required: Joi.number().min(0).max(2147483647).required().messages({
                    "number.base": "请填写数值",
                    "number.min": "不能小于{#limit}",
                    "number.max": "不能超过{#limit}",
                    "any.required": "此项必须填写",
                }),
            },
            textField: Joi.string().trim().max(64).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            remarks: Joi.string().trim().max(64).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
        },
        waterUsage: {
            cversion: Joi.number().min(0).required(),
            name: Joi.string().trim().max(64).allow("").messages({
                "string.base": "名称必须是文本格式",
                "string.empty": "请填写记录名称",
                "string.max": "长度不能超过{#limit}个字符",
                "any.required": "名称必须填写",
            }),
            year: Joi.number().integer().min(2000).max(2099).required().messages({
                "number.base": "请填写整数",
                "number.integer": "请填写整数",
                "number.min": "年份不能小于{#limit}",
                "number.max": "年份不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            waterType: Joi.number().integer().valid(1, 2, 3, 4, 5, 6, 101, 102, 103, 104, 105).allow(0).required().messages({
                "*": "请拉水源类型",
            }),
            amoutField: Joi.number().min(0).max(2147483647).required().messages({
                "number.base": "请填写数值",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            percentField: Joi.number().min(0).max(100).required().messages({
                "number.base": "请填写数值",
                "number.min": "百分比不能小于{#limit}",
                "number.max": "百分比不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            textField: Joi.string().trim().max(128).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            longTextField: Joi.string().trim().max(1024).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
        },
        waterUsageAnnual: {
            year: Joi.number().integer().min(2000).max(2099).required().messages({
                "number.base": "请填写四位数年份",
                "number.integer": "请填写整数",
                "number.min": "年份不能小于{#limit}",
                "number.max": "年份不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            serviceTypes: Joi.array().items(Joi.number().integer().min(0).max(1000)).empty("").default([]).messages({
                "*": "请拉选数据",
            }),
        },
        comProdStats: {
            cversion: Joi.number().min(0).required(),
            name: Joi.string().trim().max(64).allow("").messages({
                "string.base": "名称必须是文本格式",
                "string.empty": "请填写记录名称",
                "string.max": "长度不能超过{#limit}个字符",
                "any.required": "名称必须填写",
            }),
            year: Joi.number().integer().min(2000).max(2099).allow(7).messages({
                "*": "请拉选年份",
            }),
            testDays: Joi.number().integer().min(0).max(366).empty("").default(0).messages({
                "number.base": "请填写数值",
                "number.integer": "请填写整数",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
            }),
            floatField: Joi.number().min(0).max(2147483647).required().messages({
                "number.base": "请填写数值",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            textField: {
                required: Joi.string().trim().max(64).messages({
                    "string.base": "必须是文本格式",
                    "string.max": "不能超过{#limit}个字符",
                    "string.empty": "此项必须填写",
                }),
                unrequired: Joi.string().trim().max(64).allow("").messages({
                    "string.base": "必须是文本格式",
                    "string.max": "不能超过{#limit}个字符",
                }),
            },
            shortTextField: {
                required: Joi.string().trim().max(32).messages({
                    "string.base": "必须是文本格式",
                    "string.max": "不能超过{#limit}个字符",
                    "string.empty": "此项必须填写",
                }),
                unrequired: Joi.string().trim().max(32).allow("").messages({
                    "string.base": "必须是文本格式",
                    "string.max": "不能超过{#limit}个字符",
                }),
            },
            longTextField: {
                required: Joi.string().trim().max(128).messages({
                    "string.base": "必须是文本格式",
                    "string.max": "不能超过{#limit}个字符",
                    "string.empty": "此项必须填写",
                }),
                unrequired: Joi.string().trim().max(128).allow("").messages({
                    "string.base": "必须是文本格式",
                    "string.max": "不能超过{#limit}个字符",
                }),
            },
            largeTextField: {
                required: Joi.string().trim().max(1024).messages({
                    "string.base": "必须是文本格式",
                    "string.max": "不能超过{#limit}个字符",
                    "string.empty": "此项必须填写",
                }),
                unrequired: Joi.string().trim().max(1024).allow("").messages({
                    "string.base": "必须是文本格式",
                    "string.max": "不能超过{#limit}个字符",
                }),
            },
        },
        waterUsingEquips: {
            workStatus: Joi.number().integer().valid(1, 2, 3).required().messages({
                "*": "请拉选运行情况",
            }),
            usingType: Joi.number().integer().valid(1, 2, 3, 4, 5, 6, -1).required().messages({
                "*": "请拉选用水方式",
            }),
            workStatusUnrequired: Joi.number().integer().valid(1, 2, 3).empty("").default(0).messages({
                "*": "请拉选运行情况",
            }),
            usingTypeUnrequired: Joi.number().integer().valid(1, 2, 3, 4, 5, 6, -1).empty("").default(0).messages({
                "*": "请拉选用水方式",
            }),
        },
        comWaterMeterEquip: {
            cversion: Joi.number().min(0).required(),
            name: Joi.string().trim().max(64).allow("").messages({
                "string.base": "名称必须是文本格式",
                "string.empty": "请填写记录名称",
                "string.max": "长度不能超过{#limit}个字符",
                "any.required": "名称必须填写",
            }),
            waterType: Joi.number().integer().valid(1, 2, 3, 4, 5, 6, 101, 102, 103, 104, 105).allow(0).required().messages({
                "*": "请拉选水源类型",
            }),
            // 注意下面的valid中有默认的0值, 否则提交会校验时报
            waterTypeUnrequired: Joi.number().integer().valid(0, 1, 2, 3, 4, 5, 6, 101, 102, 103, 104, 105).empty("").default(0).messages({
                "*": "请拉选水源类型",
            }),
            meterRange: Joi.string().trim().max(64).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            accuracyLevel: Joi.number().integer().valid(1, 2, 101, 102, 103, 104).allow(0).required().messages({
                "*": "请拉选准确度等级",
            }),
            intField: Joi.number().integer().min(0).max(2147483647).required().messages({
                "number.base": "请填写数值",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            intFieldUnrequired: Joi.number().integer().min(0).max(2147483647).empty("").default(0).messages({
                "number.base": "请填写数值",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
            }),
            calibrateUnit: Joi.number().integer().valid(0, 1, 2, 3).required().messages({
                "*": "请拉选时间单位",
            }),
            equipState: Joi.number().integer().valid(1, 2).allow(0).required().messages({
                "*": "请拉选设备状态",
            }),
            monitored:  Joi.number().integer().valid(0, 1, 2).empty("").default(0).messages({
                "*": "请拉选是否接入",
            }),
            floatField: Joi.number().min(0).max(2147483647).required().messages({
                "number.base": "请填写数值",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            textField: Joi.string().trim().max(64).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            shortTextField: Joi.string().trim().max(32).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            longTextField: Joi.string().trim().max(128).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            largeTextField: Joi.string().trim().max(1024).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
        },

        waterMeterSample: {
            cversion: Joi.number().min(0).required(),
            name: Joi.string().trim().max(64).allow("").messages({
                "string.base": "名称必须是文本格式",
                "string.empty": "请填写记录名称",
                "string.max": "长度不能超过{#limit}个字符",
                "any.required": "名称必须填写",
            }),
            pipeLevel: Joi.number().integer().valid(1, 2, 3, 4, 5).allow(0).required().messages({
                "*": "请拉选水表级别",
            }),
            dateField: Joi.number().integer().min(0).required().messages({
                "number.base": "请填写数值",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            intField: Joi.number().integer().min(0).max(2147483647).required().messages({
                "number.base": "请填写数值",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            percentField: Joi.number().min(0).max(100).required().messages({
                "number.base": "请填写数值",
                "number.min": "百分比不能小于{#limit}",
                "number.max": "百分比不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            floatField: Joi.number().min(0).max(2147483647).required().messages({
                "number.base": "请填写数值",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            floatFieldUnrequired: Joi.number().min(0).max(2147483647).empty("").default(0).messages({
                "number.base": "请填写数值",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            textField: Joi.string().trim().max(64).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            shortTextField: Joi.string().trim().max(32).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            longTextField: Joi.string().trim().max(128).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            largeTextField: Joi.string().trim().max(1024).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
        },
        comWaterMeterEquipStats: {
            cversion: Joi.number().min(0).required(),
            name: Joi.string().trim().max(64).allow("").messages({
                "string.base": "名称必须是文本格式",
                "string.empty": "请填写记录名称",
                "string.max": "长度不能超过{#limit}个字符",
                "any.required": "名称必须填写",
            }),
            deviceType: Joi.number().integer().valid(0, 1, 2).allow(0).required().messages({
                "*": "请拉选计量器具类型",
            }),
            intField: Joi.number().integer().min(0).max(2147483647).required().messages({
                "number.base": "请填写数值",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            intFieldUnrequired: Joi.number().integer().min(0).max(2147483647).empty("").default(0).messages({
                "number.base": "请填写数值",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
            }),
            percentField: Joi.number().min(0).max(100).required().messages({
                "number.base": "请填写数值",
                "number.min": "百分比不能小于{#limit}",
                "number.max": "百分比不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            percentFieldUnrequired: Joi.number().min(0).max(2147483647).empty("").default(0).messages({
                "number.base": "请填写数值",
                "number.min": "百分比不能小于{#limit}",
                "number.max": "百分比不能超过{#limit}",
            }),
            floatField: Joi.number().min(0).max(2147483647).required().messages({
                "number.base": "请填写数值",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            floatFieldUnrequired: Joi.number().min(0).max(2147483647).empty("").default(0).messages({
                "number.base": "请填写数值",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            textField: Joi.string().trim().max(64).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            shortTextField: Joi.string().trim().max(32).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            longTextField: Joi.string().trim().max(128).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            largeTextField: Joi.string().trim().max(1024).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
        },
        waterUsageUnits: {
            cversion: Joi.number().min(0).required(),
            waterScale: Joi.number().integer().valid(0, 1, 2, 3, 4).allow(0).required().messages({
                "*": "请拉选水量单位",
            }),
            name: Joi.string().trim().max(64).allow("").messages({
                "string.base": "名称必须是文本格式",
                "string.empty": "请填写记录名称",
                "string.max": "长度不能超过{#limit}个字符",
                "any.required": "名称必须填写",
            }),
            year: Joi.number().integer().min(2000).max(2099).required().messages({
                "number.base": "请填写整数",
                "number.integer": "请填写整数",
                "number.min": "年份不能小于{#limit}",
                "number.max": "年份不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            dateField: Joi.number().integer().min(0).required().messages({
                "number.base": "请填写数值",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            testDays: Joi.number().integer().min(1).max(30).required().messages({
                "number.base": "请填写数值",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            startDate: Joi.number().integer().min(1).empty("").default(0).messages({
                "number.base": "请填写数值",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
            }),
            dateFieldUnrequired: Joi.number().integer().min(0).empty("").default(0).messages({
                "number.base": "请填写数值",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
            }),
            intField: Joi.number().integer().min(0).max(2147483647).required().messages({
                "number.base": "请填写数值",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            percentField: Joi.number().min(0).max(100).required().messages({
                "number.base": "请填写数值",
                "number.min": "百分比不能小于{#limit}",
                "number.max": "百分比不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            floatField: Joi.number().min(0).max(2147483647).required().messages({
                "number.base": "请填写数值",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            float: {
                unrequired: Joi.number().min(0).max(2147483647).empty("").default(0).messages({
                    "number.base": "请填写数值",
                    "number.min": "不能小于{#limit}",
                    "number.max": "不能超过{#limit}",
                }),
                required: Joi.number().min(0).max(2147483647).required().messages({
                    "number.base": "请填写数值",
                    "number.min": "不能小于{#limit}",
                    "number.max": "不能超过{#limit}",
                    "any.required": "此项必须填写",
                }),
            },
            textField: Joi.string().trim().max(64).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            shortTextField: Joi.string().trim().max(32).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            longTextField: Joi.string().trim().max(128).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            largeTextField: Joi.string().trim().max(1024).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            equipRumtime: Joi.number().integer().valid(0, 1, 2, 3).empty("").default(0).messages({
                "*": "请拉选数据",
            }),
            testMethod: Joi.array().items(Joi.number().integer().min(0).max(1000)).empty("").default([]).messages({
                "*": "请拉选数据",
            }),
            intakePurpose: Joi.number().integer().valid(-1, 0, 1, 2, 3, 4, 5).empty("").default(0).messages({
                "*": "请拉选数据",
            }),
        },
        waterBalanceInspectStats: {
            cversion: Joi.number().min(0).required(),
            name: Joi.string().trim().max(64).allow("").messages({
                "string.base": "名称必须是文本格式",
                "string.empty": "请填写记录名称",
                "string.max": "长度不能超过{#limit}个字符",
                "any.required": "名称必须填写",
            }),
            waterClass: Joi.number().integer().valid(1, 2, 3).allow(0).required().messages({
                "*": "请拉选用水分类",
            }),
            convWaterSource: Joi.number().integer().valid(1, 2, 3, 4, 5, 6).allow(0).required().messages({
                "*": "请拉选常规水源类型",
            }),
            unconvWaterSource: Joi.number().integer().valid(101, 102, 103, 104, 105).allow(0).required().messages({
                "*": "请拉选非常规水源类型",
            }),
            intField: Joi.number().integer().min(0).max(2147483647).required().messages({
                "number.base": "请填写数值",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            percentField: Joi.number().min(0).max(100).required().messages({
                "number.base": "请填写数值",
                "number.min": "百分比不能小于{#limit}",
                "number.max": "百分比不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            floatField: Joi.number().min(0).max(2147483647).required().messages({
                "number.base": "请填写数值",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            textField: Joi.string().trim().max(64).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            shortTextField: Joi.string().trim().max(32).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            longTextField: Joi.string().trim().max(128).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            largeTextField: Joi.string().trim().max(1024).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
        },
        waterUsageInspectResults: {
            cversion: Joi.number().min(0).required(),
            name: Joi.string().trim().max(64).allow("").messages({
                "string.base": "名称必须是文本格式",
                "string.empty": "请填写记录名称",
                "string.max": "长度不能超过{#limit}个字符",
                "any.required": "名称必须填写",
            }),
            waterClass: Joi.number().integer().valid(1, 2, 3).allow(0).required().messages({
                "*": "请拉选用水分类",
            }),
            intField: Joi.number().integer().min(0).max(2147483647).required().messages({
                "number.base": "请填写数值",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            percentField: Joi.number().min(0).max(100).required().messages({
                "number.base": "请填写数值",
                "number.min": "百分比不能小于{#limit}",
                "number.max": "百分比不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            floatField: Joi.number().min(0).max(2147483647).required().messages({
                "number.base": "请填写数值",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            textField: Joi.string().trim().max(64).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            shortTextField: Joi.string().trim().max(32).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            longTextField: Joi.string().trim().max(128).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            largeTextField: Joi.string().trim().max(1024).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
        },

        waterUsageInstAnalyze: {
            cversion: Joi.number().min(0).required(),
            name: Joi.string().trim().max(64).allow("").messages({
                "string.base": "名称必须是文本格式",
                "string.empty": "请填写记录名称",
                "string.max": "长度不能超过{#limit}个字符",
                "any.required": "名称必须填写",
            }),
            waterClass: Joi.number().integer().valid(1, 2, 3).allow(0).required().messages({
                "*": "请拉选用水分类",
            }),
            intField: Joi.number().integer().min(0).max(2147483647).required().messages({
                "number.base": "请填写数值",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            percentField: Joi.number().min(0).max(100).required().messages({
                "number.base": "请填写数值",
                "number.min": "百分比不能小于{#limit}",
                "number.max": "百分比不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            floatField: Joi.number().min(0).max(2147483647).required().messages({
                "number.base": "请填写数值",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            textField: Joi.string().trim().max(64).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            shortTextField: Joi.string().trim().max(32).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            longTextField: Joi.string().trim().max(128).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            largeTextField: Joi.string().trim().max(1024).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
        },

    },

    zeroCarbon: {
        commons: {
            waterScale: Joi.number().integer().valid(0, 1, 2, 3, 4).allow(0).required().messages({
                "*": "请拉选水量单位",
            }),
            cversion: Joi.number().min(0).required(),
            name: Joi.string().trim().max(64).allow("").messages({
                "string.base": "名称必须是文本格式",
                "string.empty": "请填写记录名称",
                "string.max": "长度不能超过{#limit}个字符",
                "any.required": "名称必须填写",
            }),
            boolField: Joi.number().integer().valid(0, 1).empty("").default(0).messages({
                "*": "请拉选数据",
            }),
            // 扩展版布尔, -1表示未填写的状态
            boolFieldExt: Joi.number().integer().valid(-1, 0, 1).empty("").default(-1).messages({
                "*": "请拉选数据",
            }),
            intField: Joi.number().integer().min(0).max(2147483647).required().messages({
                "number.base": "请填写数值",
                "number.integer": "请填写整数",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            intFieldUnrequired: Joi.number().integer().min(0).max(2147483647).empty("").default(0).messages({
                "number.base": "请填写数值",
                "number.integer": "请填写整数",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
            }),
            percentField: Joi.number().min(0).max(100).empty("").default(0).messages({
                "number.base": "请填写数值",
                "number.min": "百分比不能小于{#limit}",
                "number.max": "百分比不能超过{#limit}",
            }),
            floatField: Joi.number().min(0).max(2147483647).required().messages({
                "number.base": "请填写数值",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            floatFieldUnrequired: Joi.number().min(0).max(2147483647).empty("").default(0).messages({
                "number.base": "请填写数值",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            dateField: Joi.number().integer().min(0).required().messages({
                "number.base": "请填写数值",
                "number.integer": "请填写整数",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            dateFieldUnrequired: Joi.number().integer().min(0).empty("").default(0).messages({
                "number.base": "请填写数值",
                "number.integer": "请填写整数",
                "number.min": "不能小于{#limit}",
                "number.max": "不能超过{#limit}",
            }),
            yearField: Joi.number().integer().min(1900).max(2099).required().messages({
                "number.base": "请填写四位数年份",
                "number.integer": "请填写整数",
                "number.min": "年份不能小于{#limit}",
                "number.max": "年份不能超过{#limit}",
                "any.required": "此项必须填写",
            }),
            yearFieldUnrequired: Joi.number().integer().min(1900).max(2099).empty("").default(0).messages({
                "number.base": "请填写四位数年份",
                "number.integer": "请填写整数",
                "number.min": "年份不能小于{#limit}",
                "number.max": "年份不能超过{#limit}",
            }),
            /**  注: Joi会尝试将数据转换成对应的类型, 因此下面会对更长的小数舍入到两位,
                 但如果配置prefs({ convert: false }), 则其它number规则会失效,
                 综合考虑后使用precision(2)配置
             */
            moneyField: Joi.number().min(0).precision(2).required().messages({
                "number.base": "请填写数值",
                "number.min": "不能小于{#limit}",
                "number.precision": "最多{#limit}位小数",
                "any.required": "此项必须填写",
            }),
            shortTextField: Joi.string().trim().max(100).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            textField: Joi.string().trim().max(500).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            longTextField: Joi.string().trim().max(1000).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            largeTextField: Joi.string().trim().max(10000).allow("").messages({
                "string.base": "必须是文本格式",
                "string.max": "不能超过{#limit}个字符",
            }),
            waterClass: Joi.number().integer().valid(1, 2, 3, 4).allow(0).required().messages({
                "*": "请拉选用水类别",
            }),
            locationRequired: Joi.array().ordered(
                Joi.number().min(-180).max(180).required().messages({
                    "number.base": "经度必须是数字",
                    "number.min": "经度超出范围(-180°~180°)",
                    "number.max": "经度超出范围(-180°~180°)",
                    "*": "请点击右边图标获取经度"
                }),
                Joi.number().min(-90).max(90).required().messages({
                    "number.base": "纬度必须是数字",
                    "number.min": "纬度超出范围(-90°~90°)",
                    "number.max": "纬度超出范围(-90°~90°)",
                    "*": "请点击右侧图标获取纬度"
                })
            ).required().messages({
                "array.base": "请点击右侧图标获取纬度",
                "array.length": "经纬度坐标格式不正确",
                "array.ordered": "经纬度坐标格式不正确",
                "*": "请点击右侧图标获取纬度"
            }),
            locationUnrequired: Joi.array().ordered(
                Joi.number().min(-180).max(180).required().messages({
                    "number.base": "经度必须是数字",
                    "number.min": "经度超出范围(-180°~180°)",
                    "number.max": "经度超出范围(-180°~180°)",
                    "*": "请点击右边图标获取经度"
                }),
                Joi.number().min(-90).max(90).required().messages({
                    "number.base": "纬度必须是数字",
                    "number.min": "纬度超出范围(-90°~90°)",
                    "number.max": "纬度超出范围(-90°~90°)",
                    "*": "请点击右侧图标获取纬度"
                })
            ).length(2).allow("").default([0, 0]).messages({
                "array.base": "请点击右边图标获取经纬度",
                "array.length": "经纬度坐标格式不正确",
                "array.ordered": "经纬度坐标格式不正确",
                "*": "请点击右边图标获取经纬度"
            }),
            simpleArray: Joi.array().items(Joi.any()).empty("").default([]).messages({
                "*": "请输入数据",
            }),
            simpleObject: Joi.object().empty("").default({}).messages({
                "*": "请输入数据",
            }),
            simpleRadio: Joi.number().integer().min(0).max(1000).empty("").default(0).messages({
                "*": "请拉选数据",
            }),
        },
        basicInfo: {
            duration: Joi.number().integer().valid(1, 2, 3, 4, 5).empty("").default(2).messages({
                "*": "请拉选统计周期",
            }),
        },
    },
};
