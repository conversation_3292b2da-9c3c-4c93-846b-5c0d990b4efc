import React from "react";
import { List, Text } from "react-native-paper";

// Text: https://callstack.github.io/react-native-paper/docs/components/Text/

export const CardWorkbench = ({ title, desc, naviTo, icon, navigation }) => {
    return (
        <List.Item
            title={<Text style={{ fontSize: 20 }}>{title}</Text>}
            description={desc ? <Text style={{ fontSize: 14 }}>{desc}</Text> : null}
            onPress={() => navigation.navigate(naviTo)}
            left={props => <List.Icon {...props} icon={icon} />}
            right={props => <List.Icon {...props} icon="arrow-right" />}
            style={{ borderWidth: 0, marginHorizontal: 10, marginVertical: 2 }}
            titleStyle={{ fontSize: 20 }}
            titleNumberOfLines={5}
            descriptionStyle={{}}
        />
    );
};
