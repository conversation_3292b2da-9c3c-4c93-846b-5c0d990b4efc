import { joi<PERSON><PERSON><PERSON>ver } from "@hookform/resolvers/joi";
import "fast-text-encoding";
import <PERSON><PERSON> from "joi";
import React, { useEffect, useRef, useState } from "react";
import { useForm, useWatch } from "react-hook-form";
import { Alert, StyleSheet, View } from "react-native";
import { Appbar, Divider, Snackbar, useTheme } from "react-native-paper";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import BottomBarButton from "../../components/BottomBarButton";
import ControlledRadioInputWithQuery from "../../components/ControlledRadioInputWithQuery";
import ControlledTextInput from "../../components/ControlledTextInput";
import { DialogToConfirm } from "../../components/DialogToConfirm";
import HeaderBar from "../../components/HeaderBar";
import ScreenWrapper from "../ScreenWrapper";
//import ControlledCheckboxInputWithQuery from "../../components/ControlledCheckboxInputWithQuery";
import cloneDeep from "lodash/cloneDeep";
import { creatMMKVStore } from "../../services/local-storage";
import { callOneByOne, makeDataFeeder } from "../../utils";
import { validatorBase } from "../../utils/validatorBase";

// 新组件需要重新!!
import { makeRequestDeleteProjectBase as deleteQueryClient } from "../../api/deletingQueries";
import { queryGetProjectBasePage as pageFetchQuery } from "../../api/selectingQueries";
import { makeRequestUpdateProjectBase as updateQueryClient } from "../../api/updatingQueries";
import { ORG_PROJ_BASE_UPDATE as pageMainKey } from "../../config/keysConfig";
//import { makeRequestListingDepartments as allDeptsQueryClient } from "../../api/listingQueries";
//import { makeRequestListingUsers       as allUsersQueryClient } from "../../api/listingQueries";
//import { queryGetDepartmentMembers } from "../../api/selectingQueries";
import { useShallow } from "zustand/shallow";
import { projbaseUpdateStates as selectorStates } from "../../hooks/selectorStates";
import { useRerender } from "../../hooks/useRerender";
import { onPreSubmitError } from "../../utils/screens";


// 新组件需要重新设置. 注意这里虽然设置了存储 但实际上没使用存储内容,
const { setStore, getStore, clearStore, setStoreObject } = creatMMKVStore(pageMainKey.store);
const dataFeeder = makeDataFeeder();

const saveButtonIcon = "content-save-all-outline";

const ProjectBaseUpdating = ({ navigation, route }) => {
    //console.log("Page info passed from nav:", route.params.pageMeta);

    // render++;
    const { bottom, left, right } = useSafeAreaInsets();
    const theme = useTheme();
    const height = theme.isV3 ? 80 : 56;

    const rerender = useRerender();

    // 新组件需要修改!!
    const bottomBarLabels = { ok: "更新", cancel: "取消" };                    // 底部按钮显示文字
    const [snackBarMessage, setSnackBarMessage] = useState("更新项目库遇到错误"); // 下方错误提示文字内容
    const [
        classRadioState,
        subClassRadioState,
        industryRadioState,
        difficultyRadioState,
        setClassRadioState,
        setSubClassRadioState,
        setIndustryRadioState,
        setDifficultyRadioState,
        resetSelectorStates,
    ] = selectorStates(useShallow(state => [state.classRadio, state.subClassRadio, state.industryRadio, state.difficultyRadio, state.setClassRadio, state.setSubClassRadio, state.setIndustryRadio, state.setDifficultyRadio, state.resetStates])); // radio和check组件状态

    const [confimDialogConfig, setConfirmDialogConfig] = useState({});       // 对话框信息, {title, text, okLabel, onOK}, 但显示对话框的变量在showConfirm

    // 新组件不需改动
    const [okButtonLoading, setOkButtonLoading] = useState(false);           // 右下方按钮图标是否旋转
    const [okButtonDisabled, setOkButtonDisabled] = useState(false);         // 右下方按钮是否禁用
    const [cancelButtonDisabled, setCancelButtonDisabled] = useState(false); // 左下方按钮是否禁用
    const [showSnackbar, setShowSnackbar] = useState(false);                 // 下方是否显示错误通知
    const [showConfirm, setShowConfirm] = useState(false);                   // 是否显示确认对话框
    const delQueryInfo = useRef({});                                         // 用于删除本记录的查询变量

    const classStateDataProviderRef      = useRef([{ id: 1, name: "水平衡" }, { id: 11, name: "零碳诊断" }]); // 用作类型状态选项的数据源
    const subClassStateDataProviderRef   = useRef([{ id: 1, name: "表" }, { id: 2, name: "书" },]);
    const industryStateDataProviderRef   = useRef([]); // 在useEffect中根据projClass填写
    const difficultyStateDataProviderRef = useRef([{ id: 1, name: "简单" }, { id: 2, name: "中等" }, { id: 3, name: "困难" }]); // 用作难度状态选项的数据源

    const classChecked = useRef(false); // 流程: 先拉选项目类型, 根据项目类型显示下方输入框

    // 新组件需要重新定义!!
    const schema = Joi.object({
        name:       validatorBase.projbaseName.required,
        class:      validatorBase.projbaseClass.unrequired,
        subClass:   validatorBase.projbaseSubClass.unrequired,
        industry:   validatorBase.projbaseIndustry.unrequired,
        difficulty: validatorBase.projbaseDifficulty.required,
    });

    // 新组件需要重新定义defaultValues!!
    const {
        handleSubmit,
        control,
        resetField,
        reset,
        formState: { errors },
    } = useForm({
        resolver: joiResolver(schema),
        //reValidateMode: "onBlur", // https://github.com/orgs/react-hook-form/discussions/10327
        defaultValues: {
            name:       "",
            class:      "0",
            subClass:   "0",
            industry:   "0",
            difficulty: "0",
        },
    });

    const projClass = Number(useWatch({ control, name: "class" }));

    useEffect(() => {
        console.log("projClass useEffect", projClass);
        if (projClass === 1) {
            industryStateDataProviderRef.current = ([{ id: 1, name: "工业" }, { id: 2, name: "服务业" },]);
        } else if (projClass === 11) {
            industryStateDataProviderRef.current = ([{ id: 3, name: "公共机构"} ,]); // 暂时只有公共机构 { id: 1, name: "工业" }
        }
        projClass > 0 && (classChecked.current = true);
        rerender();
    }, [projClass]);

    const formDefaults  = useRef({});
    const storeDefaults = useRef({});
    const selectorDefaults = useRef({});
    const onPageInfoQuerySuccess = (data) => { // data: {"id": 3, "leader": 0, "name": "DEPT-03", "superior": 0}
        console.log("onPageInfoQuerySuccess................", data);

        // industryStateDataProviderRef的内容与class有关, 在查询时要更新
        if (data.STATUS === 0) {
            if (data.DATA.class === 1) {
                industryStateDataProviderRef.current = ([{ id: 1, name: "工业" }, { id: 2, name: "服务业" },]);
            } else if (data.DATA.class === 11) {
                industryStateDataProviderRef.current = ([{ id: 3, name: "公共机构"} ,]); // 暂时只有公共机构 { id: 1, name: "工业" }
            }

            // delQueryInfo用于记录删除, id是查询api参数, name用于界面显示删除谁
            delQueryInfo.current = { id: route.params.pageMeta.id, name: data.DATA.name };
            formDefaults.current  = {
                name:       data.DATA.name,
                class:      data.DATA.class,
                subClass:   data.DATA.subClass,
                industry:   data.DATA.industry,
                difficulty: data.DATA.difficulty,
            };
            selectorDefaults.current = {
                class:      { id: data.DATA.class,      name: classStateDataProviderRef.current.find(item => String(item.id) === String(data.DATA.class))?.name || "" },
                subClass:   { id: data.DATA.subClass,   name: subClassStateDataProviderRef.current.find(item => String(item.id) === String(data.DATA.subClass))?.name || "" },
                industry:   { id: data.DATA.industry,   name: industryStateDataProviderRef.current.find(item => String(item.id) === String(data.DATA.industry))?.name || "" },
                difficulty: { id: data.DATA.difficulty, name: difficultyStateDataProviderRef.current.find(item => String(item.id) === String(data.DATA.difficulty))?.name || "" },            };
            storeDefaults.current = {
                name:       data.DATA.name,
                ...selectorDefaults.current,
            };

            reset(formDefaults.current);                                     // 重置react-form
            setStoreObject(storeDefaults.current);                           // 设置mmkv存储
            // 设置selector菜单状态
            setClassRadioState(cloneDeep(selectorDefaults.current.class));
            setSubClassRadioState(cloneDeep(selectorDefaults.current.subClass));
            setIndustryRadioState(cloneDeep(selectorDefaults.current.industry));
            setDifficultyRadioState(cloneDeep(selectorDefaults.current.difficulty));
            // 设置下拉选项的默认数据, 静态数据不需更新
            //userStateDataProviderRef.current = cloneDeep(selectorDefaults.current.state);
        }
    };
    const pageInfoQuery = pageFetchQuery(route.params.pageMeta.id, onPageInfoQuerySuccess); // pageMeta was set in ListingTemplate.jsx



    // 新组件需要重新定义setDiagText!!
    const onHandleSubmit = (data) => {
        setCancelButtonDisabled(true);
        setOkButtonDisabled(true);
        setOkButtonLoading(true);
        dataFeeder(data);
        submitQuery.mutate();
    };
    // 新组件不需改动
    const onCancel = () => {
        clearStore();
        // 每增加一个下拉框都要设置各自的默认值
        reset(formDefaults.current);                       // 重置react-form
        setStoreObject(storeDefaults.current);             // 重置mmkv存储
        // 重置下拉组件状态
        setClassRadioState(cloneDeep(selectorDefaults.current.class));
        setSubClassRadioState(cloneDeep(selectorDefaults.current.subClass));
        setIndustryRadioState(cloneDeep(selectorDefaults.current.industry));
        setDifficultyRadioState(cloneDeep(selectorDefaults.current.difficulty));
    };

    // 新组件不需改动
    const commitOnSuccess = (data) => {
        console.log("response success, data: ", data);
        if(data.STATUS !== 0) {
            setSnackBarMessage(`项目库更新发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        } else {
            // 设置确认框数据
            setConfirmDialogConfig({
                title: "更新项目库",
                text: "项目库信息更新成功!",
                okLabel: "确定",
                onOK: () => { pageInfoQuery.mutate(); setShowConfirm(false); }});
            setShowConfirm(true);
        }
    };
    const commitOnError = (error) => {
        setSnackBarMessage(`项目库更新发生错误, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const commitOnSettled = (data, error) => {
        setCancelButtonDisabled(false);
        setOkButtonDisabled(false);
        setOkButtonLoading(false);
    };
    const submitQuery = updateQueryClient(dataFeeder, route.params.pageMeta.id, commitOnSuccess, commitOnError, commitOnSettled);

    useEffect(() => {
        pageInfoQuery.mutate();

        return () => classChecked.current = false;
    }, []);

    const deleteOnSuccess = () => {
        navigation.goBack();
    };
    const deleteOnError = (error) => {
        setSnackBarMessage(`项目库删除失败, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const deleteQuery = deleteQueryClient(delQueryInfo.current?.id, deleteOnSuccess, deleteOnError);

    const deleteAlert = () =>{
        return (
            Alert.alert("删除项目库", `删除后不可撤销, 请确认是否删除: ${delQueryInfo.current?.name}?`, [
                { text: "取消" },
                { text: "确定", onPress: () => deleteQuery.mutate() },
            ])
        );
    };

    return (
        <View style={{ flex: 1 }}>

            <HeaderBar
                title={"更新项目库"}
                navigation={navigation}
                goBackCallback={() => callOneByOne(clearStore, resetSelectorStates)} // 返回前清空mmkv存储因为临时数据不再需要, 重置状态使得页面切换后不会使用前面的数据
                menuItemArray={[{ title: "删除项目库", action: deleteAlert }]}
            />

            <ScreenWrapper contentContainerStyle={styles.container} style={{marginBottom: height+ bottom}}>
                {/*<Text>Render: {render}</Text>*/}

                <View style={styles.formEntry}>

                    <ControlledTextInput
                        rowLabel="项目库名称"
                        control={control}
                        name="name"
                        placeholder="XXXX项目"
                        onChangeText={(text) => setStore("name", text)}
                        onClearText={() => {
                            setStore("name", "");
                            resetField("name", { defaultValue: "" });
                        }}
                        editable={true}
                        mode="outlined"
                        formError={errors}
                        required={true}
                        multiline={true}
                    />

                    <ControlledRadioInputWithQuery
                        name="class"
                        rowLabel="项目类型"
                        control={control}
                        placeholder="请拉选项目类型"
                        onDialogConfirm={(obj) => {
                            console.log("confirm state: ", obj);
                            setStore("class", obj);
                            setClassRadioState(obj);
                        }}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        //dataProvider={allUsersQueryClient}
                        dataProvider={classStateDataProviderRef}
                        setFetchFailMessage={setSnackBarMessage}
                        setDisplayFailBar={setShowSnackbar}
                        dialogState={classRadioState}
                        setDialogState={setClassRadioState}
                        defaultNullOptionsTip={"未找到项目类型数据, 请联系管理员!"}
                        required={true}
                    />

                    {projClass === 1 && // 暂时只有水平衡需要拉选子类型
                    <ControlledRadioInputWithQuery
                        name="subClass"
                        rowLabel="项目子类型"
                        control={control}
                        placeholder="请拉选项目子类型"
                        onDialogConfirm={(obj) => {
                            console.log("confirm state: ", obj);
                            setStore("subClass", obj);
                            setSubClassRadioState(obj);
                        }}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        //dataProvider={allUsersQueryClient}
                        dataProvider={subClassStateDataProviderRef}
                        setFetchFailMessage={setSnackBarMessage}
                        setDisplayFailBar={setShowSnackbar}
                        dialogState={subClassRadioState}
                        setDialogState={setSubClassRadioState}
                        defaultNullOptionsTip={"未找到项目类型数据, 请联系管理员!"}
                        required={true}
                    />}

                    {classChecked.current && <ControlledRadioInputWithQuery
                        name="industry"
                        rowLabel="行业类型"
                        control={control}
                        placeholder="请拉选行业类型"
                        onDialogConfirm={(obj) => {
                            console.log("confirm state: ", obj);
                            setStore("industry", obj);
                            setIndustryRadioState(obj);
                        }}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        //dataProvider={allUsersQueryClient}
                        dataProvider={industryStateDataProviderRef}
                        setFetchFailMessage={setSnackBarMessage}
                        setDisplayFailBar={setShowSnackbar}
                        dialogState={industryRadioState}
                        setDialogState={setIndustryRadioState}
                        defaultNullOptionsTip={"未找到项目库难度数据, 请联系管理员!"}
                        required={true}
                    />}

                    {classChecked.current && <ControlledRadioInputWithQuery
                        name="difficulty"
                        rowLabel="默认难度"
                        control={control}
                        placeholder="请拉选项目难度"
                        onDialogConfirm={(obj) => {
                            console.log("confirm state: ", obj);
                            setStore("difficulty", obj);
                            setDifficultyRadioState(obj);
                        }}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        //dataProvider={allUsersQueryClient}
                        dataProvider={difficultyStateDataProviderRef}
                        setFetchFailMessage={setSnackBarMessage}
                        setDisplayFailBar={setShowSnackbar}
                        dialogState={difficultyRadioState}
                        setDialogState={setDifficultyRadioState}
                        defaultNullOptionsTip={"未找到项目库难度数据, 请联系管理员!"}
                        required={true}
                    />}

                </View>

            </ScreenWrapper>

            <Appbar
                style={[
                    styles.bottom,
                    {
                        height: height + bottom,
                    },
                    theme.isV3 && {
                        backgroundColor: theme.colors.elevation.level2,
                    },
                ]}
                safeAreaInsets={{ bottom, left, right }}
                theme={{ mode: "adaptive" }}
            //mode='center-aligned'
            >

                <View style={styles.bottomBarContainer}>
                    <Divider />
                    <View style={styles.bottomBarComponents}>
                        <BottomBarButton
                            label={bottomBarLabels.cancel}
                            disabled={cancelButtonDisabled}
                            onPress={onCancel}
                        />
                        <BottomBarButton
                            label={bottomBarLabels.ok}
                            loading={okButtonLoading}
                            disabled={okButtonDisabled}
                            icon={saveButtonIcon}
                            onPress={handleSubmit(onHandleSubmit, (formErr) => onPreSubmitError(formErr, getStore, "ProjectBaseUpdating"))}
                        />
                    </View>
                </View>
            </Appbar>

            <Snackbar
                style={styles.snackBar}
                visible={showSnackbar}
                onDismiss={() => setShowSnackbar(false)}
                onIconPress={() => setShowSnackbar(false)}
                duration={Snackbar.DURATION_LONG}
            >
                {snackBarMessage}
            </Snackbar>

            <DialogToConfirm
                visible={showConfirm}
                title={confimDialogConfig.title ? confimDialogConfig.title : ""}
                text={confimDialogConfig.text ? confimDialogConfig.text : ""}
                onOK={confimDialogConfig.onOK ? confimDialogConfig.onOK : () => setShowConfirm(false)}
                okBtnLabel={confimDialogConfig.okLabel ? confimDialogConfig.okLabel : "确认"}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    firstColumn: {
        flex: 2,
        //borderWidth: 1,
        //height: 40,
        content: {
            fontSize: 18,
        }
    },
    secondColumn: {
        flex: 3,
        // /borderWidth: 1,
        // height: 40,
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        flex: 1,
        marginHorizontal: 6
    },
    formEntry: {
        margin: 8,
    },
    snackBar: {
        flexDirection: "column",
    },
});

export default ProjectBaseUpdating;
