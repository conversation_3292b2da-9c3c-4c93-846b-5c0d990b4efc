///
/// ReactNativeAudioRecorderPlayer-Swift-Cxx-Umbrella.hpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON> @ Margelo
///

#pragma once

// Forward declarations of C++ defined types
// Forward declaration of `AVEncoderAudioQualityIOSType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AVEncoderAudioQualityIOSType; }
// Forward declaration of `AVEncodingOption` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AVEncodingOption; }
// Forward declaration of `AVLinearPCMBitDepthKeyIOSType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AVLinearPCMBitDepthKeyIOSType; }
// Forward declaration of `AVModeIOSOption` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AVModeIOSOption; }
// Forward declaration of `AudioEncoderAndroidType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AudioEncoderAndroidType; }
// Forward declaration of `AudioQualityType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AudioQualityType; }
// Forward declaration of `AudioSet` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { struct AudioSet; }
// Forward declaration of `AudioSourceAndroidType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AudioSourceAndroidType; }
// Forward declaration of `HybridAudioRecorderPlayerSpec` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { class HybridAudioRecorderPlayerSpec; }
// Forward declaration of `OutputFormatAndroidType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class OutputFormatAndroidType; }
// Forward declaration of `PlayBackType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { struct PlayBackType; }
// Forward declaration of `RecordBackType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { struct RecordBackType; }

// Include C++ defined types
#include "AVEncoderAudioQualityIOSType.hpp"
#include "AVEncodingOption.hpp"
#include "AVLinearPCMBitDepthKeyIOSType.hpp"
#include "AVModeIOSOption.hpp"
#include "AudioEncoderAndroidType.hpp"
#include "AudioQualityType.hpp"
#include "AudioSet.hpp"
#include "AudioSourceAndroidType.hpp"
#include "HybridAudioRecorderPlayerSpec.hpp"
#include "OutputFormatAndroidType.hpp"
#include "PlayBackType.hpp"
#include "RecordBackType.hpp"
#include <NitroModules/Promise.hpp>
#include <NitroModules/Result.hpp>
#include <exception>
#include <functional>
#include <memory>
#include <optional>
#include <string>
#include <unordered_map>

// C++ helpers for Swift
#include "ReactNativeAudioRecorderPlayer-Swift-Cxx-Bridge.hpp"

// Common C++ types used in Swift
#include <NitroModules/ArrayBufferHolder.hpp>
#include <NitroModules/AnyMapHolder.hpp>
#include <NitroModules/RuntimeError.hpp>
#include <NitroModules/DateToChronoDate.hpp>

// Forward declarations of Swift defined types
// Forward declaration of `HybridAudioRecorderPlayerSpec_cxx` to properly resolve imports.
namespace ReactNativeAudioRecorderPlayer { class HybridAudioRecorderPlayerSpec_cxx; }

// Include Swift defined types
#if __has_include("ReactNativeAudioRecorderPlayer-Swift.h")
// This header is generated by Xcode/Swift on every app build.
// If it cannot be found, make sure the Swift module's name (= podspec name) is actually "ReactNativeAudioRecorderPlayer".
#include "ReactNativeAudioRecorderPlayer-Swift.h"
// Same as above, but used when building with frameworks (`use_frameworks`)
#elif __has_include(<ReactNativeAudioRecorderPlayer/ReactNativeAudioRecorderPlayer-Swift.h>)
#include <ReactNativeAudioRecorderPlayer/ReactNativeAudioRecorderPlayer-Swift.h>
#else
#error ReactNativeAudioRecorderPlayer's autogenerated Swift header cannot be found! Make sure the Swift module's name (= podspec name) is actually "ReactNativeAudioRecorderPlayer", and try building the app first.
#endif
