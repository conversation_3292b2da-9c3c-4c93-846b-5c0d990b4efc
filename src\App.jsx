import { NavigationContainer } from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import { QueryClientProvider, focusManager } from "@tanstack/react-query";
import React from "react";
import { Platform } from "react-native";
import "react-native-gesture-handler";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { PaperProvider } from "react-native-paper";
import { SafeAreaInsetsContext } from "react-native-safe-area-context";
import { useShallow } from "zustand/shallow";
import { usePreferenceZustandStoredState } from "./hooks/preferenceStoredState";
import { useAppState } from "./hooks/useAppState";
import { useOnlineManager } from "./hooks/useOnlineManager";
import { LoginScreen, RegisterScreen, ResetPasswordScreen, SplashScreen } from "./screens";
import HomeScreen from "./screens/HomeScreen";
import { queryClient } from "./services/query";
import { CombinedDarkTheme, CombinedDefaultTheme } from "./theme";
import "./translations";

/**
 * AppState can tell you if the app is in the foreground or background, and notify you when the state changes.
 * AppState is frequently used to determine the intent and proper behavior when handling push notifications.
 * @typedef {("active" | "background" | "inactive" | "unknown" | "extension")} AppStateStatus
 */

/**
 * Helper to Refetch on App focus
 * https://codesandbox.io/p/sandbox/github/tanstack/query/tree/main/examples/react/react-native
 * @param {AppStateStatus} status AppState
 */
function onAppStateChange(status) {
    // React Query already supports in web browser refetch on window focus by default
    if (Platform.OS !== "web") {
        focusManager.setFocused(status === "active");
    }
}

//export const PreferencesContext = React.createContext(null);

const Stack = createStackNavigator();

export default function App() {
    useOnlineManager();
    useAppState(onAppStateChange);

    return (
        <GestureHandlerRootView style={{ flex: 1 }}>
            <QueryClientProvider client={queryClient}>
                <PaperContainer/>
            </QueryClientProvider>
        </GestureHandlerRootView>
    );
}

/**
 * Impl authentication flows for user login.
 * This container was made in a separate component because the App component couldn't use `useQuery` as there's no queryClient.
 * Authentication flows: https://reactnavigation.org/docs/auth-flow/
 * @param {Object} props
 * @returns
 */
const PaperContainer = () => {
    const [themeMode] = usePreferenceZustandStoredState(useShallow((state) => [state.theme, state.setTheme]));

    const combinedTheme = themeMode === "dark" ? CombinedDarkTheme : CombinedDefaultTheme;
    const configuredFontTheme = {
        ...combinedTheme,
        /* no fonts config yet
        fonts: configureFonts({
            config: {
                fontFamily: 'Abel',
            },
        }),
        */
    };

    return (

        <PaperProvider theme={configuredFontTheme}>
            <React.Fragment>
                <NavigationContainer theme={combinedTheme}>
                    <SafeAreaInsetsContext.Consumer>
                        {() => {
                            // {(insets) => {
                            // insets: {"bottom": 0, "left": 0, "right": 0, "top": 0}
                            // const { left, right } = insets || { left: 0, right: 0 };
                            // const collapsedDrawerWidth = 80 + Math.max(left, right);
                            return (
                                <RootScreen/>
                            );
                        }}
                    </SafeAreaInsetsContext.Consumer>
                </NavigationContainer>
            </React.Fragment>
        </PaperProvider>

    );
};

const RootScreen = () => {
    return (
        <Stack.Navigator screenOptions={{ headerShown: false }}>
            <Stack.Screen name="SplashScreen" component={SplashScreen} />
            <Stack.Screen name="LoginScreen" component={LoginScreen} />
            <Stack.Screen name="RegisterScreen" component={RegisterScreen} />
            <Stack.Screen name="HomeScreen" component={HomeScreen} />
            <Stack.Screen name="ResetPasswordScreen" component={ResetPasswordScreen} />
        </Stack.Navigator>
    );
};
