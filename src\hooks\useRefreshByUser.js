import { useState } from "react";

// https://codesandbox.io/p/sandbox/github/tanstack/query/tree/main/examples/react/react-native?file=%2Fsrc%2Fhooks%2FuseRefreshByUser.ts%3A1%2C1-21%2C1

/**
 *
 * @param {() => Promise} refetch
 * @returns
 */
export function useRefreshByUser(refetch) {
    const [isRefetchingByUser, setIsRefetchingByUser] = useState(false);

    async function refetchByUser() {
        setIsRefetchingByUser(true);

        try {
            await refetch();
        } finally {
            setIsRefetchingByUser(false);
        }
    }

    return { isRefetchingByUser, refetchByUser };
}
