///
/// AudioSet.hpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc Rousavy @ Margelo
///

#pragma once

#if __has_include(<NitroModules/JSIConverter.hpp>)
#include <NitroModules/JSIConverter.hpp>
#else
#error NitroModules cannot be found! Are you sure you installed NitroModules properly?
#endif
#if __has_include(<NitroModules/NitroDefines.hpp>)
#include <NitroModules/NitroDefines.hpp>
#else
#error NitroModules cannot be found! Are you sure you installed NitroModules properly?
#endif

// Forward declaration of `AudioSourceAndroidType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AudioSourceAndroidType; }
// Forward declaration of `OutputFormatAndroidType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class OutputFormatAndroidType; }
// Forward declaration of `AudioEncoderAndroidType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AudioEncoderAndroidType; }
// Forward declaration of `AVEncoderAudioQualityIOSType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AVEncoderAudioQualityIOSType; }
// Forward declaration of `AVModeIOSOption` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AVModeIOSOption; }
// Forward declaration of `AVEncodingOption` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AVEncodingOption; }
// Forward declaration of `AVLinearPCMBitDepthKeyIOSType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AVLinearPCMBitDepthKeyIOSType; }
// Forward declaration of `AudioQualityType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AudioQualityType; }

#include <optional>
#include "AudioSourceAndroidType.hpp"
#include "OutputFormatAndroidType.hpp"
#include "AudioEncoderAndroidType.hpp"
#include "AVEncoderAudioQualityIOSType.hpp"
#include "AVModeIOSOption.hpp"
#include "AVEncodingOption.hpp"
#include "AVLinearPCMBitDepthKeyIOSType.hpp"
#include "AudioQualityType.hpp"

namespace margelo::nitro::react_native_audio_recorder_player {

  /**
   * A struct which can be represented as a JavaScript object (AudioSet).
   */
  struct AudioSet {
  public:
    std::optional<AudioSourceAndroidType> AudioSourceAndroid     SWIFT_PRIVATE;
    std::optional<OutputFormatAndroidType> OutputFormatAndroid     SWIFT_PRIVATE;
    std::optional<AudioEncoderAndroidType> AudioEncoderAndroid     SWIFT_PRIVATE;
    std::optional<AVEncoderAudioQualityIOSType> AVEncoderAudioQualityKeyIOS     SWIFT_PRIVATE;
    std::optional<AVModeIOSOption> AVModeIOS     SWIFT_PRIVATE;
    std::optional<AVEncodingOption> AVEncodingOptionIOS     SWIFT_PRIVATE;
    std::optional<AVEncodingOption> AVFormatIDKeyIOS     SWIFT_PRIVATE;
    std::optional<double> AVNumberOfChannelsKeyIOS     SWIFT_PRIVATE;
    std::optional<AVLinearPCMBitDepthKeyIOSType> AVLinearPCMBitDepthKeyIOS     SWIFT_PRIVATE;
    std::optional<bool> AVLinearPCMIsBigEndianKeyIOS     SWIFT_PRIVATE;
    std::optional<bool> AVLinearPCMIsFloatKeyIOS     SWIFT_PRIVATE;
    std::optional<bool> AVLinearPCMIsNonInterleavedIOS     SWIFT_PRIVATE;
    std::optional<double> AVSampleRateKeyIOS     SWIFT_PRIVATE;
    std::optional<AudioQualityType> AudioQuality     SWIFT_PRIVATE;
    std::optional<double> AudioChannels     SWIFT_PRIVATE;
    std::optional<double> AudioSamplingRate     SWIFT_PRIVATE;
    std::optional<double> AudioEncodingBitRate     SWIFT_PRIVATE;
    std::optional<bool> IncludeBase64     SWIFT_PRIVATE;

  public:
    AudioSet() = default;
    explicit AudioSet(std::optional<AudioSourceAndroidType> AudioSourceAndroid, std::optional<OutputFormatAndroidType> OutputFormatAndroid, std::optional<AudioEncoderAndroidType> AudioEncoderAndroid, std::optional<AVEncoderAudioQualityIOSType> AVEncoderAudioQualityKeyIOS, std::optional<AVModeIOSOption> AVModeIOS, std::optional<AVEncodingOption> AVEncodingOptionIOS, std::optional<AVEncodingOption> AVFormatIDKeyIOS, std::optional<double> AVNumberOfChannelsKeyIOS, std::optional<AVLinearPCMBitDepthKeyIOSType> AVLinearPCMBitDepthKeyIOS, std::optional<bool> AVLinearPCMIsBigEndianKeyIOS, std::optional<bool> AVLinearPCMIsFloatKeyIOS, std::optional<bool> AVLinearPCMIsNonInterleavedIOS, std::optional<double> AVSampleRateKeyIOS, std::optional<AudioQualityType> AudioQuality, std::optional<double> AudioChannels, std::optional<double> AudioSamplingRate, std::optional<double> AudioEncodingBitRate, std::optional<bool> IncludeBase64): AudioSourceAndroid(AudioSourceAndroid), OutputFormatAndroid(OutputFormatAndroid), AudioEncoderAndroid(AudioEncoderAndroid), AVEncoderAudioQualityKeyIOS(AVEncoderAudioQualityKeyIOS), AVModeIOS(AVModeIOS), AVEncodingOptionIOS(AVEncodingOptionIOS), AVFormatIDKeyIOS(AVFormatIDKeyIOS), AVNumberOfChannelsKeyIOS(AVNumberOfChannelsKeyIOS), AVLinearPCMBitDepthKeyIOS(AVLinearPCMBitDepthKeyIOS), AVLinearPCMIsBigEndianKeyIOS(AVLinearPCMIsBigEndianKeyIOS), AVLinearPCMIsFloatKeyIOS(AVLinearPCMIsFloatKeyIOS), AVLinearPCMIsNonInterleavedIOS(AVLinearPCMIsNonInterleavedIOS), AVSampleRateKeyIOS(AVSampleRateKeyIOS), AudioQuality(AudioQuality), AudioChannels(AudioChannels), AudioSamplingRate(AudioSamplingRate), AudioEncodingBitRate(AudioEncodingBitRate), IncludeBase64(IncludeBase64) {}
  };

} // namespace margelo::nitro::react_native_audio_recorder_player

namespace margelo::nitro {

  using namespace margelo::nitro::react_native_audio_recorder_player;

  // C++ AudioSet <> JS AudioSet (object)
  template <>
  struct JSIConverter<AudioSet> final {
    static inline AudioSet fromJSI(jsi::Runtime& runtime, const jsi::Value& arg) {
      jsi::Object obj = arg.asObject(runtime);
      return AudioSet(
        JSIConverter<std::optional<AudioSourceAndroidType>>::fromJSI(runtime, obj.getProperty(runtime, "AudioSourceAndroid")),
        JSIConverter<std::optional<OutputFormatAndroidType>>::fromJSI(runtime, obj.getProperty(runtime, "OutputFormatAndroid")),
        JSIConverter<std::optional<AudioEncoderAndroidType>>::fromJSI(runtime, obj.getProperty(runtime, "AudioEncoderAndroid")),
        JSIConverter<std::optional<AVEncoderAudioQualityIOSType>>::fromJSI(runtime, obj.getProperty(runtime, "AVEncoderAudioQualityKeyIOS")),
        JSIConverter<std::optional<AVModeIOSOption>>::fromJSI(runtime, obj.getProperty(runtime, "AVModeIOS")),
        JSIConverter<std::optional<AVEncodingOption>>::fromJSI(runtime, obj.getProperty(runtime, "AVEncodingOptionIOS")),
        JSIConverter<std::optional<AVEncodingOption>>::fromJSI(runtime, obj.getProperty(runtime, "AVFormatIDKeyIOS")),
        JSIConverter<std::optional<double>>::fromJSI(runtime, obj.getProperty(runtime, "AVNumberOfChannelsKeyIOS")),
        JSIConverter<std::optional<AVLinearPCMBitDepthKeyIOSType>>::fromJSI(runtime, obj.getProperty(runtime, "AVLinearPCMBitDepthKeyIOS")),
        JSIConverter<std::optional<bool>>::fromJSI(runtime, obj.getProperty(runtime, "AVLinearPCMIsBigEndianKeyIOS")),
        JSIConverter<std::optional<bool>>::fromJSI(runtime, obj.getProperty(runtime, "AVLinearPCMIsFloatKeyIOS")),
        JSIConverter<std::optional<bool>>::fromJSI(runtime, obj.getProperty(runtime, "AVLinearPCMIsNonInterleavedIOS")),
        JSIConverter<std::optional<double>>::fromJSI(runtime, obj.getProperty(runtime, "AVSampleRateKeyIOS")),
        JSIConverter<std::optional<AudioQualityType>>::fromJSI(runtime, obj.getProperty(runtime, "AudioQuality")),
        JSIConverter<std::optional<double>>::fromJSI(runtime, obj.getProperty(runtime, "AudioChannels")),
        JSIConverter<std::optional<double>>::fromJSI(runtime, obj.getProperty(runtime, "AudioSamplingRate")),
        JSIConverter<std::optional<double>>::fromJSI(runtime, obj.getProperty(runtime, "AudioEncodingBitRate")),
        JSIConverter<std::optional<bool>>::fromJSI(runtime, obj.getProperty(runtime, "IncludeBase64"))
      );
    }
    static inline jsi::Value toJSI(jsi::Runtime& runtime, const AudioSet& arg) {
      jsi::Object obj(runtime);
      obj.setProperty(runtime, "AudioSourceAndroid", JSIConverter<std::optional<AudioSourceAndroidType>>::toJSI(runtime, arg.AudioSourceAndroid));
      obj.setProperty(runtime, "OutputFormatAndroid", JSIConverter<std::optional<OutputFormatAndroidType>>::toJSI(runtime, arg.OutputFormatAndroid));
      obj.setProperty(runtime, "AudioEncoderAndroid", JSIConverter<std::optional<AudioEncoderAndroidType>>::toJSI(runtime, arg.AudioEncoderAndroid));
      obj.setProperty(runtime, "AVEncoderAudioQualityKeyIOS", JSIConverter<std::optional<AVEncoderAudioQualityIOSType>>::toJSI(runtime, arg.AVEncoderAudioQualityKeyIOS));
      obj.setProperty(runtime, "AVModeIOS", JSIConverter<std::optional<AVModeIOSOption>>::toJSI(runtime, arg.AVModeIOS));
      obj.setProperty(runtime, "AVEncodingOptionIOS", JSIConverter<std::optional<AVEncodingOption>>::toJSI(runtime, arg.AVEncodingOptionIOS));
      obj.setProperty(runtime, "AVFormatIDKeyIOS", JSIConverter<std::optional<AVEncodingOption>>::toJSI(runtime, arg.AVFormatIDKeyIOS));
      obj.setProperty(runtime, "AVNumberOfChannelsKeyIOS", JSIConverter<std::optional<double>>::toJSI(runtime, arg.AVNumberOfChannelsKeyIOS));
      obj.setProperty(runtime, "AVLinearPCMBitDepthKeyIOS", JSIConverter<std::optional<AVLinearPCMBitDepthKeyIOSType>>::toJSI(runtime, arg.AVLinearPCMBitDepthKeyIOS));
      obj.setProperty(runtime, "AVLinearPCMIsBigEndianKeyIOS", JSIConverter<std::optional<bool>>::toJSI(runtime, arg.AVLinearPCMIsBigEndianKeyIOS));
      obj.setProperty(runtime, "AVLinearPCMIsFloatKeyIOS", JSIConverter<std::optional<bool>>::toJSI(runtime, arg.AVLinearPCMIsFloatKeyIOS));
      obj.setProperty(runtime, "AVLinearPCMIsNonInterleavedIOS", JSIConverter<std::optional<bool>>::toJSI(runtime, arg.AVLinearPCMIsNonInterleavedIOS));
      obj.setProperty(runtime, "AVSampleRateKeyIOS", JSIConverter<std::optional<double>>::toJSI(runtime, arg.AVSampleRateKeyIOS));
      obj.setProperty(runtime, "AudioQuality", JSIConverter<std::optional<AudioQualityType>>::toJSI(runtime, arg.AudioQuality));
      obj.setProperty(runtime, "AudioChannels", JSIConverter<std::optional<double>>::toJSI(runtime, arg.AudioChannels));
      obj.setProperty(runtime, "AudioSamplingRate", JSIConverter<std::optional<double>>::toJSI(runtime, arg.AudioSamplingRate));
      obj.setProperty(runtime, "AudioEncodingBitRate", JSIConverter<std::optional<double>>::toJSI(runtime, arg.AudioEncodingBitRate));
      obj.setProperty(runtime, "IncludeBase64", JSIConverter<std::optional<bool>>::toJSI(runtime, arg.IncludeBase64));
      return obj;
    }
    static inline bool canConvert(jsi::Runtime& runtime, const jsi::Value& value) {
      if (!value.isObject()) {
        return false;
      }
      jsi::Object obj = value.getObject(runtime);
      if (!JSIConverter<std::optional<AudioSourceAndroidType>>::canConvert(runtime, obj.getProperty(runtime, "AudioSourceAndroid"))) return false;
      if (!JSIConverter<std::optional<OutputFormatAndroidType>>::canConvert(runtime, obj.getProperty(runtime, "OutputFormatAndroid"))) return false;
      if (!JSIConverter<std::optional<AudioEncoderAndroidType>>::canConvert(runtime, obj.getProperty(runtime, "AudioEncoderAndroid"))) return false;
      if (!JSIConverter<std::optional<AVEncoderAudioQualityIOSType>>::canConvert(runtime, obj.getProperty(runtime, "AVEncoderAudioQualityKeyIOS"))) return false;
      if (!JSIConverter<std::optional<AVModeIOSOption>>::canConvert(runtime, obj.getProperty(runtime, "AVModeIOS"))) return false;
      if (!JSIConverter<std::optional<AVEncodingOption>>::canConvert(runtime, obj.getProperty(runtime, "AVEncodingOptionIOS"))) return false;
      if (!JSIConverter<std::optional<AVEncodingOption>>::canConvert(runtime, obj.getProperty(runtime, "AVFormatIDKeyIOS"))) return false;
      if (!JSIConverter<std::optional<double>>::canConvert(runtime, obj.getProperty(runtime, "AVNumberOfChannelsKeyIOS"))) return false;
      if (!JSIConverter<std::optional<AVLinearPCMBitDepthKeyIOSType>>::canConvert(runtime, obj.getProperty(runtime, "AVLinearPCMBitDepthKeyIOS"))) return false;
      if (!JSIConverter<std::optional<bool>>::canConvert(runtime, obj.getProperty(runtime, "AVLinearPCMIsBigEndianKeyIOS"))) return false;
      if (!JSIConverter<std::optional<bool>>::canConvert(runtime, obj.getProperty(runtime, "AVLinearPCMIsFloatKeyIOS"))) return false;
      if (!JSIConverter<std::optional<bool>>::canConvert(runtime, obj.getProperty(runtime, "AVLinearPCMIsNonInterleavedIOS"))) return false;
      if (!JSIConverter<std::optional<double>>::canConvert(runtime, obj.getProperty(runtime, "AVSampleRateKeyIOS"))) return false;
      if (!JSIConverter<std::optional<AudioQualityType>>::canConvert(runtime, obj.getProperty(runtime, "AudioQuality"))) return false;
      if (!JSIConverter<std::optional<double>>::canConvert(runtime, obj.getProperty(runtime, "AudioChannels"))) return false;
      if (!JSIConverter<std::optional<double>>::canConvert(runtime, obj.getProperty(runtime, "AudioSamplingRate"))) return false;
      if (!JSIConverter<std::optional<double>>::canConvert(runtime, obj.getProperty(runtime, "AudioEncodingBitRate"))) return false;
      if (!JSIConverter<std::optional<bool>>::canConvert(runtime, obj.getProperty(runtime, "IncludeBase64"))) return false;
      return true;
    }
  };

} // namespace margelo::nitro
