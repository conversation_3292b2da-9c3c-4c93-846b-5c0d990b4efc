import React from "react";
import { StyleSheet } from "react-native";
import { Button as PaperButton } from "react-native-paper";
import { useTheme } from "react-native-paper";
import PropTypes from "prop-types";

/**
 *
 * @param {Object} arg
 * @param {string} arg.mode
 * @param {styles} arg.style
 * @param {props} arg.props
 * @returns
 */
const Button = ({ mode, style, ...props }) => {
    const theme = useTheme();
    // console.log("Butttttton background:", theme.colors.primary); // 按钮背景颜色是theme.colors.primary

    return (
        <PaperButton
            style={[ styles.button, mode === "outlined" && { backgroundColor: theme.colors.surface }, style, ]}
            labelStyle={styles.text}
            mode={mode}
            {...props}
        />
    );
};

Button.propTypes = {
    mode: PropTypes.string,
    style: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
};

const styles = StyleSheet.create({
    button: {
        width: "100%",
        marginVertical: 10,
        paddingVertical: 2,
    },
    text: {
        fontWeight: "bold",
        fontSize: 15,
        lineHeight: 26,
    },
});


export default Button;
