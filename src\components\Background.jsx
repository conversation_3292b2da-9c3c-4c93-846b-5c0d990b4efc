import React from "react";
import { KeyboardAvoidingView, StyleSheet } from "react-native";
import { useTheme } from "react-native-paper";

const Background = ({ children }) => {
    const theme = useTheme();

    const styles = StyleSheet.create({
        background: {
            flex: 1,
            width: "100%",
            backgroundColor: theme.colors.surface,
        },
        container: {
            flex: 1,
            padding: 20,
            width: "100%",
            maxWidth: 340,
            alignSelf: "center",
            alignItems: "center",
            justifyContent: "center",
        },
    });

    /*
    <ImageBackground source={backgound_dot} resizeMode="repeat" style={styles.background}>
        <KeyboardAvoidingView style={styles.container} behavior="padding">
            {children}
        </KeyboardAvoidingView>
    </ImageBackground>
*/
    return (
        <KeyboardAvoidingView style={styles.container} behavior="padding">
            {children}
        </KeyboardAvoidingView>
    );
};

export default Background;
