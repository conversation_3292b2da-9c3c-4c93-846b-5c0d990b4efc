import { useMutation } from "@tanstack/react-query";
import { httpClient } from "../services/http";
import { ORG_ROLE_ADD as key} from "../config/keysConfig";

const RETRY_CODES = [401, 501, 502, 503, 504];
const RETRY_METHODS = ["GET", "POST", "PUT", "HEAD", "DELETE", "OPTIONS", "TRACE"];
const RETRY_INTERVALS = [2000, 3000, 5000];
const RETRY_LIMIT = RETRY_INTERVALS.length;

/**
 * 向服务端发起一个addrole的post请求.
 * 注意: 这个版本的ky客户端含有重连,
 * 并且在客户端中将token设置到标头: request.headers.set("Authorization", token)
 * @param {Object} roleInfo token string
 * @param {()=>{}} callback callback function
 */
/*
const useRoleAdding = (roleInfo) => {
    const roleInfoCopy = { ...roleInfo };
    return useQuery({
        queryKey: [QUERY_ADDING_ROLE],
        queryFn: async () => {
            const response = await httpClient.post(urlPath, { json: roleInfoCopy });
            const json = await response.json();
            console.log("queryFn receive data:", json);
            return json; // 注意不要return await!
        },
        enabled: false,
        // 逻辑上, 首次失败后, 立即检测retryDelay, 经延迟后再检测retry触发重试
        // 注意, 有Ky的retry有bug, 这里使用React Query的retry!!!!!!
        retry: (failureCount, error) => { // failureCount from 0
            console.log("failureCount:", failureCount);
            if (failureCount < RETRY_LIMIT
                && error.name === "HTTPError"
                && RETRY_CODES.includes(error.response.status)
                && RETRY_METHODS.includes(error.request.method)) {
                return true;
            } else {
                return false;
            }
        },
        retryDelay: (attemptIndex) => RETRY_INTERVALS[attemptIndex], // attemptIndex from 0
    });
};
*/

/**
 * 向服务端发起一个addrole的post请求.
 * 注意: 这个版本的ky客户端含有重连,
 * 并且在客户端中将token设置到标头: request.headers.set("Authorization", token)
 * @param {Object} roleInfo token string
 * @param {()=>{}} callback callback function
 */
const useRoleAdding = (roleInfo, onSuccess, onError, onSettled) => {
    const roleInfoCopy = { ...roleInfo };
    return useMutation({
        mutationKey: [key.query],
        mutationFn: async () => {
            const response = await httpClient.post(key.url, { json: roleInfoCopy });
            const json = await response.json();
            console.log("queryFn receive data:", json);
            return json; // 注意不要return await!
        },

        onSuccess: (data, variables, context) => {
            console.log("Call onSuccess");
            onSuccess?.();
        },
        onError: (error, variables, context) => {
            console.log("Call onError");
            onError?.();
        },
        onSettled: (data, error, variables, context) => {
            console.log("Call onSettled");
            onSettled?.();
        },

        //enabled: true, // useMutation does not have enabled

        // 逻辑上, 首次失败后, 立即检测retryDelay, 经延迟后再检测retry触发重试
        // 注意, 有Ky的retry有bug, 这里使用React Query的retry!!!!!!
        retry: (failureCount, error) => { // failureCount from 0
            console.log("failureCount:", failureCount);
            if (failureCount < RETRY_LIMIT
                && error.name === "HTTPError"
                && RETRY_CODES.includes(error.response.status)
                && RETRY_METHODS.includes(error.request.method)) {
                return true;
            } else {
                return false;
            }
        },
        retryDelay: (attemptIndex) => RETRY_INTERVALS[attemptIndex], // attemptIndex from 0
    });
};


export { useRoleAdding };
