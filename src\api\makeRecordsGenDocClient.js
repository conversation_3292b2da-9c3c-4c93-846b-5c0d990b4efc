import { useMutation } from "@tanstack/react-query";
import { noTimeoutHttpClient as httpClient } from "../services/http";
import log from "../services/logging";
import { makeFormData, mergeQueryPath } from "../utils";


/**
 * 目前与makeUpdatingClient完全一样
 * @param {Object} arg
 * @param {string} arg.query the query key for react-query
 * @param {string} arg.url the http request path which will be responed by the server
 * @returns
 */
export const makeRecordsGenDocClient = ({ query, url }) => {
    /**
     * 返回一个函数, 这个返回的函数创建一个useMutation查询, 用于发起update请求.
     * @param {Object} formDataObject object
     * @param {Number | Number[] | String | String[]} varOrVars
     * @param {()=>{}} onSuccess callback function
     * @param {()=>{}} onError callback function
     * @param {()=>{}} onSettled callback function
     */
    return (formDataObject, varOrVars, onSuccess, onError, onSettled) => {
        const appendUrl = `${url}${mergeQueryPath(varOrVars)}`; // url/sub_dir
        return useMutation({
            mutationKey: [query, varOrVars],
            mutationFn: async () => {
                const formData = makeFormData(formDataObject);
                log.debug("makeUpdatingClient post, query: %s, urlPath: %s, data: %s", query, appendUrl, formData);
                const response = await httpClient.post(appendUrl, { body: formData, retry: { limit: 0 } });
                const json = await response.json();
                log.debug("updating query client %s on %s, mutationFn receive data: %s", query, appendUrl, json);
                return json;
            },
            onSuccess: (data, variables, context) => {
                console.log("Call onSuccess");
                onSuccess?.(data);
            },
            onError: (error, variables, context) => {
                console.log("Call onError");
                onError?.(error);
            },
            onSettled: (data, error, variables, context) => {
                console.log("Call onSettled");
                onSettled?.(data, error);
            },
        });
    };
};
