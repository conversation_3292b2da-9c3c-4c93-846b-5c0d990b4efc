///
/// JAVEncodingOption.hpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON> @ Margelo
///

#pragma once

#include <fbjni/fbjni.h>
#include "AVEncodingOption.hpp"

namespace margelo::nitro::react_native_audio_recorder_player {

  using namespace facebook;

  /**
   * The C++ JNI bridge between the C++ enum "AVEncodingOption" and the the Kotlin enum "AVEncodingOption".
   */
  struct JAVEncodingOption final: public jni::JavaClass<JAVEncodingOption> {
  public:
    static auto constexpr kJavaDescriptor = "Lcom/margelo/nitro/reactnativeaudiorecorderplayer/AVEncodingOption;";

  public:
    /**
     * Convert this Java/Kotlin-based enum to the C++ enum AVEncodingOption.
     */
    [[maybe_unused]]
    [[nodiscard]]
    AVEncodingOption toCpp() const {
      static const auto clazz = javaClassStatic();
      static const auto fieldOrdinal = clazz->getField<int>("_ordinal");
      int ordinal = this->getFieldValue(fieldOrdinal);
      return static_cast<AVEncodingOption>(ordinal);
    }

  public:
    /**
     * Create a Java/Kotlin-based enum with the given C++ enum's value.
     */
    [[maybe_unused]]
    static jni::alias_ref<JAVEncodingOption> fromCpp(AVEncodingOption value) {
      static const auto clazz = javaClassStatic();
      static const auto fieldLPCM = clazz->getStaticField<JAVEncodingOption>("LPCM");
      static const auto fieldIMA4 = clazz->getStaticField<JAVEncodingOption>("IMA4");
      static const auto fieldAAC = clazz->getStaticField<JAVEncodingOption>("AAC");
      static const auto fieldMAC3 = clazz->getStaticField<JAVEncodingOption>("MAC3");
      static const auto fieldMAC6 = clazz->getStaticField<JAVEncodingOption>("MAC6");
      static const auto fieldULAW = clazz->getStaticField<JAVEncodingOption>("ULAW");
      static const auto fieldALAW = clazz->getStaticField<JAVEncodingOption>("ALAW");
      static const auto fieldMP1 = clazz->getStaticField<JAVEncodingOption>("MP1");
      static const auto fieldMP2 = clazz->getStaticField<JAVEncodingOption>("MP2");
      static const auto fieldMP4 = clazz->getStaticField<JAVEncodingOption>("MP4");
      static const auto fieldALAC = clazz->getStaticField<JAVEncodingOption>("ALAC");
      static const auto fieldAMR = clazz->getStaticField<JAVEncodingOption>("AMR");
      static const auto fieldFLAC = clazz->getStaticField<JAVEncodingOption>("FLAC");
      static const auto fieldOPUS = clazz->getStaticField<JAVEncodingOption>("OPUS");
      
      switch (value) {
        case AVEncodingOption::LPCM:
          return clazz->getStaticFieldValue(fieldLPCM);
        case AVEncodingOption::IMA4:
          return clazz->getStaticFieldValue(fieldIMA4);
        case AVEncodingOption::AAC:
          return clazz->getStaticFieldValue(fieldAAC);
        case AVEncodingOption::MAC3:
          return clazz->getStaticFieldValue(fieldMAC3);
        case AVEncodingOption::MAC6:
          return clazz->getStaticFieldValue(fieldMAC6);
        case AVEncodingOption::ULAW:
          return clazz->getStaticFieldValue(fieldULAW);
        case AVEncodingOption::ALAW:
          return clazz->getStaticFieldValue(fieldALAW);
        case AVEncodingOption::MP1:
          return clazz->getStaticFieldValue(fieldMP1);
        case AVEncodingOption::MP2:
          return clazz->getStaticFieldValue(fieldMP2);
        case AVEncodingOption::MP4:
          return clazz->getStaticFieldValue(fieldMP4);
        case AVEncodingOption::ALAC:
          return clazz->getStaticFieldValue(fieldALAC);
        case AVEncodingOption::AMR:
          return clazz->getStaticFieldValue(fieldAMR);
        case AVEncodingOption::FLAC:
          return clazz->getStaticFieldValue(fieldFLAC);
        case AVEncodingOption::OPUS:
          return clazz->getStaticFieldValue(fieldOPUS);
        default:
          std::string stringValue = std::to_string(static_cast<int>(value));
          throw std::invalid_argument("Invalid enum value (" + stringValue + "!");
      }
    }
  };

} // namespace margelo::nitro::react_native_audio_recorder_player
