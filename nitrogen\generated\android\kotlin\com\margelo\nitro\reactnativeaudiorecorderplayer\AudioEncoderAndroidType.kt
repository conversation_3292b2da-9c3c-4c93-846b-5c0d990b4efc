///
/// AudioEncoderAndroidType.kt
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON> @ Margelo
///

package com.margelo.nitro.reactnativeaudiorecorderplayer

import androidx.annotation.Keep
import com.facebook.proguard.annotations.DoNotStrip

/**
 * Represents the JavaScript enum/union "AudioEncoderAndroidType".
 */
@DoNotStrip
@Keep
enum class AudioEncoderAndroidType {
  DEFAULT,
  AMR_NB,
  AMR_WB,
  AAC,
  HE_AAC,
  AAC_ELD,
  VORBIS;

  @DoNotStrip
  @Keep
  private val _ordinal = ordinal
}
