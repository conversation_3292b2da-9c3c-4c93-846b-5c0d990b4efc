import React, { useState } from "react";
import { StyleSheet } from "react-native";
import { Text, useTheme } from "react-native-paper";
import Background from "../components/Background";
import BackButton from "../components/BackButton";
import Logo from "../components/Logo";
import Header from "../components/Header";
import TextInput from "../components/TextInput";
import PasswordTextInput from "../components/PasswordTextInput";
import Button from "../components/Button";
import { useForm, Controller } from "react-hook-form";
import { ErrorMessage } from "@hookform/error-message";
import { joiResolver } from "@hookform/resolvers/joi";
import "fast-text-encoding";
import Jo<PERSON> from "joi";
import { validatorBase } from "../utils/validatorBase";
import { getMMKV } from "../services/local-storage";
import { STORE_LOGIN_INFO, UPDATE_PASSWORD } from "../config/keysConfig";
import { useTranslation } from "react-i18next";
import { updatePasswordQuery as updateQueryClient } from "../api/updatePassword";
import { makeDataFeeder } from "../utils";


const dataFeeder = makeDataFeeder();

const ResetPasswordScreen = ({ navigation }) => {
    const mobile = getMMKV(STORE_LOGIN_INFO, "mobile");
    const { t } = useTranslation(["screenResetPassword"]);

    const schema = Joi.object({
        mobile:    validatorBase.userMobile.required,
        password1: validatorBase.userPassword.required,
        password2: validatorBase.userPassword.required,
    });
    const {
        handleSubmit,
        control,
        resetField,
        reset,
        setError,
        formState: { errors },
        //getValues,
    } = useForm({
        resolver: joiResolver(schema),
        //reValidateMode: "onBlur", // https://github.com/orgs/react-hook-form/discussions/10327
        defaultValues: {
            mobile:    mobile,
            password1: "",
            password2: "",
        },
    });

    const commitOnSuccess = () => {
        console.log("Password update successfully.");
        navigation.replace("LoginScreen");
    };
    const commitOnError = (date) => {
        console.log("Password update failed: ", date);
    };

    const submitQuery = updateQueryClient(dataFeeder, "", commitOnSuccess, commitOnError);

    const onSubmit = (data) => {
        if(data.password1 !== data.password2) {
            setError("password2", { type: "manual", message: "两次输入不一致!" }, { shouldFocus: true });
        } else {
            console.log("commit");
            dataFeeder(data);
            submitQuery.mutate();
        }
    };

    const theme = useTheme();

    const styles = StyleSheet.create({
        errMsg: {
            fontSize: 16,
            color: theme.colors.error,
        }
    });

    return (
        <Background>
            {/*<BackButton goBack={navigation.goBack} />*/}
            <Logo />
            <Header>{t("screenResetPassword:header")}</Header>

            <Controller
                control={control}
                render={({ field: { onChange, onBlur, value } }) => (
                    <TextInput
                        label={t("screenResetPassword:mobileInputTextLabel")}
                        returnKeyType="next"
                        value={value}
                        onChangeText={onChange}
                        error={errors.mobile}
                        errorText={errors.mobile}
                        autoCapitalize="none"
                        autoComplete="off"
                        textContentType="none"
                        keyboardType="numeric"
                        disabled={true}
                        onClearText={()=>resetField("mobile", { defaultValue: "" })}
                    />
                )}
                name="mobile"
            />
            <Controller
                control={control}
                render={({ field: { onChange, onBlur, value } }) => (
                    <PasswordTextInput
                        label={t("screenResetPassword:password1InputTextLabel")}
                        returnKeyType="done"
                        value={value}
                        onChangeText={onChange}
                        error={errors.password1}
                        errorText={errors?.password1?.message || ""}
                    />
                )}
                name="password1"
            />
            <Controller
                control={control}
                render={({ field: { onChange, onBlur, value } }) => (
                    <PasswordTextInput
                        label={t("screenResetPassword:password1InputTextLabel")}
                        returnKeyType="done"
                        value={value}
                        onChangeText={onChange}
                        error={errors.password2}
                        errorText={errors?.password2?.message || ""}
                    />
                )}
                name="password2"
            />







            <Button mode="contained" onPress={handleSubmit(onSubmit)} style={{ marginTop: 16 }}>
                {t("screenResetPassword:sendButton")}
            </Button>

        </Background>
    );
};



export default ResetPasswordScreen;
