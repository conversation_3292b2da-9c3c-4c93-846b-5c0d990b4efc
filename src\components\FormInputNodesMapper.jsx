import React from "react";
import "react-native";
import { View } from "react-native";
import { DEFAULT_COORDS_PICK_LIMIT, DEFAULT_IMAGE_HEIGHT, DEFAULT_IMAGE_QUALIRY, DEFAULT_IMAGE_URI, DEFAULT_IMAGES_PICK } from "../config";
import ControlledCheckboxInputWithQuery from "./ControlledCheckboxInputWithQuery";
import ControlledDateTimePicker from "./ControlledDateTimePicker";
import ControlledImageInput from "./ControlledImageInput";
import ControlledLocationInput from "./ControlledLocationInput";
import ControlledLocationsInput from "./ControlledLocationsInput";
import ControlledRadioInputWithQuery from "./ControlledRadioInputWithQuery";
import ControlledRangeInput from "./ControlledRangeInput";
import ControlledTextInput from "./ControlledTextInput";

const checkFalse = (val) => {
    if(val) {
        return parseFloat(val) ? false : true;
    } else {
        return true;
    }
};

// 暂时不包含复选框ControlledCheckboxInputWithQuery
export const FormInputNodesMapper = ({ fieldsConfig, subCversionRef, control, getStore, setStore, resetField, errors, setSnackBarMessage, setShowSnackbar, editable, getValues, rowStyle={}, parentKey="" }) => {
    return (
        <>
            {fieldsConfig.map((item, i) => {
                if(item.filterIf && item.filterIf(item.name)) {
                    return undefined;
                } else {
                    if (item.type === "PLAIN") {
                        return (
                            (item.hideWhenFalse && getValues && checkFalse(getValues(item.name))) ?
                                <View key={parentKey ? `${parentKey}-${i}` : `${i}`} />
                                :
                                <ControlledTextInput
                                    key={parentKey ? `${parentKey}-${i}` : `${i}`}
                                    name={item.name}
                                    rowLabel={item.label + (item.unit ? `(${item.unit})` : "")}
                                    control={control}
                                    placeholder={item.placeholder || ""}
                                    onChangeText={(text) => {
                                        subCversionRef.current++;
                                        setStore && setStore("subCversion", subCversionRef.current);
                                        setStore && setStore(item.name, text);
                                        item.cb && item.cb();
                                    }}
                                    onClearText={() => {
                                        subCversionRef.current++;
                                        setStore && setStore("subCversion", subCversionRef.current);
                                        setStore && setStore(item.name, "");
                                        resetField(item.name, { defaultValue: "" });
                                    }}
                                    setSnackbarMessage={setSnackBarMessage}
                                    setDisplaySnackbar={setShowSnackbar}
                                    editable={item.editable && editable}
                                    multiline={item.multiline ? true : false}
                                    mode="flat"
                                    formError={errors}
                                    rowStyle={rowStyle}
                                    //disabled={formDisabledGlobal}
                                    //required={false} // item.props中配置required为true显示必填星号
                                    toolTip={item.toolTip}
                                    {...item.props}
                                />
                        );
                    } else if (item.type === "RADIO") {
                        return (
                            <ControlledRadioInputWithQuery
                                key={parentKey ? `${parentKey}-${i}` : `${i}`}
                                name={item.name}
                                rowLabel={item.label + (item.unit ? `(${item.unit})` : "")}
                                control={control}
                                placeholder={"请拉选" + item.label + "数据"}
                                onDialogConfirm={(obj) => {
                                    subCversionRef.current++;
                                    setStore && setStore("subCversion", subCversionRef.current);
                                    setStore && setStore(item.name, obj);
                                    item.setSelectorState(obj);
                                    item.cb && item.cb();
                                }}
                                okBtnLabel={"确定"}
                                cancelBtnLabel={"取消"}
                                formError={errors}
                                dataProvider={item.dataProvider}
                                setSnackbarMessage={setSnackBarMessage}
                                setDisplaySnackbar={setShowSnackbar}
                                setFetchFailMessage={setSnackBarMessage}
                                setDisplayFailBar={setShowSnackbar}
                                dialogState={item.selectorState}
                                setDialogState={item.setSelectorState}
                                defaultNullOptionsTip={"未找到数据, 请联系管理员!"}
                                rowStyle={rowStyle}
                                //disabled={editable}
                                editable={item.editable && editable}
                                //required={false}
                                toolTip={item.toolTip}
                                {...item.props}
                            />
                        );
                    } else if (item.type === "DATE") {
                        return (
                            <ControlledDateTimePicker
                                key={parentKey ? `${parentKey}-${i}` : `${i}`}
                                name={item.name}
                                mode={"date"}
                                rowLabel={item.label + (item.unit ? `(${item.unit})` : "")}
                                placeholder={"请拉选日期"}
                                dialogState={item.selectorState}
                                onDialogConfirm={(obj) => {
                                    subCversionRef.current++;
                                    setStore && setStore("subCversion", subCversionRef.current);
                                    setStore && setStore(item.name, obj);
                                    item.setSelectorState(obj);
                                    item.cb && item.cb();
                                }}
                                formError={errors}
                                control={control}
                                rowStyle={rowStyle}
                                //disabled={formDisabledGlobal}
                                setSnackbarMessage={setSnackBarMessage}
                                setDisplaySnackbar={setShowSnackbar}
                                editable={item.editable && editable}
                                //required={false}
                                toolTip={item.toolTip}
                                {...item.props}
                            />
                        );
                    } else if (item.type === "RANGE") {
                        return (
                            <ControlledRangeInput
                                key={parentKey ? `${parentKey}-${i}` : `${i}`}
                                name={item.name}
                                rowLabel={item.label + (item.unit ? `(${item.unit})` : "")}
                                control={control}
                                placeholder={"请点击输入区间数据"}
                                onDialogConfirm={(obj) => {
                                    subCversionRef.current++;
                                    setStore && setStore("subCversion", subCversionRef.current);
                                    setStore && setStore(item.name, obj);
                                    item.setSelectorState(obj);
                                    item.cb && item.cb();
                                }}
                                okBtnLabel={"确定"}
                                cancelBtnLabel={"取消"}
                                formError={errors}
                                dialogState={item.selectorState}
                                setDialogState={item.setSelectorState}
                                rowStyle={rowStyle}
                                //disabled={formDisabledGlobal}
                                setSnackbarMessage={setSnackBarMessage}
                                setDisplaySnackbar={setShowSnackbar}
                                editable={item.editable && editable}
                                //required={false}
                                toolTip={item.toolTip}
                                {...item.props}
                            />
                        );
                    } else if (item.type === "CHECK") {
                    // CHECK类型未测试
                        return (
                            <ControlledCheckboxInputWithQuery
                                key={parentKey ? `${parentKey}-${i}` : `${i}`}
                                name={item.name}
                                rowLabel={item.label + (item.unit ? `(${item.unit})` : "")}
                                control={control}
                                placeholder={"请拉选" + item.label + "数据"}
                                onDialogConfirm={(obj) => {
                                    subCversionRef.current++;
                                    setStore && setStore("subCversion", subCversionRef.current);
                                    setStore && setStore(item.name, obj);
                                    item.setSelectorState(obj);
                                    item.cb && item.cb();
                                }}
                                okBtnLabel={"确定"}
                                cancelBtnLabel={"取消"}
                                formError={errors}
                                dataProvider={item.dataProvider}
                                setSnackbarMessage={setSnackBarMessage}
                                setDisplaySnackbar={setShowSnackbar}
                                setFetchFailMessage={setSnackBarMessage}
                                setDisplayFailBar={setShowSnackbar}
                                dialogState={item.selectorState}
                                setDialogState={item.setSelectorState}
                                defaultNullOptionsTip={"未找到数据, 请联系管理员!"}
                                rowStyle={rowStyle}
                                //disabled={editable}
                                editable={item.editable && editable}
                                //required={false}
                                toolTip={item.toolTip}
                                {...item.props}
                            />
                        );
                    } else if (item.type === "IMAGE") {
                        return (
                            (item.hideWhenFalse && getValues && checkFalse(getValues(item.name))) ?
                                <View key={parentKey ? `${parentKey}-${i}` : `${i}`} />
                                :
                                <ControlledImageInput
                                    key={parentKey ? `${parentKey}-${i}` : `${i}`}
                                    name={item.name}
                                    rowLabel={item.label + (item.unit ? `(${item.unit})` : "")}
                                    control={control}
                                    placeholder={item.placeholder || DEFAULT_IMAGE_URI}
                                    onPickImageCB={(path) => {
                                        const maxPickNum = item?.pickNum || DEFAULT_IMAGES_PICK;
                                        const pickedImages = getStore(item.name);

                                        if(pickedImages.length < maxPickNum){
                                            setStore && setStore(item.name, [...getStore(item.name), path]);

                                            // set fctrl to "a", append
                                            const fctrl = getStore("fctrl");
                                            //console.log("On picking new image, fctrl init ", fctrl);
                                            if(fctrl[item.name] === "") {
                                                fctrl[item.name] = "a";
                                                setStore && setStore("fctrl", fctrl);
                                                resetField("fctrl", { defaultValue: fctrl });
                                            }
                                            //console.log("On picking new image, fctrl set to ", fctrl);

                                            subCversionRef.current++;
                                            setStore && setStore("subCversion", subCversionRef.current);

                                            item.cb && item.cb();
                                        } else {
                                            console.warn(`${item.name}: 最多选择${maxPickNum}张图片.`);
                                        }
                                    }}
                                    onDeleteImageCB={() => {
                                        setStore && setStore(item.name, []);                     // reset zustand mmkv
                                        resetField(item.name, { defaultValue: [] }); // reset react-hook-form

                                        // set fctrl to "c", clear
                                        const fctrl = getStore("fctrl");
                                        //console.log("On clearing new image, fctrl init ", fctrl);
                                        if(fctrl[item.name].toLowerCase() !== "c") {
                                            fctrl[item.name] = "c";
                                            setStore && setStore("fctrl", fctrl);
                                            resetField("fctrl", { defaultValue: fctrl });
                                        }
                                        //console.log("On clearing new image, fctrl set to ", fctrl);

                                        subCversionRef.current++;
                                        setStore && setStore("subCversion", subCversionRef.current);
                                    }}
                                    pickNum={item?.pickNum || DEFAULT_IMAGES_PICK}
                                    crop={("crop" in item) ? item.crop : false}
                                    compress={item?.compress || DEFAULT_IMAGE_QUALIRY}
                                    width={item?.width || null}
                                    height={item?.height || DEFAULT_IMAGE_HEIGHT}
                                    setSnackbarMessage={setSnackBarMessage}
                                    setDisplaySnackbar={setShowSnackbar}
                                    editable={item.editable && editable}
                                    formError={errors}
                                    //disabled={formDisabledGlobal}
                                    //required={false} // item.props中配置required为true显示必填星号
                                    toolTip={item.toolTip}
                                    {...item.props}
                                />
                        );
                    } else if (item.type === "LOCATION") {
                        // LOCATION类型未测试
                        return (
                            <ControlledLocationInput
                                key={parentKey ? `${parentKey}-${i}` : `${i}`}
                                name={item.name}
                                rowLabel={item.label + (item.unit ? `(${item.unit})` : "")}
                                control={control}
                                placeholder={item.placeholder || ""}
                                onChangeText={(text) => {
                                    subCversionRef.current++;
                                    setStore && setStore("subCversion", subCversionRef.current);
                                    setStore && setStore(item.name, text);
                                    item.cb && item.cb();
                                }}
                                onClearText={() => {
                                    subCversionRef.current++;
                                    setStore && setStore("subCversion", subCversionRef.current);
                                    setStore && setStore(item.name, "");
                                    resetField(item.name, { defaultValue: "" });
                                }}
                                setSnackbarMessage={setSnackBarMessage}
                                setDisplaySnackbar={setShowSnackbar}
                                editable={item.editable && editable}
                                multiline={item.multiline ? true : false}
                                mode="flat"
                                formError={errors}
                                rowStyle={rowStyle}
                                //disabled={formDisabledGlobal}
                                //required={false} // item.props中配置required为true显示必填星号
                                toolTip={item.toolTip}
                                {...item.props}
                            />
                        );
                    } else if (item.type === "LOCATIONS") {
                        return (
                            (item.hideWhenFalse && getValues && checkFalse(getValues(item.name))) ?
                                <View key={parentKey ? `${parentKey}-${i}` : `${i}`} />
                                :
                                <ControlledLocationsInput
                                    key={parentKey ? `${parentKey}-${i}` : `${i}`}
                                    name={item.name}
                                    rowLabel={item.label + (item.unit ? `(${item.unit})` : "")}
                                    control={control}
                                    placeholder={item.placeholder || ""}
                                    locationStates={item.locationStates}
                                    setLocationStates={item.setLocationStates}
                                    onPickCoordsCB={ coordsObj => { // 获取一个坐标的回调, coordsObj := {name, desc, x, y}
                                        // 在此确定是否将新坐标添加进去, 是因为在底层添加必需传递更多参数, 是的耦合度增加
                                        const pickLimit = item?.pickLimit || DEFAULT_COORDS_PICK_LIMIT;
                                        const pickedCoords = item.locationStates;
                                        if(pickedCoords.length < pickLimit) {
                                            const newCoords = [...item.locationStates, coordsObj];

                                            item.setLocationStates(newCoords);                  // react-hook-form修改内部状态
                                            setStore && setStore(item.name, newCoords);         // mmkv保存当前输入
                                            resetField(item.name, { defaultValue: newCoords }); // react-hook-form修改内部状态

                                            subCversionRef.current++;
                                            setStore && setStore("subCversion", subCversionRef.current);
                                            item.cb && item.cb();
                                        } else {
                                            console.warn(`${item.name}: 最多获取${pickLimit}个坐标.`);
                                        }
                                    }}
                                    onDeleteLocationCB={(coordsObj) => { // 删除坐标的回调, coordsObj := {name, desc, x, y}
                                        // 一次只能删除一个坐标, 由于涉及到版本号改变, 在下一层不能完成所有逻辑, 考虑弹出框确认提示
                                        const leftCoords = item.locationStates.filter(item => item.name !== coordsObj.name || item.desc !== coordsObj.desc);

                                        item.setLocationStates(leftCoords);                  // reset picker state
                                        setStore && setStore(item.name, leftCoords);         // reset mmkv
                                        resetField(item.name, { defaultValue: leftCoords }); // reset react-hook-form

                                        subCversionRef.current++;
                                        setStore && setStore("subCversion", subCversionRef.current);
                                    }}
                                    onClearLocationCB={() => {
                                        item.setLocationStates([]);                  // reset picker state
                                        setStore && setStore(item.name, []);         // reset mmkv
                                        resetField(item.name, { defaultValue: [] }); // reset react-hook-form

                                        subCversionRef.current++;
                                        setStore && setStore("subCversion", subCversionRef.current);
                                    }}
                                    pickerNameLabel={item?.pickName || "名称"}
                                    pickerDescLabel={item?.pickDesc || "描述"}
                                    pickerGpsLabel={item?.pickGps   || "经纬度"}
                                    pickLimit={item?.pickLimit || DEFAULT_COORDS_PICK_LIMIT}
                                    setSnackbarMessage={setSnackBarMessage}
                                    setDisplaySnackbar={setShowSnackbar}
                                    editable={item.editable && editable}
                                    formError={errors}
                                    rowStyle={rowStyle}
                                    //disabled={formDisabledGlobal}
                                    //required={false} // item.props中配置required为true显示必填星号
                                    toolTip={item.toolTip}
                                    {...item.props}
                                />
                        );
                    }
                    else {
                    //console.error("Invalid form input node type: ~s", item.type);

                    }
                }
            })}
        </>
    );
};
