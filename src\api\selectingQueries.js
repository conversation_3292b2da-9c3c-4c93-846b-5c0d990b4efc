import * as keys from "../config/keysConfig";
import { makeSelectingClient as httpClient } from "./makeSelectingClient";

export const queryGetOrgPage           = httpClient(keys.ORG_SELECT);             // 客户更新屏幕, 拉取页面数据
export const queryGetDepartmentPage    = httpClient(keys.ORG_DEPARTMENT_SELECT);  // 部门更新屏幕, 拉取页面数据
export const queryGetDepartmentMembers = httpClient(keys.DEPT_MEMBERS_GET);       // 部门更新屏幕, 控件, 拉取部门成员
export const queryGetUserPage          = httpClient(keys.ORG_USER_SELECT);        // 用户更新屏幕, 拉取页面数据
export const queryGetPositionPage      = httpClient(keys.ORG_POSITION_SELECT);    // 岗位更新屏幕, 拉取页面数据
export const queryGetClientPage        = httpClient(keys.ORG_CLIENT_SELECT);      // 客户更新屏幕, 拉取页面数据
export const queryGetRolePage          = httpClient(keys.ORG_ROLE_SELECT);        // 客户更新屏幕, 拉取页面数据
export const queryGetProjectBasePage   = httpClient(keys.ORG_PROJ_BASE_SELECT);   // 项目库屏幕, 拉取页面数据

export const queryGetProjectWBPage     = httpClient(keys.WATER_BALANCE_SELECT);   // 水平衡项目屏幕, 拉取页面数据
export const queryGetProjectZCPage     = httpClient(keys.ZERO_CARBON_SELECT);     // 零碳诊断项目屏幕, 拉取页面数据
