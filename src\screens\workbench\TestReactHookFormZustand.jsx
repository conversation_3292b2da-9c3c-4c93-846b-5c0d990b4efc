import React, { useEffect, useState } from "react";
import { DataTable, Divider, Appbar, useTheme, Text, TextInput } from "react-native-paper";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { StyleSheet, View } from "react-native";
import ScreenWrapper from "../ScreenWrapper";
import BottomBarButton from "../../components/BottomBarButton";

import { useTestReactHookFormState } from "../../hooks/testReactHookFormState";
import { SubmitHandler, useForm, Controller, useController, Control } from "react-hook-form";
import { userLoginState } from "../../hooks/loginStoredState";
import LocalStorage from "../../services/local-storage";
import { useShallow } from "zustand/shallow";


const MMKV_KEY = "TEST_REACT_HOOK_FORM_ZUSTAND";
let render = 0;

/**
 * 测试react-hook-form的屏幕!
 * @param {Object} args
 * @returns
 */
const TestReactHookForm = ({ navigation }) => {
    const { bottom, left, right } = useSafeAreaInsets();
    const theme = useTheme();
    const height = theme.isV3 ? 80 : 56;

    const [saveButtonLoading] = useState(false);
    const [saveButtonDisabled] = useState(false);
    const [saveButtonIcon] = useState("content-save-all-outline");
    //console.log("Store:", LocalStorage.getString(MMKV_KEY));
    //console.log("Store Login Info:", JSON.parse(LocalStorage.getString("login-info")).state);
    //console.log("Store All Keys", LocalStorage.getAllKeys()); // -> ["TEST_REACT_HOOK_FORM_MMKV", ...]
    //console.log("Store DELETE NOT Existed:", LocalStorage.delete("asgfdgd")); // -> undefined

    render++;

    const [setName, setAge] = useTestReactHookFormState(useShallow((state) => [state.setName, state.setAge]));

    const {
        handleSubmit,
        control,
        formState: { errors },
    } = useForm({
        //defaultValues: useTestReactHookFormState(useShallow(state) => {
        //    return {name: state.name, age: state.age};
        //})),
        defaultValues: {name: "", age: 0},
    });

    useEffect(()=>{
    }, []);

    return (
        <>
            <ScreenWrapper contentContainerStyle={styles.container}>
                <Text>Render: {render}</Text>

                <View style={styles.formEntry}>
                    <Controller
                        control={control}
                        rules={{
                            required: true,
                        }}
                        render={({ field }) => (
                            <TextInput
                                mode="outlined"
                                label="Full Name"
                                placeholder="Enter Full Name"
                                onBlur={field.onBlur}
                                onChangeText={text => {
                                    console.log("onChangeText:", text);
                                    //setName(text); // setName is a hook and will trigger rendering
                                    LocalStorage.set(MMKV_KEY, JSON.stringify({ name: text }));
                                    field.onChange(text);
                                }}
                                value={field.value}
                                onChange={(event) => {
                                    // onChange will be called before onChangeText
                                    console.log("onChange: ", event.nativeEvent.text);
                                    //setName(event.nativeEvent.text); // setName is a hook and will trigger rendering
                                    //LocalStorage.set(MMKV_KEY, JSON.stringify({ name: event.nativeEvent.text }));
                                    //field.onChange
                                }}
                            />
                        )}
                        name="name"

                    />
                    {errors.fullName && (
                        <Text style={{ margin: 8, marginLeft: 16, color: "red" }}>
                                This is a required field.
                        </Text>
                    )}
                </View>

            </ScreenWrapper>

            <Appbar
                style={[
                    styles.bottom,
                    {
                        height: height + bottom,
                    },
                    theme.isV3 && {
                        backgroundColor: theme.colors.elevation.level2,
                    },
                ]}
                safeAreaInsets={{ bottom, left, right }}
                theme={{ mode: "adaptive" }}
            //mode='center-aligned'
            >

                <View style={styles.bottomBarContainer}>
                    <Divider />
                    <View style={styles.bottomBarComponents}>
                        <BottomBarButton
                            label={"取消"}
                            disabled={false}
                            onPress={()=>{}}
                        />
                        <BottomBarButton
                            label={"保存"}
                            loading={saveButtonLoading}
                            disabled={saveButtonDisabled}
                            icon={saveButtonIcon}
                            onPress={()=>{}}
                        />
                    </View>
                </View>
            </Appbar>
        </>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    firstColumn: {
        flex: 2,
        //borderWidth: 1,
        //height: 40,
        content: {
            fontSize: 18,
        }
    },
    secondColumn: {
        flex: 3,
        // /borderWidth: 1,
        // height: 40,
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        flex: 1,
        marginHorizontal: 6
    },
    formEntry: {
        margin: 8,
    },
});

export default TestReactHookForm;
