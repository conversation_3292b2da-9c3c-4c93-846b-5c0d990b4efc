///
/// ReactNativeAudioRecorderPlayer-Swift-Cxx-Bridge.hpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON> @ Margelo
///

#pragma once

// Forward declarations of C++ defined types
// Forward declaration of `AVEncoderAudioQualityIOSType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AVEncoderAudioQualityIOSType; }
// Forward declaration of `AVEncodingOption` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AVEncodingOption; }
// Forward declaration of `AVLinearPCMBitDepthKeyIOSType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AVLinearPCMBitDepthKeyIOSType; }
// Forward declaration of `AVModeIOSOption` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AVModeIOSOption; }
// Forward declaration of `AudioEncoderAndroidType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AudioEncoderAndroidType; }
// Forward declaration of `AudioQualityType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AudioQualityType; }
// Forward declaration of `AudioSet` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { struct AudioSet; }
// Forward declaration of `AudioSourceAndroidType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AudioSourceAndroidType; }
// Forward declaration of `HybridAudioRecorderPlayerSpec` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { class HybridAudioRecorderPlayerSpec; }
// Forward declaration of `OutputFormatAndroidType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class OutputFormatAndroidType; }
// Forward declaration of `PlayBackType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { struct PlayBackType; }
// Forward declaration of `RecordBackType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { struct RecordBackType; }

// Forward declarations of Swift defined types
// Forward declaration of `HybridAudioRecorderPlayerSpec_cxx` to properly resolve imports.
namespace ReactNativeAudioRecorderPlayer { class HybridAudioRecorderPlayerSpec_cxx; }

// Include C++ defined types
#include "AVEncoderAudioQualityIOSType.hpp"
#include "AVEncodingOption.hpp"
#include "AVLinearPCMBitDepthKeyIOSType.hpp"
#include "AVModeIOSOption.hpp"
#include "AudioEncoderAndroidType.hpp"
#include "AudioQualityType.hpp"
#include "AudioSet.hpp"
#include "AudioSourceAndroidType.hpp"
#include "HybridAudioRecorderPlayerSpec.hpp"
#include "OutputFormatAndroidType.hpp"
#include "PlayBackType.hpp"
#include "RecordBackType.hpp"
#include <NitroModules/Promise.hpp>
#include <NitroModules/PromiseHolder.hpp>
#include <NitroModules/Result.hpp>
#include <exception>
#include <functional>
#include <memory>
#include <optional>
#include <string>
#include <unordered_map>

/**
 * Contains specialized versions of C++ templated types so they can be accessed from Swift,
 * as well as helper functions to interact with those C++ types from Swift.
 */
namespace margelo::nitro::react_native_audio_recorder_player::bridge::swift {

  // pragma MARK: std::shared_ptr<Promise<std::string>>
  /**
   * Specialized version of `std::shared_ptr<Promise<std::string>>`.
   */
  using std__shared_ptr_Promise_std__string__ = std::shared_ptr<Promise<std::string>>;
  inline std::shared_ptr<Promise<std::string>> create_std__shared_ptr_Promise_std__string__() {
    return Promise<std::string>::create();
  }
  inline PromiseHolder<std::string> wrap_std__shared_ptr_Promise_std__string__(std::shared_ptr<Promise<std::string>> promise) {
    return PromiseHolder<std::string>(std::move(promise));
  }
  
  // pragma MARK: std::function<void(const std::string& /* result */)>
  /**
   * Specialized version of `std::function<void(const std::string&)>`.
   */
  using Func_void_std__string = std::function<void(const std::string& /* result */)>;
  /**
   * Wrapper class for a `std::function<void(const std::string& / * result * /)>`, this can be used from Swift.
   */
  class Func_void_std__string_Wrapper final {
  public:
    explicit Func_void_std__string_Wrapper(std::function<void(const std::string& /* result */)>&& func): _function(std::make_shared<std::function<void(const std::string& /* result */)>>(std::move(func))) {}
    inline void call(std::string result) const {
      _function->operator()(result);
    }
  private:
    std::shared_ptr<std::function<void(const std::string& /* result */)>> _function;
  };
  Func_void_std__string create_Func_void_std__string(void* _Nonnull swiftClosureWrapper);
  inline Func_void_std__string_Wrapper wrap_Func_void_std__string(Func_void_std__string value) {
    return Func_void_std__string_Wrapper(std::move(value));
  }
  
  // pragma MARK: std::function<void(const std::exception_ptr& /* error */)>
  /**
   * Specialized version of `std::function<void(const std::exception_ptr&)>`.
   */
  using Func_void_std__exception_ptr = std::function<void(const std::exception_ptr& /* error */)>;
  /**
   * Wrapper class for a `std::function<void(const std::exception_ptr& / * error * /)>`, this can be used from Swift.
   */
  class Func_void_std__exception_ptr_Wrapper final {
  public:
    explicit Func_void_std__exception_ptr_Wrapper(std::function<void(const std::exception_ptr& /* error */)>&& func): _function(std::make_shared<std::function<void(const std::exception_ptr& /* error */)>>(std::move(func))) {}
    inline void call(std::exception_ptr error) const {
      _function->operator()(error);
    }
  private:
    std::shared_ptr<std::function<void(const std::exception_ptr& /* error */)>> _function;
  };
  Func_void_std__exception_ptr create_Func_void_std__exception_ptr(void* _Nonnull swiftClosureWrapper);
  inline Func_void_std__exception_ptr_Wrapper wrap_Func_void_std__exception_ptr(Func_void_std__exception_ptr value) {
    return Func_void_std__exception_ptr_Wrapper(std::move(value));
  }
  
  // pragma MARK: std::optional<std::string>
  /**
   * Specialized version of `std::optional<std::string>`.
   */
  using std__optional_std__string_ = std::optional<std::string>;
  inline std::optional<std::string> create_std__optional_std__string_(const std::string& value) {
    return std::optional<std::string>(value);
  }
  
  // pragma MARK: std::optional<AudioSourceAndroidType>
  /**
   * Specialized version of `std::optional<AudioSourceAndroidType>`.
   */
  using std__optional_AudioSourceAndroidType_ = std::optional<AudioSourceAndroidType>;
  inline std::optional<AudioSourceAndroidType> create_std__optional_AudioSourceAndroidType_(const AudioSourceAndroidType& value) {
    return std::optional<AudioSourceAndroidType>(value);
  }
  
  // pragma MARK: std::optional<OutputFormatAndroidType>
  /**
   * Specialized version of `std::optional<OutputFormatAndroidType>`.
   */
  using std__optional_OutputFormatAndroidType_ = std::optional<OutputFormatAndroidType>;
  inline std::optional<OutputFormatAndroidType> create_std__optional_OutputFormatAndroidType_(const OutputFormatAndroidType& value) {
    return std::optional<OutputFormatAndroidType>(value);
  }
  
  // pragma MARK: std::optional<AudioEncoderAndroidType>
  /**
   * Specialized version of `std::optional<AudioEncoderAndroidType>`.
   */
  using std__optional_AudioEncoderAndroidType_ = std::optional<AudioEncoderAndroidType>;
  inline std::optional<AudioEncoderAndroidType> create_std__optional_AudioEncoderAndroidType_(const AudioEncoderAndroidType& value) {
    return std::optional<AudioEncoderAndroidType>(value);
  }
  
  // pragma MARK: std::optional<AVEncoderAudioQualityIOSType>
  /**
   * Specialized version of `std::optional<AVEncoderAudioQualityIOSType>`.
   */
  using std__optional_AVEncoderAudioQualityIOSType_ = std::optional<AVEncoderAudioQualityIOSType>;
  inline std::optional<AVEncoderAudioQualityIOSType> create_std__optional_AVEncoderAudioQualityIOSType_(const AVEncoderAudioQualityIOSType& value) {
    return std::optional<AVEncoderAudioQualityIOSType>(value);
  }
  
  // pragma MARK: std::optional<AVModeIOSOption>
  /**
   * Specialized version of `std::optional<AVModeIOSOption>`.
   */
  using std__optional_AVModeIOSOption_ = std::optional<AVModeIOSOption>;
  inline std::optional<AVModeIOSOption> create_std__optional_AVModeIOSOption_(const AVModeIOSOption& value) {
    return std::optional<AVModeIOSOption>(value);
  }
  
  // pragma MARK: std::optional<AVEncodingOption>
  /**
   * Specialized version of `std::optional<AVEncodingOption>`.
   */
  using std__optional_AVEncodingOption_ = std::optional<AVEncodingOption>;
  inline std::optional<AVEncodingOption> create_std__optional_AVEncodingOption_(const AVEncodingOption& value) {
    return std::optional<AVEncodingOption>(value);
  }
  
  // pragma MARK: std::optional<double>
  /**
   * Specialized version of `std::optional<double>`.
   */
  using std__optional_double_ = std::optional<double>;
  inline std::optional<double> create_std__optional_double_(const double& value) {
    return std::optional<double>(value);
  }
  
  // pragma MARK: std::optional<AVLinearPCMBitDepthKeyIOSType>
  /**
   * Specialized version of `std::optional<AVLinearPCMBitDepthKeyIOSType>`.
   */
  using std__optional_AVLinearPCMBitDepthKeyIOSType_ = std::optional<AVLinearPCMBitDepthKeyIOSType>;
  inline std::optional<AVLinearPCMBitDepthKeyIOSType> create_std__optional_AVLinearPCMBitDepthKeyIOSType_(const AVLinearPCMBitDepthKeyIOSType& value) {
    return std::optional<AVLinearPCMBitDepthKeyIOSType>(value);
  }
  
  // pragma MARK: std::optional<bool>
  /**
   * Specialized version of `std::optional<bool>`.
   */
  using std__optional_bool_ = std::optional<bool>;
  inline std::optional<bool> create_std__optional_bool_(const bool& value) {
    return std::optional<bool>(value);
  }
  
  // pragma MARK: std::optional<AudioQualityType>
  /**
   * Specialized version of `std::optional<AudioQualityType>`.
   */
  using std__optional_AudioQualityType_ = std::optional<AudioQualityType>;
  inline std::optional<AudioQualityType> create_std__optional_AudioQualityType_(const AudioQualityType& value) {
    return std::optional<AudioQualityType>(value);
  }
  
  // pragma MARK: std::optional<AudioSet>
  /**
   * Specialized version of `std::optional<AudioSet>`.
   */
  using std__optional_AudioSet_ = std::optional<AudioSet>;
  inline std::optional<AudioSet> create_std__optional_AudioSet_(const AudioSet& value) {
    return std::optional<AudioSet>(value);
  }
  
  // pragma MARK: std::unordered_map<std::string, std::string>
  /**
   * Specialized version of `std::unordered_map<std::string, std::string>`.
   */
  using std__unordered_map_std__string__std__string_ = std::unordered_map<std::string, std::string>;
  inline std::unordered_map<std::string, std::string> create_std__unordered_map_std__string__std__string_(size_t size) {
    std::unordered_map<std::string, std::string> map;
    map.reserve(size);
    return map;
  }
  inline std::vector<std::string> get_std__unordered_map_std__string__std__string__keys(const std__unordered_map_std__string__std__string_& map) {
    std::vector<std::string> keys;
    keys.reserve(map.size());
    for (const auto& entry : map) {
      keys.push_back(entry.first);
    }
    return keys;
  }
  inline void emplace_std__unordered_map_std__string__std__string_(std__unordered_map_std__string__std__string_& map, const std::string& key, const std::string& value) {
    map.emplace(key, value);
  }
  
  // pragma MARK: std::optional<std::unordered_map<std::string, std::string>>
  /**
   * Specialized version of `std::optional<std::unordered_map<std::string, std::string>>`.
   */
  using std__optional_std__unordered_map_std__string__std__string__ = std::optional<std::unordered_map<std::string, std::string>>;
  inline std::optional<std::unordered_map<std::string, std::string>> create_std__optional_std__unordered_map_std__string__std__string__(const std::unordered_map<std::string, std::string>& value) {
    return std::optional<std::unordered_map<std::string, std::string>>(value);
  }
  
  // pragma MARK: std::function<void(const RecordBackType& /* recordingMeta */)>
  /**
   * Specialized version of `std::function<void(const RecordBackType&)>`.
   */
  using Func_void_RecordBackType = std::function<void(const RecordBackType& /* recordingMeta */)>;
  /**
   * Wrapper class for a `std::function<void(const RecordBackType& / * recordingMeta * /)>`, this can be used from Swift.
   */
  class Func_void_RecordBackType_Wrapper final {
  public:
    explicit Func_void_RecordBackType_Wrapper(std::function<void(const RecordBackType& /* recordingMeta */)>&& func): _function(std::make_shared<std::function<void(const RecordBackType& /* recordingMeta */)>>(std::move(func))) {}
    inline void call(RecordBackType recordingMeta) const {
      _function->operator()(recordingMeta);
    }
  private:
    std::shared_ptr<std::function<void(const RecordBackType& /* recordingMeta */)>> _function;
  };
  Func_void_RecordBackType create_Func_void_RecordBackType(void* _Nonnull swiftClosureWrapper);
  inline Func_void_RecordBackType_Wrapper wrap_Func_void_RecordBackType(Func_void_RecordBackType value) {
    return Func_void_RecordBackType_Wrapper(std::move(value));
  }
  
  // pragma MARK: std::function<void(const PlayBackType& /* playbackMeta */)>
  /**
   * Specialized version of `std::function<void(const PlayBackType&)>`.
   */
  using Func_void_PlayBackType = std::function<void(const PlayBackType& /* playbackMeta */)>;
  /**
   * Wrapper class for a `std::function<void(const PlayBackType& / * playbackMeta * /)>`, this can be used from Swift.
   */
  class Func_void_PlayBackType_Wrapper final {
  public:
    explicit Func_void_PlayBackType_Wrapper(std::function<void(const PlayBackType& /* playbackMeta */)>&& func): _function(std::make_shared<std::function<void(const PlayBackType& /* playbackMeta */)>>(std::move(func))) {}
    inline void call(PlayBackType playbackMeta) const {
      _function->operator()(playbackMeta);
    }
  private:
    std::shared_ptr<std::function<void(const PlayBackType& /* playbackMeta */)>> _function;
  };
  Func_void_PlayBackType create_Func_void_PlayBackType(void* _Nonnull swiftClosureWrapper);
  inline Func_void_PlayBackType_Wrapper wrap_Func_void_PlayBackType(Func_void_PlayBackType value) {
    return Func_void_PlayBackType_Wrapper(std::move(value));
  }
  
  // pragma MARK: std::shared_ptr<margelo::nitro::react_native_audio_recorder_player::HybridAudioRecorderPlayerSpec>
  /**
   * Specialized version of `std::shared_ptr<margelo::nitro::react_native_audio_recorder_player::HybridAudioRecorderPlayerSpec>`.
   */
  using std__shared_ptr_margelo__nitro__react_native_audio_recorder_player__HybridAudioRecorderPlayerSpec_ = std::shared_ptr<margelo::nitro::react_native_audio_recorder_player::HybridAudioRecorderPlayerSpec>;
  std::shared_ptr<margelo::nitro::react_native_audio_recorder_player::HybridAudioRecorderPlayerSpec> create_std__shared_ptr_margelo__nitro__react_native_audio_recorder_player__HybridAudioRecorderPlayerSpec_(void* _Nonnull swiftUnsafePointer);
  void* _Nonnull get_std__shared_ptr_margelo__nitro__react_native_audio_recorder_player__HybridAudioRecorderPlayerSpec_(std__shared_ptr_margelo__nitro__react_native_audio_recorder_player__HybridAudioRecorderPlayerSpec_ cppType);
  
  // pragma MARK: std::weak_ptr<margelo::nitro::react_native_audio_recorder_player::HybridAudioRecorderPlayerSpec>
  using std__weak_ptr_margelo__nitro__react_native_audio_recorder_player__HybridAudioRecorderPlayerSpec_ = std::weak_ptr<margelo::nitro::react_native_audio_recorder_player::HybridAudioRecorderPlayerSpec>;
  inline std__weak_ptr_margelo__nitro__react_native_audio_recorder_player__HybridAudioRecorderPlayerSpec_ weakify_std__shared_ptr_margelo__nitro__react_native_audio_recorder_player__HybridAudioRecorderPlayerSpec_(const std::shared_ptr<margelo::nitro::react_native_audio_recorder_player::HybridAudioRecorderPlayerSpec>& strong) { return strong; }
  
  // pragma MARK: Result<std::shared_ptr<Promise<std::string>>>
  using Result_std__shared_ptr_Promise_std__string___ = Result<std::shared_ptr<Promise<std::string>>>;
  inline Result_std__shared_ptr_Promise_std__string___ create_Result_std__shared_ptr_Promise_std__string___(const std::shared_ptr<Promise<std::string>>& value) {
    return Result<std::shared_ptr<Promise<std::string>>>::withValue(value);
  }
  inline Result_std__shared_ptr_Promise_std__string___ create_Result_std__shared_ptr_Promise_std__string___(const std::exception_ptr& error) {
    return Result<std::shared_ptr<Promise<std::string>>>::withError(error);
  }
  
  // pragma MARK: Result<void>
  using Result_void_ = Result<void>;
  inline Result_void_ create_Result_void_() {
    return Result<void>::withValue();
  }
  inline Result_void_ create_Result_void_(const std::exception_ptr& error) {
    return Result<void>::withError(error);
  }
  
  // pragma MARK: Result<std::string>
  using Result_std__string_ = Result<std::string>;
  inline Result_std__string_ create_Result_std__string_(const std::string& value) {
    return Result<std::string>::withValue(value);
  }
  inline Result_std__string_ create_Result_std__string_(const std::exception_ptr& error) {
    return Result<std::string>::withError(error);
  }

} // namespace margelo::nitro::react_native_audio_recorder_player::bridge::swift
