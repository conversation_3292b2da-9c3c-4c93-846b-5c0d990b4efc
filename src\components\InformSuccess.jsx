import React, {useState} from "react";
import {  StyleSheet } from "react-native";
import { Portal, Dialog, Button } from "react-native-paper";
import { DialogTextComponent } from "./DialogTextComponent";

// https://callstack.github.io/react-native-paper/docs/components/Modal

export const InformSuccess = ({ title, message, callback }) => {
    const [visible, setVisible] = useState(true);
    return (
        <Portal>
            <Dialog
                onDismiss={() => {
                    setVisible(false);
                    callback?.();
                }}
                visible={visible}
                dismissable={false}>
                <Dialog.Title>{title}</Dialog.Title>
                <Dialog.Content>
                    <DialogTextComponent>{message}</DialogTextComponent>
                </Dialog.Content>
                <Dialog.Actions>
                    <Button onPress={()=>setVisible(false)}>确定</Button>
                </Dialog.Actions>
            </Dialog>
        </Portal>
    );
};

const styles = StyleSheet.create({
    backgroundColor: "white",
    padding: 20
});
