import * as keys from "../config/keysConfig";
import { makeDeletingClient as httpClient } from "./makeDeletingClient";


export const makeRequestDeleteOrg         = httpClient(keys.ORG_DELETE);
export const makeRequestDeleteDepartment  = httpClient(keys.ORG_DEPARTMENT_DELETE);
export const makeRequestDeleteUser        = httpClient(keys.ORG_USER_DELETE);
export const makeRequestDeletePosition    = httpClient(keys.ORG_POSITION_DELETE);
export const makeRequestDeleteClient      = httpClient(keys.ORG_CLIENT_DELETE);
export const makeRequestDeleteRole        = httpClient(keys.ORG_ROLE_DELETE);
export const makeRequestDeleteProjectBase = httpClient(keys.ORG_PROJ_BASE_DELETE);
export const makeRequestDeleteProjectWB   = httpClient(keys.WATER_BALANCE_DELETE);
export const makeRequestDeleteProjectZC   = httpClient(keys.ZERO_CARBON_DELETE);
