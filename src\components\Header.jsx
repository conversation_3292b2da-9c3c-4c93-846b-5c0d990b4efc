import React from "react";
import { StyleSheet } from "react-native";
import { Text, useTheme } from "react-native-paper";

const Header = (props) => {
    const theme = useTheme();
    const styles = StyleSheet.create({
        header: {
            fontSize: 21,
            color: theme.colors.primary,
            fontWeight: "bold",
            paddingVertical: 12,
        },
    });

    return <Text style={styles.header} {...props} />;
};

export default Header;
