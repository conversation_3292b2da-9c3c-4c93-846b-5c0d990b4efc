///
/// ReactNativeAudioRecorderPlayerOnLoad.kt
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc Rousavy @ Margelo
///

package com.margelo.nitro.reactnativeaudiorecorderplayer

import android.util.Log

internal class ReactNativeAudioRecorderPlayerOnLoad {
  companion object {
    private const val TAG = "ReactNativeAudioRecorderPlayerOnLoad"
    private var didLoad = false
    /**
     * Initializes the native part of "ReactNativeAudioRecorderPlayer".
     * This method is idempotent and can be called more than once.
     */
    @JvmStatic
    fun initializeNative() {
      if (didLoad) return
      try {
        Log.i(TAG, "Loading ReactNativeAudioRecorderPlayer C++ library...")
        System.loadLibrary("ReactNativeAudioRecorderPlayer")
        Log.i(TAG, "Successfully loaded ReactNativeAudioRecorderPlayer C++ library!")
        didLoad = true
      } catch (e: Error) {
        Log.e(TAG, "Failed to load ReactNativeAudioRecorderPlayer C++ library! Is it properly installed and linked? " +
                    "Is the name correct? (see `CMakeLists.txt`, at `add_library(...)`)", e)
        throw e
      }
    }
  }
}
