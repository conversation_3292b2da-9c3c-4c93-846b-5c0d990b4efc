import * as React from "react";

import { Paragraph, Subheading, Text, useTheme } from "react-native-paper";

/**
 * https://github.com/callstack/react-native-paper/tree/main/example/src/Examples/Dialogs/DialogTextComponent.tsx
 * @param {object} arg
 * @param {boolean} arg.isSubheading
 * @returns
 */
export const DialogTextComponent = ({ isSubheading = false, ...props }) => {
    const theme = useTheme();

    if (theme.isV3) {
        return (
            <Text
                variant={isSubheading ? "bodyLarge" : "bodyMedium"}
                style={{ color: theme.colors.onSurfaceVariant }}
                {...props}
            />
        );
    } else if (isSubheading) {
        return <Subheading {...props} />;
    }
    return <Paragraph {...props} />;
};
