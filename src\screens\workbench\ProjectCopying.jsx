import { joi<PERSON><PERSON><PERSON>ver } from "@hookform/resolvers/joi";
import "fast-text-encoding";
import <PERSON><PERSON> from "joi";
import React, { useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { Keyboard, StyleSheet, TouchableWithoutFeedback, View } from "react-native";
import { Appbar, Divider, Snackbar, useTheme } from "react-native-paper";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import BottomBarButton from "../../components/BottomBarButton";
import ControlledRadioInputWithQuery from "../../components/ControlledRadioInputWithQuery";
import ControlledTextInput from "../../components/ControlledTextInput";
import { DialogToConfirm } from "../../components/DialogToConfirm";
import HeaderBar from "../../components/HeaderBar";
import ScreenWrapper from "../ScreenWrapper";

import { creatMMKVStore } from "../../services/local-storage";
import { identity, isEmptyObject, makeDataFeeder, whenLet } from "../../utils";
import { validatorBase } from "../../utils/validatorBase";

// 新组件需要重新!!
import { makeRequestCopyMyProj as insertQueryClient } from "../../api/insertingQueries";
import { COPY_MY_PROJ as pageMainKey } from "../../config/keysConfig"; // 用于页面信息的提交
//import { makeRequestListingDepartments as allDeptsQueryClient } from "../../api/listingQueries";
import { useShallow } from "zustand/shallow";
import { makeReqListMyProjALL, makeReqListMyProjWB, makeReqListMyProjZC } from "../../api/listingQueries";
import { projectCopyingStates as selectorStates } from "../../hooks/selectorStates";
import { onPreSubmitError } from "../../utils/screens";

// 在mmkv中存储所有控件的数据, 新组件需要重新设置
const { setStore, getStore, clearStore, getStoreObject, setStoreObject } = creatMMKVStore(pageMainKey.store);
const dataFeeder = makeDataFeeder();
// 数据规范要求服务端数据包含id为0的默认值, 同时mmkv存储的是一个对象, 因此以下几行不再需要
//const superiorReplacer = makeReplacer("superior", "name", "id"); // 数据中superior对应的值, 将它到对象数组中去匹配name键, 若成功, superior的值替换成name所在对象的id的值
//const deptDefaults = { id: 0, name: "无上级部门" };                // 需要添加的默认部门占位
//const deptLookUpKey = "id";                                      // radio按钮根据值方向查找的键值
//const threadFunctions = [superiorReplacer.replace];
//let render = 0;

/**
 * 需要注意其中包含4种数据/状态:
 * 1. InputText手动输入的数据, 在useForm中申明, 直接由react-hook-form的controller处理, 以便减少渲染.
 * 2. 拉选框状态数据, 在selectorStates.js中预先定义, 包括radio和checkbox两种, 需要通过zustand进行全局状态管理, 其中包含的是复合数据, radio的是对象{id, name}, checkbox的是对象数组[{id, name}*], 控件要求包含id和name.
 * 3. mmkv存储数据, 在此通过creatMMKVStore创建, 用于存储当前的控件数据以及载入时恢复原先状态, 其中保存的InputText数据内容与react-form的相同, 拉选框数据内容与zustand的相同.
 * 4. 完整的react-hook-form数据, 其中包含第一种数据, radio拉选框数据只包含其id, checkbox的数据则是id的数组. hook-form数据转换成FormData后由http发送给服务端.
 * @param {Object} navigation
 * @returns
 */
const ProjectCopying = ({ navigation }) => {
    // render++;
    const { bottom, left, right } = useSafeAreaInsets();
    const theme = useTheme();
    const height = theme.isV3 ? 80 : 56;

    // 新组件需要修改!!
    const bottomBarLabels = { ok: "创建", cancel: "取消" }; // 底部按钮显示文字
    const [confimDialogConfig, setConfirmDialogConfig] = useState({}); // 对话框信息, {title, text, okLabel, onOK}, 但显示对话框的变量在showConfirm
    const [snackBarMessage, setSnackBarMessage] = useState("拷贝项目遇到错误"); // 下方错误提示文字内容
    // 拉选框组件使用全局状态
    const [
        projClassRadioState,
        //managerRadioState,
        fatherRadioState,
        setProjClassRadioState,
        //setManagerRadioState,
        setFatherRadioState,
    ] = selectorStates(useShallow(state => [state.projClassRadio, state.fatherRadio, state.setProjClassRadio, state.setFatherRadio])); // radio和check组件状态

    // 新组件不需改动
    const saveButtonIcon = "content-save-all-outline";
    const [okButtonLoading, setOkButtonLoading] = useState(false);           // 右下方按钮图标是否旋转
    const [okButtonDisabled, setOkButtonDisabled] = useState(false);         // 右下方按钮是否禁用
    const [cancelButtonDisabled, setCancelButtonDisabled] = useState(false); // 左下方按钮是否禁用
    const [showSnackbar, setShowSnackbar] = useState(false);                 // 下方是否显示错误通知
    const [showConfirm, setShowConfirm] = useState(false);                   // 是否显示确认对话框

    const projClassDataProviderRef = useRef([{ id: 1, name: "水平衡" }, { id: 11, name: "零碳诊断" }]); // 用作项目类型的选项数据源
    //const difficultyStateDataProviderRef = useRef([{ id: 1, name: "简单" }, { id: 2, name: "中等" }, { id: 3, name: "困难" }]);  // 用作难度状态选项的数据源
    //const managerLevelDataProviderRef    = useRef([{ id: 1, name: "低" },   { id: 2, name: "中" }, { id: 3, name: "高" }]);     // 用作类型状态选项的数据源

    const formDefaults = useRef({
        projName:    "",
        projClass:   "",
        //manager:     "",
        father:      "",
    });
    const storeDefaults = useRef({
        projName: "",
        projClass: { id: "", name: "" },
        //manager:   { id: "", name: "" },
        father:    { id: "", name: "" },
    });
    const selectorDefaults = useRef({
        projClass:      { id: "", name: "" },
        //manager:        { id: "", name: "" },
        father:         { id: "", name: "" },
    });

    // 新组件需要重新定义!!
    const schema = Joi.object({
        projName:  validatorBase.projectName.required,
        projClass: validatorBase.projectClass.required,
        //manager:   validatorBase.wbManager.required,
        father:    validatorBase.projectFather.required,
    });

    // 新组件需要重新定义defaultValues!!
    const {
        handleSubmit,
        control,
        resetField,
        reset,
        formState: { errors },
        //getValues,
    } = useForm({
        resolver: joiResolver(schema),
        //reValidateMode: "onBlur", // https://github.com/orgs/react-hook-form/discussions/10327
        defaultValues: {
            projName:    whenLet(getStore("projName"),      identity, ""),
            projClass:   whenLet(getStore("projClass")?.id, identity, ""),
            //manager:     whenLet(getStore("manager")?.id,   identity, ""),
            father:      whenLet(getStore("father")?.id,    identity, ""),
        },
    });

    // 新组件需要重新定义setDiagText!!
    const onHandleSubmit = data => {
        // data: {"name": "mmm", "superior": 0}
        // 由于这里的data的版本可能先于mmkv的版本, 因此必须在此将data转储到一个引用地址, 以供query使用, 这就是dataFeeder的用处, 另外dataFeeder可以做一些数据预处理
        setCancelButtonDisabled(true);
        setOkButtonDisabled(true);
        setOkButtonLoading(true);
        dataFeeder(data);
        console.log("commit data:", data);
        submitQuery.mutate();
    };
    // 新组件不需改动
    const onCancel = () => {
        clearStore();
        // 每增加一个下拉框都要设置各自的默认值
        reset(formDefaults.current);            // 重置react-form
        setStoreObject(storeDefaults.current);  // 重置mmkv存储
        // 重置selector控件状态
        setProjClassRadioState(selectorDefaults.current.projClass);
        //setManagerRadioState(selectorDefaults.current.manager);
        setFatherRadioState(selectorDefaults.current.father);
    };

    // 新组件不需改动
    const commitOnSuccess = data => {
        console.log("response success, data: ", data);
        if (data.STATUS !== 0) {
            setSnackBarMessage(`项目拷贝创建发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        } else {
            // 设置对话框数据
            setConfirmDialogConfig({
                title: "项目拷贝",
                text: "新项目拷贝成功, 请到主页查看新项目消息, 或者继续拷贝项目!",
                okLabel: "确定",
                onOK: () => {
                    clearStore();
                    // 每增加一个下拉框都要设置各自的默认值
                    reset(formDefaults.current);            // 重置react-form
                    setStoreObject(storeDefaults.current);  // 重置mmkv存储
                    // 重置radio状态
                    setProjClassRadioState(selectorDefaults.current.projClass);
                    //setManagerRadioState(selectorDefaults.current.manager);
                    setFatherRadioState(selectorDefaults.current.father);
                    setShowConfirm(false);
                },
            });
            setShowConfirm(true);
        }
    };
    const commitOnError = error => {
        setSnackBarMessage(`项目创建发生错误, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const commitOnSettled = (data, error) => {
        setCancelButtonDisabled(false);
        setOkButtonDisabled(false);
        setOkButtonLoading(false);
    };
    const submitQuery = insertQueryClient(dataFeeder, commitOnSuccess, commitOnError, commitOnSettled);

    // 组件载入时从mmkv存储恢复下拉控件状态
    useEffect(() => {
        const storedObject = getStoreObject();
        isEmptyObject(storedObject.projClass) ? setProjClassRadioState(selectorDefaults.current.projClass) : setProjClassRadioState(storedObject.projClass);
        //isEmptyObject(storedObject.manager)   ? setManagerRadioState(selectorDefaults.current.manager)     : setManagerRadioState(storedObject.manager);
        isEmptyObject(storedObject.father)    ? setFatherRadioState(selectorDefaults.current.father)       : setFatherRadioState(storedObject.father);
    }, []);
    //console.log("getValues...................", getValues());

    return (
        <TouchableWithoutFeedback onPress={() => Keyboard.dismiss()}>
            <View style={{ flex: 1 }}>
                <HeaderBar
                    title={"拷贝项目"}
                    navigation={navigation}
                    //goBackCallback={() => {}}
                    //menuItemArray={[{ title: "标题", action: ()=>{} }]}
                />

                <ScreenWrapper contentContainerStyle={styles.container} style={{ marginBottom: height + bottom }}>
                    {/*<Text>Render: {render}</Text>*/}

                    <View style={styles.formEntry}>

                    <ControlledRadioInputWithQuery
                        name="projClass"
                        rowLabel="项目类型"
                        control={control}
                        placeholder="请拉选项目类型"
                        onDialogConfirm={obj => {
                            console.log("confirm projClass: ", obj);
                            setStore("projClass", obj);
                            setProjClassRadioState(obj);
                        }}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        dataProvider={projClassDataProviderRef}
                        setFetchFailMessage={setSnackBarMessage}
                        setDisplayFailBar={setShowSnackbar}
                        dialogState={projClassRadioState}
                        setDialogState={setProjClassRadioState}
                        defaultNullOptionsTip={"未找到项目类型数据, 请联系管理员!"}
                        required={true}
                    />

                    <ControlledRadioInputWithQuery
                        name="father"
                        rowLabel="项目模板"
                        control={control}
                        placeholder="请拉选项目"
                        onDialogConfirm={obj => {
                            console.log("confirm father: ", obj);
                            setStore("father", obj);
                            setFatherRadioState(obj);
                        }}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        // 注意, dataProvider中要统一都是查询或者都是useRef, 否则渲染会报错
                        dataProvider={projClassRadioState?.id === 1 ? makeReqListMyProjWB : (projClassRadioState?.id === 11 ? makeReqListMyProjZC : makeReqListMyProjALL)}
                        setFetchFailMessage={setSnackBarMessage}
                        setDisplayFailBar={setShowSnackbar}
                        dialogState={fatherRadioState}
                        setDialogState={setFatherRadioState}
                        defaultNullOptionsTip={"未找到项目数据, 请联系项目创建人把您添加到抄送列表中!"}
                        required={true}
                    />

                    <ControlledTextInput
                        rowLabel="新项目名"
                        control={control}
                        name="projName"
                        placeholder="XXXX项目"
                        onChangeText={text => setStore("projName", text)}
                        onClearText={() => {
                            setStore("projName", "");
                            resetField("projName", { defaultValue: "" });
                        }}
                        editable={true}
                        mode="outlined"
                        formError={errors}
                        required={true}
                        multiline={true}
                    />

                </View>
            </ScreenWrapper>

            <Appbar
                style={[
                    styles.bottom,
                    {
                        height: height + bottom,
                    },
                    theme.isV3 && {
                        backgroundColor: theme.colors.elevation.level2,
                    },
                ]}
                safeAreaInsets={{ bottom, left, right }}
                theme={{ mode: "adaptive" }}
                //mode='center-aligned'
            >
                <View style={styles.bottomBarContainer}>
                    <Divider />
                    <View style={styles.bottomBarComponents}>
                        <BottomBarButton label={bottomBarLabels.cancel} disabled={cancelButtonDisabled} onPress={onCancel} />
                        <BottomBarButton label={bottomBarLabels.ok} loading={okButtonLoading} disabled={okButtonDisabled} icon={saveButtonIcon} onPress={handleSubmit(onHandleSubmit, formErr => onPreSubmitError(formErr, getStore, "WaterBalanceInserting"))} />
                    </View>
                </View>
            </Appbar>

            <Snackbar
                style={styles.snackBar}
                visible={showSnackbar}
                onDismiss={() => {
                    setShowSnackbar(false);
                }}
                onIconPress={() => {
                    setShowSnackbar(false);
                }}
                duration={Snackbar.DURATION_LONG}>
                {snackBarMessage}
            </Snackbar>

            <DialogToConfirm visible={showConfirm} title={confimDialogConfig.title ? confimDialogConfig.title : ""} text={confimDialogConfig.text ? confimDialogConfig.text : ""} onOK={confimDialogConfig.onOK ? confimDialogConfig.onOK : () => setShowConfirm(false)} okBtnLabel={confimDialogConfig.okLabel ? confimDialogConfig.okLabel : "确认"} />
            </View>
        </TouchableWithoutFeedback>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    firstColumn: {
        flex: 2,
        //borderWidth: 1,
        //height: 40,
        content: {
            fontSize: 18,
        }
    },
    secondColumn: {
        flex: 3,
        // /borderWidth: 1,
        // height: 40,
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        flex: 1,
        marginHorizontal: 6
    },
    formEntry: {
        margin: 8,
    },
    snackBar: {
        flexDirection: "column",
    },
});

export default ProjectCopying;
