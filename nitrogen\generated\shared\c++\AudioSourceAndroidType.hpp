///
/// AudioSourceAndroidType.hpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc R<PERSON>avy @ Margelo
///

#pragma once

#include <cmath>
#if __has_include(<NitroModules/JSIConverter.hpp>)
#include <NitroModules/JSIConverter.hpp>
#else
#error NitroModules cannot be found! Are you sure you installed NitroModules properly?
#endif
#if __has_include(<NitroModules/NitroDefines.hpp>)
#include <NitroModules/NitroDefines.hpp>
#else
#error NitroModules cannot be found! Are you sure you installed NitroModules properly?
#endif

namespace margelo::nitro::react_native_audio_recorder_player {

  /**
   * An enum which can be represented as a JavaScript enum (AudioSourceAndroidType).
   */
  enum class AudioSourceAndroidType {
    DEFAULT      SWIFT_NAME(default) = 0,
    MIC      SWIFT_NAME(mic) = 1,
    VOICE_UPLINK      SWIFT_NAME(voiceUplink) = 2,
    VOICE_DOWNLINK      SWIFT_NAME(voiceDownlink) = 3,
    VOICE_CALL      SWIFT_NAME(voiceCall) = 4,
    CAMCORDER      SWIFT_NAME(camcorder) = 5,
    VOICE_RECOGNITION      SWIFT_NAME(voiceRecognition) = 6,
    VOICE_COMMUNICATION      SWIFT_NAME(voiceCommunication) = 7,
    REMOTE_SUBMIX      SWIFT_NAME(remoteSubmix) = 8,
    UNPROCESSED      SWIFT_NAME(unprocessed) = 9,
    RADIO_TUNER      SWIFT_NAME(radioTuner) = 1998,
    HOTWORD      SWIFT_NAME(hotword) = 1999,
  } CLOSED_ENUM;

} // namespace margelo::nitro::react_native_audio_recorder_player

namespace margelo::nitro {

  using namespace margelo::nitro::react_native_audio_recorder_player;

  // C++ AudioSourceAndroidType <> JS AudioSourceAndroidType (enum)
  template <>
  struct JSIConverter<AudioSourceAndroidType> final {
    static inline AudioSourceAndroidType fromJSI(jsi::Runtime& runtime, const jsi::Value& arg) {
      int enumValue = JSIConverter<int>::fromJSI(runtime, arg);
      return static_cast<AudioSourceAndroidType>(enumValue);
    }
    static inline jsi::Value toJSI(jsi::Runtime& runtime, AudioSourceAndroidType arg) {
      int enumValue = static_cast<int>(arg);
      return JSIConverter<int>::toJSI(runtime, enumValue);
    }
    static inline bool canConvert(jsi::Runtime&, const jsi::Value& value) {
      if (!value.isNumber()) {
        return false;
      }
      double integer;
      double fraction = modf(value.getNumber(), &integer);
      if (fraction != 0.0) {
        // It is some kind of floating point number - our enums are ints.
        return false;
      }
      // Check if we are within the bounds of the enum.
      return integer >= 0 && integer <= 11;
    }
  };

} // namespace margelo::nitro
