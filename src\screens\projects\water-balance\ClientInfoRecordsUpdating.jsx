import { joi<PERSON>esolver } from "@hookform/resolvers/joi";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { Alert, StyleSheet, View } from "react-native";
import { Divider, Snackbar } from "react-native-paper";
//import debounce from "lodash/debounce";
import "fast-text-encoding";
import Jo<PERSON> from "joi";
import Button from "../../../components/Button";
import { DialogToConfirm } from "../../../components/DialogToConfirm";
import HeaderBar from "../../../components/HeaderBar";
import { LoadingIndicator } from "../../../components/LoadingIndicator";
import ScreenWrapper from "../../ScreenWrapper";
//import ControlledTextInput from "../../../components/ControlledTextInput";
import { FormListNodesMapper } from "../../../components/FormListNodesMapper";
import { creatMMKVStore, makeStoredValueToFormValue } from "../../../services/local-storage";
import { callOneByOne, makeDataFeeder, parseIndustryCode } from "../../../utils";
import { checkPermits } from "../../../utils/permits";
import { isServerDataNewer, makeGetClientCversion, radioIdToObject } from "../../../utils/records";
import { genRadioValidatorRule, validatorBase } from "../../../utils/validatorBase";


// 新组件需要重新!! 如果遇到非水平衡表格就要更改key
import { useShallow } from "zustand/shallow";
import {
    makeReqDelWaterBalanceRecords as recordDeleteQueryMaker,
    makeReqGetWaterBalanceRecords as recordSelectQueryMaker,
    makeReqUpdWaterBalanceRecords as recordUpdateQueryMaker,
} from "../../../api/projectRecordsQueries";
import { UPD_WATER_BALANCE_RECORDS as pageMainKey } from "../../../config/keysConfig";
import { prodSystemEnum } from "../../../config/waterBalance";
import { wbClientInfoStates as selectorStates } from "../../../hooks/selectorStates";
import { onPreSubmitError } from "../../../utils/screens";
//import { roundNearest } from "../../../utils/numeric";
//import log from "../../../services/logging";


const dataFeeder = makeDataFeeder();

/**
 * 客户资料清单
 * 目前只有表有此需求, 因此在表格类型列表中只有表项目会显示出来.
 * @param {object} args
 * @param {object} args.navigation
 * @param {object} args.route
 * @returns
 */
const RecordsUpdating = ({ navigation, route }) => {
    console.log("RecordsUpdating pageMeta from nav:", route.params.pageMeta);
    console.log("RecordsUpdating projMeta from nav:", route.params.projMeta);
    const projSubclass = route.params.projMeta.subclass; // 1表, 2书
    const projIndustry = parseIndustryCode(route.params.projMeta.industry); // 1 -> industry, 2 -> service

    // formType对应着服务端的form-type, 为0时表示表单是用户填写, 为1表示服务端生成, 为1时只读
    const { queryKwd, id: recordPubid, name: recordName, formType } = route.params.pageMeta; // recordPubid is the alias of id
    const saveButtonIcon = "content-save-all-outline";
    const formEditableGlobal = checkPermits() ? (formType === 0 ? true : false) : false;

    // 使用memoize来优化
    const { setStore, getStore, clearStore, setStoreObject } = useMemo(() => {
        console.log("creatMMKVStore memo:", pageMainKey.store, queryKwd, recordPubid);
        return creatMMKVStore(`${pageMainKey.store}/${queryKwd}/${recordPubid}`);
    }, [queryKwd, recordPubid]);

    const storedValueToFormValue = useRef(makeStoredValueToFormValue(getStore));
    const getClientCversion = useRef(makeGetClientCversion(() => getStore("mainCversion"), () => getStore("subCversion")));

    // 客户端cversion = `${mainCversion}.${subCversion}`
    (getStore("mainCversion") === undefined) ? setStore("mainCversion", 0) : undefined; // 客户端主版本号
    (getStore("subCversion")  === undefined) ? setStore("subCversion",  0) : undefined; // 客户端次版本号, 修改一次自增一次
    //let mainCversion = getStore("mainCversion"); // 主版本号, 与服务端cversion对应
    // 次版本号, 表示本地更改次数, 用于程序上对子版本号的增加, 用于判断与服务端版本的差异, 同时可用于判断避免重复从MMKV获取数据
    const subCversionRef = useRef(getStore("subCversion"));

    // 新组件需要修改!!
    const bottomBarLabels = { ok: "提交" };                    // 底部按钮显示文字
    const [snackBarMessage, setSnackBarMessage] = useState("更新表单遇到错误"); // 下方错误提示文字内容

    const [
        prodSystemRadioState,
        exptProdDatePickerState,
        setProdSystemRadioState,
        setExptProdDatePickerState,
        resetSelectorStates,
    ] = selectorStates(useShallow(state => [
        state.prodSystemRadio,
        state.exptProdDatePicker,
        state.setProdSystemRadio,
        state.setExptProdDatePicker,
        state.resetStates,
    ])); // radio组件状态

    const [confimDialogConfig, setConfirmDialogConfig] = useState({});       // 对话框信息, {title, text, okLabel, onOK}, 但显示对话框的变量在showConfirm

    // 新组件不需改动
    const [screenTitle,      setScreenTitle]      = useState(route.params.pageMeta.name); // 屏幕上方导航栏标题
    const [okButtonLoading,  setOkButtonLoading]  = useState(false);            // 右下方按钮图标是否旋转
    const [okButtonDisabled, setOkButtonDisabled] = useState(!checkPermits());  // 右下方按钮是否禁用
    const [showSnackbar,     setShowSnackbar]     = useState(false);            // 下方是否显示错误通知
    const [showConfirm,      setShowConfirm]      = useState(false);            // 是否显示确认对话框
    const [loadingIndicatorVisible, setLoadingIndicatorVisible] = useState(false); // 是否显示loading
    const delQueryInfo = useRef({ id: recordPubid, name: recordName });         // 用于删除本记录的查询变量

    //const waterTypeStateDataProviderRef     = useRef([{ id: 1, name: "自来水" }, { id: 2, name: "地表水" }, { id: 3, name: "地下水" }, { id: 4, name: "市政中水" }]);
    //const accuracyLevelStateDataProviderRef = useRef([{ id: 1, name: "一级" }, { id: 2, name: "二级" }, ]);
    //const equipStateStateDataProviderRef    = useRef([{ id: 1, name: "合格" }, { id: 2, name: "不合格" },]);
    const prodSystemStateDataProviderRef      = useRef(prodSystemEnum);

    // 用于存储waterScale的值, 当unitToggleState发生改变时需要通过该值更新表单字段
    //const waterUnitScaleRef = useRef(0);

    const prodSystemValidatorRule = useRef(genRadioValidatorRule(0, "生产制度", 1, 2, 3));
    // Define form
    // 新组件需要重新定义!!
    const schema = Joi.object({
        orgName:       validatorBase.waterBalance.commons.textField,
        deptName:      validatorBase.waterBalance.commons.textField,
        clientName:    validatorBase.waterBalance.commons.textField,
        clientCode:    validatorBase.waterBalance.commons.textField,
        projName:      validatorBase.waterBalance.commons.textField,
        builtInst:     validatorBase.waterBalance.commons.textField,
        legalRep:      validatorBase.waterBalance.commons.textField,
        contactAddr:   validatorBase.waterBalance.commons.textField,
        licence:       validatorBase.waterBalance.commons.textField,
        projAddr:      validatorBase.waterBalance.commons.longTextField,
        contact:       validatorBase.waterBalance.commons.textField,
        contactInfo:   validatorBase.waterBalance.commons.textField,
        zipCode:       validatorBase.waterBalance.commons.textField,
        projInvt:      validatorBase.waterBalance.commons.moneyField,
        envInvt:       validatorBase.waterBalance.commons.moneyField,
        exptProdDate:  validatorBase.waterBalance.commons.dateField,
        projArea:      validatorBase.waterBalance.commons.floatField,
        buildingArea:  validatorBase.waterBalance.commons.floatField,
        greenArea:     validatorBase.waterBalance.commons.floatField,
        factoryArea:   validatorBase.waterBalance.commons.floatField,
        workshopArea:  validatorBase.waterBalance.commons.floatField,
        warehouseArea: validatorBase.waterBalance.commons.floatField,
        officeArea:    validatorBase.waterBalance.commons.floatField,
        prodSystem:    prodSystemValidatorRule.current,
        wordDaysY:     validatorBase.waterBalance.commons.intField,
        staffNum:      validatorBase.waterBalance.commons.intField,
        diningNum:     validatorBase.waterBalance.commons.intField,
        lodgingNum:    validatorBase.waterBalance.commons.intField,
        // other fields
        others:       validatorBase.waterBalance.commons.textField,
        remarks:      validatorBase.waterBalance.commons.longTextField,
    });

    // 新组件需要重新定义defaultValues!!
    const {
        handleSubmit,
        control,
        resetField,
        reset,
        formState: { errors },
        //setValue,
        //getValues,
    } = useForm({
        resolver: joiResolver(schema),
        //reValidateMode: "onBlur", // https://github.com/orgs/react-hook-form/discussions/10327
        defaultValues: { // 默认值在组件载入时会被更新, 这里的初始值正常情况下没有用处
            // 注意, 默认为0的是允许默认为0的值, 或者通过计算得到的值
            orgName:       "",
            deptName:      "",
            clientName:    "",
            clientCode:    "",
            projName:      "",
            builtInst:     "",
            legalRep:      "",
            contactAddr:   "",
            licence:       "",
            projAddr:      "",
            contact:       "",
            contactInfo:   "",
            zipCode:       "",
            projInvt:      "",
            envInvt:       "",
            exptProdDate:  "",
            projArea:      "",
            buildingArea:  "",
            greenArea:     "",
            factoryArea:   "",
            workshopArea:  "",
            warehouseArea: "",
            officeArea:    "",
            prodSystem:    "",
            wordDaysY:     "",
            staffNum:      "",
            diningNum:     "",
            lodgingNum:    "",
            // other fields
            others:        "",
            remarks:       "",
        },
    });

    // Query: select record
    const onRecordSelectSuccess = (data) => { // data: {"id": 3, "leader": 0, "name": "DEPT-03", "superior": 0}
        // 注意, 进入onSuccess之前就意味着服务端版本比客户端版本新, 因此要更新客户端主版本号
        if (data.STATUS === 0) {
            setStore("mainCversion", data.DATA.cversion);
            setStore("subCversion", 0);

            const formObject = {
                orgName: String(data.DATA.orgName),
                deptName: String(data.DATA.deptName),
                clientName: String(data.DATA.clientName),
                clientCode: String(data.DATA.clientCode),
                projName: String(data.DATA.projName),
                builtInst: String(data.DATA.builtInst),
                legalRep: String(data.DATA.legalRep),
                contactAddr: String(data.DATA.contactAddr),
                licence: String(data.DATA.licence),
                projAddr: String(data.DATA.projAddr),
                contact: String(data.DATA.contact),
                contactInfo: String(data.DATA.contactInfo),
                zipCode: String(data.DATA.zipCode),
                projInvt: String(data.DATA.projInvt),
                envInvt: String(data.DATA.envInvt),
                exptProdDate: String(data.DATA.exptProdDate),
                projArea: String(data.DATA.projArea),
                buildingArea: String(data.DATA.buildingArea),
                greenArea: String(data.DATA.greenArea),
                factoryArea: String(data.DATA.factoryArea),
                workshopArea: String(data.DATA.workshopArea),
                warehouseArea: String(data.DATA.warehouseArea),
                officeArea: String(data.DATA.officeArea),
                prodSystem: String(data.DATA.prodSystem),
                wordDaysY: String(data.DATA.wordDaysY),
                staffNum: String(data.DATA.staffNum),
                diningNum: String(data.DATA.diningNum),
                lodgingNum: String(data.DATA.lodgingNum),
                // other fields
                others:       String(data.DATA.others),
                remarks:      String(data.DATA.remarks),
            };
            reset(formObject);           // 重置react-form

            const storeObjects = checkPermits() && (formType === 0) && {
                ...formObject,
                prodSystem:      radioIdToObject(prodSystemStateDataProviderRef.current, data.DATA.prodSystem),
                //waterType:     radioIdToObject(waterTypeStateDataProviderRef.current,     data.DATA.waterType),
                //accuracyLevel: radioIdToObject(accuracyLevelStateDataProviderRef.current, data.DATA.accuracyLevel),
                //equipState:    radioIdToObject(equipStateStateDataProviderRef.current,    data.DATA.equipState),
            };
            storeObjects && setStoreObject(storeObjects, false); // 判断用户权限, 把服务端数据写到mmkv存储, 只有可写用户才需要本地存储, 不会首先清空老数据

            // 设置selector菜单状态
            setProdSystemRadioState(radioIdToObject(prodSystemStateDataProviderRef.current, data.DATA.prodSystem));
            setExptProdDatePickerState(formObject.exptProdDate);
            //setEndDatePickerState(formObject.endDate);

            //waterUnitScaleRef.current = data.DATA.waterScale || 0; // 根据服务端数据更新
            // 设置屏幕标题
            (screenTitle !== formObject.name) && setScreenTitle(formObject.name);
        } else {
            console.log("STATUS !== 0", data.STATUS);
            setSnackBarMessage(`表单数据拉取发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        }
    };
    const onRecordSelectError = (err) => {
        console.log("Record selecting error:", err);
    };
    const onRecordSelectSettled = (err) => {
        loadingIndicatorVisible && setLoadingIndicatorVisible(false);
    };
    const getPath = formType === 0 ? "get" : "getsum";
    const recordSelectQuery = recordSelectQueryMaker([queryKwd, getPath, recordPubid], onRecordSelectSuccess, onRecordSelectError, onRecordSelectSettled);

    // Query: update record
    const onHandleSubmit = (data) => {
        setOkButtonDisabled(true);
        setOkButtonLoading(true);
        dataFeeder({ ...data,
            cversion: getClientCversion.current(),
            name:       data.clientName,
            // unifify data units
            // ...
        }); // append client cversion
        recordUpdateQuery.mutate();
    };

    // 新组件不需改动
    const onRecordUpdateSuccess = (data) => {
        console.log(" Record update success response data: ", data);
        if(data.STATUS !== 0) {
            setSnackBarMessage(`表单更新发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        } else {
            // 设置确认框数据
            setConfirmDialogConfig({
                title: "更新表单",
                text: "表单信息更新成功!",
                okLabel: "确定",
                onOK: () => {
                    setShowConfirm(false);
                    recordSelectQuery.mutate();
                }});
            setShowConfirm(true);
        }
    };
    const onRecordUpdateError = (error) => {
        setSnackBarMessage(`表单更新发生错误, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const onRecordUpdateSettled = (data, error) => {
        setOkButtonDisabled(false);
        setOkButtonLoading(false);
    };
    const recordUpdateQuery = recordUpdateQueryMaker(dataFeeder, [queryKwd, "upd", recordPubid], onRecordUpdateSuccess, onRecordUpdateError, onRecordUpdateSettled);

    // Query: delete record
    const recordDeleteOnSuccess = () => {
        clearStore();
        navigation.goBack();
    };
    const recordDeleteOnError = (error) => {
        setSnackBarMessage(`表单删除失败, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const recordDeleteQuery = recordDeleteQueryMaker([queryKwd, "del", recordPubid], recordDeleteOnSuccess, recordDeleteOnError);

    const deleteAlert = () => {
        return (
            Alert.alert("删除表单", `删除后不可撤销, 请确认是否删除: ${delQueryInfo.current?.name}?`, [
                { text: "取消" },
                { text: "确定", onPress: () => recordDeleteQuery.mutate() },
            ])
        );
    };

    const clearCache = () => {
        return (
            Alert.alert("清空本地缓存", "1. 用于未知原因导致数据无法提交的情况\r\n2. 未提交的内容将会丢失, 请做好数据截图备份\r\n3. 已提交到服务端的数据不受影响\r\n4. 确认清空请点击“确认”按钮", [
                { text: "取消" },
                { text: "确定", onPress: () => { clearStore(); navigation.goBack(); } },
            ])
        );
    };

    // Hook: onMount fetch data
    useEffect(() => {
        // 优化方案: 根据用户权限进行mutate, 只有可写用户才需要本地存储
        const localCversion = getClientCversion.current();
        const remoteCversion = route.params.pageMeta.cversion;
        console.log("localCversion:", localCversion, "remoteSversion:", remoteCversion, "subCversionRef:", subCversionRef.current);

        const restoreLocalData = () => {
            const formObject = { // 默认值用户表单的数据显示
                orgName: storedValueToFormValue.current("orgName"),
                deptName: storedValueToFormValue.current("deptName"),
                clientName: storedValueToFormValue.current("clientName"),
                clientCode: storedValueToFormValue.current("clientCode"),
                projName: storedValueToFormValue.current("projName"),
                builtInst: storedValueToFormValue.current("builtInst"),
                legalRep: storedValueToFormValue.current("legalRep"),
                contactAddr: storedValueToFormValue.current("contactAddr"),
                licence: storedValueToFormValue.current("licence"),
                projAddr: storedValueToFormValue.current("projAddr"),
                contact: storedValueToFormValue.current("contact"),
                contactInfo: storedValueToFormValue.current("contactInfo"),
                zipCode: storedValueToFormValue.current("zipCode"),
                projInvt: storedValueToFormValue.current("projInvt"),
                envInvt: storedValueToFormValue.current("envInvt"),
                exptProdDate: storedValueToFormValue.current("exptProdDate"),
                projArea: storedValueToFormValue.current("projArea"),
                buildingArea: storedValueToFormValue.current("buildingArea"),
                greenArea: storedValueToFormValue.current("greenArea"),
                factoryArea: storedValueToFormValue.current("factoryArea"),
                workshopArea: storedValueToFormValue.current("workshopArea"),
                warehouseArea: storedValueToFormValue.current("warehouseArea"),
                officeArea: storedValueToFormValue.current("officeArea"),
                prodSystem: storedValueToFormValue.current("prodSystem"),
                wordDaysY: storedValueToFormValue.current("wordDaysY"),
                staffNum: storedValueToFormValue.current("staffNum"),
                diningNum: storedValueToFormValue.current("diningNum"),
                lodgingNum: storedValueToFormValue.current("lodgingNum"),
                // other fields
                others:       storedValueToFormValue.current("others"),
                remarks:      storedValueToFormValue.current("remarks"),
            };
            reset(formObject); // 重置react-form数据

            // 设置selector数据
            const defaultProdSystem = 0;
            setProdSystemRadioState(getStore("prodSystem") || radioIdToObject(prodSystemStateDataProviderRef.current, defaultProdSystem));
            setExptProdDatePickerState(formObject.exptProdDate);
            //setEndDatePickerState(formObject.endDate);

            //waterUnitScaleRef.current = getStore("waterScale")?.id || defaultWaterUnitScale; // 根据本地存储更新
        };

        // 根据反馈, 要求默认显示0
        if (localCversion === 0) {
            recordSelectQuery.mutate();
            return;
        }

        if(formType === 1) {
            recordSelectQuery.mutate();
        } else if (localCversion === 0 && remoteCversion > 0) {
            console.log("Local cversion === 0, serve may have something nontrivial initialized, query mutate!");
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        } else if (isServerDataNewer(localCversion, remoteCversion) && subCversionRef.current > 0) { // 服务端有更新数据, 但本地有修改
            console.log("Client cversion fell behind the server cversion, someone might modified the database, choose to use the data version!");
            Alert.alert("注意", "由于其他人向服务器提交了数据, 服务端数据比本地数据新, 请选择使用哪份数据?", [
                { text: "使用本地数据", onPress: () => { setStore("mainCversion", remoteCversion); restoreLocalData(); } },
                { text: "使用服务端数据", onPress: () => { setLoadingIndicatorVisible(true); recordSelectQuery.mutate(); } },
            ]);
        } else if (isServerDataNewer(localCversion, remoteCversion)) { // localCversion==0时要mutate是因为服务端可能在创建记录时生成了必要数据
            console.log("Server cversion may be newer, query mutate:", localCversion, remoteCversion);
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        }
        else {
            console.log("Local cversion >= server cversion, query will not mutate!");
            restoreLocalData();
        }
    }, []);


    // remarks不需配置
    const FieldsConfig_1_book = {
        industry: [
            {
                inputs: [
                //{ name: "name",        label: "表单名称",     unit: "",     type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                    { name: "orgName",       label: "组织名称",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true, required: true}, },
                    { name: "deptName",      label: "部门名称",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true, required: true}, },
                    { name: "clientName",    label: "企业名称",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true, required: true}, },
                    { name: "clientCode",    label: "客户编码",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true, required: true}, },
                    { name: "projName",      label: "项目名称",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                    { name: "builtInst",     label: "建设单位",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                    { name: "legalRep",      label: "法人",       unit: "",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "contactAddr",   label: "联系地址",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                    { name: "licence",       label: "营业执照",    unit: "",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "projAddr",      label: "项目实施地点", unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                    { name: "contact",       label: "联系人",      unit: "",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "contactInfo",   label: "联系方式",    unit: "",       type: "PLAIN", editable: true, placeholder: "", },
                    { name: "zipCode",       label: "邮编",       unit: "",       type: "PLAIN", editable: true, placeholder: "", },
                    { name: "projInvt",      label: "项目总投资",   unit: "元",     type: "PLAIN", editable: true, placeholder: "", },
                    { name: "envInvt",       label: "环保投资",     unit: "元",    type: "PLAIN", editable: true, placeholder: "", },
                    { name: "exptProdDate",  label: "预投产日期",   unit: "",      type: "DATE", editable: true, placeholder: "", selectorState: exptProdDatePickerState, setSelectorState: setExptProdDatePickerState,},
                    { name: "projArea",      label: "项目占地面积", unit: "平方米", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "buildingArea",  label: "总建筑面积",   unit: "平方米", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "greenArea",     label: "绿化面积",    unit: "平方米",  type: "PLAIN", editable: true, placeholder: "", },
                    { name: "factoryArea",   label: "整个厂区面积", unit: "平方米",  type: "PLAIN", editable: true, placeholder: "", },
                    { name: "workshopArea",  label: "车间面积",     unit: "平方米", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "warehouseArea", label: "库房面积",     unit: "平方米", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "officeArea",    label: "办公室面积",   unit: "平方米",  type: "PLAIN", editable: true, placeholder: "", },
                    { name: "prodSystem",    label: "生产制度",     unit: "",      type: "RADIO", editable: true, placeholder: "", selectorState: prodSystemRadioState, setSelectorState: setProdSystemRadioState, dataProvider: prodSystemEnum,},
                    { name: "wordDaysY",     label: "年工作日",     unit: "",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "staffNum",      label: "项目定员",     unit: "",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "diningNum",     label: "就餐人数",     unit: "",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "lodgingNum",    label: "住宿人数",     unit: "",      type: "PLAIN", editable: true, placeholder: "", },

                ]
            },
        ],
        service: [
            {
                inputs: [
                //{ name: "name",        label: "表单名称",     unit: "",     type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                    { name: "orgName",       label: "组织名称",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true, required: true}, },
                    { name: "deptName",      label: "部门名称",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true, required: true}, },
                    { name: "clientName",    label: "企业名称",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true, required: true}, },
                    { name: "clientCode",    label: "客户编码",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true, required: true}, },
                    { name: "projName",      label: "项目名称",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                    { name: "builtInst",     label: "建设单位",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                    { name: "legalRep",      label: "法人",       unit: "",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "contactAddr",   label: "联系地址",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                    { name: "licence",       label: "营业执照",    unit: "",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "projAddr",      label: "项目实施地点", unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                    { name: "contact",       label: "联系人",      unit: "",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "contactInfo",   label: "联系方式",    unit: "",       type: "PLAIN", editable: true, placeholder: "", },
                    { name: "zipCode",       label: "邮编",       unit: "",       type: "PLAIN", editable: true, placeholder: "", },
                    { name: "projInvt",      label: "项目总投资",   unit: "元",     type: "PLAIN", editable: true, placeholder: "", },
                    { name: "envInvt",       label: "环保投资",     unit: "元",    type: "PLAIN", editable: true, placeholder: "", },
                    { name: "exptProdDate",  label: "预投产日期",   unit: "",      type: "DATE", editable: true, placeholder: "", selectorState: exptProdDatePickerState, setSelectorState: setExptProdDatePickerState,},
                    { name: "projArea",      label: "项目占地面积", unit: "平方米", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "buildingArea",  label: "总建筑面积",   unit: "平方米", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "greenArea",     label: "绿化面积",    unit: "平方米",  type: "PLAIN", editable: true, placeholder: "", },
                    { name: "factoryArea",   label: "整个厂区面积", unit: "平方米",  type: "PLAIN", editable: true, placeholder: "", },
                    { name: "workshopArea",  label: "车间面积",     unit: "平方米", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "warehouseArea", label: "库房面积",     unit: "平方米", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "officeArea",    label: "办公室面积",   unit: "平方米",  type: "PLAIN", editable: true, placeholder: "", },
                    { name: "prodSystem",    label: "生产制度",     unit: "",      type: "RADIO", editable: true, placeholder: "", selectorState: prodSystemRadioState, setSelectorState: setProdSystemRadioState, dataProvider: prodSystemEnum,},
                    { name: "wordDaysY",     label: "年工作日",     unit: "",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "staffNum",      label: "项目定员",     unit: "",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "diningNum",     label: "就餐人数",     unit: "",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "lodgingNum",    label: "住宿人数",     unit: "",      type: "PLAIN", editable: true, placeholder: "", },

                ]
            },
        ],
    };

    const FieldsConfig_1_table = {
        industry: [
            {
                inputs: [
                //{ name: "name",        label: "表单名称",     unit: "",     type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                    { name: "orgName",       label: "组织名称",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true, required: true}, },
                    { name: "deptName",      label: "部门名称",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true, required: true}, },
                    { name: "clientName",    label: "企业名称",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true, required: true}, },
                    { name: "clientCode",    label: "客户编码",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true, required: true}, },
                    { name: "projName",      label: "项目名称",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                    { name: "builtInst",     label: "建设单位",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                    { name: "legalRep",      label: "法人",       unit: "",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "contactAddr",   label: "联系地址",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                    { name: "licence",       label: "营业执照",    unit: "",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "projAddr",      label: "项目实施地点", unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                    { name: "contact",       label: "联系人",      unit: "",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "contactInfo",   label: "联系方式",    unit: "",       type: "PLAIN", editable: true, placeholder: "", },
                    { name: "zipCode",       label: "邮编",       unit: "",       type: "PLAIN", editable: true, placeholder: "", },
                    { name: "projInvt",      label: "项目总投资",   unit: "元",     type: "PLAIN", editable: true, placeholder: "", },
                    { name: "envInvt",       label: "环保投资",     unit: "元",    type: "PLAIN", editable: true, placeholder: "", },
                    { name: "exptProdDate",  label: "预投产日期",   unit: "",      type: "DATE", editable: true, placeholder: "", selectorState: exptProdDatePickerState, setSelectorState: setExptProdDatePickerState,},
                    { name: "projArea",      label: "项目占地面积", unit: "平方米", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "buildingArea",  label: "总建筑面积",   unit: "平方米", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "greenArea",     label: "绿化面积",    unit: "平方米",  type: "PLAIN", editable: true, placeholder: "", },
                    { name: "factoryArea",   label: "整个厂区面积", unit: "平方米",  type: "PLAIN", editable: true, placeholder: "", },
                    { name: "workshopArea",  label: "车间面积",     unit: "平方米", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "warehouseArea", label: "库房面积",     unit: "平方米", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "officeArea",    label: "办公室面积",   unit: "平方米",  type: "PLAIN", editable: true, placeholder: "", },
                    { name: "prodSystem",    label: "生产制度",     unit: "",      type: "RADIO", editable: true, placeholder: "", selectorState: prodSystemRadioState, setSelectorState: setProdSystemRadioState, dataProvider: prodSystemEnum,},
                    { name: "wordDaysY",     label: "年工作日",     unit: "",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "staffNum",      label: "项目定员",     unit: "",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "diningNum",     label: "就餐人数",     unit: "",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "lodgingNum",    label: "住宿人数",     unit: "",      type: "PLAIN", editable: true, placeholder: "", },

                ]
            },
        ],
        service: [
            {
                inputs: [
                //{ name: "name",        label: "表单名称",     unit: "",     type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                    { name: "orgName",       label: "组织名称",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true, required: true}, },
                    { name: "deptName",      label: "部门名称",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true, required: true}, },
                    { name: "clientName",    label: "企业名称",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true, required: true}, },
                    { name: "clientCode",    label: "客户编码",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true, required: true}, },
                    { name: "projName",      label: "项目名称",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                    { name: "builtInst",     label: "建设单位",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                    { name: "legalRep",      label: "法人",       unit: "",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "contactAddr",   label: "联系地址",    unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                    { name: "licence",       label: "营业执照",    unit: "",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "projAddr",      label: "项目实施地点", unit: "",      type: "PLAIN", editable: true, placeholder: "", props: {multiline: true}, },
                    { name: "contact",       label: "联系人",      unit: "",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "contactInfo",   label: "联系方式",    unit: "",       type: "PLAIN", editable: true, placeholder: "", },
                    { name: "zipCode",       label: "邮编",       unit: "",       type: "PLAIN", editable: true, placeholder: "", },
                    { name: "projInvt",      label: "项目总投资",   unit: "元",     type: "PLAIN", editable: true, placeholder: "", },
                    { name: "envInvt",       label: "环保投资",     unit: "元",    type: "PLAIN", editable: true, placeholder: "", },
                    { name: "exptProdDate",  label: "预投产日期",   unit: "",      type: "DATE", editable: true, placeholder: "", selectorState: exptProdDatePickerState, setSelectorState: setExptProdDatePickerState,},
                    { name: "projArea",      label: "项目占地面积", unit: "平方米", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "buildingArea",  label: "总建筑面积",   unit: "平方米", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "greenArea",     label: "绿化面积",    unit: "平方米",  type: "PLAIN", editable: true, placeholder: "", },
                    { name: "factoryArea",   label: "整个厂区面积", unit: "平方米",  type: "PLAIN", editable: true, placeholder: "", },
                    { name: "workshopArea",  label: "车间面积",     unit: "平方米", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "warehouseArea", label: "库房面积",     unit: "平方米", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "officeArea",    label: "办公室面积",   unit: "平方米",  type: "PLAIN", editable: true, placeholder: "", },
                    { name: "prodSystem",    label: "生产制度",     unit: "",      type: "RADIO", editable: true, placeholder: "", selectorState: prodSystemRadioState, setSelectorState: setProdSystemRadioState, dataProvider: prodSystemEnum,},
                    { name: "wordDaysY",     label: "年工作日",     unit: "",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "staffNum",      label: "项目定员",     unit: "",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "diningNum",     label: "就餐人数",     unit: "",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "lodgingNum",    label: "住宿人数",     unit: "",      type: "PLAIN", editable: true, placeholder: "", },

                ]
            },
        ],
    };

    const FieldsConfig_2_book  = {industry: [], service: []};
    const FieldsConfig_2_table = {industry: [], service: []};

    const FieldsConfig_3_book = {
        industry: [
        // 其它部分
            {
                inputs: [
                //{ name: "others", label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "",  props: {multiline: true}, },
                ]
            },
        ],
        service: [
            // 其它部分
            {
                inputs: [
                    //{ name: "others", label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "",  props: {multiline: true}, },
                ]
            },
        ],
    };
    const FieldsConfig_3_table = {
        industry: [
        // 其它部分
            {
                inputs: [
                //{ name: "others", label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "",  props: {multiline: true}, },
                ]
            },
        ],
        service: [
            // 其它部分
            {
                inputs: [
                    //{ name: "others", label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "",  props: {multiline: true}, },
                ]
            },
        ],
    };

    let FieldsConfig = [];
    switch(projSubclass) {
        case 1: // 表
            FieldsConfig = [...FieldsConfig_1_table[projIndustry], ...FieldsConfig_2_table[projIndustry], ...FieldsConfig_3_table[projIndustry]];
            break;
        case 2: // 书
            FieldsConfig = [...FieldsConfig_1_book[projIndustry], ...FieldsConfig_2_book[projIndustry], ...FieldsConfig_3_book[projIndustry]];
            break;
        default:
            console.warn("Unknown subclass:", projSubclass);
            FieldsConfig = [];
            break;
    }

    //const FieldsConfig = [...FieldsConfig_1_book, ...FieldsConfig_2_book, ...FieldsConfig_3_book];

    return (
        <View style={{ flex: 1 }}>

            <HeaderBar
                //title={screenTitle}
                navigation={navigation}
                goBackCallback={() => {
                    callOneByOne(
                        //() => { _mainCversionGetter=undefined; _subCversionGetter=undefined;},
                        resetSelectorStates,
                    );
                    /*callOneByOne(clearStore, resetSelectorStates)*/  // 返回前清空mmkv存储因为临时数据不再需要, 重置状态使得页面切换后不会使用前面的数据
                }}
                menuItemArray={formType === 0 ? [{ title: "删除表单", action: deleteAlert }, { title: "清空缓存", action: clearCache }] : []}
            />

            <ScreenWrapper contentContainerStyle={styles.container}>

                <View style={styles.formEntry}>
                    <FormListNodesMapper
                        fieldsConfig={FieldsConfig}
                        subCversionRef={subCversionRef}
                        control={control}
                        setStore={setStore}
                        resetField={resetField}
                        errors={errors}
                        setSnackBarMessage={setSnackBarMessage}
                        setShowSnackbar={setShowSnackbar}
                        //formDisabledGlobal={formDisabledGlobal}
                        editable={formEditableGlobal}
                        rowStyle={styles.rowContainer}
                    />
                    <Divider bold={true}/>

                    {/*formType === 0 && <ControlledTextInput
                        name="remarks"
                        rowLabel="备注"
                        control={control}
                        placeholder=""
                        onChangeText={(text) => {
                            subCversionRef.current++;
                            setStore("subCversion", subCversionRef.current);
                            setStore("remarks", text);
                        }}
                        onClearText={() => {
                            subCversionRef.current++;
                            setStore("subCversion", subCversionRef.current);
                            setStore("remarks", "");
                            resetField("remarks", { defaultValue: "" });
                        }}
                        editable={formEditableGlobal}
                        mode="flat"
                        multiline={true}
                        formError={errors}
                        style={styles.rowContainerLongText}
                        //disabled={formDisabledGlobal}
                        required={false}
                    />*/}
                </View>

                {formType === 0 && <View style={styles.button}>
                    <Button mode="contained"
                        disabled={okButtonDisabled}
                        loading={okButtonLoading}
                        icon={saveButtonIcon}
                        onPress={handleSubmit(onHandleSubmit, (formErr) => onPreSubmitError(formErr, getStore, "ClientInfoRecordsUpdating"))}
                    >{bottomBarLabels.ok}</Button>
                </View>}
            </ScreenWrapper>

            <LoadingIndicator
                title={""}
                message={"加载中....."}
                visible={loadingIndicatorVisible}
                onClose={()=>setLoadingIndicatorVisible(false)}
            />

            <Snackbar
                style={styles.snackBar}
                visible={showSnackbar}
                onDismiss={() => setShowSnackbar(false)}
                onIconPress={() => setShowSnackbar(false)}
                duration={Snackbar.DURATION_LONG}
            >
                {snackBarMessage}
            </Snackbar>

            <DialogToConfirm
                visible={showConfirm}
                title={confimDialogConfig.title ? confimDialogConfig.title : ""}
                text={confimDialogConfig.text ? confimDialogConfig.text : ""}
                onOK={confimDialogConfig.onOK ? confimDialogConfig.onOK : () => setShowConfirm(false)}
                okBtnLabel={confimDialogConfig.okLabel ? confimDialogConfig.okLabel : "确认"}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    rowContainer:{
        firstColumn: {
            flex: 2,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 3,
        // /borderWidth: 1,
        // height: 40,
        },
    },
    rowContainerLongText: {
        firstColumn: {
            flex: 1,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 4,
            // /borderWidth: 1,
            // height: 40,
        },
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        width: "80%",
        alignSelf: "center",
    },
    formEntry: {
        margin: 8,
    },
    snackBar: {
        flexDirection: "column",
    },
});

export default RecordsUpdating;
