/**
 * 本文件对projects目录下的屏幕进行导入导出.
 * 导出后在HomeScreen处加入Stack导航.
 */



/**
 * 水平衡项目
 */

export { default as WaterBalanceIndex } from "./water-balance/ProjectIndex";
export { default as WaterBalanceOverview } from "./water-balance/WaterBalanceOverview";

// 企业基本情况表
export { default as BasicInfoRecordsListing } from "./water-balance/BasicInfoRecordsListing";
export { default as BasicInfoRecordsUpdating } from "./water-balance/BasicInfoRecordsUpdating";

// 客户资料清单
export { default as ClientInfoRecordsListing } from "./water-balance/ClientInfoRecordsListing";
export { default as ClientInfoRecordsUpdating } from "./water-balance/ClientInfoRecordsUpdating";

// 公用工程
export { default as ClientPublicUtilityRecordsListing } from "./water-balance/ClientPublicUtilityRecordsListing";
export { default as ClientPublicUtilityRecordsUpdating } from "./water-balance/ClientPublicUtilityRecordsUpdating";

// 项目产品规模
export { default as ClientProductScaleRecordsListing } from "./water-balance/ClientProductScaleRecordsListing";
export { default as ClientProductScaleRecordsUpdating } from "./water-balance/ClientProductScaleRecordsUpdating";

// 项目原辅材料消耗
export { default as ClientMaterialConsumeRecordsListing } from "./water-balance/ClientMaterialConsumeRecordsListing";
export { default as ClientMaterialConsumeRecordsUpdating } from "./water-balance/ClientMaterialConsumeRecordsUpdating";

// 项目所需设备清单
export { default as ClientRequiredDeviceRecordsListing } from "./water-balance/ClientRequiredDeviceRecordsListing";
export { default as ClientRequiredDeviceRecordsUpdating } from "./water-balance/ClientRequiredDeviceRecordsUpdating";

// 人均生活用水量、单位面积绿化用水量计算表(参考计算)
export { default as WaterUsageCalcListing } from "./water-balance/WaterUsageCalcRecordsListing";
export { default as WaterUsageCalcUpdating } from "./water-balance/WaterUsageCalcRecordsUpdating";

// 表1用水单位取水水源情况表
export { default as WaterSourceRecordsListing } from "./water-balance/WaterSourceRecordsListing";
export { default as WaterSourceRecordsUpdating } from "./water-balance/WaterSourceRecordsUpdating";

// 表2用水单位年用水情况表
// old, will be deprecated
//export { default as WaterUsageRecordsListing } from "./water-balance/WaterUsageRecordsListing";
//export { default as WaterUsageRecordsUpdating } from "./water-balance/WaterUsageRecordsUpdating";
// new
export { default as WaterUsageAnnualRecordsListing } from "./water-balance/WaterUsageAnnualRecordsListing";
export { default as WaterUsageAnnualRecordsUpdating } from "./water-balance/WaterUsageAnnualRecordsUpdating";

// 表8企业近三年生产情况统计表
export { default as ComProdStatsRecordsListing } from "./water-balance/ComProdStatsRecordsListing";
export { default as ComProdStatsRecordsUpdating } from "./water-balance/ComProdStatsRecordsUpdating";

// 服务业： 2.3 运营情况统计表, 表8企业近三年生产情况统计表的服务业版本
export { default as ComOpStatsRecordsListing } from "./water-balance/ComOpStatsRecordsListing";
export { default as ComOpStatsRecordsUpdating } from "./water-balance/ComOpStatsRecordsUpdating";

// 表9用水单位水计量器具配备统计表
export { default as ComWaterMeterEquipStatsRecordsListing } from "./water-balance/ComWaterMeterEquipStatsRecordsListing";
export { default as ComWaterMeterEquipStatsRecordsUpdating } from "./water-balance/ComWaterMeterEquipStatsRecordsUpdating";

// 表10用水单位水计量器具配备情况
export { default as ComWaterMeterEquipRecordsListing } from "./water-balance/ComWaterMeterEquipRecordsListing";
export { default as ComWaterMeterEquipRecordsUpdating } from "./water-balance/ComWaterMeterEquipRecordsUpdating";

// 表11水表原始数据记录表
export { default as WaterMeterSampleRecordsListing } from "./water-balance/WaterMeterSampleRecordsListing";
export { default as WaterMeterSampleRecordsUpdating } from "./water-balance/WaterMeterSampleRecordsUpdating";

// 添加用水单元用水记录(根据基本信息添加多天数据)
export { default as WaterUsageUnitsRecordsAdding } from "./water-balance/WaterUsageUnitsRecordsAdding";

// 主要生产用水
export { default as WaterUsageUnitsFirstRecordsListing } from "./water-balance/WaterUsageUnitsFirstRecordsListing";
export { default as WaterUsageUnitsFirstRecordsUpdating } from "./water-balance/WaterUsageUnitsFirstRecordsUpdating";

// 辅助生产用水
export { default as WaterUsageUnitsSecondRecordsListing } from "./water-balance/WaterUsageUnitsSecondRecordsListing";
export { default as WaterUsageUnitsSecondRecordsUpdating } from "./water-balance/WaterUsageUnitsSecondRecordsUpdating";

// 附属生产用水
export { default as WaterUsageUnitsThirdRecordsListing } from "./water-balance/WaterUsageUnitsThirdRecordsListing";
export { default as WaterUsageUnitsThirdRecordsUpdating } from "./water-balance/WaterUsageUnitsThirdRecordsUpdating";

// 非生产用水
export { default as WaterUsageUnitsFourthRecordsListing } from "./water-balance/WaterUsageUnitsFourthRecordsListing";
export { default as WaterUsageUnitsFourthRecordsUpdating } from "./water-balance/WaterUsageUnitsFourthRecordsUpdating";

// 用水单位水平衡测试总统计表
export { default as WaterBalanceInspectStatsRecordsListing } from "./water-balance/WaterBalanceInspectStatsRecordsListing";
export { default as WaterBalanceInspectStatsRecordsUpdating } from "./water-balance/WaterBalanceInspectStatsRecordsUpdating";
export { default as WaterBalanceInspectStatsRecordsWhole } from "./water-balance/WaterBalanceInspectStatsRecordsWhole";

// 现状用水测试结果
export { default as WaterUsageInspectResultsRecordsListing } from "./water-balance/WaterUsageInspectResultsRecordsListing";
export { default as WaterUsageInspectResultsRecordsUpdating } from "./water-balance/WaterUsageInspectResultsRecordsUpdating";

// 用水单位用水分析表
export { default as WaterUsageInstAnalyzeRecordsListing } from "./water-balance/WaterUsageInstAnalyzeRecordsListing";
//export { default as WaterUsageInstAnalyzeRecordsUpdating } from "./water-balance/WaterUsageInstAnalyzeRecordsUpdating";
export { default as WaterUsageInstAnalyzeRecordsWhole } from "./water-balance/WaterUsageInstAnalyzeRecordsWhole";

// 用水定额参考表
export { default as WaterQuotaInfoRecordsListing } from "./water-balance/WaterQuotaInfoRecordsListing";
export { default as WaterQuotaInfoRecordsUpdating } from "./water-balance/WaterQuotaInfoRecordsUpdating";

// 节水器具配备情况
export { default as WaterSavingEquipsRecordsListing } from "./water-balance/WaterSavingEquipsRecordsListing";
export { default as WaterSavingEquipsRecordsUpdating } from "./water-balance/WaterSavingEquipsRecordsUpdating";
// 节水器具配备情况新版本, 完成后替换上面的老版本
export { default as WaterSavingEquipsRecordsListing2 } from "./water-balance/WaterSavingEquipsRecordsListing2";
export { default as WaterSavingEquipsRecordsUpdating2 } from "./water-balance/WaterSavingEquipsRecordsUpdating2";

// 主要用水设备、设施一览表
export { default as WaterUsingEquipsRecordsListing } from "./water-balance/WaterUsingEquipsRecordsListing";
export { default as WaterUsingEquipsRecordsUpdating } from "./water-balance/WaterUsingEquipsRecordsUpdating";



/**
 * 附件: 画图板
 */

export { default as SketchesListing } from "./SketchesListing";
export { default as SketchingScreen } from "./SketchingScreen";

export { default as SketchingTest } from "./SketchingTest";



/**
 * 零碳诊断
 */

export { default as ZCScreensIndex } from "./zero-carbon/ScreensIndex";

// 单位基本情况表
export { default as ZCBasicInfoRecordsListing } from "./zero-carbon/BasicInfoRecordsListing";
export { default as ZCBasicInfoRecordsUpdating } from "./zero-carbon/BasicInfoRecordsUpdating";

// 用能人数统计表
export { default as ZCEnergyConsumersRecordsListing } from "./zero-carbon/EnergyConsumersRecordsListing";
export { default as ZCEnergyConsumersRecordsUpdating } from "./zero-carbon/EnergyConsumersRecordsUpdating";

// 能源账单表
export { default as ZCEnergyBillsRecordsListing } from "./zero-carbon/EnergyBillsRecordsListing";
export { default as ZCEnergyBillsRecordsUpdating } from "./zero-carbon/EnergyBillsRecordsUpdating";

// 采暖费用信息
export { default as ZCHeatingBillsRecordsListing } from "./zero-carbon/HeatingBillsRecordsListing";
export { default as ZCHeatingBillsRecordsUpdating } from "./zero-carbon/HeatingBillsRecordsUpdating";

// 用能系统基本情况
export { default as EnergySystemInfoRecordsListing } from "./zero-carbon/EnergySystemInfoRecordsListing";
export { default as EnergySystemInfoRecordsUpdating } from "./zero-carbon/EnergySystemInfoRecordsUpdating";
