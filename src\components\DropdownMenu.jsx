import React, { useState } from "react";
import { Menu, IconButton } from "react-native-paper";


const dropdownConfig = [
    {
        title: "Scene animation: none",
        value: 0,
        action: () => console.log("Menu select -> Scene animation: none"),
    },
    {
        title: "Scene animation: shifting",
        value: 1,
        action: () => console.log("Menu select -> Scene animation: shifting"),
    },
    {
        title: "Scene animation: opacity",
        value: 2,
        action: () => console.log("Menu select -> Scene animation: opacity"),
    },
];

const DropdownMenu = ({ dropdownConfig, editable }) => {
    const [menuVisible, setMenuVisible] = useState(false);
    const [selectIndex, setSelectIndex] = useState();
    const DROPDOWN_ICON = "chevron-down";

    return (
        <Menu
            visible={menuVisible}
            onDismiss={() => setMenuVisible(false)}
            anchor={
                <IconButton
                    icon={DROPDOWN_ICON}
                    size={32}
                    disabled={!editable}
                    onPress={() => !editable || setMenuVisible(true)} />
            }
        >
            {dropdownConfig?.map((menuItem, menuIndex) => {
                return (
                    <Menu.Item
                        key={menuIndex}
                        trailingIcon={selectIndex === menuIndex ? "check" : undefined}
                        onPress={() => {
                            menuItem.action();
                            setSelectIndex(menuIndex);
                            setMenuVisible(false);
                        }}
                        title={menuItem.title}
                    />
                );
            })}
        </Menu>
    );
};

export default DropdownMenu;
