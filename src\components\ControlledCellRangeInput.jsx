import React, { useState } from "react";
import { StyleSheet, Pressable } from "react-native";
import { TextInput, useTheme } from "react-native-paper";
import { DialogWithRange } from "./DialogWithRange";
//import { parseServerState } from "../utils/messages";
//import { isArray, isFunction, isObject } from "../utils";
//import log from "../services/logging";
//import PropTypes from "prop-types";


/**
 * 由于CellTextInput的左边有固定的文字标签,
 * 因此在这里, 原本用作TextInput的label的内容, 在此被placeholder取代
 * @param {Object} arg
 * @param {string} arg.title
 * @param {string} arg.placeholder
 * @param {function} arg.onDialogConfirm
 * @param {string} arg.okBtnLabel
 * @param {string} arg.cancelBtnLabel
 * @param {Query} arg.queryClient
 * @param {function} arg.setFetchFailMessage
 * @param {function} arg.setDisplayFailBar
 * @param {Object} arg.dialogState
 * @param {function} arg.setDialogState
 */
const ControlledCellRangeInput = ({ title, placeholder, onDialogConfirm, okBtnLabel, cancelBtnLabel, dialogState, setDialogState, editable = true, ...props }) => {
    const [dialogVisible, setDialogVisible] = useState(false);
    const [nullOptionsTip] = useState(props.defaultNullOptionsTip);

    const theme = useTheme();
    const styles = StyleSheet.create({
        input: {
            width: "100%",
            backgroundColor: theme.colors.surface,
            fontSize: 18,
            //borderWidth: 1,
            //borderColor: "red",
        },
        error: {
            fontSize: 13,
            color: theme.colors.error,
            paddingTop: 8,
        },
    });

    return (
        <>
            <Pressable onPress={() => {
                //if(!props.disabled) {
                if (editable) {
                    setDialogVisible(true);
                }
            }} >
                <TextInput
                    value={dialogState || ""}  // 如果name为假值时就显示空串
                    placeholder={placeholder}
                    editable={false}
                    disabled={!editable}
                    style={styles.input}
                    selectionColor={theme.colors.primary}
                    underlineColor={theme.colors.elevation.level0}
                    outlineColor={theme.colors.elevation.level0} // 透明, 文本外框颜色
                    placeholderTextColor={theme.colors.elevation.level5} // 占位符文字颜色
                    mode={"outlined"}
                    right={editable && <TextInput.Icon icon="chevron-down" onPress={() => {
                        setDialogVisible(true);
                    }} /> }
                    {...props}
                />
            </Pressable>
            <DialogWithRange
                visible={dialogVisible}
                title={title}
                onOK={(rangeStr) => { // rangeStr: "(-1,1)"
                    console.log("rangeInput onOK:", rangeStr);
                    setDialogVisible(false);
                    onDialogConfirm(rangeStr);}}
                onCancel={() => { setDialogVisible(false); }}
                okBtnLabel={okBtnLabel}
                cancelBtnLabel={cancelBtnLabel}
                dialogState={dialogState}
                setDialogState={setDialogState}
                nullOptionsTip={nullOptionsTip}
                {...props}
            />
        </>
    );
};

/*
ControlledCellRadioInputWithQuery.propTypes = {
    title: PropTypes.string,
    value: PropTypes.string,
    //label: PropTypes.string,
    getStoredVal: PropTypes.func,
    placeholder: PropTypes.string,
    onDialogConfirm: PropTypes.func,
    onChangeText: PropTypes.func,
    editable: PropTypes.bool,
    mode: PropTypes.string,
};
*/

export default ControlledCellRangeInput;
