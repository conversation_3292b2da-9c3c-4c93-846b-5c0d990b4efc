import * as React from "react";
import { useFocusEffect } from "@react-navigation/native";

/**
 * src: https://codesandbox.io/p/sandbox/github/tanstack/query/tree/main/examples/react/react-native?file=%2Fsrc%2Fhooks%2FuseRefreshOnFocus.ts%3A7%2C18
 * example: https://www.dhiwise.com/post/how-to-use-the-usefocuseffect-hook-in-react-effectively
 * useCallback: https://react.dev/reference/react/useCallback
 * 为什么这里不能添加`refetch`依赖?
 * A: 如果refetch函数会引起状态改变, 那么状态会导致组件刷新,
 *    如果fetch函数是定义在组件内部, 则每次刷新都会改变这个函数, 都会发生useCallback调用, 从而导致无限渲染.
 *    验证方法: 见refetch定义在组件外部, 则不会无限渲染.
 * @param {() => void} refetch
 */
export function useRefreshOnFocus(refetch) {
    const enabledRef = React.useRef(false);

    useFocusEffect(
        React.useCallback(() => {
            if (enabledRef.current) {
                refetch();
            } else {
                enabledRef.current = true;
            }
        //}, [refetch]), //dependent on refetch will result in infinite renders
        }, []),
    );
}
