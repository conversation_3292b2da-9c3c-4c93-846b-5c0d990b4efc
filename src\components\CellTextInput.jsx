import React, { useState } from "react";
import { StyleSheet } from "react-native";
import { TextInput, useTheme } from "react-native-paper";
import PropTypes from "prop-types";

/**
 * 由于CellTextInput的左边有固定的文字标签,
 * 因此在这里, 原本用作TextInput的label的内容, 在此被placeholder取代
 * @param {Object} arg
 * @param {string} arg.value
 * @param {function} arg.onClearText
 * @param {boolean} arg.disabled
 * @param {"flat" | "outlined"} arg.mode
 */
const CellTextInput = ({ value, placeholder, onClearText, onChangeText, editable = false, mode = "outlined", ...props }) => {
    const [iconColor, setIconColor] = useState("transparent");
    const [labelText, setLabelText] = useState(props.errorText || placeholder);
    const [placeholderText, setPlaceholderText] = useState(placeholder);
    const theme = useTheme();
    const styles = StyleSheet.create({
        input: {
            width: "100%",
            backgroundColor: theme.colors.surface,
            fontSize: 18,
            //borderWidth: 1,
            //borderColor: "red",
        },
        error: {
            fontSize: 13,
            color: theme.colors.error,
            paddingTop: 8,
        },
    });

    return (
        <TextInput
            value={value}
            label={labelText}
            placeholder={placeholderText}
            editable={editable}
            style={styles.input}
            selectionColor={theme.colors.primary}
            underlineColor={theme.colors.elevation.level0}
            outlineColor={theme.colors.elevation.level0} // 透明, 文本外框颜色
            placeholderTextColor={theme.colors.elevation.level5} // 占位符文字颜色
            mode={mode}
            right={onClearText ? <TextInput.Icon icon="close" color={iconColor} onPress={() => {
                onClearText();
                setLabelText(placeholder);
                setIconColor(theme.colors.primary);
            }} /> : undefined}
            onFocus={() => {
                setLabelText(props.errorText || placeholder);
                setIconColor(props.errorText ? theme.colors.error : theme.colors.primary); // display the icon with color
                setPlaceholderText(" "); // display nothing, for prettier display, should be a true value, or it will display the placeholder text
            }}
            onBlur={() => {
                setIconColor("transparent");     // hide the icon
                setPlaceholderText(placeholder); // restore placeholder
                setLabelText("");                // for prettier display
            }}
            onChangeText={(text) => onChangeText({value: text, error: ""})}
            {...props}
        />
    );
};

CellTextInput.propTypes = {
    value: PropTypes.string,
    //label: PropTypes.string,
    placeholder: PropTypes.string,
    onClearText: PropTypes.func,
    onChangeText: PropTypes.func,
    editable: PropTypes.bool,
    mode: PropTypes.string,
};

export default CellTextInput;
