///
/// JFunc_void_RecordBackType.hpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc R<PERSON>av<PERSON> @ Margelo
///

#pragma once

#include <fbjni/fbjni.h>
#include <functional>

#include <functional>
#include "RecordBackType.hpp"
#include "JRecordBackType.hpp"
#include <optional>

namespace margelo::nitro::react_native_audio_recorder_player {

  using namespace facebook;

  /**
   * Represents the Java/Kotlin callback `(recordingMeta: RecordBackType) -> Unit`.
   * This can be passed around between C++ and Java/Kotlin.
   */
  struct JFunc_void_RecordBackType: public jni::JavaClass<JFunc_void_RecordBackType> {
  public:
    static auto constexpr kJavaDescriptor = "Lcom/margelo/nitro/reactnativeaudiorecorderplayer/Func_void_RecordBackType;";

  public:
    /**
     * Invokes the function this `JFunc_void_RecordBackType` instance holds through JNI.
     */
    void invoke(const RecordBackType& recordingMeta) const {
      static const auto method = javaClassStatic()->getMethod<void(jni::alias_ref<JRecordBackType> /* recordingMeta */)>("invoke");
      method(self(), JRecordBackType::fromCpp(recordingMeta));
    }
  };

  /**
   * An implementation of Func_void_RecordBackType that is backed by a C++ implementation (using `std::function<...>`)
   */
  struct JFunc_void_RecordBackType_cxx final: public jni::HybridClass<JFunc_void_RecordBackType_cxx, JFunc_void_RecordBackType> {
  public:
    static jni::local_ref<JFunc_void_RecordBackType::javaobject> fromCpp(const std::function<void(const RecordBackType& /* recordingMeta */)>& func) {
      return JFunc_void_RecordBackType_cxx::newObjectCxxArgs(func);
    }

  public:
    /**
     * Invokes the C++ `std::function<...>` this `JFunc_void_RecordBackType_cxx` instance holds.
     */
    void invoke_cxx(jni::alias_ref<JRecordBackType> recordingMeta) {
      _func(recordingMeta->toCpp());
    }

  public:
    [[nodiscard]]
    inline const std::function<void(const RecordBackType& /* recordingMeta */)>& getFunction() const {
      return _func;
    }

  public:
    static auto constexpr kJavaDescriptor = "Lcom/margelo/nitro/reactnativeaudiorecorderplayer/Func_void_RecordBackType_cxx;";
    static void registerNatives() {
      registerHybrid({makeNativeMethod("invoke_cxx", JFunc_void_RecordBackType_cxx::invoke_cxx)});
    }

  private:
    explicit JFunc_void_RecordBackType_cxx(const std::function<void(const RecordBackType& /* recordingMeta */)>& func): _func(func) { }

  private:
    friend HybridBase;
    std::function<void(const RecordBackType& /* recordingMeta */)> _func;
  };

} // namespace margelo::nitro::react_native_audio_recorder_player
