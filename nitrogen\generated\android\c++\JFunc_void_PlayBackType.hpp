///
/// JFunc_void_PlayBackType.hpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc R<PERSON>avy @ Margelo
///

#pragma once

#include <fbjni/fbjni.h>
#include <functional>

#include <functional>
#include "PlayBackType.hpp"
#include "JPlayBackType.hpp"
#include <optional>

namespace margelo::nitro::react_native_audio_recorder_player {

  using namespace facebook;

  /**
   * Represents the Java/Kotlin callback `(playbackMeta: PlayBackType) -> Unit`.
   * This can be passed around between C++ and Java/Kotlin.
   */
  struct JFunc_void_PlayBackType: public jni::JavaClass<JFunc_void_PlayBackType> {
  public:
    static auto constexpr kJavaDescriptor = "Lcom/margelo/nitro/reactnativeaudiorecorderplayer/Func_void_PlayBackType;";

  public:
    /**
     * Invokes the function this `JFunc_void_PlayBackType` instance holds through JNI.
     */
    void invoke(const PlayBackType& playbackMeta) const {
      static const auto method = javaClassStatic()->getMethod<void(jni::alias_ref<JPlayBackType> /* playbackMeta */)>("invoke");
      method(self(), JPlayBackType::fromCpp(playbackMeta));
    }
  };

  /**
   * An implementation of Func_void_PlayBackType that is backed by a C++ implementation (using `std::function<...>`)
   */
  struct JFunc_void_PlayBackType_cxx final: public jni::HybridClass<JFunc_void_PlayBackType_cxx, JFunc_void_PlayBackType> {
  public:
    static jni::local_ref<JFunc_void_PlayBackType::javaobject> fromCpp(const std::function<void(const PlayBackType& /* playbackMeta */)>& func) {
      return JFunc_void_PlayBackType_cxx::newObjectCxxArgs(func);
    }

  public:
    /**
     * Invokes the C++ `std::function<...>` this `JFunc_void_PlayBackType_cxx` instance holds.
     */
    void invoke_cxx(jni::alias_ref<JPlayBackType> playbackMeta) {
      _func(playbackMeta->toCpp());
    }

  public:
    [[nodiscard]]
    inline const std::function<void(const PlayBackType& /* playbackMeta */)>& getFunction() const {
      return _func;
    }

  public:
    static auto constexpr kJavaDescriptor = "Lcom/margelo/nitro/reactnativeaudiorecorderplayer/Func_void_PlayBackType_cxx;";
    static void registerNatives() {
      registerHybrid({makeNativeMethod("invoke_cxx", JFunc_void_PlayBackType_cxx::invoke_cxx)});
    }

  private:
    explicit JFunc_void_PlayBackType_cxx(const std::function<void(const PlayBackType& /* playbackMeta */)>& func): _func(func) { }

  private:
    friend HybridBase;
    std::function<void(const PlayBackType& /* playbackMeta */)> _func;
  };

} // namespace margelo::nitro::react_native_audio_recorder_player
