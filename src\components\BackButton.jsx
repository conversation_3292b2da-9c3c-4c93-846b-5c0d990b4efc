import React from "react";
import { IconButton } from "react-native-paper";
import { TouchableOpacity, StyleSheet } from "react-native";
import { getStatusBarHeight } from "react-native-status-bar-height";
import PropTypes from "prop-types";

const BackButton = ({ goBack }) => {
    return (
        <TouchableOpacity onPress={goBack} style={styles.container}>
            <IconButton icon="arrow-left" size={styles.size} />
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    container: {
        position: "absolute",
        top: 10 + getStatusBarHeight(),
        left: 4,
    },
    size: 28,
});

BackButton.propTypes = {
    goBack: PropTypes.func.isRequired,
};


export default BackButton;
