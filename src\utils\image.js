/**
 * 计算图像在画布中的布局和变换参数, 用于Skia的Group组件实现旋转和缩放, 以及Skia的Image组件的属性参数.
 * JsDoc by: Trae, DeepSeek-V3-0324
 *
 * @param {number} imageWidth - 原始图像的宽度(像素)
 * @param {number} imageHeight - 原始图像的高度(像素)
 * @param {number} canvasWidth - 画布的宽度(像素)
 * @param {number|string} [canvasHeight="auto"] - 画布的高度(像素)，默认为"auto"表示根据图像宽高比自动计算
 *
 * @returns {Object} 包含布局和画布高度的对象
 * @property {Object} layout - 图像的布局和变换参数
 * @property {number} layout.x - 图像在画布中的x坐标(像素)
 * @property {number} layout.y - 图像在画布中的y坐标(像素)
 * @property {number} [layout.translateX] - 旋转时的中心点x坐标(仅旋转时存在)
 * @property {number} [layout.translateY] - 旋转时的中心点y坐标(仅旋转时存在)
 * @property {number} [layout.rotate] - 旋转角度(弧度，仅旋转时存在)
 * @property {number} [layout.scale] - 缩放比例(仅旋转时存在)
 * @property {number} layout.width - 图像在画布中的显示宽度(像素)
 * @property {number} layout.height - 图像在画布中的显示高度(像素)
 * @property {number} canvasHeight - 计算得出的画布高度(像素)
 */
export const layoutTransform = (imageWidth, imageHeight, canvasWidth, canvasHeight = "auto") => {

    const imageAspectRatio = imageWidth / imageHeight;
    const canvasSize = {
        width: canvasWidth,
        height: (canvasHeight === "auto") ? (imageAspectRatio >= 1 ? canvasWidth / imageAspectRatio : canvasWidth * imageAspectRatio) : canvasHeight,
    };

    const isImageHorizontal = imageWidth > imageHeight; // 判断图片方向
    const isCanvasHorizontal = canvasSize.width > canvasSize.height; // 判断画布方向

    // 需要旋转的情况
    if (isImageHorizontal !== isCanvasHorizontal) {
        // Skia使用Group组件实现旋转
        const scale = isImageHorizontal
            ? (Math.min(canvasSize.width / imageHeight, canvasSize.height / imageWidth))   // 横图在竖屏上显示，旋转后以高度为基准
            : (Math.min(canvasSize.width / imageHeight, canvasSize.height / imageWidth));  // 竖图在横屏上显示，旋转后以宽度为基准
        // 计算居中位置
        const centerX = canvasSize.width / 2;
        const centerY = canvasSize.height / 2;

        return({
            layout:{
                translateX: centerX,
                translateY: centerY,
                rotate:  Math.PI / 2,
                scale: scale,
                x: -imageWidth / 2,
                y: -imageHeight / 2,
                width: imageWidth,
                height: imageHeight
            },
            canvasHeight: canvasSize.height,
        });
    } else {
        // 图片和画布方向一致，不需要旋转
        const canvasRatio = canvasSize.width / canvasSize.height;
        const imageRatio = imageWidth / imageHeight;
        const scale = (canvasRatio > imageRatio) ? canvasSize.height / imageHeight : canvasSize.width / imageWidth;

        // 计算居中位置
        const x = (canvasSize.width - imageWidth * scale) / 2;
        const y = (canvasSize.height - imageHeight * scale) / 2;

        return({
            layout:{
                x: x,
                y: y,
                width: imageWidth * scale,
                height: imageHeight * scale,
            },
            canvasHeight: canvasSize.height,
        });
    }
};
