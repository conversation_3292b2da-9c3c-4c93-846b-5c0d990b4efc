///
/// HybridAudioRecorderPlayerSpec_cxx.swift
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc Rousavy @ Margelo
///

import Foundation
import NitroModules

/**
 * A class implementation that bridges HybridAudioRecorderPlayerSpec over to C++.
 * In C++, we cannot use Swift protocols - so we need to wrap it in a class to make it strongly defined.
 *
 * Also, some Swift types need to be bridged with special handling:
 * - Enums need to be wrapped in Structs, otherwise they cannot be accessed bi-directionally (Swift bug: https://github.com/swiftlang/swift/issues/75330)
 * - Other HybridObjects need to be wrapped/unwrapped from the Swift TCxx wrapper
 * - Throwing methods need to be wrapped with a Result<T, Error> type, as exceptions cannot be propagated to C++
 */
public class HybridAudioRecorderPlayerSpec_cxx {
  /**
   * The Swift <> C++ bridge's namespace (`margelo::nitro::react_native_audio_recorder_player::bridge::swift`)
   * from `ReactNativeAudioRecorderPlayer-Swift-Cxx-Bridge.hpp`.
   * This contains specialized C++ templates, and C++ helper functions that can be accessed from Swift.
   */
  public typealias bridge = margelo.nitro.react_native_audio_recorder_player.bridge.swift

  /**
   * Holds an instance of the `HybridAudioRecorderPlayerSpec` Swift protocol.
   */
  private var __implementation: any HybridAudioRecorderPlayerSpec

  /**
   * Holds a weak pointer to the C++ class that wraps the Swift class.
   */
  private var __cxxPart: bridge.std__weak_ptr_margelo__nitro__react_native_audio_recorder_player__HybridAudioRecorderPlayerSpec_

  /**
   * Create a new `HybridAudioRecorderPlayerSpec_cxx` that wraps the given `HybridAudioRecorderPlayerSpec`.
   * All properties and methods bridge to C++ types.
   */
  public init(_ implementation: any HybridAudioRecorderPlayerSpec) {
    self.__implementation = implementation
    self.__cxxPart = .init()
    /* no base class */
  }

  /**
   * Get the actual `HybridAudioRecorderPlayerSpec` instance this class wraps.
   */
  @inline(__always)
  public func getHybridAudioRecorderPlayerSpec() -> any HybridAudioRecorderPlayerSpec {
    return __implementation
  }

  /**
   * Casts this instance to a retained unsafe raw pointer.
   * This acquires one additional strong reference on the object!
   */
  public func toUnsafe() -> UnsafeMutableRawPointer {
    return Unmanaged.passRetained(self).toOpaque()
  }

  /**
   * Casts an unsafe pointer to a `HybridAudioRecorderPlayerSpec_cxx`.
   * The pointer has to be a retained opaque `Unmanaged<HybridAudioRecorderPlayerSpec_cxx>`.
   * This removes one strong reference from the object!
   */
  public class func fromUnsafe(_ pointer: UnsafeMutableRawPointer) -> HybridAudioRecorderPlayerSpec_cxx {
    return Unmanaged<HybridAudioRecorderPlayerSpec_cxx>.fromOpaque(pointer).takeRetainedValue()
  }

  /**
   * Gets (or creates) the C++ part of this Hybrid Object.
   * The C++ part is a `std::shared_ptr<margelo::nitro::react_native_audio_recorder_player::HybridAudioRecorderPlayerSpec>`.
   */
  public func getCxxPart() -> bridge.std__shared_ptr_margelo__nitro__react_native_audio_recorder_player__HybridAudioRecorderPlayerSpec_ {
    let cachedCxxPart = self.__cxxPart.lock()
    if cachedCxxPart.__convertToBool() {
      return cachedCxxPart
    } else {
      let newCxxPart = bridge.create_std__shared_ptr_margelo__nitro__react_native_audio_recorder_player__HybridAudioRecorderPlayerSpec_(self.toUnsafe())
      __cxxPart = bridge.weakify_std__shared_ptr_margelo__nitro__react_native_audio_recorder_player__HybridAudioRecorderPlayerSpec_(newCxxPart)
      return newCxxPart
    }
  }

  

  /**
   * Get the memory size of the Swift class (plus size of any other allocations)
   * so the JS VM can properly track it and garbage-collect the JS object if needed.
   */
  @inline(__always)
  public var memorySize: Int {
    return MemoryHelper.getSizeOf(self.__implementation) + self.__implementation.memorySize
  }

  // Properties
  

  // Methods
  @inline(__always)
  public final func startRecorder(uri: bridge.std__optional_std__string_, audioSets: bridge.std__optional_AudioSet_, meteringEnabled: bridge.std__optional_bool_) -> bridge.Result_std__shared_ptr_Promise_std__string___ {
    do {
      let __result = try self.__implementation.startRecorder(uri: { () -> String? in
        if let __unwrapped = uri.value {
          return String(__unwrapped)
        } else {
          return nil
        }
      }(), audioSets: { () -> AudioSet? in
        if let __unwrapped = audioSets.value {
          return __unwrapped
        } else {
          return nil
        }
      }(), meteringEnabled: meteringEnabled.value)
      let __resultCpp = { () -> bridge.std__shared_ptr_Promise_std__string__ in
        let __promise = bridge.create_std__shared_ptr_Promise_std__string__()
        let __promiseHolder = bridge.wrap_std__shared_ptr_Promise_std__string__(__promise)
        __result
          .then({ __result in __promiseHolder.resolve(std.string(__result)) })
          .catch({ __error in __promiseHolder.reject(__error.toCpp()) })
        return __promise
      }()
      return bridge.create_Result_std__shared_ptr_Promise_std__string___(__resultCpp)
    } catch (let __error) {
      let __exceptionPtr = __error.toCpp()
      return bridge.create_Result_std__shared_ptr_Promise_std__string___(__exceptionPtr)
    }
  }
  
  @inline(__always)
  public final func pauseRecorder() -> bridge.Result_std__shared_ptr_Promise_std__string___ {
    do {
      let __result = try self.__implementation.pauseRecorder()
      let __resultCpp = { () -> bridge.std__shared_ptr_Promise_std__string__ in
        let __promise = bridge.create_std__shared_ptr_Promise_std__string__()
        let __promiseHolder = bridge.wrap_std__shared_ptr_Promise_std__string__(__promise)
        __result
          .then({ __result in __promiseHolder.resolve(std.string(__result)) })
          .catch({ __error in __promiseHolder.reject(__error.toCpp()) })
        return __promise
      }()
      return bridge.create_Result_std__shared_ptr_Promise_std__string___(__resultCpp)
    } catch (let __error) {
      let __exceptionPtr = __error.toCpp()
      return bridge.create_Result_std__shared_ptr_Promise_std__string___(__exceptionPtr)
    }
  }
  
  @inline(__always)
  public final func resumeRecorder() -> bridge.Result_std__shared_ptr_Promise_std__string___ {
    do {
      let __result = try self.__implementation.resumeRecorder()
      let __resultCpp = { () -> bridge.std__shared_ptr_Promise_std__string__ in
        let __promise = bridge.create_std__shared_ptr_Promise_std__string__()
        let __promiseHolder = bridge.wrap_std__shared_ptr_Promise_std__string__(__promise)
        __result
          .then({ __result in __promiseHolder.resolve(std.string(__result)) })
          .catch({ __error in __promiseHolder.reject(__error.toCpp()) })
        return __promise
      }()
      return bridge.create_Result_std__shared_ptr_Promise_std__string___(__resultCpp)
    } catch (let __error) {
      let __exceptionPtr = __error.toCpp()
      return bridge.create_Result_std__shared_ptr_Promise_std__string___(__exceptionPtr)
    }
  }
  
  @inline(__always)
  public final func stopRecorder() -> bridge.Result_std__shared_ptr_Promise_std__string___ {
    do {
      let __result = try self.__implementation.stopRecorder()
      let __resultCpp = { () -> bridge.std__shared_ptr_Promise_std__string__ in
        let __promise = bridge.create_std__shared_ptr_Promise_std__string__()
        let __promiseHolder = bridge.wrap_std__shared_ptr_Promise_std__string__(__promise)
        __result
          .then({ __result in __promiseHolder.resolve(std.string(__result)) })
          .catch({ __error in __promiseHolder.reject(__error.toCpp()) })
        return __promise
      }()
      return bridge.create_Result_std__shared_ptr_Promise_std__string___(__resultCpp)
    } catch (let __error) {
      let __exceptionPtr = __error.toCpp()
      return bridge.create_Result_std__shared_ptr_Promise_std__string___(__exceptionPtr)
    }
  }
  
  @inline(__always)
  public final func startPlayer(uri: bridge.std__optional_std__string_, httpHeaders: bridge.std__optional_std__unordered_map_std__string__std__string__) -> bridge.Result_std__shared_ptr_Promise_std__string___ {
    do {
      let __result = try self.__implementation.startPlayer(uri: { () -> String? in
        if let __unwrapped = uri.value {
          return String(__unwrapped)
        } else {
          return nil
        }
      }(), httpHeaders: { () -> Dictionary<String, String>? in
        if let __unwrapped = httpHeaders.value {
          return { () -> Dictionary<String, String> in
            var __dictionary = Dictionary<String, String>(minimumCapacity: __unwrapped.size())
            let __keys = bridge.get_std__unordered_map_std__string__std__string__keys(__unwrapped)
            for __key in __keys {
              let __value = __unwrapped[__key]!
              __dictionary[String(__key)] = String(__value)
            }
            return __dictionary
          }()
        } else {
          return nil
        }
      }())
      let __resultCpp = { () -> bridge.std__shared_ptr_Promise_std__string__ in
        let __promise = bridge.create_std__shared_ptr_Promise_std__string__()
        let __promiseHolder = bridge.wrap_std__shared_ptr_Promise_std__string__(__promise)
        __result
          .then({ __result in __promiseHolder.resolve(std.string(__result)) })
          .catch({ __error in __promiseHolder.reject(__error.toCpp()) })
        return __promise
      }()
      return bridge.create_Result_std__shared_ptr_Promise_std__string___(__resultCpp)
    } catch (let __error) {
      let __exceptionPtr = __error.toCpp()
      return bridge.create_Result_std__shared_ptr_Promise_std__string___(__exceptionPtr)
    }
  }
  
  @inline(__always)
  public final func stopPlayer() -> bridge.Result_std__shared_ptr_Promise_std__string___ {
    do {
      let __result = try self.__implementation.stopPlayer()
      let __resultCpp = { () -> bridge.std__shared_ptr_Promise_std__string__ in
        let __promise = bridge.create_std__shared_ptr_Promise_std__string__()
        let __promiseHolder = bridge.wrap_std__shared_ptr_Promise_std__string__(__promise)
        __result
          .then({ __result in __promiseHolder.resolve(std.string(__result)) })
          .catch({ __error in __promiseHolder.reject(__error.toCpp()) })
        return __promise
      }()
      return bridge.create_Result_std__shared_ptr_Promise_std__string___(__resultCpp)
    } catch (let __error) {
      let __exceptionPtr = __error.toCpp()
      return bridge.create_Result_std__shared_ptr_Promise_std__string___(__exceptionPtr)
    }
  }
  
  @inline(__always)
  public final func pausePlayer() -> bridge.Result_std__shared_ptr_Promise_std__string___ {
    do {
      let __result = try self.__implementation.pausePlayer()
      let __resultCpp = { () -> bridge.std__shared_ptr_Promise_std__string__ in
        let __promise = bridge.create_std__shared_ptr_Promise_std__string__()
        let __promiseHolder = bridge.wrap_std__shared_ptr_Promise_std__string__(__promise)
        __result
          .then({ __result in __promiseHolder.resolve(std.string(__result)) })
          .catch({ __error in __promiseHolder.reject(__error.toCpp()) })
        return __promise
      }()
      return bridge.create_Result_std__shared_ptr_Promise_std__string___(__resultCpp)
    } catch (let __error) {
      let __exceptionPtr = __error.toCpp()
      return bridge.create_Result_std__shared_ptr_Promise_std__string___(__exceptionPtr)
    }
  }
  
  @inline(__always)
  public final func resumePlayer() -> bridge.Result_std__shared_ptr_Promise_std__string___ {
    do {
      let __result = try self.__implementation.resumePlayer()
      let __resultCpp = { () -> bridge.std__shared_ptr_Promise_std__string__ in
        let __promise = bridge.create_std__shared_ptr_Promise_std__string__()
        let __promiseHolder = bridge.wrap_std__shared_ptr_Promise_std__string__(__promise)
        __result
          .then({ __result in __promiseHolder.resolve(std.string(__result)) })
          .catch({ __error in __promiseHolder.reject(__error.toCpp()) })
        return __promise
      }()
      return bridge.create_Result_std__shared_ptr_Promise_std__string___(__resultCpp)
    } catch (let __error) {
      let __exceptionPtr = __error.toCpp()
      return bridge.create_Result_std__shared_ptr_Promise_std__string___(__exceptionPtr)
    }
  }
  
  @inline(__always)
  public final func seekToPlayer(time: Double) -> bridge.Result_std__shared_ptr_Promise_std__string___ {
    do {
      let __result = try self.__implementation.seekToPlayer(time: time)
      let __resultCpp = { () -> bridge.std__shared_ptr_Promise_std__string__ in
        let __promise = bridge.create_std__shared_ptr_Promise_std__string__()
        let __promiseHolder = bridge.wrap_std__shared_ptr_Promise_std__string__(__promise)
        __result
          .then({ __result in __promiseHolder.resolve(std.string(__result)) })
          .catch({ __error in __promiseHolder.reject(__error.toCpp()) })
        return __promise
      }()
      return bridge.create_Result_std__shared_ptr_Promise_std__string___(__resultCpp)
    } catch (let __error) {
      let __exceptionPtr = __error.toCpp()
      return bridge.create_Result_std__shared_ptr_Promise_std__string___(__exceptionPtr)
    }
  }
  
  @inline(__always)
  public final func setVolume(volume: Double) -> bridge.Result_std__shared_ptr_Promise_std__string___ {
    do {
      let __result = try self.__implementation.setVolume(volume: volume)
      let __resultCpp = { () -> bridge.std__shared_ptr_Promise_std__string__ in
        let __promise = bridge.create_std__shared_ptr_Promise_std__string__()
        let __promiseHolder = bridge.wrap_std__shared_ptr_Promise_std__string__(__promise)
        __result
          .then({ __result in __promiseHolder.resolve(std.string(__result)) })
          .catch({ __error in __promiseHolder.reject(__error.toCpp()) })
        return __promise
      }()
      return bridge.create_Result_std__shared_ptr_Promise_std__string___(__resultCpp)
    } catch (let __error) {
      let __exceptionPtr = __error.toCpp()
      return bridge.create_Result_std__shared_ptr_Promise_std__string___(__exceptionPtr)
    }
  }
  
  @inline(__always)
  public final func setPlaybackSpeed(playbackSpeed: Double) -> bridge.Result_std__shared_ptr_Promise_std__string___ {
    do {
      let __result = try self.__implementation.setPlaybackSpeed(playbackSpeed: playbackSpeed)
      let __resultCpp = { () -> bridge.std__shared_ptr_Promise_std__string__ in
        let __promise = bridge.create_std__shared_ptr_Promise_std__string__()
        let __promiseHolder = bridge.wrap_std__shared_ptr_Promise_std__string__(__promise)
        __result
          .then({ __result in __promiseHolder.resolve(std.string(__result)) })
          .catch({ __error in __promiseHolder.reject(__error.toCpp()) })
        return __promise
      }()
      return bridge.create_Result_std__shared_ptr_Promise_std__string___(__resultCpp)
    } catch (let __error) {
      let __exceptionPtr = __error.toCpp()
      return bridge.create_Result_std__shared_ptr_Promise_std__string___(__exceptionPtr)
    }
  }
  
  @inline(__always)
  public final func setSubscriptionDuration(sec: Double) -> bridge.Result_void_ {
    do {
      try self.__implementation.setSubscriptionDuration(sec: sec)
      return bridge.create_Result_void_()
    } catch (let __error) {
      let __exceptionPtr = __error.toCpp()
      return bridge.create_Result_void_(__exceptionPtr)
    }
  }
  
  @inline(__always)
  public final func addRecordBackListener(callback: bridge.Func_void_RecordBackType) -> bridge.Result_void_ {
    do {
      try self.__implementation.addRecordBackListener(callback: { () -> (RecordBackType) -> Void in
        let __wrappedFunction = bridge.wrap_Func_void_RecordBackType(callback)
        return { (__recordingMeta: RecordBackType) -> Void in
          __wrappedFunction.call(__recordingMeta)
        }
      }())
      return bridge.create_Result_void_()
    } catch (let __error) {
      let __exceptionPtr = __error.toCpp()
      return bridge.create_Result_void_(__exceptionPtr)
    }
  }
  
  @inline(__always)
  public final func removeRecordBackListener() -> bridge.Result_void_ {
    do {
      try self.__implementation.removeRecordBackListener()
      return bridge.create_Result_void_()
    } catch (let __error) {
      let __exceptionPtr = __error.toCpp()
      return bridge.create_Result_void_(__exceptionPtr)
    }
  }
  
  @inline(__always)
  public final func addPlayBackListener(callback: bridge.Func_void_PlayBackType) -> bridge.Result_void_ {
    do {
      try self.__implementation.addPlayBackListener(callback: { () -> (PlayBackType) -> Void in
        let __wrappedFunction = bridge.wrap_Func_void_PlayBackType(callback)
        return { (__playbackMeta: PlayBackType) -> Void in
          __wrappedFunction.call(__playbackMeta)
        }
      }())
      return bridge.create_Result_void_()
    } catch (let __error) {
      let __exceptionPtr = __error.toCpp()
      return bridge.create_Result_void_(__exceptionPtr)
    }
  }
  
  @inline(__always)
  public final func removePlayBackListener() -> bridge.Result_void_ {
    do {
      try self.__implementation.removePlayBackListener()
      return bridge.create_Result_void_()
    } catch (let __error) {
      let __exceptionPtr = __error.toCpp()
      return bridge.create_Result_void_(__exceptionPtr)
    }
  }
  
  @inline(__always)
  public final func mmss(secs: Double) -> bridge.Result_std__string_ {
    do {
      let __result = try self.__implementation.mmss(secs: secs)
      let __resultCpp = std.string(__result)
      return bridge.create_Result_std__string_(__resultCpp)
    } catch (let __error) {
      let __exceptionPtr = __error.toCpp()
      return bridge.create_Result_std__string_(__exceptionPtr)
    }
  }
  
  @inline(__always)
  public final func mmssss(milisecs: Double) -> bridge.Result_std__string_ {
    do {
      let __result = try self.__implementation.mmssss(milisecs: milisecs)
      let __resultCpp = std.string(__result)
      return bridge.create_Result_std__string_(__resultCpp)
    } catch (let __error) {
      let __exceptionPtr = __error.toCpp()
      return bridge.create_Result_std__string_(__exceptionPtr)
    }
  }
}
