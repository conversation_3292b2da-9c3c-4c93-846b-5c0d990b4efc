import React, { useState } from "react";
import { Pressable, StyleSheet } from "react-native";
import { TextInput, useTheme } from "react-native-paper";
import log from "../services/logging";
import { isArray, isFunction, isObject } from "../utils";
import { parseServerState } from "../utils/messages";
import { DialogWithRadioButton } from "./DialogWithRadioButton";
//import PropTypes from "prop-types";


/**
 * 由于CellTextInput的左边有固定的文字标签,
 * 因此在这里, 原本用作TextInput的label的内容, 在此被placeholder取代
 * @param {Object} arg
 * @param {string} arg.title
 * @param {string} arg.placeholder
 * @param {function} arg.onDialogConfirm
 * @param {string} arg.okBtnLabel
 * @param {string} arg.cancelBtnLabel
 * @param {Query} arg.queryClient
 * @param {function} arg.setFetchFailMessage
 * @param {function} arg.setDisplayFailBar
 * @param {Object} arg.dialogState
 * @param {function} arg.setDialogState
 */
const ControlledCellRadioInputWithQuery = ({ title, placeholder, onDialogConfirm, okBtnLabel, cancelBtnLabel, dataProvider, setFetchFailMessage, setDisplayFailBar, dialogState, setDialogState, editable = true, ...props }) => {
    const [dialogVisible, setDialogVisible] = useState(false);
    const [nullOptionsTip] = useState(props.defaultNullOptionsTip);

    const theme = useTheme();
    const styles = StyleSheet.create({
        input: {
            width: "100%",
            backgroundColor: theme.colors.surface,
            fontSize: 18,
            //borderWidth: 1,
            //borderColor: "red",
        },
        error: {
            fontSize: 13,
            color: theme.colors.error,
            paddingTop: 8,
        },
    });

    const [dialogItemArray, setDialogItemArray] = useState([]);

    const onFetchSuccess = (data) => {
        if (data.STATUS === 0) {
            console.log("data.DATA", data.DATA);
            setDialogItemArray(data.DATA);
        } else {
            setFetchFailMessage(parseServerState(data.STATUS || data.status, data.DATA.info));
            setDisplayFailBar(true);
        }
    };
    // if dataProvider is a function, it should be a query
    const dialogDataQuery = isFunction(dataProvider) ? dataProvider(onFetchSuccess) : undefined;

    /**
     * dataProvider can be an array with right struct ([{id, name}*]),
     * or a function that returns an array with that struct,
     * or an object (such as useRef) whose "current" value is an array with that struct.
    */
    const setupDataProviderOtherThanQuery = () => {
        if (isArray(dataProvider)) {
            setDialogItemArray(dataProvider);
        } else if (isFunction(dataProvider)) {
            setDialogItemArray(dataProvider());
        } else if (isObject(dataProvider)) {
            if (dataProvider.current) {
                setDialogItemArray(dataProvider.current);
            } else {
                log.error("Not support this object of dataProvider: %s", dataProvider);
            }
        } else {
            log.error("Not support this type of dataProvider: %s", dataProvider);
        }
    };

    const onInputPress = () => {
        //dialogDataQuery ? dialogDataQuery?.mutate() : setupDataProviderOtherThanQuery();
        if (dialogDataQuery) {
            if (dialogDataQuery.mutate) {
                dialogDataQuery.mutate();
            } else {
                setupDataProviderOtherThanQuery();
            }
        } else {
            setupDataProviderOtherThanQuery();
        }
        setDialogVisible(true);
    };

    return (
        <>
            <Pressable onPress={() => {
                //if(!props.disabled) {
                if (editable || (props.disabled !== undefined && !props.disabled)) {
                    //dialogDataQuery ? dialogDataQuery?.mutate() : setupDataProviderOtherThanQuery();
                    //setDialogVisible(true);
                    onInputPress();
                }
            }} >
                <TextInput
                    value={dialogState?.name || ""}  // 如果name为假值时就显示空串
                    placeholder={placeholder}
                    editable={false}
                    disabled={!editable}
                    style={styles.input}
                    selectionColor={theme.colors.primary}
                    underlineColor={theme.colors.elevation.level0}
                    outlineColor={theme.colors.elevation.level0} // 透明, 文本外框颜色
                    placeholderTextColor={theme.colors.elevation.level5} // 占位符文字颜色
                    mode={"outlined"}
                    right={editable && <TextInput.Icon icon="chevron-down" onPress={() => {
                        //dialogDataQuery ? dialogDataQuery?.mutate() : setupDataProviderOtherThanQuery();
                        //setDialogVisible(true);
                        onInputPress();
                    }} /> }
                    {...props}
                />
            </Pressable>
            <DialogWithRadioButton
                visible={dialogVisible}
                title={title}
                onOK={(checkedData) => { // checkedData: {"id": 2, "name": "DEPT-02"}
                    console.log("radioButton onOK:", checkedData);
                    setDialogVisible(false);
                    onDialogConfirm(checkedData);}}
                onCancel={() => { setDialogVisible(false); }}
                okBtnLabel={okBtnLabel}
                cancelBtnLabel={cancelBtnLabel}
                dialogState={dialogState}
                setDialogState={setDialogState}
                dialogItemArray={dialogItemArray}
                nullOptionsTip={nullOptionsTip}
                {...props}
            />
        </>
    );
};

/*
ControlledCellRadioInputWithQuery.propTypes = {
    title: PropTypes.string,
    value: PropTypes.string,
    //label: PropTypes.string,
    getStoredVal: PropTypes.func,
    placeholder: PropTypes.string,
    onDialogConfirm: PropTypes.func,
    onChangeText: PropTypes.func,
    editable: PropTypes.bool,
    mode: PropTypes.string,
};
*/

export default ControlledCellRadioInputWithQuery;
