import React, { useState, useRef, useEffect } from "react";
import { Divider, Appbar, useTheme, Snackbar } from "react-native-paper";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { StyleSheet, View } from "react-native";
import { useForm } from "react-hook-form";
import { joiResolver } from "@hookform/resolvers/joi";
import "fast-text-encoding";
import <PERSON><PERSON> from "joi";
import HeaderBar from "../../components/HeaderBar";
import ScreenWrapper from "../ScreenWrapper";
import BottomBarButton from "../../components/BottomBarButton";
import { DialogToConfirm } from "../../components/DialogToConfirm";
import ControlledTextInput from "../../components/ControlledTextInput";
//import ControlledRadioInputWithQuery from "../../components/ControlledRadioInputWithQuery";
//import ControlledCheckboxInputWithQuery from "../../components/ControlledCheckboxInputWithQuery";

import { creatMMKVStore } from "../../services/local-storage";
import { whenLet, identity, makeDataFeeder } from "../../utils";
import { validatorBase } from "../../utils/validatorBase";

// 新组件需要重新!!
import { ORG_CLIENT_ADD as pageMainKey } from "../../config/keysConfig"; // 用于页面信息的提交
import { makeRequestInsertClient as insertQueryClient } from "../../api/insertingQueries";
//import { makeRequestListingDepartments as allDeptsQueryClient } from "../../api/listingQueries";
//import { makeRequestListingUsers       as allUsersQueryClient } from "../../api/listingQueries";
import { clientInsertStates as selectorStates } from "../../hooks/selectorStates";
import { useShallow } from "zustand/shallow";
import { onPreSubmitError } from "../../utils/screens";


// 在mmkv中存储所有控件的数据, 新组件需要重新设置
const { setStore, getStore, clearStore, setStoreObject } = creatMMKVStore(pageMainKey.store);
const dataFeeder = makeDataFeeder();
// 数据规范要求服务端数据包含id为0的默认值, 同时mmkv存储的是一个对象, 因此以下几行不再需要
//const superiorReplacer = makeReplacer("superior", "name", "id"); // 数据中superior对应的值, 将它到对象数组中去匹配name键, 若成功, superior的值替换成name所在对象的id的值
//const deptDefaults = { id: 0, name: "无上级部门" };                // 需要添加的默认部门占位
//const deptLookUpKey = "id";                                      // radio按钮根据值方向查找的键值
//const threadFunctions = [superiorReplacer.replace];
//let render = 0;

/**
 * 暂时包含三个字段: 姓名, 手机, 状态
 * 需要注意其中包含4种数据/状态:
 * 1. InputText手动输入的数据, 在useForm中申明, 直接由react-hook-form的controller处理, 以便减少渲染.
 * 2. 拉选框状态数据, 在selectorStates.js中预先定义, 包括radio和checkbox两种, 需要通过zustand进行全局状态管理, 其中包含的是复合数据, radio的是对象{id, name}, checkbox的是对象数组[{id, name}*], 控件要求包含id和name.
 * 3. mmkv存储数据, 在此通过creatMMKVStore创建, 用于存储当前的控件数据以及载入时恢复原先状态, 其中保存的InputText数据内容与react-form的相同, 拉选框数据内容与zustand的相同.
 * 4. 完整的react-hook-form数据, 其中包含第一种数据, radio拉选框数据只包含其id, checkbox的数据则是id的数组. hook-form数据转换成FormData后由http发送给服务端.
 * @param {Object} navigation
 * @returns
 */
const ClientsInserting = ({ navigation }) => {
    // render++;
    const { bottom, left, right } = useSafeAreaInsets();
    const theme = useTheme();
    const height = theme.isV3 ? 80 : 56;

    // 新组件需要修改!!
    const bottomBarLabels = { ok: "添加客户", cancel: "取消" };                // 底部按钮显示文字
    const [confimDialogConfig, setConfirmDialogConfig] = useState({});       // 对话框信息, {title, text, okLabel, onOK}, 但显示对话框的变量在showConfirm
    const [snackBarMessage, setSnackBarMessage] = useState("添加客户遇到错误"); // 下方错误提示文字内容
    // 拉选框组件使用全局状态
    const [
        clientState,
        setClientState,
    ] = selectorStates(useShallow(state => [state.state, state.setState, ])); // radio和check组件状态

    // 新组件不需改动
    const saveButtonIcon = "content-save-all-outline";
    const [okButtonLoading, setOkButtonLoading] = useState(false);           // 右下方按钮图标是否旋转
    const [okButtonDisabled, setOkButtonDisabled] = useState(false);         // 右下方按钮是否禁用
    const [cancelButtonDisabled, setCancelButtonDisabled] = useState(false); // 左下方按钮是否禁用
    const [showSnackbar, setShowSnackbar] = useState(false);                 // 下方是否显示错误通知
    const [showConfirm, setShowConfirm] = useState(false);                   // 是否显示确认对话框

    //const clientStateDataProviderRef = useRef([{id: 0, name: "正常"}, {id: 1, name: "禁用"}]); // 用作用户状态选项的数据源

    const formDefaults  = useRef({
        name:          "",
        address:       "",
        email:         "",
        contactName:   "",
        contactMobile: "",
    });
    const storeDefaults = useRef({
        name:          "",
        address:       "",
        email:         "",
        contactName:   "",
        contactMobile: "",
    });
    const selectorDefaults = useRef({
        state: { id: 0, name: "正常" },
    });

    // 新组件需要重新定义!!
    const schema = Joi.object({
        name:          validatorBase.clientName.required,
        address:       validatorBase.clientAddr.unrequired,
        email:         validatorBase.email.unrequired,
        contactName:   validatorBase.userName.required,
        contactMobile: validatorBase.mobile.required,
    });

    // 新组件需要重新定义defaultValues!!
    const {
        handleSubmit,
        control,
        resetField,
        reset,
        formState: { errors },
        //getValues,
    } = useForm({
        resolver: joiResolver(schema),
        //reValidateMode: "onBlur", // https://github.com/orgs/react-hook-form/discussions/10327
        defaultValues: {
            name:          whenLet(getStore("name"),          identity, ""),
            address:       whenLet(getStore("address"),       identity, ""),
            email:         whenLet(getStore("email"),         identity, ""),
            contactName:   whenLet(getStore("contactName"),   identity, ""),
            contactMobile: whenLet(getStore("contactMobile"), identity, ""),
        },
    });

    // 新组件需要重新定义setDiagText!!
    const onHandleSubmit = (data) => { // data: {"name": "mmm", "superior": 0}
        // 由于这里的data的版本可能先于mmkv的版本, 因此必须在此将data转储到一个引用地址, 以供query使用, 这就是dataFeeder的用处, 另外dataFeeder可以做一些数据预处理
        setCancelButtonDisabled(true);
        setOkButtonDisabled(true);
        setOkButtonLoading(true);
        dataFeeder(data);
        console.log("commit data:", data);
        submitQuery.mutate();
    };
    // 新组件不需改动
    const onCancel = () => {
        clearStore();
        // 每增加一个下拉框都要设置各自的默认值
        reset(formDefaults.current);              // 重置react-form
        setStoreObject(storeDefaults.current);    // 重置mmkv存储
        //setClientState(selectorDefaults.current.state);  // 重置radio状态
    };

    // 新组件不需改动
    const commitOnSuccess = (data) => {
        console.log("response success, data: ", data);
        if (data.STATUS !== 0) {
            setSnackBarMessage(`客户添加发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        } else {
            // 设置对话框数据
            setConfirmDialogConfig({
                title: "添加客户",
                text: "新客户添加成功!",
                okLabel: "确定",
                onOK: () => {
                    clearStore();
                    // 每增加一个下拉框都要设置各自的默认值
                    reset(formDefaults.current);                   // 重置react-form
                    setStoreObject(storeDefaults.current);         // 重置mmkv存储
                    //setClientState(selectorDefaults.current.state);  // 重置radio状态
                    setShowConfirm(false);
                }
            });
            setShowConfirm(true);
        }
    };
    const commitOnError = (error) => {
        setSnackBarMessage(`客户添加发生错误, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const commitOnSettled = (data, error) => {
        setCancelButtonDisabled(false);
        setOkButtonDisabled(false);
        setOkButtonLoading(false);
    };
    const submitQuery = insertQueryClient(dataFeeder, commitOnSuccess, commitOnError, commitOnSettled);

    // 组件载入时从mmkv存储恢复下拉控件状态
    useEffect(()=>{
        //const storedUserState = getStore("state");
        //isEmptyObject(storedUserState) ? setClientState(selectorDefaults.current.state) : setClientState(storedUserState);
    }, []);

    return (
        <View style={{ flex: 1 }}>

            <HeaderBar
                title={"添加客户"}
                navigation={navigation}
            //goBackCallback={() => {}}
            //menuItemArray={[{ title: "标题", action: ()=>{} }]}
            />

            <ScreenWrapper contentContainerStyle={styles.container} style={{marginBottom: height+ bottom}}>
                {/*<Text>Render: {render}</Text>*/}

                <View style={styles.formEntry}>
                    <ControlledTextInput
                        rowLabel="客户名称"
                        control={control}
                        name="name"
                        placeholder="XXXX公司"
                        onChangeText={(text) => setStore("name", text)}
                        onClearText={() => {
                            setStore("name", "");
                            resetField("name", { defaultValue: "" });
                        }}
                        editable={true}
                        mode="outlined"
                        formError={errors}
                        required={true}
                        multiline={true}
                    />

                    <ControlledTextInput
                        rowLabel="客户地址"
                        control={control}
                        name="address"
                        placeholder="XX市XX区XX路XX号"
                        onChangeText={(text) => setStore("address", text)}
                        onClearText={() => {
                            setStore("address", "");
                            resetField("address", { defaultValue: "" });
                        }}
                        editable={true}
                        mode="outlined"
                        formError={errors}
                        required={false}
                        multiline={true}
                    />

                    <ControlledTextInput
                        rowLabel="联系人姓名"
                        control={control}
                        name="contactName"
                        placeholder="张三"
                        onChangeText={(text) => setStore("contactName", text)}
                        onClearText={() => {
                            setStore("contactName", "");
                            resetField("contactName", { defaultValue: "" });
                        }}
                        editable={true}
                        mode="outlined"
                        formError={errors}
                        required={true}
                    />

                    <ControlledTextInput
                        rowLabel="联系人手机号"
                        control={control}
                        name="contactMobile"
                        placeholder="18888888888"
                        onChangeText={(text) => setStore("contactMobile", text)}
                        onClearText={() => {
                            setStore("contactMobile", "");
                            resetField("contactMobile", { defaultValue: "" });
                        }}
                        editable={true}
                        mode="outlined"
                        formError={errors}
                        required={true}
                    />

                    <ControlledTextInput
                        rowLabel="联系电子邮箱"
                        control={control}
                        name="email"
                        placeholder="<EMAIL>"
                        onChangeText={(text) => setStore("email", text)}
                        onClearText={() => {
                            setStore("email", "");
                            resetField("email", { defaultValue: "" });
                        }}
                        editable={true}
                        mode="outlined"
                        formError={errors}
                        required={false}
                    />




                </View>

            </ScreenWrapper>

            <Appbar
                style={[
                    styles.bottom,
                    {
                        height: height + bottom,
                    },
                    theme.isV3 && {
                        backgroundColor: theme.colors.elevation.level2,
                    },
                ]}
                safeAreaInsets={{ bottom, left, right }}
                theme={{ mode: "adaptive" }}
            //mode='center-aligned'
            >

                <View style={styles.bottomBarContainer}>
                    <Divider />
                    <View style={styles.bottomBarComponents}>
                        <BottomBarButton
                            label={bottomBarLabels.cancel}
                            disabled={cancelButtonDisabled}
                            onPress={onCancel}
                        />
                        <BottomBarButton
                            label={bottomBarLabels.ok}
                            loading={okButtonLoading}
                            disabled={okButtonDisabled}
                            icon={saveButtonIcon}
                            onPress={handleSubmit(onHandleSubmit, (formErr) => onPreSubmitError(formErr, getStore, "ClientsInserting"))}
                        />
                    </View>
                </View>
            </Appbar>

            <Snackbar
                style={styles.snackBar}
                visible={showSnackbar}
                onDismiss={() => {
                    setShowSnackbar(false);
                }}
                onIconPress={() => {
                    setShowSnackbar(false);
                }}
                duration={Snackbar.DURATION_LONG}
            >
                {snackBarMessage}
            </Snackbar>

            <DialogToConfirm
                visible={showConfirm}
                title={confimDialogConfig.title ? confimDialogConfig.title : ""}
                text={confimDialogConfig.text ? confimDialogConfig.text : ""}
                onOK={confimDialogConfig.onOK ? confimDialogConfig.onOK : () => setShowConfirm(false)}
                okBtnLabel={confimDialogConfig.okLabel ? confimDialogConfig.okLabel : "确认"}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    firstColumn: {
        flex: 2,
        //borderWidth: 1,
        //height: 40,
        content: {
            fontSize: 18,
        }
    },
    secondColumn: {
        flex: 3,
        // /borderWidth: 1,
        // height: 40,
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        flex: 1,
        marginHorizontal: 6
    },
    formEntry: {
        margin: 8,
    },
    snackBar: {
        flexDirection: "column",
    },
});

export default ClientsInserting;
