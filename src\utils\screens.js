import log from "../services/logging";

// 提交表单之前打印react-hook-form错误, 并通过网络提交到服务器
/**
 *
 * @param {object} errors
 * @param {function | null | undefined} getStore
 * @param {string} screenName
 */
export const onPreSubmitError = (errors, getStore, screenName) => {
    let errFields = {};
    if(getStore) {
        for (const key in errors) {
            errFields[key] = getStore(key);
        }
    }
    log.error("Presubmit errors in Component <%s> with react-hook-form error <%s> where the fields had value <%s>", screenName, errors, errFields);
};
