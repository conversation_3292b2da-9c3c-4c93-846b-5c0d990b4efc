import { useMutation } from "@tanstack/react-query";
import { httpClient } from "../services/http";
import { mergeQueryPath } from "../utils";
import log from "../services/logging";


const RETRY_CODES = [401, 501, 502, 503, 504];
const RETRY_METHODS = ["GET", "POST", "PUT", "HEAD", "DELETE", "OPTIONS", "TRACE"];
const RETRY_INTERVALS = [2000, 3000, 5000];
const RETRY_LIMIT = RETRY_INTERVALS.length;


/**
 * 目前与makeSelectingClient完全一样, 除了这里使用post
 * selectint clients make http request on url/var
 * selectingClient与listingClient的区别在于, selectingClient的查询需要传递变量, 而listingClient不需要.
 * 表现上, listingClient的请求路径是url, 而selectingClient的请求路径是url/sub_dir.
 * 实质上, listingClient在服务端是select出一组记录, 而selectingClient是在服务端select出一个记录中的某个字段.
 * @param {Object} arg
 * @param {string} arg.query the query key for react-query
 * @param {string} arg.url the http request path which will be responed by the server
 * @returns
 */
const makeRecordsAddingClient = ({ query, url }) => {
    /**
     * 返回一个函数, 这个返回的函数创建一个useMutation查询, 用于发起select请求.
     * 注意: 这个版本的ky客户端含有重连,
     * 并且在客户端中将token设置到标头: request.headers.set("Authorization", token)
     * @param {(data)=>{}} onSuccess token string
     * @param {(error)=>{}} onError callback function
     * @param {(data, error)=>{}} onSettled callback function
     */
    return (varOrVars, onSuccess, onError, onSettled) => {
        return useMutation({
            mutationKey: [query, varOrVars],
            mutationFn: async () => {
                const response = await httpClient.post(`${url}${mergeQueryPath(varOrVars)}`); // url/sub_dir
                const json = await response.json();
                log.debug("selecting query client %s on %s/%s, mutationFn receive data: %s", query, url, varOrVars, json);
                return json; // 注意不要return await!
            },

            onSuccess: (data, variables, context) => {
                log.debug("makeSelectingClient onSuccess, query: %s, url: %s, data: %s, vars, %s, context: %s", query, url, data, variables, context);
                onSuccess?.(data);
            },
            onError: (error, variables, context) => {
                log.debug("makeSelectingClient onError, error: %s, query: %s, url: %s, vars, %s, context: %s", error, query, url, variables, context);
                onError?.(error);
            },
            onSettled: (data, error, variables, context) => {
                log.debug("makeSelectingClient onSettled, query: %s, url: %s, var: %s, context: %s", query, url, variables, context);
                onSettled?.(data, error);
            },

            // 逻辑上, 首次失败后, 立即检测retryDelay, 经延迟后再检测retry触发重试
            // 注意, 有Ky的retry有bug, 这里使用React Query的retry!!!!!!
            retry: (failureCount, error) => { // failureCount from 0
                log.debug("failureCount: %s", failureCount);
                if (failureCount < RETRY_LIMIT
                    && error.name === "HTTPError"
                    && RETRY_CODES.includes(error.response.status)
                    && RETRY_METHODS.includes(error.request.method)) {
                    return true;
                } else {
                    return false;
                }
            },
            retryDelay: (attemptIndex) => RETRY_INTERVALS[attemptIndex], // attemptIndex from 0
        });
    };
};

export { makeRecordsAddingClient };
