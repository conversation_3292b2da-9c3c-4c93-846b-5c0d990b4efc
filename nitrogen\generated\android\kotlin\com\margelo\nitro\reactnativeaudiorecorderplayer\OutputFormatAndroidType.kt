///
/// OutputFormatAndroidType.kt
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON> @ Margelo
///

package com.margelo.nitro.reactnativeaudiorecorderplayer

import androidx.annotation.Keep
import com.facebook.proguard.annotations.DoNotStrip

/**
 * Represents the JavaScript enum/union "OutputFormatAndroidType".
 */
@DoNotStrip
@Keep
enum class OutputFormatAndroidType {
  DEFAULT,
  THREE_GPP,
  MPEG_4,
  AMR_NB,
  AMR_WB,
  AAC_ADIF,
  AAC_ADTS,
  OUTPUT_FORMAT_RTP_AVP,
  MPEG_2_TS,
  WEBM;

  @DoNotStrip
  @Keep
  private val _ordinal = ordinal
}
