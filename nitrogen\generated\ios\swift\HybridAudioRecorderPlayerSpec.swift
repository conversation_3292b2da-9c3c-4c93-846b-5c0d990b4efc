///
/// HybridAudioRecorderPlayerSpec.swift
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc Rousavy @ Margelo
///

import Foundation
import NitroModules

/// See ``HybridAudioRecorderPlayerSpec``
public protocol HybridAudioRecorderPlayerSpec_protocol: HybridObject {
  // Properties
  

  // Methods
  func startRecorder(uri: String?, audioSets: AudioSet?, meteringEnabled: Bool?) throws -> Promise<String>
  func pauseRecorder() throws -> Promise<String>
  func resumeRecorder() throws -> Promise<String>
  func stopRecorder() throws -> Promise<String>
  func startPlayer(uri: String?, httpHeaders: Dictionary<String, String>?) throws -> Promise<String>
  func stopPlayer() throws -> Promise<String>
  func pausePlayer() throws -> Promise<String>
  func resumePlayer() throws -> Promise<String>
  func seekToPlayer(time: Double) throws -> Promise<String>
  func setVolume(volume: Double) throws -> Promise<String>
  func setPlaybackSpeed(playbackSpeed: Double) throws -> Promise<String>
  func setSubscriptionDuration(sec: Double) throws -> Void
  func addRecordBackListener(callback: @escaping (_ recordingMeta: RecordBackType) -> Void) throws -> Void
  func removeRecordBackListener() throws -> Void
  func addPlayBackListener(callback: @escaping (_ playbackMeta: PlayBackType) -> Void) throws -> Void
  func removePlayBackListener() throws -> Void
  func mmss(secs: Double) throws -> String
  func mmssss(milisecs: Double) throws -> String
}

/// See ``HybridAudioRecorderPlayerSpec``
public class HybridAudioRecorderPlayerSpec_base {
  private weak var cxxWrapper: HybridAudioRecorderPlayerSpec_cxx? = nil
  public func getCxxWrapper() -> HybridAudioRecorderPlayerSpec_cxx {
  #if DEBUG
    guard self is HybridAudioRecorderPlayerSpec else {
      fatalError("`self` is not a `HybridAudioRecorderPlayerSpec`! Did you accidentally inherit from `HybridAudioRecorderPlayerSpec_base` instead of `HybridAudioRecorderPlayerSpec`?")
    }
  #endif
    if let cxxWrapper = self.cxxWrapper {
      return cxxWrapper
    } else {
      let cxxWrapper = HybridAudioRecorderPlayerSpec_cxx(self as! HybridAudioRecorderPlayerSpec)
      self.cxxWrapper = cxxWrapper
      return cxxWrapper
    }
  }
}

/**
 * A Swift base-protocol representing the AudioRecorderPlayer HybridObject.
 * Implement this protocol to create Swift-based instances of AudioRecorderPlayer.
 * ```swift
 * class HybridAudioRecorderPlayer : HybridAudioRecorderPlayerSpec {
 *   // ...
 * }
 * ```
 */
public typealias HybridAudioRecorderPlayerSpec = HybridAudioRecorderPlayerSpec_protocol & HybridAudioRecorderPlayerSpec_base
