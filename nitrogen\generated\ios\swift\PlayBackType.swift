///
/// PlayBackType.swift
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON> @ Margelo
///

import NitroModules

/**
 * Represents an instance of `PlayBackType`, backed by a C++ struct.
 */
public typealias PlayBackType = margelo.nitro.react_native_audio_recorder_player.PlayBackType

public extension PlayBackType {
  private typealias bridge = margelo.nitro.react_native_audio_recorder_player.bridge.swift

  /**
   * Create a new instance of `PlayBackType`.
   */
  init(isMuted: Bool?, duration: Double, currentPosition: Double) {
    self.init({ () -> bridge.std__optional_bool_ in
      if let __unwrappedValue = isMuted {
        return bridge.create_std__optional_bool_(__unwrappedValue)
      } else {
        return .init()
      }
    }(), duration, currentPosition)
  }

  var isMuted: Bool? {
    @inline(__always)
    get {
      return self.__isMuted.value
    }
    @inline(__always)
    set {
      self.__isMuted = { () -> bridge.std__optional_bool_ in
        if let __unwrappedValue = newValue {
          return bridge.create_std__optional_bool_(__unwrappedValue)
        } else {
          return .init()
        }
      }()
    }
  }
  
  var duration: Double {
    @inline(__always)
    get {
      return self.__duration
    }
    @inline(__always)
    set {
      self.__duration = newValue
    }
  }
  
  var currentPosition: Double {
    @inline(__always)
    get {
      return self.__currentPosition
    }
    @inline(__always)
    set {
      self.__currentPosition = newValue
    }
  }
}
