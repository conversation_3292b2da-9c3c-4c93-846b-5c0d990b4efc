import React from "react";
import Background from "../components/Background";
import Logo from "../components/Logo";
import Header from "../components/Header";
import Button from "../components/Button";
import Paragraph from "../components/Paragraph";
import { useTranslation } from "react-i18next";

const StartScreen = ({ navigation }) => {
    const { t } = useTranslation(["screenStart"]);
    // console.log("scrat screen navigation: ", navigation);
    return (
        <Background>
            <Logo />
            <Header>{t("screenStart:header")}</Header>
            <Paragraph>
                {t("screenStart:paragraph")}
            </Paragraph>
            <Button mode="contained" onPress={() => navigation.navigate("LoginScreen")}>
                {t("screenStart:loginButton")}
            </Button>
            <Button mode="outlined" onPress={() => navigation.navigate("RegisterScreen")}>
                {t("screenStart:signButton")}
            </Button>
        </Background>
    );
};

export default StartScreen;
