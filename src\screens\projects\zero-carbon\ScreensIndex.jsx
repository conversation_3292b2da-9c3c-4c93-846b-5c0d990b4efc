import React, { useState } from "react";
import { View } from "react-native";
import { Divider, List, } from "react-native-paper";
import { makeReqGenWaterBalanceDoc as genDocQueryMaker, } from "../../../api/projectRecordsQueries";
import { CardProjectMessage } from "../../../components/CardProjectMessage";
import { DialogToConfirm } from "../../../components/DialogToConfirm";
import HeaderBar from "../../../components/HeaderBar";
import { PromptDialog } from "../../../components/PromptDialog";
import ScreenWrapper from "../../ScreenWrapper";
//import { useSafeAreaInsets } from "react-native-safe-area-context";


// 权限模拟函数, 暂时全部都可写, 但要根据服务端数据进行判断
const canIWrite = () => {
    return true;
};
/**
 * 在"任务"主页点击项目卡片后进入的屏幕
 * route.params.pageMeta从MessageScreen传递过来
 * pageMeta.class=1表示水平衡, pageMeta.subclass=1表示表, 2表示书
 * @param {*} param0
 * @returns
 */
export const ScreensIndex = ({ navigation, route }) => {
    console.log("ProjectIndex screen, Page info passed from nav:", route.params.pageMeta);
    const pageMeta = route.params.pageMeta;
    const { contentId: projPubid  } = pageMeta;
    const genDocQueryKwd = "all";

    //const { bottom, left, right } = useSafeAreaInsets();
    //const theme = useTheme();
    //const height = theme.isV3 ? 80 : 56;

    //const editableIcon     = "folder-edit-outline";
    //const readOnlyIcon     = "folder-outline";
    //const infoSectionIcon  = canIWrite() ? "folder-edit-outline" : "folder-eye-outline";
    const tableSectionIcon = canIWrite() ? "folder-edit-outline" : "folder-eye-outline";
    console.log("coming to zero carbon project index screen!");

    // Query: generate all docs of this project
    const projGenDocOnSuccess = () => {
        console.log("genDoc success!");
    };
    const projGenDocOnError = (error) => {
        console.log("genDoc error:", error);
    };
    const payload = {prompts: promptsText};
    const projGenDocQuery = genDocQueryMaker(payload, [genDocQueryKwd, projPubid], projGenDocOnSuccess, projGenDocOnError);

    const [promptsVisible, setPromptsVisible] = useState(false);
    const [promptsText, setPromptsText] = useState("根据水平衡资讯行业的知识总结所有相关表格");
    const [dialogConfirmVisible, setDialogConfirmVisible] = useState(false);

    return (
        <View style={{ flex: 1 }}>

            <HeaderBar
                title={route.params.pageMeta.title}
                navigation={navigation}
                //goBackCallback={() => {}}
                // 获得外链需要根据权限开启
                //menuItemArray={[{ title: "获得外链", action: ()=>{} }]}
            />
            <ScreenWrapper>
                <Divider bold={true} />

                <List.Section>
                    {pageMeta.class === 11 && // 零碳诊断
                        <>
                            <CardProjectMessage title="单位基本情况表"   naviTo="ZCBasicInfoRecordsListing"         icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "binfo",  title: "单位基本情况表",     recordListNaviTo: "ZCBasicInfoRecordsUpdating" }}/>
                            <CardProjectMessage title="用能人数统计表"   naviTo="ZCEnergyConsumersRecordsListing"   icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "egcons", title: "用能人数统计表",     recordListNaviTo: "ZCEnergyConsumersRecordsUpdating" }}/>
                            <CardProjectMessage title="能源账单表"      naviTo="ZCEnergyBillsRecordsListing"       icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "egbill", title: "能源账单表",         recordListNaviTo: "ZCEnergyBillsRecordsUpdating" }}/>
                            <CardProjectMessage title="采暖费用信息"    naviTo="ZCHeatingBillsRecordsListing"      icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "htbill", title: "采暖费用信息",        recordListNaviTo: "ZCHeatingBillsRecordsUpdating" }}/>
                            <CardProjectMessage title="用能系统基本情况" naviTo="EnergySystemInfoRecordsListing"    icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "esinfo", title: "用能系统基本情况",    recordListNaviTo: "EnergySystemInfoRecordsUpdating" }}/>




                            {/** More tables here */}
                        </>}

                </List.Section>
                <Divider bold={true} />




                {/*verifySomeUserTypes(UserTypeList.sysAdmin, UserTypeList.qa_0)  && <>
                    <List.Section title={"功能测试"}>
                        <CardProjectMessage title="用水器具统计表(new)" naviTo="WaterSavingEquipsRecordsListing2" icon={tableSectionIcon} navigation={navigation}  pageMeta={{ ...pageMeta, queryKwd: "wsequip2", title: "用水器具统计表(new)", recordListNaviTo: "WaterSavingEquipsRecordsUpdating2" }} />

                    </List.Section>
                    <Divider bold={true} />
                    <List.Section title={"项目信息(测试用)"}>
                        <CardProjectMessage title="客户资料清单"    naviTo="ClientInfoRecordsListing"             icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "cinfo",     title: "客户资料清单",     recordListNaviTo: "ClientInfoRecordsUpdating" }}/>
                        <CardProjectMessage title="公用工程"       naviTo="ClientPublicUtilityRecordsListing"    icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "cputil",     title: "公用工程",        recordListNaviTo: "ClientPublicUtilityRecordsUpdating" }}/>
                        <CardProjectMessage title="项目产品规模"    naviTo="ClientProductScaleRecordsListing"     icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "cpscale",    title: "项目产品规模",     recordListNaviTo: "ClientProductScaleRecordsUpdating" }}/>
                        <CardProjectMessage title="项目原辅材料消耗" naviTo="ClientMaterialConsumeRecordsListing"  icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "cmconsume",  title: "项目原辅材料消耗",  recordListNaviTo: "ClientMaterialConsumeRecordsUpdating" }}/>
                        <CardProjectMessage title="项目所需设备清单" naviTo="ClientRequiredDeviceRecordsListing"   icon={tableSectionIcon}  navigation={navigation} pageMeta={{ ...pageMeta, queryKwd: "crdevice",   title: "项目所需设备清单",  recordListNaviTo: "ClientRequiredDeviceRecordsUpdating" }}/>
                    </List.Section>
                    <Divider bold={true} />
                </>*/}

            </ScreenWrapper>

            <PromptDialog
                visible={promptsVisible}
                setVisible={setPromptsVisible}
                promptsText={promptsText}
                onOKCallback={(prompts) => {
                    setPromptsText(prompts);
                    setTimeout(() => {
                        projGenDocQuery.mutate();
                    }, 200);
                    setDialogConfirmVisible(true);
                }}
                onCancelCallback={() => {}}
                dialogTitle={"请填写AI提示词"}
                okBtnLabel={"确定"}
                cancelBtnLabel={"取消"}
            />

            <DialogToConfirm
                visible={dialogConfirmVisible}
                title={"生成文档"}
                text={"已将请求发送给AI, 稍后请在Web端查看结果!"}
                onOK={() => {setDialogConfirmVisible(false);}}
                okBtnLabel={"确认"}
            />

        </View>
    );
};

/*
const styles = StyleSheet.create({
    container: {
        flex: 1,
        marginTop: 4,
        marginBottom: 4,
        marginHorizontal: 10,
        //marginVertical: 10,
    },
    card: {
        borderWidth: 1,
        marginVertical: 2,
    }
});
*/

export default ScreensIndex;
