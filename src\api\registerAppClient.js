import { useMutation } from "@tanstack/react-query";
import { noTokenHttpClient as httpClient } from "../services/http";
import log from "../services/logging";
import { APP_VERSION, CLIENT_OS, CLIENT_OS_VERSION } from "../services/system";
import { makeFormData } from "../utils";


const RETRY_CODES = [401, 501, 502, 503, 504];
const RETRY_METHODS = ["POST", "GET", "PUT", "HEAD", "DELETE", "OPTIONS", "TRACE"];
const RETRY_INTERVALS = [2000, 3000, 5000];
const RETRY_LIMIT = RETRY_INTERVALS.length;

const alertDelay = 200;

/**
 * Return a `useMutation` query object. The query result will be stored in MMKV
 * @param {Object} arg
 * @param {string} arg.query the query key for react-query
 * @param {string} arg.url the http request path which will be responed by the server
 * @returns
 */
export const makeRegisterAppClient = (queryKey, url) => {
    /**
     * 返回一个函数, 这个返回的函数创建一个useMutation查询, 用于发起update请求.
     * @param {Function} dataGetter function
     * @param {Number | Number[] | String | String[]} varOrVars
     * @param {()=>{}} onSuccess callback function
     * @param {()=>{}} onError callback function
     * @param {()=>{}} onSettled callback function
     */
    return (dataGetter, onSuccess, onError, onSettled) => {
        let startTime = 0;
        return useMutation({
            mutationKey: [queryKey],
            mutationFn: async () => {
                startTime = Date.now();
                const payloadData = {
                    ...dataGetter(),
                    os: CLIENT_OS,
                    osVersion: CLIENT_OS_VERSION,
                    appVersion: APP_VERSION,
                };
                const formData = makeFormData(payloadData);
                log.debug("makeRegisterAppClient post, query: %s, urlPath: %s, data: %s", queryKey, url, formData);
                const response = await httpClient.post(url, { body: formData, retry: { limit: 0 } });
                const json = await response.json();
                log.debug("makeRegisterAppClient %s on %s, mutationFn receive data: %s", queryKey, url, json);
                return json;
            },
            onSuccess: (data, variables, context) => {
                console.log("Call onSuccess: ", data);
                const now = Date.now();
                if (now - startTime > alertDelay) {
                    onSuccess?.(data);
                } else {
                    setTimeout(() => {
                        onSuccess?.(data);
                    }, alertDelay - (now - startTime));
                }

            },
            onError: (error, variables, context) => {
                console.log("Call onError");
                onError?.(error);
            },
            onSettled: (data, error, variables, context) => {
                console.log("Call onSettled");
                onSettled?.(data, error);
            },


            // 逻辑上, 首次失败后, 立即检测retryDelay, 经延迟后再检测retry触发重试
            // 注意, 有Ky的retry有bug, 这里使用React Query的retry!!!!!!
            retry: (failureCount, error) => { // failureCount from 0
                log.debug("Retry query: %s, urlPath: %s, error: %s, failureCount: %s", queryKey, url, error, failureCount);
                if (failureCount < RETRY_LIMIT
                    && error.name === "HTTPError"
                    && RETRY_CODES.includes(error.response.status)
                    && RETRY_METHODS.includes(error.request.method)) {
                    return true;
                } else {
                    return false;
                }
            },
            retryDelay: (attemptIndex) => RETRY_INTERVALS[attemptIndex], // attemptIndex from 0
        });
    };
};
