import { getCurrentTime } from "../../utils/time";
import { loggingHttpClient } from "../http";
import { DEFAULT_LOCAL_LOGGING_LEVEL, DEFAULT_REMOTE_LOGGING_LEVEL, BASE_LOGGING_URL, REMOTE_LOGGING } from "../../config";
import { isError } from "../../utils";

/**
 * Interpolate args into a format string, like C's `printf` or Common Lisp's `format`.
 * Eg. format("Ich liebe %s!", "FengNa")
 * @param {string} fmtString A format string .
 * @param  {...any} args Any args which will be interpolated into `fmtString`.
 * @returns {string} Return the interpolated string.
 */
const format = (fmtString, ...args) => args.reduce((s, v) => s.replace("%s", v), fmtString);

/**
 * log level definition
 */
const levels = {
    debug: 0,
    info: 1,
    warn: 2,
    error: 3,
};

/**
 * log levels are used to control if this message should be logged or not,
 * log only for levels[level] >= levelSetting
 */
let localLevelSetting = levels[DEFAULT_LOCAL_LOGGING_LEVEL];
let remoteLevelSetting = levels[DEFAULT_REMOTE_LOGGING_LEVEL];
const setLocalLoggingLevel = (level) => localLevelSetting = level;
const setRemoteLoggingLevel = (level) => remoteLevelSetting = level;

/**
 * Logging into the console
 * @param {string} logLevel logging level
 * @param {string} time time string, "YYYY-MM-DD HH:MM-SS"
 * @param {string} message logging message
 * @param {string | undefined} logKeyword logging keyword
 */
const logLocal = (logLevel, time, fmtString, ...args) => {
    if (__DEV__ && levels[logLevel] >= localLevelSetting) {
        console.log(logLevel.toUpperCase() + " " + time + " " + fmtString, ...args);
    }
};

/**
 * Logging into the remote server via http
 * @param {string} logLevel logging level
 * @param {string} time "YYYY-MM-DD HH:MM-SS"
 * @param {string} message logging message
 * @param {string | undefined} logKeyword logging keyword
 */
const logRemote = async (logLevel, time, fmtString, ...args) => {
    if (REMOTE_LOGGING && levels[logLevel] >= remoteLevelSetting) {
        const logStr = format("%s %s " + fmtString, logLevel.toUpperCase(), time, ...args.map(arg => {
            if (isError(arg)) {
                return `${arg.name}: ${arg.message}`;
            } else {
                return JSON.stringify(arg);
            }
        }));
        try {
            await loggingHttpClient.post("logging", { headers: { "content-type": "text/plain" }, body: logStr });
        } catch (err) {
            console.log(`Failed to log to the remote server "${BASE_LOGGING_URL}"  for message "${logStr}" with response err "${err.message}".`);
        }
    }
};

/**
 * Logging to the local console and remote server according to the logging config.
 * @param {string} level logging level
 * @param {string} message logging message
 * @param {string | undefined} logKeyword logging keyword
 */
const logging = (level, fmtString, ...args) => {
    const now = getCurrentTime();
    logLocal(level, now, fmtString, ...args);
    logRemote(level, now, fmtString, ...args);
};

/**
 * Logging to the local console and remote server according to the logging config.
 * Eg.
 * `log.debug("debug-message...%s %s %s", "ich", 886, "甲乙");`
 * `log.info("info-message...%s %s %s", "mir", 887, "丙丁");`
 * `log.warn("warn-message...%s %s %s", "du", 888, "戊己");`
 * `log.error("error-message...%s %s %s", "dich", 889, "庚辛");`
 */
const log = {
    debug: (fmtString, ...args) => logging("debug", fmtString, ...args),
    info:  (fmtString, ...args) => logging("info",  fmtString, ...args),
    warn:  (fmtString, ...args) => logging("warn",  fmtString, ...args),
    error: (fmtString, ...args) => logging("error", fmtString, ...args),
};

export default log;
export { setLocalLoggingLevel, setRemoteLoggingLevel };
