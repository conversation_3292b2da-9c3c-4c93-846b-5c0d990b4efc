///
/// AudioSet.kt
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc Rousavy @ Margelo
///

package com.margelo.nitro.reactnativeaudiorecorderplayer

import androidx.annotation.Keep
import com.facebook.proguard.annotations.DoNotStrip
import com.margelo.nitro.core.*

/**
 * Represents the JavaScript object/struct "AudioSet".
 */
@DoNotStrip
@Keep
data class AudioSet
  @DoNotStrip
  @Keep
  constructor(
    val AudioSourceAndroid: AudioSourceAndroidType?,
    val OutputFormatAndroid: OutputFormatAndroidType?,
    val AudioEncoderAndroid: AudioEncoderAndroidType?,
    val AVEncoderAudioQualityKeyIOS: AVEncoderAudioQualityIOSType?,
    val AVModeIOS: AVModeIOSOption?,
    val AVEncodingOptionIOS: AVEncodingOption?,
    val AVFormatIDKeyIOS: AVEncodingOption?,
    val AVNumberOfChannelsKeyIOS: Double?,
    val AVLinearPCMBitDepthKeyIOS: AVLinearPCMBitDepthKeyIOSType?,
    val AVLinearPCMIsBigEndianKeyIOS: Boolean?,
    val AVLinearPCMIsFloatKeyIOS: Boolean?,
    val AVLinearPCMIsNonInterleavedIOS: Boolean?,
    val AVSampleRateKeyIOS: Double?,
    val AudioQuality: AudioQualityType?,
    val AudioChannels: Double?,
    val AudioSamplingRate: Double?,
    val AudioEncodingBitRate: Double?,
    val IncludeBase64: Boolean?
  ) {
  /* main constructor */
}
