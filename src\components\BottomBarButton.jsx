import React from "react";
import { StyleSheet } from "react-native";
import { Button } from "react-native-paper";
import PropTypes from "prop-types";

/**
 * 底部Appbar按钮
 * @param {Object} arg
 * @param {string} arg.label
 * @param {bool} arg.loading
 * @param {bool} arg.disabled
 * @param {function} arg.onPress
 * @param {string | undefined} arg.icon
 * @param {props} arg.props
 * @returns
 */
const BottomBarButton = ({ label, loading = false, disabled, onPress, icon = undefined, ...props }) => {
    return (
        <Button
            mode="elevated"
            loading={loading}
            disabled={disabled}
            icon={icon}
            onPress={onPress}
            style={styles.style}
            labelStyle={styles.labelStyle}
            {...props}
        >
            {label}
        </Button>
    );
};

const styles = StyleSheet.create({
    style: {
        flex: 1,
        marginHorizontal: 4
    },
    labelStyle: {
        fontSize: 18,
        textAlign: "justify",
        textAlignVertical: "center",
        paddingTop: 4,
    },
});

BottomBarButton.propTypes = {
    label: PropTypes.string,
    loading: PropTypes.bool,
    disabled: PropTypes.bool,
    onPress: PropTypes.func,
    icon: PropTypes.string,

};

export default BottomBarButton;
