import { joi<PERSON>es<PERSON>ver } from "@hookform/resolvers/joi";
import "fast-text-encoding";
import <PERSON><PERSON> from "joi";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { Alert, StyleSheet, View } from "react-native";
import { Divider, Snackbar } from "react-native-paper";
import Button from "../../../components/Button";
import { DialogToConfirm } from "../../../components/DialogToConfirm";
import HeaderBar from "../../../components/HeaderBar";
import { LoadingIndicator } from "../../../components/LoadingIndicator";
import { creatMMKVStore, makeStoredValueToFormValue } from "../../../services/local-storage";
import { callOneByOne, makeDataFeeder } from "../../../utils";
import { checkPermits } from "../../../utils/permits";
import { isServerDataNewer, makeGetClientCversion, radioIdToObject } from "../../../utils/records";
import { validatorBase } from "../../../utils/validatorBase";
import ScreenWrapper from "../../ScreenWrapper";

// 新组件需要重新!!
import { useShallow } from "zustand/shallow";
import {
    makeReqDelWaterBalanceRecords as recordDeleteQueryMaker,
    makeReqGetWaterBalanceRecords as recordSelectQueryMaker,
    makeReqUpdWaterBalanceRecords as recordUpdateQueryMaker,
} from "../../../api/projectRecordsQueries";
import { FormListNodesMapper } from "../../../components/FormListNodesMapper";
import { UPD_WATER_BALANCE_RECORDS as pageMainKey } from "../../../config/keysConfig";
import { findServiceWaterClassName, serviceWaterClassEnum as waterClassEnum } from "../../../config/waterBalance";
import { wbWaterSavingEquipsUpdateStates as selectorStates } from "../../../hooks/selectorStates";
import { onPreSubmitError } from "../../../utils/screens";

const dataFeeder = makeDataFeeder();

/**
 * 服务业表: 节水器具配备情况.
 * 目前只有服务业表项目包括此表. 添加自定义字段尚未实现.
 * 注意, 项目数据表单与工作台管理表单的数据有一个显著的区别:
 * 项目数据不需要像用户编号那样脱敏, 两边传输的数据是直接的, 不需要做{id, name}这种映射, 部分需要映射的可以在客户端本地完成,
 * @param {object} args
 * @param {object} args.navigation
 * @param {object} args.route
 * @returns
 */
const RecordsUpdating = ({ navigation, route }) => {
    console.log("RecordsUpdating pageMeta from nav:", route.params.pageMeta);
    console.log("RecordsUpdating projMeta from nav:", route.params.projMeta);
    const projSubclass = route.params.projMeta.subclass; // 1表, 2书
    // 注: 工业和服务业表格相同, 暂时不作改动
    //const projIndustry = parseIndustryCode(route.params.projMeta.industry); // 1 -> industry, 2 -> service

    const { queryKwd, id: recordPubid, name: recordName, formType } = route.params.pageMeta; // recordPubid is the alias of id
    const saveButtonIcon = "content-save-all-outline";
    const formEditableGlobal = checkPermits() ? (formType === 0 ? true : false) : false;

    // 使用memoize来优化
    const { setStore, getStore, clearStore, setStoreObject } = useMemo(() => {
        console.log("creatMMKVStore memo:", pageMainKey.store, queryKwd, recordPubid);
        return creatMMKVStore(`${pageMainKey.store}/${queryKwd}/${recordPubid}`);
    }, [queryKwd, recordPubid]);

    const storedValueToFormValue = useRef(makeStoredValueToFormValue(getStore));
    const getClientCversion = useRef(makeGetClientCversion(() => getStore("mainCversion"), () => getStore("subCversion")));

    // 客户端cversion = `${mainCversion}.${subCversion}`
    (getStore("mainCversion") === undefined) ? setStore("mainCversion", 0) : undefined; // 客户端主版本号
    (getStore("subCversion")  === undefined) ? setStore("subCversion",  0) : undefined; // 客户端次版本号, 修改一次自增一次
    //let mainCversion = getStore("mainCversion"); // 主版本号, 与服务端cversion对应
    // 次版本号, 表示本地更改次数, 用于程序上对子版本号的增加, 用于判断与服务端版本的差异, 同时可用于判断避免重复从MMKV获取数据
    const subCversionRef = useRef(getStore("subCversion"));

    const waterClassStateDataProviderRef = useRef(waterClassEnum);

    // 新组件需要修改!!
    const bottomBarLabels = { ok: "提交" };                    // 底部按钮显示文字
    const [snackBarMessage, setSnackBarMessage] = useState("更新表单遇到错误"); // 下方错误提示文字内容

    const [
        waterClassRadioState,
        setWaterClassRadioState,
        resetSelectorStates,
    ] = selectorStates(useShallow(state => [
        state.waterClassRadio,
        state.setWaterClassRadio,
        state.resetStates,
    ])); // radio组件状态

    const [confimDialogConfig, setConfirmDialogConfig] = useState({});       // 对话框信息, {title, text, okLabel, onOK}, 但显示对话框的变量在showConfirm

    // 新组件不需改动
    const [screenTitle,      setScreenTitle]      = useState(route.params.pageMeta.name); // 屏幕上方导航栏标题
    const [okButtonLoading,  setOkButtonLoading]  = useState(false);            // 右下方按钮图标是否旋转
    const [okButtonDisabled, setOkButtonDisabled] = useState(!checkPermits());  // 右下方按钮是否禁用
    const [showSnackbar,     setShowSnackbar]     = useState(false);            // 下方是否显示错误通知
    const [showConfirm,      setShowConfirm]      = useState(false);            // 是否显示确认对话框
    const [loadingIndicatorVisible, setLoadingIndicatorVisible] = useState(false); // 是否显示loading
    const delQueryInfo = useRef({ id: recordPubid, name: recordName });         // 用于删除本记录的查询变量

    // 用于存储waterScale的值, 当unitToggleState发生改变时需要通过该值更新表单字段
    //const waterUnitScaleRef = useRef(0);

    // Define form
    // 新组件需要重新定义!!
    const schema = Joi.object({
        name:              validatorBase.waterBalance.commons.name,
        waterClass:        validatorBase.waterBalance.factBase.waterClass,
        //unitId:          validatorBase.waterBalance.commons.intField, //预留
        unitName:          validatorBase.waterBalance.commons.name,
        urinal:            validatorBase.waterBalance.commons.intFieldUnrequired,
        squatToilet:       validatorBase.waterBalance.commons.intFieldUnrequired,
        toilet:            validatorBase.waterBalance.commons.intFieldUnrequired,
        faucet:            validatorBase.waterBalance.commons.intFieldUnrequired,
        shower:            validatorBase.waterBalance.commons.intFieldUnrequired,
        waterDispenser:    validatorBase.waterBalance.commons.intFieldUnrequired,
        otherEquips:       validatorBase.waterBalance.commons.longTextField,
        ecoUrinal:         validatorBase.waterBalance.commons.intFieldUnrequired,
        ecoSquatToilet:    validatorBase.waterBalance.commons.intFieldUnrequired,
        ecoToilet:         validatorBase.waterBalance.commons.intFieldUnrequired,
        ecoFaucet:         validatorBase.waterBalance.commons.intFieldUnrequired,
        ecoShower:         validatorBase.waterBalance.commons.intFieldUnrequired,
        ecoWaterDispenser: validatorBase.waterBalance.commons.intFieldUnrequired,
        otherEcoEquips:    validatorBase.waterBalance.commons.longTextField,
        // other fields
        others:       validatorBase.waterBalance.commons.textField,
        remarks:      validatorBase.waterBalance.commons.longTextField,
    });

    // 新组件需要重新定义defaultValues!!
    const {
        handleSubmit,
        control,
        resetField,
        reset,
        formState: { errors },
        //setValue,
        //getValues,
    } = useForm({
        resolver: joiResolver(schema),
        //reValidateMode: "onBlur", // https://github.com/orgs/react-hook-form/discussions/10327
        defaultValues: { // 默认值在组件载入时会被更新, 这里的初始值正常情况下没有用处
            name:              "",
            waterClass:        "",
            //unitId:          "",
            unitName:          "",
            urinal:            "",
            squatToilet:       "",
            toilet:            "",
            faucet:            "",
            shower:            "",
            waterDispenser:    "",
            otherEquips:       "{}",
            ecoUrinal:         "",
            ecoSquatToilet:    "",
            ecoToilet:         "",
            ecoFaucet:         "",
            ecoShower:         "",
            ecoWaterDispenser: "",
            otherEcoEquips:    "{}",
            others:            "",
            remarks:           "",
        },
    });

    // Query: select record
    const onRecordSelectSuccess = (data) => { // data: {"id": 3, "leader": 0, "name": "DEPT-03", "superior": 0}
        // 注意, 进入onSuccess之前就意味着服务端版本比客户端版本新, 因此要更新客户端主版本号
        console.log("onRecordSelectSuccess:", data);
        if (data.STATUS === 0) {
            setStore("mainCversion", data.DATA.cversion);
            setStore("subCversion", 0);

            const formObjects  = {
                name:              String(data.DATA.name),
                waterClass:        String(data.DATA.waterClass),
                //unitId:          String(data.DATA.unitId),
                unitName:          String(data.DATA.unitName),
                urinal:            String(data.DATA.urinal),
                squatToilet:       String(data.DATA.squatToilet),
                toilet:            String(data.DATA.toilet),
                faucet:            String(data.DATA.faucet),
                shower:            String(data.DATA.shower),
                waterDispenser:    String(data.DATA.waterDispenser),
                otherEquips:       String(data.DATA.otherEquips),
                ecoUrinal:         String(data.DATA.ecoUrinal),
                ecoSquatToilet:    String(data.DATA.ecoSquatToilet),
                ecoToilet:         String(data.DATA.ecoToilet),
                ecoFaucet:         String(data.DATA.ecoFaucet),
                ecoShower:         String(data.DATA.ecoShower),
                ecoWaterDispenser: String(data.DATA.ecoWaterDispenser),
                otherEcoEquips:    String(data.DATA.otherEcoEquips),
                others:            String(data.DATA.others),
                remarks:           String(data.DATA.remarks),
            };
            reset(formObjects);           // 重置react-form

            const storeObjects = checkPermits() && {
                ...formObjects,
                waterClass: radioIdToObject(waterClassStateDataProviderRef.current, data.DATA.waterClass),
            };
            storeObjects && setStoreObject(storeObjects, false); // 判断用户权限, 把服务端数据写到mmkv存储, 只有可写用户才需要本地存储, 不会首先清空老数据

            // 设置selector菜单状态
            setWaterClassRadioState(storeObjects.waterClass);

            // 设置屏幕标题
            (screenTitle !== formObjects.name) && setScreenTitle(formObjects.name);
        } else  {
            console.log("STATUS !== 0", data.STATUS);
            setSnackBarMessage(`表单数据拉取发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        }
    };
    const onRecordSelectError = (err) => {
        console.log("Record selecting error:", err);
    };
    const onRecordSelectSettled = (err) => {
        loadingIndicatorVisible && setLoadingIndicatorVisible(false);
    };
    const recordSelectQuery = recordSelectQueryMaker([queryKwd, "get", recordPubid], onRecordSelectSuccess, onRecordSelectError, onRecordSelectSettled);

    // query update
    // 新组件需要重新定义setDiagText!!
    const onHandleSubmit = (data) => {
        setOkButtonDisabled(true);
        setOkButtonLoading(true);
        dataFeeder({
            ...data,
            name: `${findServiceWaterClassName(data.waterClass) + "/" + data.unitName}`,
            cversion: getClientCversion.current(),
        }); // append client cversion
        recordUpdateQuery.mutate();
    };

    // 新组件不需改动
    const onRecordUpdateSuccess = (data) => {
        console.log("response success, data: ", data);
        if(data.STATUS !== 0) {
            setSnackBarMessage(`表单更新发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        } else {
            // 设置确认框数据
            setConfirmDialogConfig({
                title: "更新表单",
                text: "表单信息更新成功!",
                okLabel: "确定",
                onOK: () => {
                    setShowConfirm(false);
                    recordSelectQuery.mutate();
                }});
            setShowConfirm(true);
        }
    };
    const onRecordUpdateError = (error) => {
        setSnackBarMessage(`表单更新发生错误, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const onRecordUpdateSettled = (data, error) => {
        setOkButtonDisabled(false);
        setOkButtonLoading(false);
    };
    const recordUpdateQuery = recordUpdateQueryMaker(dataFeeder, [queryKwd, "upd", recordPubid], onRecordUpdateSuccess, onRecordUpdateError, onRecordUpdateSettled);

    // query delete
    const recordDeleteOnSuccess = () => {
        clearStore();
        navigation.goBack();
    };
    const recordDeleteOnError = (error) => {
        setSnackBarMessage(`表单删除失败, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const recordDeleteQuery = recordDeleteQueryMaker([queryKwd, "del", recordPubid], recordDeleteOnSuccess, recordDeleteOnError);

    const deleteAlert = () => {
        return (
            Alert.alert("删除表单", `删除后不可撤销, 请确认是否删除: ${delQueryInfo.current?.name}?`, [
                { text: "取消" },
                { text: "确定", onPress: () => recordDeleteQuery.mutate() },
            ])
        );
    };

    const clearCache = () => {
        return (
            Alert.alert("清空本地缓存", "1. 用于未知原因导致数据无法提交的情况\r\n2. 未提交的内容将会丢失, 请做好数据截图备份\r\n3. 已提交到服务端的数据不受影响\r\n4. 确认清空请点击“确认”按钮", [
                { text: "取消" },
                { text: "确定", onPress: () => { clearStore(); navigation.goBack(); } },
            ])
        );
    };


    // Hook: onMount fetch data
    useEffect(() => {
        // 优化方案: 根据用户权限进行mutate, 只有可写用户才需要本地存储
        const localCversion = getClientCversion.current();
        const remoteCversion = route.params.pageMeta.cversion;
        console.log("localCversion:", localCversion, "remoteSversion:", remoteCversion, "subCversionRef:", subCversionRef.current);

        const restoreLocalData = () => {
            const formObjects = { // 默认值用户表单的数据显示
                name:              storedValueToFormValue.current("name"),
                waterClass:        storedValueToFormValue.current("waterClass"),
                //unitId:          storedValueToFormValue.current("name"),
                unitName:          storedValueToFormValue.current("unitName"),
                urinal:            storedValueToFormValue.current("urinal"),
                squatToilet:       storedValueToFormValue.current("squatToilet"),
                toilet:            storedValueToFormValue.current("toilet"),
                faucet:            storedValueToFormValue.current("faucet"),
                shower:            storedValueToFormValue.current("shower"),
                waterDispenser:    storedValueToFormValue.current("waterDispenser"),
                otherEquips:       storedValueToFormValue.current("otherEquips"),
                ecoUrinal:         storedValueToFormValue.current("ecoUrinal"),
                ecoSquatToilet:    storedValueToFormValue.current("ecoSquatToilet"),
                ecoToilet:         storedValueToFormValue.current("ecoToilet"),
                ecoFaucet:         storedValueToFormValue.current("ecoFaucet"),
                ecoShower:         storedValueToFormValue.current("ecoShower"),
                ecoWaterDispenser: storedValueToFormValue.current("ecoWaterDispenser"),
                otherEcoEquips:    storedValueToFormValue.current("otherEcoEquips"),
                others:            storedValueToFormValue.current("others"),
                remarks:           storedValueToFormValue.current("remarks"),
            };
            reset(formObjects); // 重置react-form数据

            // 设置selector数据
            setWaterClassRadioState(getStore("waterClass"));
            //setWaterUnitScaleState(getStore("waterScale") || radioIdToObject(waterUnitScaleStateDataProviderRef.current, defaultWaterUnitScale));
            //setWaterTypeRadioState(getStore("waterType"));
            //setMainUseCheckState(getStore("mainUse"));
            //setTurbidityState(getStore("turbidity"));

            //waterUnitScaleRef.current = getStore("waterScale")?.id || defaultWaterUnitScale; // 根据本地存储更新
        };

        if (formType === 1) {
            recordSelectQuery.mutate();
        } else if (localCversion === 0 && remoteCversion > 0) {
            console.log("Local cversion === 0, serve may have something nontrivial initialized, query mutate!");
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        } else if (isServerDataNewer(localCversion, remoteCversion) && subCversionRef.current > 0) { // 服务端有更新数据, 但本地有修改
            console.log("Client cversion fell behind the server cversion, someone might modified the database, choose to use the data version!");
            Alert.alert("注意", "由于其他人向服务器提交了数据, 服务端数据比本地数据新, 请选择使用哪份数据?", [
                { text: "使用本地数据",   onPress: () => { setStore("mainCversion", remoteCversion); restoreLocalData(); } },
                { text: "使用服务端数据", onPress: () => { setLoadingIndicatorVisible(true); recordSelectQuery.mutate(); } },
            ]);
        } else if (isServerDataNewer(localCversion, remoteCversion)) { // localCversion==0时要mutate是因为服务端可能在创建记录时生成了必要数据
            console.log("Server cversion may be newer, query mutate:", localCversion, remoteCversion);
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        }
        else {
            console.log("Local cversion >= server cversion, query will not mutate, use local data!");
            restoreLocalData();
        }
    }, []);

    const FieldsConfig_1_book = [
        {
            inputs: [
                //{ name: "name",         label: "表单名称",      unit: "",     type: "PLAIN", editable: true, multiline: true, },
                { name: "waterClass",     label: "用水类别",      unit: "",     type: "RADIO", editable: true, placeholder: "", selectorState: waterClassRadioState,  setSelectorState: setWaterClassRadioState, dataProvider: waterClassEnum, },
                { name: "unitName",       label: "用水单元名称",   unit: "",     type: "PLAIN", editable: true, placeholder: "", },
            ],
        },
        {
            nodeType: "Section", title: "主要用水设备和器具数量", nodes: [
                {
                    inputs: [
                        { name: "urinal",         label: "小便器",        unit: "个",   type: "PLAIN", editable: true, placeholder: "", },
                        { name: "squatToilet",    label: "蹲便器",        unit: "个",   type: "PLAIN", editable: true, placeholder: "", },
                        { name: "toilet",         label: "坐便器",        unit: "个",   type: "PLAIN", editable: true, placeholder: "", },
                        { name: "faucet",         label: "水龙头",        unit: "个",   type: "PLAIN", editable: true, placeholder: "", },
                        { name: "shower",         label: "花洒",          unit: "个",   type: "PLAIN", editable: true, placeholder: "", },
                        { name: "waterDispenser", label: "饮水机",        unit: "个",   type: "PLAIN", editable: true, placeholder: "", },
                    ]
                }]
        },
        {
            nodeType: "Section", title: "节水型用水设备和器具配备数量", nodes: [
                {
                    inputs: [
                        { name: "ecoUrinal",         label: "小便器",        unit: "个",   type: "PLAIN", editable: true, placeholder: "", },
                        { name: "ecoSquatToilet",    label: "蹲便器",        unit: "个",   type: "PLAIN", editable: true, placeholder: "", },
                        { name: "ecoToilet",         label: "坐便器",        unit: "个",   type: "PLAIN", editable: true, placeholder: "", },
                        { name: "ecoFaucet",         label: "水龙头",        unit: "个",   type: "PLAIN", editable: true, placeholder: "", },
                        { name: "ecoShower",         label: "花洒",          unit: "个",   type: "PLAIN", editable: true, placeholder: "", },
                        { name: "ecoWaterDispenser", label: "饮水机",        unit: "个",   type: "PLAIN", editable: true, placeholder: "", },
                    ]
                }],
        },
    ];
    const FieldsConfig_1_table = [
        {
            inputs: [
                //{ name: "name",         label: "表单名称",      unit: "",     type: "PLAIN", editable: true, multiline: true, },
                { name: "waterClass",     label: "用水类别",      unit: "",     type: "RADIO", editable: true, placeholder: "", selectorState: waterClassRadioState,  setSelectorState: setWaterClassRadioState, dataProvider: waterClassEnum, },
                { name: "unitName",       label: "用水单元名称",   unit: "",     type: "PLAIN", editable: true, placeholder: "", },
            ],
        },
        {
            nodeType: "Section", title: "主要用水设备和器具数量", nodes: [
                {
                    inputs: [
                        //{ name: "name",         label: "表单名称",      unit: "",     type: "PLAIN", editable: true, multiline: true, },
                        { name: "urinal",         label: "小便器",        unit: "个",   type: "PLAIN", editable: true, placeholder: "", },
                        { name: "squatToilet",    label: "蹲便器",        unit: "个",   type: "PLAIN", editable: true, placeholder: "", },
                        { name: "toilet",         label: "坐便器",        unit: "个",   type: "PLAIN", editable: true, placeholder: "", },
                        { name: "faucet",         label: "水龙头",        unit: "个",   type: "PLAIN", editable: true, placeholder: "", },
                        { name: "shower",         label: "花洒",          unit: "个",   type: "PLAIN", editable: true, placeholder: "", },
                        { name: "waterDispenser", label: "饮水机",        unit: "个",   type: "PLAIN", editable: true, placeholder: "", },
                    ]
                }]
        },
        {
            nodeType: "Section", title: "节水型用水设备和器具配备数量", nodes: [
                {
                    inputs: [
                        { name: "ecoUrinal",         label: "小便器",        unit: "个",   type: "PLAIN", editable: true, placeholder: "", },
                        { name: "ecoSquatToilet",    label: "蹲便器",        unit: "个",   type: "PLAIN", editable: true, placeholder: "", },
                        { name: "ecoToilet",         label: "坐便器",        unit: "个",   type: "PLAIN", editable: true, placeholder: "", },
                        { name: "ecoFaucet",         label: "水龙头",        unit: "个",   type: "PLAIN", editable: true, placeholder: "", },
                        { name: "ecoShower",         label: "花洒",          unit: "个",   type: "PLAIN", editable: true, placeholder: "", },
                        { name: "ecoWaterDispenser", label: "饮水机",        unit: "个",   type: "PLAIN", editable: true, placeholder: "", },
                    ]
                }],
        },
    ];

    const FieldsConfig_2_book = [];
    const FieldsConfig_2_table = [];

    const FieldsConfig_3_book = [
        // 其它部分
        {
            inputs: [
                //{ name: "others",  label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "", },
            ]
        },
    ];
    const FieldsConfig_3_table = [
        // 其它部分
        {
            inputs: [
                //{ name: "others",  label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "", },
            ]
        },
    ];

    //const FieldsConfig = [...FieldsConfig_1, ...FieldsConfig_2, ...FieldsConfig_3];
    let FieldsConfig = [];
    switch(projSubclass) {
        case 1: // 表
            FieldsConfig = [...FieldsConfig_1_table, ...FieldsConfig_2_table, ...FieldsConfig_3_table];
            break;
        case 2: // 书
            FieldsConfig = [...FieldsConfig_1_book, ...FieldsConfig_2_book, ...FieldsConfig_3_book];
            break;
        default:
            console.warn("Unknown subclass:", projSubclass);
            FieldsConfig = [];
            break;
    }

    return (
        <View style={{ flex: 1 }}>

            <HeaderBar
                //title={screenTitle}
                navigation={navigation}
                goBackCallback={() => {
                    callOneByOne(
                        //() => { _mainCversionGetter=undefined; _subCversionGetter=undefined;},
                        resetSelectorStates);
                    /*callOneByOne(clearStore, resetSelectorStates)*/  // 返回前清空mmkv存储因为临时数据不再需要, 重置状态使得页面切换后不会使用前面的数据
                }}
                menuItemArray={[{ title: "删除表单", action: deleteAlert }, { title: "清空缓存", action: clearCache }]}
            />

            <ScreenWrapper contentContainerStyle={styles.container}>

                <View style={styles.formEntry}>

                    <FormListNodesMapper
                        fieldsConfig={FieldsConfig}
                        subCversionRef={subCversionRef}
                        control={control}
                        setStore={setStore}
                        resetField={resetField}
                        errors={errors}
                        setSnackBarMessage={setSnackBarMessage}
                        setShowSnackbar={setShowSnackbar}
                        editable={formEditableGlobal}
                        rowStyle={styles.rowContainer}
                    />

                    <Divider/>

                    <Divider bold={true}/>

                </View>

                <View style={styles.button}>
                    <Button mode="contained"
                        disabled={okButtonDisabled}
                        loading={okButtonLoading}
                        icon={saveButtonIcon}
                        onPress={handleSubmit(onHandleSubmit, (formErr) => onPreSubmitError(formErr, getStore, "WaterSourceRecordsUpdating"))}
                    >{bottomBarLabels.ok}</Button>
                </View>
            </ScreenWrapper>

            <LoadingIndicator
                title={""}
                message={"加载中....."}
                visible={loadingIndicatorVisible}
                onClose={()=>setLoadingIndicatorVisible(false)}
            />

            <Snackbar
                style={styles.snackBar}
                visible={showSnackbar}
                onDismiss={() => setShowSnackbar(false)}
                onIconPress={() => setShowSnackbar(false)}
                duration={Snackbar.DURATION_LONG}
            >
                {snackBarMessage}
            </Snackbar>

            <DialogToConfirm
                visible={showConfirm}
                title={confimDialogConfig.title ? confimDialogConfig.title : ""}
                text={confimDialogConfig.text ? confimDialogConfig.text : ""}
                onOK={confimDialogConfig.onOK ? confimDialogConfig.onOK : () => setShowConfirm(false)}
                okBtnLabel={confimDialogConfig.okLabel ? confimDialogConfig.okLabel : "确认"}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    rowContainer:{
        firstColumn: {
            flex: 2,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 3,
        // /borderWidth: 1,
        // height: 40,
        },
    },
    rowContainerLongText: {
        firstColumn: {
            flex: 1,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 4,
            // /borderWidth: 1,
            // height: 40,
        },
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        width: "80%",
        alignSelf: "center",
    },
    formEntry: {
        margin: 8,
    },
    snackBar: {
        flexDirection: "column",
    },
});

export default RecordsUpdating;
