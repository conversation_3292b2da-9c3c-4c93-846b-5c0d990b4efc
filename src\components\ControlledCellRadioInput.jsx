import React, { useState } from "react";
import { StyleSheet, Pressable } from "react-native";
import { TextInput, useTheme } from "react-native-paper";
import { DialogWithRadioButton } from "./DialogWithRadioButton";
import PropTypes from "prop-types";


/**
 * 由于CellTextInput的左边有固定的文字标签,
 * 因此在这里, 原本用作TextInput的label的内容, 在此被placeholder取代
 * @param {Object} arg
 * @param {string} arg.title
 * @param {string} arg.value
 * @param {function} arg.onRadioConfirm
 * @param {function} arg.defaultRadioCheck
 * @param {array} arg.radioItemArray
 */
const ControlledCellRadioInput = ({ title, placeholder, onRadioConfirm, defaultRadioCheck, okBtnLabel, cancelBtnLabel, radioItemArray, ...props }) => {
    //const [iconColor, setIconColor] = useState("transparent");
    //const [labelText, setLabelText] = useState(props.errorText || placeholder);

    const [radioVisible, setRadioVisible] = useState(false);
    //const [value, setValue] = useState(getStoredVal()?.name);
    const theme = useTheme();
    const styles = StyleSheet.create({
        input: {
            width: "100%",
            backgroundColor: theme.colors.surface,
            fontSize: 18,
            //borderWidth: 1,
            //borderColor: "red",
        },
        error: {
            fontSize: 13,
            color: theme.colors.error,
            paddingTop: 8,
        },
    });

    return (
        <>
            <Pressable onPress={() => setRadioVisible(true)} >
                <TextInput
                    value={props.resultText}
                    //label={labelText}
                    placeholder={placeholder}
                    editable={false}
                    style={styles.input}
                    selectionColor={theme.colors.primary}
                    underlineColor={theme.colors.elevation.level0}
                    outlineColor={theme.colors.elevation.level0} // 透明, 文本外框颜色
                    placeholderTextColor={theme.colors.elevation.level5} // 占位符文字颜色
                    mode={"flat"}
                    right={<TextInput.Icon icon="chevron-down" onPress={() => {
                        setRadioVisible(true);
                    }} /> }

                    //onChangeText={(text) => onChangeText(text)}
                    {...props}
                />
            </Pressable>
            <DialogWithRadioButton
                visible={radioVisible}
                title={title}
                onOK={(checkedData) => { // checkedData: {"id": 2, "name": "DEPT-02"}
                    setRadioVisible(false);
                    //setLabelText(checkedData);
                    props.setResultText(checkedData.name);
                    onRadioConfirm(checkedData);}}
                onCancel={() => { setRadioVisible(false); }}
                okBtnLabel={okBtnLabel}
                cancelBtnLabel={cancelBtnLabel}
                defaultRadioCheck={defaultRadioCheck}
                radioItemArray={radioItemArray}
            />
        </>
    );
};

ControlledCellRadioInput.propTypes = {
    title: PropTypes.string,
    value: PropTypes.string,
    //label: PropTypes.string,
    getStoredVal: PropTypes.func,
    placeholder: PropTypes.string,
    onRadioConfirm: PropTypes.func,
    onChangeText: PropTypes.func,
    editable: PropTypes.bool,
    mode: PropTypes.string,
    radioItemArray: PropTypes.array,
};

export default ControlledCellRadioInput;
