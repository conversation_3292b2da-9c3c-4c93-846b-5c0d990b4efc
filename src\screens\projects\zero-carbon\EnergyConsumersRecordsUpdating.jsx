import { joiResolver } from "@hookform/resolvers/joi";
import "fast-text-encoding";
import <PERSON><PERSON> from "joi";
import debounce from "lodash/debounce";
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useForm, useWatch } from "react-hook-form";
import { Alert, StyleSheet, View } from "react-native";
import { Snackbar } from "react-native-paper";
import Button from "../../../components/Button";
import { DialogToConfirm } from "../../../components/DialogToConfirm";
import { FormListNodesMapper } from "../../../components/FormListNodesMapper";
import HeaderBar from "../../../components/HeaderBar";
import { LoadingIndicator } from "../../../components/LoadingIndicator";
import { creatMMKVStore, makeStoredValueToFormValue } from "../../../services/local-storage";
import { callOneByOne, ifTruthLet, isNullness, makeDataFeeder } from "../../../utils";
import { checkPermits } from "../../../utils/permits";
import { isServerDataNewer, makeGetClientCversion, radioIdToObject } from "../../../utils/records";
import { validatorBase } from "../../../utils/validatorBase";
import ScreenWrapper from "../../ScreenWrapper";


// 新组件需要重新!!
import { useShallow } from "zustand/shallow";
import {
    makeReqDelZeroCarbonRecords as recordDeleteQueryMaker,
    makeReqGetZeroCarbonRecords as recordSelectQueryMaker,
    makeReqUpdZeroCarbonRecords as recordUpdateQueryMaker,
} from "../../../api/projectRecordsQueries";
import { UPD_ZERO_CARBON_RECORDS as pageMainKey } from "../../../config/keysConfig";
import { statisticsYearEnum } from "../../../config/zeroCarbon";
import { zcEnergyComsumersStates as selectorStates } from "../../../hooks/selectorStates";
import { onPreSubmitError } from "../../../utils/screens";


const dataFeeder = makeDataFeeder();

/**
 * 用能人数统计表
 * @param {object} args
 * @param {object} args.navigation
 * @param {object} args.route
 * @returns
 */
const RecordsUpdating = ({ navigation, route }) => {
    console.log("RecordsUpdating pageMeta from nav:", route.params.pageMeta);
    console.log("RecordsUpdating projMeta from nav:", route.params.projMeta);

    const { title: screenTitle, } = route.params.projMeta;
    //const projClass    = route.params.projMeta.class;     // 项目class, 1水平衡, 11零碳诊断
    //const industryType = route.params.projMeta.industry;  // 行业类型, 1工业, 2服务业
    //const projIndustry = useRef(parseIndustryCode(industryType, projClass)); // "publicInst"

    // formType对应着服务端的form-type, 为0时表示表单是用户填写, 为1表示服务端生成, 为1时只读
    const { queryKwd, id: recordPubid, name: recordName, formType } = route.params.pageMeta; // recordPubid is the alias of id
    const saveButtonIcon = "content-save-all-outline";
    const formEditableGlobal = checkPermits() ? (formType === 0 ? true : false) : false;

    // 使用memoize来优化
    const { setStore, getStore, clearStore, setStoreObject } = useMemo(() => {
        console.log("creatMMKVStore memo:", pageMainKey.store, queryKwd, recordPubid);
        return creatMMKVStore(`${pageMainKey.store}/${queryKwd}/${recordPubid}`);
    }, [queryKwd, recordPubid]);

    const storedValueToFormValue = useRef(makeStoredValueToFormValue(getStore));
    const getClientCversion = useRef(makeGetClientCversion(() => getStore("mainCversion"), () => getStore("subCversion")));

    // 客户端cversion = `${mainCversion}.${subCversion}`
    // 这两行没出重复渲染都要获取, 不放到useEffect中
    (getStore("mainCversion") === undefined) && setStore("mainCversion", 0); // 客户端主版本号
    (getStore("subCversion")  === undefined) && setStore("subCversion",  0); // 客户端次版本号, 修改一次自增一次
    //let mainCversion = getStore("mainCversion"); // 主版本号, 与服务端cversion对应
    // 次版本号, 表示本地更改次数, 用于程序上对子版本号的增加, 用于判断与服务端版本的差异, 同时可用于判断避免重复从MMKV获取数据
    const subCversionRef = useRef(getStore("subCversion"));

    // 新组件需要修改!!
    const bottomBarLabels = { ok: "提交" };                    // 底部按钮显示文字
    const [snackBarMessage, setSnackBarMessage] = useState("更新表单遇到错误"); // 下方错误提示文字内容
    const [confimDialogConfig, setConfirmDialogConfig] = useState({});       // 对话框信息, {title, text, okLabel, onOK}, 但显示对话框的变量在showConfirm
    const [okButtonLoading,  setOkButtonLoading]  = useState(false);            // 右下方按钮图标是否旋转
    const [okButtonDisabled, setOkButtonDisabled] = useState(!checkPermits());  // 右下方按钮是否禁用
    const [showSnackbar,     setShowSnackbar]     = useState(false);            // 下方是否显示错误通知
    const [showConfirm,      setShowConfirm]      = useState(false);            // 是否显示确认对话框
    const [loadingIndicatorVisible, setLoadingIndicatorVisible] = useState(true); // 是否显示loading, 还负责是否渲染ScreenWrapper组件, 为false时渲染.
    const delQueryInfo = useRef({ id: recordPubid, name: recordName });         // 用于删除本记录的查询变量

    const [
        yearRadioState,
        setYearRadioState,
        resetSelectorStates,
    ] = selectorStates(useShallow(state => [
        state.yearRadio,
        state.setYearRadio,
        state.resetStates,
    ])); // radio组件状态

    //const defaultProvince = useRef(13); // 河北
    const yearRadioStateDataProviderRef    = useRef(statisticsYearEnum);


    // Define form
    // 新组件需要重新定义!!
    const schema = Joi.object({
        year:           validatorBase.zeroCarbon.commons.yearField,
        headcount:      validatorBase.zeroCarbon.commons.intField,
        conDispCount:   validatorBase.zeroCarbon.commons.intField,
        //contractCount:  validatorBase.zeroCarbon.commons.intField,
        //dispatchCount:  validatorBase.zeroCarbon.commons.intField,
        flowCount:      validatorBase.zeroCarbon.commons.intField,
        supportCount:   validatorBase.zeroCarbon.commons.intField,
        countSum:       validatorBase.zeroCarbon.commons.intField,
        // other fields
        others:       validatorBase.waterBalance.waterUsageUnits.textField,
        remarks:      validatorBase.waterBalance.waterUsageUnits.longTextField,
    });

    const defaultFormValues = useMemo(() => ({
        // 注意, 默认为0的是允许默认为0的值, 或者通过计算得到的值
        year:          "",
        headcount:     "",
        conDispCount:  "",
        //contractCount: "",
        //dispatchCount: "",
        flowCount:     "",
        supportCount:  "",
        countSum:      "",
        // other fields
        others:       "",
        remarks:      "",
    }), []);

    // 新组件需要重新定义defaultValues!!
    const {
        handleSubmit,
        control,
        resetField,
        reset,
        formState: { errors },
        setValue,
        getValues,
    } = useForm({
        resolver: joiResolver(schema),
        //reValidateMode: "onBlur", // https://github.com/orgs/react-hook-form/discussions/10327
        // 默认值在组件载入时会被更新, 这里的初始值正常情况下没有用处
        defaultValues: defaultFormValues
    });

    // Query: select record
    const onRecordSelectSuccess = useCallback((data) => { // data: {"id": 3, "leader": 0, "name": "DEPT-03", "superior": 0}
        // 注意, 进入onSuccess之前就意味着服务端版本比客户端版本新, 因此要更新客户端主版本号
        console.log("onRecordSelectSuccess:", data);
        if (data.STATUS === 0) {
            setStore("mainCversion", data.DATA.cversion);
            setStore("subCversion", 0);

            const formObject = {
                year:          String(data.DATA.year),
                headcount:     String(data.DATA.headcount),
                conDispCount:  String(data.DATA.conDispCount),
                //contractCount: String(data.DATA.contractCount),
                //dispatchCount: String(data.DATA.dispatchCount),
                flowCount:     String(data.DATA.flowCount),
                supportCount:  String(data.DATA.supportCount),
                countSum:      String(data.DATA.countSum),
                // other fields
                others:       String(data.DATA.others),
                remarks:      String(data.DATA.remarks),
            };
            reset(formObject);           // 重置react-form

            const storeObjects = checkPermits() && (formType === 0) && {
                ...formObject,
                year: radioIdToObject(yearRadioStateDataProviderRef.current, data.DATA.year),
            };
            storeObjects && setStoreObject(storeObjects, false); // 判断用户权限, 把服务端数据写到mmkv存储, 只有可写用户才需要本地存储, 不会首先清空老数据

            // 设置selector菜单状态
            setYearRadioState(storeObjects.year);
        } else {
            console.log("STATUS !== 0", data.STATUS);
            setSnackBarMessage(`表单数据拉取发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        }
    }, [setStore, setStoreObject, reset, formType, setYearRadioState, yearRadioStateDataProviderRef]);

    const onRecordSelectError = useCallback((err) => {
        console.log("Record selecting error:", err);
    }, []);

    const onRecordSelectSettled = useCallback((data, err) => {
        //console.log("Record selecting settled...............:", data, err);
        const loadingDuration = 100;
        const currentTime = Date.now();
        const mutationTime = data?.mutationTime || currentTime;

        //console.log("record selecting time left: ", currentTime - data.mutationTime);
        if (currentTime - mutationTime < loadingDuration) {
            setTimeout(() => {
                loadingIndicatorVisible && setLoadingIndicatorVisible(false);
            }, currentTime - data.mutationTime);
        } else {
            loadingIndicatorVisible && setLoadingIndicatorVisible(false);
        }
    }, [loadingIndicatorVisible]); // 必需要依赖, 不然不会关闭loading

    const getPath = formType === 0 ? "get" : "getsum";
    const recordSelectQuery = recordSelectQueryMaker([queryKwd, getPath, recordPubid], onRecordSelectSuccess, onRecordSelectError, onRecordSelectSettled);

    // Query: update record
    const onHandleSubmit = useCallback((data) => {
        setOkButtonDisabled(true);
        setOkButtonLoading(true);
        dataFeeder({ ...data,
            cversion: getClientCversion.current(),
            name:     String(data.year),
        }); // append client cversion
        recordUpdateQuery.mutate();
    }, [getClientCversion, recordUpdateQuery]);

    // 新组件不需改动
    const onRecordUpdateSuccess = useCallback((data) => {
        console.log(" Record update success response data: ", data);
        if(data.STATUS !== 0) {
            setSnackBarMessage(`表单更新发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        } else {
            // 设置确认框数据
            setConfirmDialogConfig({
                title: "更新表单",
                text: "表单信息更新成功!",
                okLabel: "确定",
                onOK: () => {
                    setShowConfirm(false);
                    recordSelectQuery.mutate();
                }});
            setShowConfirm(true);
        }
    }, [recordSelectQuery]);

    const onRecordUpdateError = useCallback((error) => {
        setSnackBarMessage(`表单更新发生错误, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    }, []);

    const onRecordUpdateSettled = useCallback((data, error) => {
        setOkButtonDisabled(false);
        setOkButtonLoading(false);
    }, []);

    const recordUpdateQuery = recordUpdateQueryMaker(dataFeeder, [queryKwd, "upd", recordPubid], onRecordUpdateSuccess, onRecordUpdateError, onRecordUpdateSettled);

    // Query: delete record
    const recordDeleteOnSuccess = useCallback(() => {
        clearStore();
        resetSelectorStates();
        navigation.goBack();
    }, [clearStore, resetSelectorStates, navigation]);
    const recordDeleteOnError = useCallback((error) => {
        setSnackBarMessage(`表单删除失败, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    }, []);
    const recordDeleteQuery = recordDeleteQueryMaker([queryKwd, "del", recordPubid], recordDeleteOnSuccess, recordDeleteOnError);

    const deleteAlert = useCallback(() => {
        return (
            Alert.alert("删除表单", `删除后不可撤销, 请确认是否删除: ${delQueryInfo.current?.name}?`, [
                { text: "取消" },
                { text: "确定", onPress: () => recordDeleteQuery.mutate() },
            ])
        );
    }, [recordDeleteQuery]);

    const clearCache = useCallback(() => {
        return (
            Alert.alert("清空本地缓存", "1. 用于未知原因导致数据无法提交的情况\r\n2. 未提交的内容将会丢失, 请做好数据截图备份\r\n3. 已提交到服务端的数据不受影响\r\n4. 确认清空请点击“确认”按钮", [
                { text: "取消" },
                { text: "确定", onPress: () => {
                    clearStore();
                    resetSelectorStates();
                    navigation.goBack();
                }},
            ])
        );
    }, [clearStore, resetSelectorStates, navigation]);

    /**
     * 监测数据与自动计算
     */

    const headcount = Number(useWatch({ control, name: "headcount" }));
    const conDispCount = Number(useWatch({ control, name: "conDispCount" }));
    //const contractCount = Number(useWatch({ control, name: "contractCount" }));
    //const dispatchCount = Number(useWatch({ control, name: "dispatchCount" }));
    const flowCount = Number(useWatch({ control, name: "flowCount" }));
    const supportCount = Number(useWatch({ control, name: "supportCount" }));

    const updateGlobalState = debounce((headcount, conDispCount, flowCount, supportCount) => {
        const total = headcount + conDispCount + Math.round(flowCount / 2) + supportCount;
        setValue("countSum", `${total}`);
        setStore("countSum", getValues("countSum"));

        subCversionRef.current++;
    }, 100);

    useEffect(() => {
        (formType === 0) && updateGlobalState(headcount, conDispCount, flowCount, supportCount);
    }, [headcount, conDispCount, flowCount, supportCount]);


    // Hook: onMount fetch data
    useEffect(() => {
        // 优化方案: 根据用户权限进行mutate, 只有可写用户才需要本地存储
        const localCversion = getClientCversion.current();
        const remoteCversion = route.params.pageMeta.cversion;
        console.log("localCversion:", localCversion, "remoteSversion:", remoteCversion, "subCversionRef:", subCversionRef.current);

        const restoreLocalData = () => {
            const formObject = { // 默认值用户表单的数据显示
                year:          storedValueToFormValue.current("year"),
                headcount:     storedValueToFormValue.current("headcount"),
                conDispCount:  storedValueToFormValue.current("conDispCount"),
                //contractCount: storedValueToFormValue.current("contractCount"),
                //dispatchCount: storedValueToFormValue.current("dispatchCount"),
                flowCount:     storedValueToFormValue.current("flowCount"),
                supportCount:  storedValueToFormValue.current("supportCount"),
                countSum:    storedValueToFormValue.current("countSum"),
                // other fields
                others:       storedValueToFormValue.current("others"),
                remarks:      storedValueToFormValue.current("remarks"),
            };
            reset(formObject); // 重置react-form数据

            // 设置selector数据
            setYearRadioState(ifTruthLet(getStore("year"),                 isNullness, () => {return {id: 0, name: ""};}, value => value));
        };

        // 生成记录时服务端填充了部分有效数据, 因此对于初始版本也必须从服务端拉数据
        if (localCversion === 0) {
            recordSelectQuery.mutate();
            return;
        }

        if(formType === 1) {
            recordSelectQuery.mutate();
        } else if (localCversion === 0 && remoteCversion === 0) { // 初始版本不同步服务器更合理, 更方便输入
            setTimeout(() => {
                setLoadingIndicatorVisible(false);
            }, 10);
            //recordSelectQuery.mutate();
            return;
        } else if (localCversion === 0 && remoteCversion > 0) {
            console.log("Local cversion === 0, serve may have something nontrivial initialized, query mutate!");
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        } else if (isServerDataNewer(localCversion, remoteCversion) && subCversionRef.current > 0) { // 服务端有更新数据, 但本地有修改
            console.log("Client cversion fell behind the server cversion, someone might modified the database, choose to use the data version!");
            Alert.alert("注意", "由于其他人向服务器提交了数据, 服务端数据比本地数据新, 请选择使用哪份数据?", [
                { text: "使用本地数据", onPress: () => { setStore("mainCversion", remoteCversion); restoreLocalData(); } },
                { text: "使用服务端数据", onPress: () => { setLoadingIndicatorVisible(true); recordSelectQuery.mutate(); } },
            ]);
        } else if (isServerDataNewer(localCversion, remoteCversion)) { // localCversion==0时要mutate是因为服务端可能在创建记录时生成了必要数据
            console.log("Server cversion may be newer, query mutate:", localCversion, remoteCversion);
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        }
        else {
            console.log("Local cversion >= server cversion, query will not mutate!");
            setTimeout(() =>{
                console.log("Use local data, delay rendering!");
                restoreLocalData();
                setLoadingIndicatorVisible(false);
            }, 50);
        }
    }, []); // 这里只要求载入时运行一次, 添加依赖项会导致太多重复渲染

    const fieldsConfig = [
        {
            inputs: [
                { name: "year",             label: "统计周期",      unit: "年",  type: "RADIO", editable: true, placeholder: "", selectorState: yearRadioState, setSelectorState: setYearRadioState, dataProvider: yearRadioStateDataProviderRef, },
                { name: "headcount",        label: "在编人数",      unit: "",    type: "PLAIN", editable: true, placeholder: "", },
                { name: "conDispCount",     label: "合同或派遣人数", unit: "",    type: "PLAIN", editable: true, placeholder: "", },
                //{ name: "contractCount",    label: "合同人数",      unit: "",    type: "PLAIN", editable: true, placeholder: "", },
                //{ name: "dispatchCount",    label: "派遣人数",      unit: "",    type: "PLAIN", editable: true, placeholder: "", },
                { name: "flowCount",        label: "流动人数",      unit: "",    type: "PLAIN", editable: true, placeholder: "", },
                { name: "supportCount",     label: "工勤人员",      unit: "",    type: "PLAIN", editable: true, placeholder: "物业、保洁、保安、食堂等工作人员", },
                { name: "countSum",         label: "合计",         unit: "",    type: "PLAIN", editable: false, placeholder: "", },

            ],
        },
    ];

    /**
     * 这里不能使用React.memo来优化性能, 否则会导致useWatch字段在输入时失去焦点
     * useWatch 依赖于组件的重新渲染来更新其监听的值, 当用户输入时， useWatch 会捕获值的变化
     * 使用 memo 阻止了这种重新渲染, 由于 MemoizedFormListNodesMapper 没有重新渲染，React 可能会重置 DOM 元素的状态，导致输入框失去焦点
     * 谨慎使用 React.memo：
     * 只在确实需要优化性能且确定不会影响功能的情况下使用
     * 对于包含表单控件的组件，尤其要小心使用 memo
     * https://github.com/orgs/react-hook-form/discussions/8168
     */
    //const MemoizedFormListNodesMapper = React.memo(FormListNodesMapper); // 使用memo会导致useWatch字段在输入时失去焦点

    return (
        <View style={{ flex: 1 }}>

            <HeaderBar
                title={screenTitle}
                navigation={navigation}
                goBackCallback={() => {
                    callOneByOne(
                        //() => { _mainCversionGetter=undefined; _subCversionGetter=undefined;},
                        resetSelectorStates,
                    );
                    /*callOneByOne(clearStore, resetSelectorStates)*/  // 返回前清空mmkv存储因为临时数据不再需要, 重置状态使得页面切换后不会使用前面的数据
                }}
                menuItemArray={formType === 0 ? [{ title: "删除表单", action: deleteAlert }, { title: "清空缓存", action: clearCache }] : []}
            />

            {!loadingIndicatorVisible && <ScreenWrapper contentContainerStyle={styles.container}>

                <View style={styles.formEntry}>
                    <FormListNodesMapper
                        fieldsConfig={fieldsConfig}
                        subCversionRef={subCversionRef}
                        control={control}
                        setStore={setStore}
                        resetField={resetField}
                        errors={errors}
                        setSnackBarMessage={setSnackBarMessage}
                        setShowSnackbar={setShowSnackbar}
                        //formDisabledGlobal={formDisabledGlobal}
                        editable={formEditableGlobal}
                        rowStyle={styles.rowContainer}
                    />

                    {/*formType === 0 && <ControlledTextInput
                        name="remarks"
                        rowLabel="备注"
                        control={control}
                        placeholder=""
                        onChangeText={(text) => {
                            subCversionRef.current++;
                            setStore("subCversion", subCversionRef.current);
                            setStore("remarks", text);
                        }}
                        onClearText={() => {
                            subCversionRef.current++;
                            setStore("subCversion", subCversionRef.current);
                            setStore("remarks", "");
                            resetField("remarks", { defaultValue: "" });
                        }}
                        editable={formEditableGlobal}
                        mode="flat"
                        multiline={true}
                        formError={errors}
                        style={styles.rowContainerLongText}
                        //disabled={formDisabledGlobal}
                        required={false}
                    />*/}
                </View>

                {formType === 0 && <View style={styles.button}>
                    <Button mode="contained"
                        disabled={okButtonDisabled}
                        loading={okButtonLoading}
                        icon={saveButtonIcon}
                        onPress={handleSubmit(onHandleSubmit, (formErr) => onPreSubmitError(formErr, getStore, "WaterUsageUnitsFirstRecordsUpdating"))}
                    >{bottomBarLabels.ok}</Button>
                </View>}
            </ScreenWrapper>}

            <LoadingIndicator
                title={""}
                message={"加载中....."}
                visible={loadingIndicatorVisible}
                onClose={()=>setLoadingIndicatorVisible(false)}
            />

            <Snackbar
                style={styles.snackBar}
                visible={showSnackbar}
                onDismiss={() => setShowSnackbar(false)}
                onIconPress={() => setShowSnackbar(false)}
                duration={Snackbar.DURATION_LONG}
            >
                {snackBarMessage}
            </Snackbar>

            <DialogToConfirm
                visible={showConfirm}
                title={confimDialogConfig.title ? confimDialogConfig.title : ""}
                text={confimDialogConfig.text ? confimDialogConfig.text : ""}
                onOK={confimDialogConfig.onOK ? confimDialogConfig.onOK : () => setShowConfirm(false)}
                okBtnLabel={confimDialogConfig.okLabel ? confimDialogConfig.okLabel : "确认"}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    rowContainer:{
        firstColumn: {
            flex: 2,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 3,
        // /borderWidth: 1,
        // height: 40,
        },
    },
    rowContainerLongText: {
        firstColumn: {
            flex: 1,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 4,
            // /borderWidth: 1,
            // height: 40,
        },
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        width: "80%",
        alignSelf: "center",
    },
    formEntry: {
        margin: 8,
    },
    snackBar: {
        flexDirection: "column",
    },
});

export default RecordsUpdating;
