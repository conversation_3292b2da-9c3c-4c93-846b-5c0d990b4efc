///
/// OutputFormatAndroidType.swift
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON> @ Margelo
///

/**
 * Represents the JS enum `OutputFormatAndroidType`, backed by a C++ enum.
 */
public typealias OutputFormatAndroidType = margelo.nitro.react_native_audio_recorder_player.OutputFormatAndroidType

public extension OutputFormatAndroidType {
  /**
   * Get a OutputFormatAndroidType for the given String value, or
   * return `nil` if the given value was invalid/unknown.
   */
  init?(fromString string: String) {
    switch string {
      case "DEFAULT":
        self = .default
      case "THREE_GPP":
        self = .threeGpp
      case "MPEG_4":
        self = .mpeg4
      case "AMR_NB":
        self = .amrNb
      case "AMR_WB":
        self = .amrWb
      case "AAC_ADIF":
        self = .aacAdif
      case "AAC_ADTS":
        self = .aacAdts
      case "OUTPUT_FORMAT_RTP_AVP":
        self = .outputFormatRtpAvp
      case "MPEG_2_TS":
        self = .mpeg2Ts
      case "WEBM":
        self = .webm
      default:
        return nil
    }
  }

  /**
   * Get the String value this OutputFormatAndroidType represents.
   */
  var stringValue: String {
    switch self {
      case .default:
        return "DEFAULT"
      case .threeGpp:
        return "THREE_GPP"
      case .mpeg4:
        return "MPEG_4"
      case .amrNb:
        return "AMR_NB"
      case .amrWb:
        return "AMR_WB"
      case .aacAdif:
        return "AAC_ADIF"
      case .aacAdts:
        return "AAC_ADTS"
      case .outputFormatRtpAvp:
        return "OUTPUT_FORMAT_RTP_AVP"
      case .mpeg2Ts:
        return "MPEG_2_TS"
      case .webm:
        return "WEBM"
    }
  }
}
