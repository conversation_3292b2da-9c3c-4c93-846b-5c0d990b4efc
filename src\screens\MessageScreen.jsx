import React, { useEffect, useRef } from "react";
import { Platform, StyleSheet } from "react-native";
import { Appbar, Divider, List } from "react-native-paper";
//import { useSafeAreaInsets } from "react-native-safe-area-context";
import { createDrawerNavigator } from "@react-navigation/drawer";
import { getHeaderTitle } from "@react-navigation/elements";
import { useFocusEffect, useIsFocused } from "@react-navigation/native";
import { CardStyleInterpolators } from "@react-navigation/stack";
import { useShallow } from "zustand/shallow";
import { makeReqListProjectMessages as queryClient } from "../api/listingQueries";
import { HeaderMenu } from "../components/HeaderBar";
import MessageCard from "../components/MessageCard";
import { tabBarBadgedIconState as BadgedIconState, loadingIndicatorState, userProjMessages as pageDataState, snackbarState } from "../hooks/globalStates";
import { useRefreshOnFocus } from "../hooks/useRefreshOnFocus";
import log from "../services/logging";
import { parseServerState } from "../utils/messages";
import { DrawerItems } from "./DrawerItems";
import EmptyScreen from "./EmptyScreen";
import ScreenWrapper from "./ScreenWrapper";


// 使用ScrollView进行滚动: https://reactnative.dev/docs/scrollview
// 可用过FlatList进行优化: https://reactnative.dev/docs/flatlist
// 标题居中方法:
// 1. <Drawer.Screen options={{ title: "智能低碳", headerTitleAlign: "center" }}>, 在沙盒中有效, 但模拟器测试中无效
// 2. <Appbar.Header elevated mode="center-aligned">, 有效!
// https://github.com/react-navigation/react-navigation/issues/9823
// https://reactnavigation.org/docs/drawer-navigator/#header-related-options
// https://reactnavigation.org/docs/screen-options
// https://reactnavigation.org/docs/headers

// 状态不变的情况下切换tabs, 本页面会产生4次渲染, 默认数据渲染页面1次, useRefreshOnFocus3次, 其中一次是loading产生的, 一次是改变dataArray产生的

/**
 * 消息定义见服务端: src/model/query/projects.lisp, %get-user-project-messages
 * 以下几个是`task-messages`中的原始字段, 生成消息时定义:
 * id: 消息的pubid
 * title: 消息标题
 * subtitle: 消息子标题
 * description: 消息描述
 * state: 消息状态
 * confirmed: 消息是否已确认
 *
 * 以下3个是根据type计算出的文本信息, 显示在MessageCard上:
 * schedule: 截至日期
 * ps: 消息ps
 * signature: 消息签名
 *
 * type, subtype都是`task-messages`中的原始字段, 生成消息时定义; contentId根据type去其他地方索引:
 * type: 消息类型, 1流程消息(对应allflows数据), 目前只有类型1.
 * subtype: 在客户端用于判断该消息路由到那个屏.
 * contentId: 如果type=1, contentId表示allprojects中的pubid.
 *
 * 以下4个是根据type计算出的, 目前只有type=1时有效, 对应于allprojects中的三个字段:
 * class: type=1时, 对应all-projects的class, 即项目类型. 1表示水平衡, 11表示零碳诊断.
 * subclass: type=1时, 对应all-projects的subclass, 即项目子类型. 对于水平衡项目, 1表, 2书, 零碳诊断无子类型.
 * industry: type=1时, 对应all-projects的industry, 即行业类型. 水平衡项目, 1: 工业, 2: 服务业; 零碳诊断, 1: 工业, 3公共机构.
 * principalPubid: type=1时, 对应all-projects的pubid.
 *
 * @param {object} messageItem
 * @returns
 */
const NextScreenSelector = (messageItem) => {
    if (messageItem.type === 1) {
        if (messageItem.class === 1) {
            return "WaterBalanceIndex";  // screens/projects/water-balance/ProjectIndex.jsx
        } else if (messageItem.class === 11) {
            return "ZCScreensIndex";  // screens/projects/zero-carbon/ProjectIndex.jsx
        } else {
            return log.error("Invalid message item: ", messageItem);
        }
    } else {
        console.log("Invalid message type: ", messageItem.type, "of messageItem:", messageItem);
        return "";
    }
};

const Drawer = createDrawerNavigator();
const CardDecke = ({ navigation }) => {

    const isFocused = useIsFocused();

    const emptyScreenText = "暂无任务";

    const [snackbarOnOpen] = snackbarState(useShallow(state => [state.onOpen]));
    const [loadingIndicatorVisible, loadingIndicatorOnOpen, loadingIndicatorOnClose] = loadingIndicatorState(useShallow(state => [state.visible, state.onOpen, state.onClose]));

    const [messageTabBadgeState, setMessageTabBadgeState] = BadgedIconState(useShallow(state => [state.msgTab, state.setMsgTab,]));
    const badgeNumRef = useRef(0);

    const dataArrayRef = useRef([]);
    const [dataArray, setDataArray] = pageDataState(useShallow((state) => [state.data, state.setData]));
    const onSuccess = (data) => {
        if (data.STATUS === 0) {
            // id = 0 has the special meaning, eg. as the root, but it cannot be modified by users
            // dataArray产生一次渲染
            dataArrayRef.current = (data.DATA.filter(item => item?.id !== 0));
            setDataArray(dataArrayRef.current);
        } else {
            snackbarOnOpen(`遇到查询错误: ${parseServerState(data.STATUS || data.status, data.DATA.info)}`);
        }
    };
    const onError = () => {};
    const onSettled = () => {
        //loading条状态产生一次渲染
        loadingIndicatorVisible && loadingIndicatorOnClose();  // 关闭loading不能放在dataArray.map中, 否则会造成无限渲染
    };

    const pageDataQuery = queryClient(onSuccess, onError, onSettled);

    // call on focus, and will not run on mount
    // 导致3次渲染: 一次由loading条导致, 两次由mutate导致
    useRefreshOnFocus(()=>{
        //loadingIndicatorVisible || loadingIndicatorOnOpen("");
        pageDataQuery.mutate();
    });

    // callen on mount
    useEffect(()=>{
        loadingIndicatorVisible || loadingIndicatorOnOpen("");
        pageDataQuery.mutate();
    }, []);

    // call when this screen is focused and messageTabBadgedNum is changed
    useFocusEffect(
        React.useCallback(() => {
            if (messageTabBadgeState.hasNew) {
                //loadingIndicatorVisible || loadingIndicatorOnOpen("");
                pageDataQuery.mutate();
            } else {
                console.log("useFocusEffect: no new message avaible.");
            }
        }, [messageTabBadgeState]),
    );

    // callen after rendering, 不改状态时不导致渲染
    useEffect(()=>{
        if (isFocused) {
            if(messageTabBadgeState.newNum !== badgeNumRef.current) {
                setMessageTabBadgeState(false, badgeNumRef.current);
            }
        }
    });

    return (
        <ScreenWrapper contentContainerStyle={styles.screenContainer}>
            <Divider/>
            <List.Section style={styles.listContainer}>
                {dataArray.length === 0 ? <EmptyScreen text={emptyScreenText} /> :
                    <>
                        {isFocused && dataArray.map((item, i) => { // isFocused prevent rendering when newMessageAvaibleP has been changed
                            const naviTo = NextScreenSelector(item);

                            (i === 0) && (badgeNumRef.current = 0);
                            item.confirmed || badgeNumRef.current++;
                            console.log("Render messagecard: ", i, item);

                            return (
                                <MessageCard
                                    key={i}
                                    msgId={item.id}
                                    title={item.title}
                                    subtitle={item.subtitle}
                                    description={item.description}
                                    postscript={item.ps}
                                    schedule={item.schedule || ""}
                                    signature={item.signature || ""}
                                    state={item.state}
                                    confirmed={item.confirmed}
                                    navigation={navigation}
                                    onPress={() => {
                                        naviTo ? navigation.navigate(naviTo, { pageMeta: item }) : snackbarOnOpen("未获取到消息数据, 请联系管理员!");
                                    }}
                                />
                            );
                        })}
                    </>
                }

            </List.Section>

            {/*<Divider style={{ marginBottom: height + bottom }} />*/}
        </ScreenWrapper>
    );
};

const MessageScreen = ({ navigation }) => {

    //console.log("navigation in MessageScreen:", navigation);
    const cardStyleInterpolator =
        Platform.OS === "android"
            ? CardStyleInterpolators.forFadeFromBottomAndroid
            : CardStyleInterpolators.forHorizontalIOS;
    return (

        <Drawer.Navigator drawerContent={() => <DrawerItems navigation={navigation} />}
            screenOptions={({ navigation }) => {
                return {
                    detachPreviousScreen: !navigation.isFocused(),
                    cardStyleInterpolator,
                    header: ({ navigation, route, options, back }) => {
                        const title = getHeaderTitle(options, route.name);
                        return (
                            <Appbar.Header elevated mode="center-aligned">
                                {back ? (
                                    <Appbar.BackAction onPress={() => navigation.goBack()} />
                                ) : navigation.openDrawer ? (
                                    <Appbar.Action
                                        icon="menu"
                                        //icon={() => <Avatar.Icon size={28} icon="menu" />}
                                        isLeading
                                        onPress={() => navigation.openDrawer()}
                                    />
                                ) : null}
                                <Appbar.Content title={title} />
                                {/*<Appbar.Action icon="dots-vertical" onPress={()=>{}} />*/}
                                <HeaderMenu menuItemArray = {[{ title: "隐藏已完成", action: ()=>{console.log("隐藏已完成!");} }]} />
                            </Appbar.Header>
                        );
                    },
                };
            }}
        >
            <Drawer.Screen
                name="Root"
                component={CardDecke}
                // headerTitleAlign设置居中对于Android无效
                options={{ title: "智能低碳", headerTitleAlign: "center" }}
            />
        </Drawer.Navigator>

    );
};

const styles = StyleSheet.create({
    screenContainer: {
        //borderWidth: 1,
        //flex: 1,
        //marginTop: 4,
        //marginBottom: 4,
        //marginHorizontal: 10,
        //marginVertical: 10,
    },
    listContainer: {
        marginTop: 0,
        marginBottom: 0
    }
});

export default MessageScreen;
