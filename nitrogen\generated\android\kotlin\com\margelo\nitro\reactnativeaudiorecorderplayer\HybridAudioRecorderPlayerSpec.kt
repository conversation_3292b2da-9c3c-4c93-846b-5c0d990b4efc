///
/// HybridAudioRecorderPlayerSpec.kt
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc Rousavy @ Margelo
///

package com.margelo.nitro.reactnativeaudiorecorderplayer

import androidx.annotation.Keep
import com.facebook.jni.HybridData
import com.facebook.proguard.annotations.DoNotStrip
import com.margelo.nitro.core.*

/**
 * A Kotlin class representing the AudioRecorderPlayer HybridObject.
 * Implement this abstract class to create Kotlin-based instances of AudioRecorderPlayer.
 */
@DoNotStrip
@Keep
@Suppress(
  "KotlinJniMissingFunction", "unused",
  "RedundantSuppression", "RedundantUnitReturnType", "SimpleRedundantLet",
  "LocalVariableName", "PropertyName", "PrivatePropertyName", "FunctionName"
)
abstract class HybridAudioRecorderPlayerSpec: HybridObject() {
  @DoNotStrip
  private var mHybridData: HybridData = initHybrid()

  init {
    super.updateNative(mHybridData)
  }

  override fun updateNative(hybridData: HybridData) {
    mHybridData = hybridData
    super.updateNative(hybridData)
  }

  // Properties
  

  // Methods
  @DoNotStrip
  @Keep
  abstract fun startRecorder(uri: String?, audioSets: AudioSet?, meteringEnabled: Boolean?): Promise<String>
  
  @DoNotStrip
  @Keep
  abstract fun pauseRecorder(): Promise<String>
  
  @DoNotStrip
  @Keep
  abstract fun resumeRecorder(): Promise<String>
  
  @DoNotStrip
  @Keep
  abstract fun stopRecorder(): Promise<String>
  
  @DoNotStrip
  @Keep
  abstract fun startPlayer(uri: String?, httpHeaders: Map<String, String>?): Promise<String>
  
  @DoNotStrip
  @Keep
  abstract fun stopPlayer(): Promise<String>
  
  @DoNotStrip
  @Keep
  abstract fun pausePlayer(): Promise<String>
  
  @DoNotStrip
  @Keep
  abstract fun resumePlayer(): Promise<String>
  
  @DoNotStrip
  @Keep
  abstract fun seekToPlayer(time: Double): Promise<String>
  
  @DoNotStrip
  @Keep
  abstract fun setVolume(volume: Double): Promise<String>
  
  @DoNotStrip
  @Keep
  abstract fun setPlaybackSpeed(playbackSpeed: Double): Promise<String>
  
  @DoNotStrip
  @Keep
  abstract fun setSubscriptionDuration(sec: Double): Unit
  
  abstract fun addRecordBackListener(callback: (recordingMeta: RecordBackType) -> Unit): Unit
  
  @DoNotStrip
  @Keep
  private fun addRecordBackListener_cxx(callback: Func_void_RecordBackType): Unit {
    val __result = addRecordBackListener(callback)
    return __result
  }
  
  @DoNotStrip
  @Keep
  abstract fun removeRecordBackListener(): Unit
  
  abstract fun addPlayBackListener(callback: (playbackMeta: PlayBackType) -> Unit): Unit
  
  @DoNotStrip
  @Keep
  private fun addPlayBackListener_cxx(callback: Func_void_PlayBackType): Unit {
    val __result = addPlayBackListener(callback)
    return __result
  }
  
  @DoNotStrip
  @Keep
  abstract fun removePlayBackListener(): Unit
  
  @DoNotStrip
  @Keep
  abstract fun mmss(secs: Double): String
  
  @DoNotStrip
  @Keep
  abstract fun mmssss(milisecs: Double): String

  private external fun initHybrid(): HybridData

  companion object {
    private const val TAG = "HybridAudioRecorderPlayerSpec"
  }
}
