import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { CommonActions } from "@react-navigation/native";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Easing } from "react-native";
import { BottomNavigation, Snackbar } from "react-native-paper";
import MessageScreen from "./MessageScreen";
import WorkbenchScreen from "./WorkbenchScreen";
//import KnowledgeBaseScreen from "./KnowledgeBaseScreen";
import { useShallow } from "zustand/shallow";
import { makePingQuery } from "../api/ping";
import BadgedIcon from "../components/BadgedIcon";
import { DownloadUpdateProgress } from "../components/DialogProgress";
import { DialogToConfirm, DialogToConfirmGlobal } from "../components/DialogToConfirm";
import { LoadingIndicator } from "../components/LoadingIndicator";
import { confirmDialogState, loadingIndicatorState, snackbarState, tabBarBadgedIconState, updateProgressState } from "../hooks/globalStates"; // error be signed
import { userLoginState } from "../hooks/loginStoredState";
import { useInterval } from "../hooks/useInterval";
import { APP_VERSION } from "../services/system";
import { downloadAPK } from "../services/system/update";
import { versionOlderThan } from "../utils";


const Tab = createBottomTabNavigator();

/**
 * 使用Tab.Navigator定义主界面的Tab导航,
 * 每个Tab页签对应一个屏幕
 * @param {Object} navigation
 * @returns
 */
const HomeTabs = ({ navigation }) => {
    const { t } = useTranslation(["screenHome"]);

    const [snackbarVisible, snackbarMessage, closeSnackbar,] = snackbarState(useShallow(state => [state.visible, state.message, state.onClose]));
    const [loadingIndicatorVisible, loadingIndicatorTitle, loadingIndicatorMessage, closeLoadingIndicator] = loadingIndicatorState(useShallow(state => [state.visible, state.title, state.message, state.onClose]));
    const [initDialog] = confirmDialogState(useShallow(state => [state.initDialog]));
    const [updateDialogVisible, setUpdateDialogVisible] = useState(false);

    const [upProgressVisible, upProgressValue, setUpProgressVisible, setUpProgressValue] = updateProgressState(useShallow(state => [state.visible, state.progress, state.setVisible, state.setProgress]));

    // 在MessageScreen, 消息的刷新有两种情况: 1. useFocusEffect在获得聚焦时刷新; 2. messageTabBadgedVisible变化时刷新.
    // 对于第一种情况, 未读消息数量在数组map时得到
    const [
        messageTabBadgeState,
        setMessageTabBadgeState,
        workbenchTabBadgeState,
        setWorkbenchTabBadgeState,
        knowdedgeBaseTabBadgeState,
        setKnowdedgeBaseTabBadgeState,
    ] = tabBarBadgedIconState(useShallow(state => [
        state.msgTab,
        state.setMsgTab,
        state.workbenchTab,
        state.setWorkbenchTab,
        state.knowdedgeBaseTab,
        state.setKnowdedgeBaseTab,
    ]));

    const [clientLatestVersion] = userLoginState(useShallow((state) => [state.latestVersion]));

    const rpcMap = {
        msgBudge:          (num) => {       messageTabBadgeState.hasNew ||       setMessageTabBadgeState(true, num); },
        workBenchBudge:    (num) => {     workbenchTabBadgeState.hasNew ||     setWorkbenchTabBadgeState(true, num); },
        knowdegeBaseBudge: (num) => { knowdedgeBaseTabBadgeState.hasNew || setKnowdedgeBaseTabBadgeState(true, num); },
        notice:            (arr) => { initDialog( arr[0],  arr[1], ()=>console.log(`确认公告: ${arr[0]}`), undefined); },
    };

    const pingOnSuccess = (data) => {
        console.log(`Ping success with server status: ${data.STATUS}, info: ${data.DATA.info}, rpc: ${JSON.stringify(data.rpc)}`);
        if (data.STATUS === 0) {
            if (data.rpc) {
                for (const [key, value] of Object.entries(data.rpc)) {
                    rpcMap[key] ? rpcMap[key](value) : undefined;
                }
            }
        } else {
            console.log("Ping failed due to server error:", data.STATUS, data.DATA.info);
        }
    };
    const pingOnError = (error) => {
        console.log("Ping failed:", error);
    };
    const pingOnSettled = (data, error) => {
        //console.log("Ping on settled:", data, error);
    };

    const pingQuery = makePingQuery(pingOnSuccess, pingOnError, pingOnSettled);

    useInterval(()=>{
        pingQuery.mutate();
    }, 500000);

    useEffect(()=>{
        pingQuery.mutate();
        if(versionOlderThan(APP_VERSION, clientLatestVersion)) {
            console.log("Current version:", APP_VERSION, "latestVersion:", clientLatestVersion, "pop a dialog to update!");
            setUpdateDialogVisible(true);
            // 注意, 版本升级不能与全局对话框复用, 因为登录公告, 会覆盖掉升级对话框
            //initDialog(
            //    "版本更新",
            //    "发现更新的客户端版本, 是否立即更新?",
            //    ()=>Linking.openURL(DOWNLOAD_URL_ANDROID),
            //    ()=>console.log("Close dialog without updating")
            //);
        }
    }, []);

    return (
        <>
            <Tab.Navigator screenOptions={{ headerShown: true, }}
                tabBar={({ navigation, state, descriptors, insets }) => (
                    <BottomNavigation.Bar
                        navigationState={state}
                        safeAreaInsets={insets}
                        onTabPress={({ route, preventDefault }) => {
                            const event = navigation.emit({
                                type: "tabPress",
                                target: route.key,
                                canPreventDefault: true,
                            });

                            if (event.defaultPrevented) {
                                preventDefault();
                            } else {
                                navigation.dispatch({
                                    ...CommonActions.navigate(route.name, route.params),
                                    target: state.key,
                                });
                            }
                        }}
                        renderIcon={({ route, focused, color }) =>
                            descriptors[route.key].options.tabBarIcon?.({
                                focused,
                                color,
                                size: 24,
                            }) || null
                        }
                        sceneAnimationEnabled
                        sceneAnimationType={"opacity"}
                        sceneAnimationEasing={Easing.ease}
                        getLabelText={({ route }) => descriptors[route.key].options.title}
                    />
                )}
            >
                <Tab.Screen
                    name="MessageTabScreen"
                    component={MessageScreen}
                    options={{
                        tabBarIcon: ({ focused, color, size }) => {
                            const icon = focused ? "comment-alert" : "comment-alert-outline";
                            return <BadgedIcon iconName={icon} iconSize={size} iconColor={color} budgeVisible={messageTabBadgeState.hasNew || messageTabBadgeState.newNum} />;
                        },
                        tabBarBadge: 3,
                        headerShown: false,
                        title: t("screenHome:tabNameMessage"),
                    }}
                />
                <Tab.Screen
                    name="WorkbenchTabScreen"
                    component={WorkbenchScreen}
                    options={{
                        tabBarIcon: ({ focused, color, size }) => {
                            const icon = focused ? "archive-edit" : "archive-edit-outline";
                            return <BadgedIcon iconName={icon} iconSize={size} iconColor={color} budgeVisible={workbenchTabBadgeState.hasNew || workbenchTabBadgeState.newNum} />;
                        },
                        headerShown: false,
                        title: t("screenHome:tabNameWorkbench"),
                    }}
                />

                {/*<Tab.Screen
                    name="KnowdedgeBaseTabScreen"
                    component={KnowledgeBaseScreen}
                    options={{
                        tabBarIcon: ({ focused, color, size }) => {
                            const icon = focused ? "book-search" : "book-search-outline";
                            return <BadgedIcon iconName={icon} iconSize={size} iconColor={color} budgeVisible={knowdedgeBaseTabBadgeState.hasNew || knowdedgeBaseTabBadgeState.newNum} />;
                        },
                        headerShown: false,
                        title: t("screenHome:tabNameKnowledgeBase"),
                    }}
                />*/}

                {/*<Tab.Screen
                name="AddressTabScreen"
                component={AddressBookScreen}
                options={{
                    tabBarIcon: ({ color, size }) => {
                        return <Icon name="account-box-multiple-outline" size={size} color={color} />;
                    },
                    headerShown: false,
                    title: t("screenHome:tabNameAddressbook"),
                }}
            />*/}
            </Tab.Navigator>

            <Snackbar
                visible={snackbarVisible}
                onDismiss={() => {
                    closeSnackbar();
                }}
                onIconPress={() => {
                    closeSnackbar();
                }}
                duration={Snackbar.DURATION_LONG}
            >
                {snackbarMessage}
            </Snackbar>

            <LoadingIndicator
                title={loadingIndicatorTitle}
                message={loadingIndicatorMessage}
                visible={loadingIndicatorVisible}
                onClose={closeLoadingIndicator}
            />

            <DialogToConfirmGlobal
                globalStatus={confirmDialogState}
                okBtnLabel={"确认"}
                cancelBtnLabel={"取消"}
            />

            <DialogToConfirm
                title={"版本更新"}
                text={"发现更新的客户端版本, 是否立即更新?"}
                visible={updateDialogVisible}
                onOK={()=>{
                    //Linking.openURL(DOWNLOAD_URL_ANDROID);
                    setUpdateDialogVisible(false);
                    setUpProgressVisible(true);
                    downloadAPK(setUpProgressValue, ()=>setUpProgressVisible(false), ()=>setUpProgressVisible(false), ()=>setUpProgressVisible(false));
                }}
                onCancel={()=>{
                    console.log("Close dialog without updating!");
                    setUpdateDialogVisible(false);
                }}
                okBtnLabel={"确认"}
                cancelBtnLabel={"取消"}
            />

            <DownloadUpdateProgress
                progress={upProgressValue}
                visible={upProgressVisible}
                setVisible={setUpProgressVisible}
            />
        </>
    );
};

/*
const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
    },
    badge: {
        position: "absolute",
        top: 0,
        right: -5,
    },
});
*/

export { HomeTabs };
