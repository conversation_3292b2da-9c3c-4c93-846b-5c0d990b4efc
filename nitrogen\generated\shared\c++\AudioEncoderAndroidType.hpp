///
/// AudioEncoderAndroidType.hpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON> @ Margelo
///

#pragma once

#include <cmath>
#if __has_include(<NitroModules/JSIConverter.hpp>)
#include <NitroModules/JSIConverter.hpp>
#else
#error NitroModules cannot be found! Are you sure you installed NitroModules properly?
#endif
#if __has_include(<NitroModules/NitroDefines.hpp>)
#include <NitroModules/NitroDefines.hpp>
#else
#error NitroModules cannot be found! Are you sure you installed NitroModules properly?
#endif

namespace margelo::nitro::react_native_audio_recorder_player {

  /**
   * An enum which can be represented as a JavaScript enum (AudioEncoderAndroidType).
   */
  enum class AudioEncoderAndroidType {
    DEFAULT      SWIFT_NAME(default) = 0,
    AMR_NB      SWIFT_NAME(amrNb) = 1,
    AMR_WB      SWIFT_NAME(amrWb) = 2,
    AAC      SWIFT_NAME(aac) = 3,
    HE_AAC      SWIFT_NAME(heAac) = 4,
    AAC_ELD      SWIFT_NAME(aacEld) = 5,
    VORBIS      SWIFT_NAME(vorbis) = 6,
  } CLOSED_ENUM;

} // namespace margelo::nitro::react_native_audio_recorder_player

namespace margelo::nitro {

  using namespace margelo::nitro::react_native_audio_recorder_player;

  // C++ AudioEncoderAndroidType <> JS AudioEncoderAndroidType (enum)
  template <>
  struct JSIConverter<AudioEncoderAndroidType> final {
    static inline AudioEncoderAndroidType fromJSI(jsi::Runtime& runtime, const jsi::Value& arg) {
      int enumValue = JSIConverter<int>::fromJSI(runtime, arg);
      return static_cast<AudioEncoderAndroidType>(enumValue);
    }
    static inline jsi::Value toJSI(jsi::Runtime& runtime, AudioEncoderAndroidType arg) {
      int enumValue = static_cast<int>(arg);
      return JSIConverter<int>::toJSI(runtime, enumValue);
    }
    static inline bool canConvert(jsi::Runtime&, const jsi::Value& value) {
      if (!value.isNumber()) {
        return false;
      }
      double integer;
      double fraction = modf(value.getNumber(), &integer);
      if (fraction != 0.0) {
        // It is some kind of floating point number - our enums are ints.
        return false;
      }
      // Check if we are within the bounds of the enum.
      return integer >= 0 && integer <= 6;
    }
  };

} // namespace margelo::nitro
