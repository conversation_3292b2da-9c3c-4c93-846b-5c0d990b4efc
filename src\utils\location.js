/**
 * 将十进制度数转换为度分秒格式
 * @param {number} decimal 十进制度数
 * @param {number} [precision=0] 秒的小数位数
 * @returns {array} 符号, 度, 分, 秒的经纬度坐标数组
 */
const decimalToDMS = (decimal, precision = 0) => {
    // 处理负数
    const absolute = Math.abs(decimal);
    const sign = decimal < 0 ? -1 : 1;

    // 计算度、分、秒
    let degrees = Math.floor(absolute);
    const minutesDecimal = (absolute - degrees) * 60;
    let minutes = Math.floor(minutesDecimal);
    /**
   * toFixed() 方法返回的是一个字符串，而不是数字.
   * + 的作用就是确保 seconds 是一个数字类型，而不是字符串类型。
   * 有几种方法可以将字符串转换为数字：
   * 使用一元加号 +str
   * 使用 Number(str)
   * 使用 parseFloat(str)
   * 使用一元加号是因为它是最简洁的写法, 这三种写法是等价的.
   */
    let seconds = +(((minutesDecimal - minutes) * 60).toFixed(precision));

    // 处理进位情况
    if (Math.abs(seconds - 60) < Number.EPSILON) {
        minutes += 1;
        seconds = 0;
    }
    if (minutes === 60) {
        degrees += 1;
        minutes = 0;
    }

    // 确保秒数显示正确的小数位数
    seconds = +seconds.toFixed(precision);

    return [sign, degrees, minutes, seconds];
};

/**
 * 将经纬度坐标转换为度分秒格式
 * @param {array} dms 符号, 度, 分, 秒的经度坐标数组
 * @param {"zh" | "en" | undefined} lang 语言
 * @returns {string} 经度, 度分秒格式的字符串
 */
const DMDStringLongitude = (dms, lang = "zh") => {
    const [sign_, degrees, minutes, seconds] = dms;
    return lang === "zh" ? `${sign_ === -1 ? "西经" : "东经"}${degrees}°${minutes}′${seconds}″` : `${degrees}°${minutes}′${seconds}″${sign_ === -1 ? "W" : "E"}`;
};

/**
 * 将经纬度坐标转换为度分秒格式
 * @param {array} dms 符号, 度, 分, 秒的纬度坐标数组
 * @param {"zh" | "en" | undefined} lang 语言
 * @returns {string} 纬度, 度分秒格式的字符串
 */
const DMDStringLatitude = (dms, lang = "zh") => {
    const [sign_, degrees, minutes, seconds] = dms;
    return lang === "zh" ? `${sign_ === -1 ? "南纬" : "北纬"}${degrees}°${minutes}′${seconds}″` : `${degrees}°${minutes}′${seconds}″${sign_ === -1 ? "S" : "N"}`;
};

/**
 * 将经纬度坐标转换为度分秒格式
 * @param {number} longitude 经度
 * @param {number} latitude 纬度
 * @param {number} [precision=0] precision 秒的数值精度, 保留小数点后precision位
 * @param {"zh" | "en" | undefined} lang 语言
 * @returns {object} 包含经纬度的度分秒格式的对象
 */
export const coordsToDMS = (longitude, latitude, precision = 0, lang = "zh") => {
    return {
        longitude: DMDStringLongitude(decimalToDMS(longitude, precision), lang),
        latitude: DMDStringLatitude(decimalToDMS(latitude, precision), lang),
    };
};

/**
 * 将经纬度坐标转换为度分秒格式的字符串
 * @param {number} longitude 经度
 * @param {number} latitude 纬度
 * @param {string} [separator=", "] separator 分隔符
 * @param {number} [precision=0] precision 秒的数值精度, 保留小数点后precision位
 * @param {"zh" | "en" | undefined} lang 语言
 * @returns {string} 经纬度的度分秒格式的字符串, 用separator分隔
 */
export const coordsToDMSString = (longitude, latitude, separator = ", ", precision = 0, lang = "zh") => {
    const dms = coordsToDMS(longitude, latitude, precision, lang);
    return `${dms.longitude}${separator}${dms.latitude}`;
};

/**
 * 专用于ControlledLocationInput组件的经纬度坐标显示.
 * 注意, 经纬度[0, 0]在取值范围上是合法的, 但项目实际上不可能, 因此作为空置处理, 同时[0, 0]也是经纬度的缺省值.
 * @param {number[]} coords 数组: [经度, 纬度]
 * @param {string} [separator="\n"] separator 分隔符
 * @param {number} [precision=0] precision 秒的数值精度, 保留小数点后precision位
 * @param {"zh" | "en" | undefined} lang 语言
 * @returns {string} 经纬度度分秒格式的字符串, 用separator分隔
 */
export const showLocationInputCoords = (coords, separator = "\n", precision = 0, lang = "zh") => {
    if(coords) {
        const [longitude, latitude] = coords;
        // 只有当经纬度都为0时才返回空字符串
        return (longitude === 0 && latitude === 0) ? "" : coordsToDMSString(longitude, latitude, separator, precision, lang);
    } else {
        return "";
    }
};


/**
 *
 * @param {object} coordsObj 坐标描述对象: {name: string, desc: string, x: number, y: number}
 * @returns
 */
export const showLocationCoordsObj = (coordsObj) => {
    if(coordsObj) {
        const {name, nameLabel, desc, descLabel, x, y} = coordsObj;
        // 只有当经纬度都为0时才返回空字符串
        return  `${name ? ((nameLabel ? (nameLabel + ": ") : "名称: ") + name + "\n") : ""}${desc ? ((descLabel ? (descLabel + ": ") : "位置: ") + desc + "\n") : ""}${coordsToDMSString(x || 0, y || 0, "\n", 0, "zh")}`;
    } else {
        return "";
    }
};
