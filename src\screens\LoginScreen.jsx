import isString from "lodash/isString";
import PropTypes from "prop-types";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { StyleSheet, TouchableOpacity, View } from "react-native";
import { Text, useTheme } from "react-native-paper";
import { useShallow } from "zustand/shallow";
import { usePasswordLogin } from "../api/userLogin";
import Background from "../components/Background";
import Button from "../components/Button";
import Header from "../components/Header";
import Logo from "../components/Logo";
import PasswordTextInput from "../components/PasswordTextInput";
import TextInput from "../components/TextInput";
import { CUSTOMER_SERVICE_HOTLINE_2 } from "../config";
import { mobileValidator, passwordValidator } from "../helpers/validators";
import { userLoginState } from "../hooks/loginStoredState";
import { callPhone } from "../services/system/phone";
import { assignPermits } from "../utils/permits";

/**
 * 通过账号密码登录, 登录后获得token, 并将登录返回的用户信息写入本地存储.
 * 在用户信息存储中, roles是"工作台->角色权限管理"中的自定义项目的编号, permits是定义在服务端, 位于*role-bit-mask*的权限细项
 * @param {object} args
 * @returns
 */
const LoginScreen = ({ navigation }) => {
    const [
        setUserToken,
        setUserPubid,
        setUserName,
        setUserMobile,
        setUserRoles,
        setUserPermits,
        setUserDepartment,
        setUserPosition,
        setUserType,
        setLatestVersion,
    ] = userLoginState(useShallow((state) => [state.setToken, state.setPubid, state.setName, state.setMobile, state.setRoles, state.setPermits, state.setDepartment, state.setPosition, state.setUserType, state.setLatestVersion]));
    const [userAccount, setUserAccount] = useState({ value: "", error: "" });
    const [userPassword, setUserPassword] = useState({ value: "", error: "" });
    const { t } = useTranslation(["screenLogin"]);
    const theme = useTheme();

    const styles = StyleSheet.create({
        forgotPassword: {
            width: "100%",
            alignItems: "flex-end",
            marginBottom: 24,
        },
        row: {
            flexDirection: "row",
            margin: 5,
        },
        forgot: {
            fontSize: 13,
            color: theme.colors.secondary,
        },
        link: {
            marginLeft: 6,
            fontWeight: "bold",
            color: theme.colors.primary,
        },
        versionTip: {
            margin: 5,
            alignItems: "center",  // 添加此行使内部元素水平居中
            justifyContent: "center",  // 添加此行使内部元素垂直居中
        },
    });

    const gotoMainPage = (json) => {
        if (!!json.token && isString(json.token)) {
            setUserToken(json.token);
            setUserPubid(json.pubid);
            setUserName(json.name);
            setUserMobile(json.mobile);
            setUserRoles(json.roles);
            setUserPermits(json.permits);
            setUserDepartment(json.department);
            setUserPosition(json.position);
            setUserType(json.userType);
            setLatestVersion(json.latestVersion);

            assignPermits(json.permits || []);
        }
        navigation.reset({
            index: 0,
            routes: [{ name: "HomeScreen" }],
        });
    };

    const loginQuery = usePasswordLogin(userAccount.value, userPassword.value, gotoMainPage);

    // does not matter to be a async function or not
    const onLoginPressed = () => {
        const accountError = mobileValidator(userAccount.value);
        const passwordError = passwordValidator(userPassword.value);
        if (accountError || passwordError) {
            setUserAccount({ value: userAccount.value, error: accountError });
            setUserPassword({ value: userPassword.value, error: passwordError });
            return;
        }
        loginQuery.refetch();
    };

    const onClearAccount = () => setUserAccount({ value: "", error: "" });

    // https://tanstack.com/query/latest/docs/react/guides/query-functions#handling-and-throwing-errors
    if (loginQuery.isError) {
        console.info("Error occured when fetching to login:", loginQuery.error.message);
    }

    return (
        <Background>
            {/*<BackButton goBack={navigation.goBack} />*/}
            <Logo />
            <Header>{t("screenLogin:welcome")}</Header>
            <TextInput
                label={t("screenLogin:accountInputTextLabel")}
                returnKeyType="next"
                value={userAccount.value}
                onChangeText={(text) => setUserAccount({ value: text, error: "" })}
                error={!!userAccount.error}
                errorText={userAccount.error}
                autoCapitalize="none"
                autoComplete="off"
                textContentType="none"
                keyboardType="numeric"
                onClearText={onClearAccount}
            />
            <PasswordTextInput
                label={t("screenLogin:passwordInutTextLabel")}
                returnKeyType="done"
                value={userPassword.value}
                onChangeText={(text) => setUserPassword({ value: text, error: "" })}
                error={!!userPassword.error}
                errorText={userPassword.error}
            />

            {/*<View style={styles.forgotPassword}>
                <TouchableOpacity onPress={() => navigation.navigate("ResetPasswordScreen")}>
                    <Text style={styles.forgot}>{t("screenLogin:forgotPasswordText")}</Text>
                </TouchableOpacity>
            </View>*/}

            <Button mode="contained" onPress={onLoginPressed}>
                {t("screenLogin:loginButtonText")}
            </Button>

            <View style={styles.row}>
                <Text>{t("screenLogin:noAccountText")}</Text>
                <TouchableOpacity onPress={() => navigation.navigate("RegisterScreen")}>
                    <Text style={styles.link}>{t("screenLogin:toRegisterText")}</Text>
                </TouchableOpacity>
            </View>

            <View style={styles.versionTip}>
                <Text>{"声明: 本版本为内测版!"} </Text>
                <Text>{"获取推荐码请拨打电话或添加微信:"} </Text>
                <TouchableOpacity onPress={() => callPhone(CUSTOMER_SERVICE_HOTLINE_2)}>
                    <Text style={styles.link}>{CUSTOMER_SERVICE_HOTLINE_2}</Text>
                </TouchableOpacity>
            </View>

        </Background>
    );
};

LoginScreen.propTypes = {
    navigation: PropTypes.shape({
        navigate: PropTypes.func.isRequired,
        reset: PropTypes.func.isRequired,
    }).isRequired,
};

export default LoginScreen;
