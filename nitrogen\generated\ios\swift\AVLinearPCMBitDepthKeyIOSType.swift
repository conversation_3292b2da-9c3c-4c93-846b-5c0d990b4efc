///
/// AVLinearPCMBitDepthKeyIOSType.swift
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc Rousavy @ Margelo
///

/**
 * Represents the JS enum `AVLinearPCMBitDepthKeyIOSType`, backed by a C++ enum.
 */
public typealias AVLinearPCMBitDepthKeyIOSType = margelo.nitro.react_native_audio_recorder_player.AVLinearPCMBitDepthKeyIOSType

public extension AVLinearPCMBitDepthKeyIOSType {
  /**
   * Get a AVLinearPCMBitDepthKeyIOSType for the given String value, or
   * return `nil` if the given value was invalid/unknown.
   */
  init?(fromString string: String) {
    switch string {
      case "bit8":
        self = .bit8
      case "bit16":
        self = .bit16
      case "bit24":
        self = .bit24
      case "bit32":
        self = .bit32
      default:
        return nil
    }
  }

  /**
   * Get the String value this AVLinearPCMBitDepthKeyIOSType represents.
   */
  var stringValue: String {
    switch self {
      case .bit8:
        return "bit8"
      case .bit16:
        return "bit16"
      case .bit24:
        return "bit24"
      case .bit32:
        return "bit32"
    }
  }
}
