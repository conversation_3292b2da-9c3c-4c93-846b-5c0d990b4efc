import { noTimeoutHttpClient as httpClient } from "../services/http";
import log from "../services/logging";

/**
 * 将二进制上传到服务器, 允许携带附加数据.
 * @param {string} uploadURL 服务端接受此请求的api路径, 例如"upload-image".
 * @param {string} uploadName 提交到服务端的文件名, 包含扩展名, 例如"xxxx.jpg"
 * @param {string} mimeType 文件的mimeType, 例如"image/jpeg"
 * @param {string} base64Data 文件的base64编码数据, 不包括前面的mine部分, 例如"/9j/4AAQ......".
 * @param {Object} appendData 附加数据, 例如{pubid: "xxxx", clientid: "xxxx"}, 注意值不能包含复合数据, 遇到复合数据要在外面先序列化为字符串.
 */
export const uploadBase64 = async (uploadURL, uploadName, mimeType, base64Data, appendData = {}) => {
    try {
        const formData = new FormData();
        formData.append("file", {
            uri:  `data:${mimeType};base64,${base64Data}`,
            type: mimeType,
            name: uploadName,
        });
        for (const [key, value] of Object.entries(appendData)) {
            formData.append(key, value); // value 必需是简单类型, 复合类型必需先序列化成字符串
        }

        const response = await httpClient.post(uploadURL, {
            method:  "POST",
            body:    formData,
            headers: {
                "Content-Type": "multipart/form-data",
                // You may need to include additional headers depending on your API requirements
            },
        });

        const responseData = await response.json();
        return responseData;
    } catch (error) {
        log.error(`Upload base64Data: ${mimeType} with name ${uploadName} met error ${error}.`);
        return {"STATUS": error?.response?.status || -1, "INFO": error?.name || "未知错误" };
    }
};
