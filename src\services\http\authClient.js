import ky from "ky";
import { BASE_URL } from "../../config";
import { APP_VERSION, CLIENT_OS } from "../system";

// https://github.com/sindresorhus/ky

const RETRY_CODES = [401, 501, 502, 503, 504];
const RETRY_METHODS = ["get", "post", "put", "head", "delete", "options", "trace"];
const RETRY_INTERVALS = [0, 300, 1000, 5000];
const RETRY_LIMIT = RETRY_INTERVALS.length - 1;
const TIMEOUT = 10000;


/**
 * ky的自定义实例, 用于登录验证的HTTP通信, 标头不插入身份信息.
 * 用法: httpClient(input, options?), 其中input, options与fetch的参数相同, 但是`credentials`默认是`same-origin`, ky相比fetch添加了更多选项.
 * 简例: const json = await httpClient.post('https://example.com', {json: {foo: true}}).json();
 */
export const authHttpClient = ky.extend({
    prefixUrl: BASE_URL,
    timeout: TIMEOUT,
    retry: {
        limit: RETRY_LIMIT,
        methods: RETRY_METHODS,
        statusCodes: RETRY_CODES,
        delay: attemptCount => RETRY_INTERVALS[attemptCount],
    },
    hooks: {
        beforeRequest: [
            (request) => {
                request.headers.set("APPOS",         CLIENT_OS);
                request.headers.set("AppVersion",    APP_VERSION);
            },
        ],
    },
});


