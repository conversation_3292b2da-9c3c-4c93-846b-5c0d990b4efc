///
/// JRecordBackType.hpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON> @ Margelo
///

#pragma once

#include <fbjni/fbjni.h>
#include "RecordBackType.hpp"

#include <optional>

namespace margelo::nitro::react_native_audio_recorder_player {

  using namespace facebook;

  /**
   * The C++ JNI bridge between the C++ struct "RecordBackType" and the the Kotlin data class "RecordBackType".
   */
  struct JRecordBackType final: public jni::JavaClass<JRecordBackType> {
  public:
    static auto constexpr kJavaDescriptor = "Lcom/margelo/nitro/reactnativeaudiorecorderplayer/RecordBackType;";

  public:
    /**
     * Convert this Java/Kotlin-based struct to the C++ struct RecordBackType by copying all values to C++.
     */
    [[maybe_unused]]
    [[nodiscard]]
    RecordBackType toCpp() const {
      static const auto clazz = javaClassStatic();
      static const auto fieldIsRecording = clazz->getField<jni::JBoolean>("isRecording");
      jni::local_ref<jni::JBoolean> isRecording = this->getFieldValue(fieldIsRecording);
      static const auto fieldCurrentPosition = clazz->getField<double>("currentPosition");
      double currentPosition = this->getFieldValue(fieldCurrentPosition);
      static const auto fieldCurrentMetering = clazz->getField<jni::JDouble>("currentMetering");
      jni::local_ref<jni::JDouble> currentMetering = this->getFieldValue(fieldCurrentMetering);
      static const auto fieldRecordSecs = clazz->getField<jni::JDouble>("recordSecs");
      jni::local_ref<jni::JDouble> recordSecs = this->getFieldValue(fieldRecordSecs);
      return RecordBackType(
        isRecording != nullptr ? std::make_optional(static_cast<bool>(isRecording->value())) : std::nullopt,
        currentPosition,
        currentMetering != nullptr ? std::make_optional(currentMetering->value()) : std::nullopt,
        recordSecs != nullptr ? std::make_optional(recordSecs->value()) : std::nullopt
      );
    }

  public:
    /**
     * Create a Java/Kotlin-based struct by copying all values from the given C++ struct to Java.
     */
    [[maybe_unused]]
    static jni::local_ref<JRecordBackType::javaobject> fromCpp(const RecordBackType& value) {
      return newInstance(
        value.isRecording.has_value() ? jni::JBoolean::valueOf(value.isRecording.value()) : nullptr,
        value.currentPosition,
        value.currentMetering.has_value() ? jni::JDouble::valueOf(value.currentMetering.value()) : nullptr,
        value.recordSecs.has_value() ? jni::JDouble::valueOf(value.recordSecs.value()) : nullptr
      );
    }
  };

} // namespace margelo::nitro::react_native_audio_recorder_player
