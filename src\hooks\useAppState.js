import { useEffect } from "react";
import { AppState } from "react-native";

/**
 * AppState can tell you if the app is in the foreground or background,
 * and notify you when the state changes.
 *
 * AppState is frequently used to determine the intent and proper behavior
 * when handling push notifications.
 *
 * App State Events
 *      change - This even is received when the app state has changed.
 *      focus [Android] - Received when the app gains focus (the user is interacting with the app).
 *      blur [Android] - Received when the user is not actively interacting with the app.
 *
 * App States
 *      active - The app is running in the foreground
 *      background - The app is running in the background. The user is either in another app or on the home screen
 *      inactive [iOS] - This is a transition state that happens when the app launches, is asking for permissions or when a call or SMS message is received.
 *      unknown [iOS] - Initial value until the current app state is determined
 *      extension [iOS] - The app is running as an app extension
 *
 * For more information, see Apple's documentation: https://developer.apple.com/library/ios/documentation/iPhone/Conceptual/iPhoneOSProgrammingGuide/TheAppLifeCycle/TheAppLifeCycle.html
 * @typedef {("active" | "background" | "inactive" | "unknown" | "extension")} AppStateStatus
 * -- From react-native type definition
 */

/**
 * Refetch on App focus
 * https://tanstack.com/query/latest/docs/react/react-native#refetch-on-app-focus
 * @param {(status:AppStateStatus)=>void} onChange
 */
export function useAppState(onChange) {
    useEffect(() => {
        const subscription = AppState.addEventListener("change", onChange);
        return () => {
            subscription.remove();
        };
    }, [onChange]);
}
