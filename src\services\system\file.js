
/**
 * Get the extension name of an uri
 * @param {string} uri
 * @returns
 */
export const getFileExtension = (uri) => {
    const lastDotIndex = uri.lastIndexOf(".");
    if (lastDotIndex !== -1) {
        return uri.slice(lastDotIndex + 1);
    }
    return null; // or an appropriate default value
};

/**
 * Get the schema of an uri, return "file" for local file, or "http" for remote file.
 * fileScheme("file:///data/user/0/....") -> file
 * fileScheme("http://47.109.179.133:5577/....") -> http
 * @param {string} uri the file uri
 */
export const fileScheme = (uri) => uri.split(":")[0].toLowerCase();

/**
 * Predicate to check if the uri is a remote file.
 * @param {string} uri The file uri
 * @returns
 */
export const isRemoteFile = (uri) => {
    const scheme = fileScheme(uri);
    return scheme === "http" || scheme === "https";
};

/**
 * Predicate to check if the uri is a local file.
 * @param {string} uri The file uri
 * @returns
 */
export const isLocalFile = (uri) => !(isRemoteFile(uri));
