import { useQuery } from "@tanstack/react-query";
import { httpClient } from "../services/http";
import { CLIENT_OS } from "../config";
import { CONFIRM_MESSAGE as key } from "../config/keysConfig";
import { getUniversalTime } from "../utils/time";
import { mergeQueryPath } from "../utils";
import log from "../services/logging";

const RETRY_CODES = [401, 501, 502, 503, 504];
const RETRY_METHODS = ["GET", "POST", "PUT", "HEAD", "DELETE", "OPTIONS", "TRACE"];
const RETRY_INTERVALS = [2000, 3000, 5000];
const RETRY_LIMIT = RETRY_INTERVALS.length;

/**
 * Return a useQuery of react query.
 * @param {string} msgId 本条消息的pubid
 * @param {(msg)=>{}} action A function that will be called if the client receives a message from the server.
 */
const makeConfirmMessageQuery = (msgId, action) => {
    // no retry for both ky and query, user will retry by himself
    return useQuery({
        queryKey: [key.query, msgId],
        queryFn: async () => {
            const formData = new FormData();
            const now = getUniversalTime();
            formData.append("os", CLIENT_OS);
            formData.append("time", now);
            console.debug(`Make a CONFIRM_MESSAGE to server for message ${msgId} with universal time ${now}.`);
            const response = await httpClient.post(`${key.url}${mergeQueryPath(msgId)}`, { body: formData, retry: { limit: 0 } });
            const json = await response.json();
            console.log("makeConfirmMessageQuery get server response: ", json);
            action ? action(json) : undefined;
            return json;
        },
        //refetchInterval: 1000,
        //refetchIntervalInBackground: true,
        enabled: false, // 不能为true, 否则刚创建时就会执行查询

        // 逻辑上, 首次失败后, 立即检测retryDelay, 经延迟后再检测retry触发重试
        // 注意, 有Ky的retry有bug, 这里使用React Query的retry!!!!!!
        retry: (failureCount, error) => { // failureCount from 0
            log.debug("failureCount: %s", failureCount);
            if (failureCount < RETRY_LIMIT
                && error.name === "HTTPError"
                && RETRY_CODES.includes(error.response.status)
                && RETRY_METHODS.includes(error.request.method)) {
                return true;
            } else {
                return false;
            }
        },
        retryDelay: (attemptIndex) => RETRY_INTERVALS[attemptIndex], // attemptIndex from 0
    });
};

export { makeConfirmMessageQuery };
