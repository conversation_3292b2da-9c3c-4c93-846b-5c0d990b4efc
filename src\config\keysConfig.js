// This file contains all query keys of react-query

export const QUERY_PASSWORD_LOGIN           = "login-with-password";
export const QUERY_TOKEN_LOGIN              = "login-with-token";
export const QUERY_ADDING_ROLE              = "adding-role";

export const REG_APP_CLIENT_KEY                 = "register-app-client"; // 注册App客户, react hook form使用
export const REG_APP_CLIENT_URL                 = "regapp";              // 注册App客户, http ky使用

export const STORE_LOGIN_INFO      = "STORE_LOGIN_INFO";       // store token
export const STORE_PERSISTENCE_KEY = "STORE_PERSISTENCE_KEY";  // store navigation state
export const STORE_PREFERENCES_KEY = "STORE_PREFERENCES_KEY";  // theme, etc


export const URL_LOGIN = "login";
export const PING                  = { query: "query-ping",           url: "ping" };
export const GET_AVAIBLE_SERVICES  = { query: "get-avaible-services", url: "listservices" };
export const UPDATE_PASSWORD       = { query: "update-password",      url: "uppass" };
export const CONFIRM_MESSAGE       = { query: "confirm-message",      url: "utils/msgconfirm" };

export const ORG_ADD               = { query: "org-add",            url: "orgadd",         store: "org-add" };      // 管理员添加新app客户
export const ORG_SELECT            = { query: "org-select",         url: "orgsel",         store: "org-update" };   // 与UPDATE共用一个MMKV, SELECT后作为默认值存储
export const ORG_UPDATE            = { query: "org-update",         url: "orgupd",         store: "org-update" };   // 管理员更新app客户
export const ORG_DELETE            = { query: "org-delete",         url: "orgdel",         store: "org-delete" };   // 管理员删除app客户

export const LIST_ALL_ORGS         = { query: "list-all-orgs",      url: "listallorgs",     store: "list-all-orgs" };     // 列出所有app客户清单
export const LIST_ALL_USERS        = { query: "list-all-users",     url: "listallusers",    store: "list-all-users" };     // 列出所有用户清单
export const LIST_ALL_DEPARTMENTS  = { query: "list-all-depts",     url: "listalldepts",    store: "list-all-depts" };     // 查询所有部门
export const LIST_ALL_POSITIONS    = { query: "list-all-poses",     url: "listallposes",    store: "list-all-poses" };     // 查询所有职位
export const LIST_ALL_ROLES        = { query: "list-all-roles",     url: "listallroles",    store: "list-all-roles" };     // 查询所有角色
export const LIST_ALL_CLIENTS      = { query: "list-all-clients",   url: "listallclients",  store: "list-all-clients" };   // 查询所有客户
export const LIST_ORG_PROJ_BASE    = { query: "list-org-proj-base", url: "listorgprojbase", store: "list-org-proj-base" }; // 查询所有项目库
export const LIST_ORG_WB_PROJ_BASE = { query: "list-org-wb-proj-base", url: "listorgwbprojbase", store: "list-org-wb-proj-base" }; // 查询水平衡项目库
export const LIST_ORG_ZC_PROJ_BASE = { query: "list-org-zc-proj-base", url: "listorgzcprojbase", store: "list-org-zc-proj-base" }; // 查询零碳诊断项目库

export const ORG_USER_ADD          = { query: "org-add-user",      url: "orgadduser",      store: "org-add-user" };      // 管理员添加新用户
export const ORG_USER_SELECT       = { query: "org-select-user",   url: "orgselectuser",   store: "org-update-user" };   // 与UPDATE共用一个MMKV, SELECT后作为默认值存储
export const ORG_USER_UPDATE       = { query: "org-update-user",   url: "orgupdateuser",   store: "org-update-user" };   // 管理员更新新用户
export const ORG_USER_DELETE       = { query: "org-delete-user",   url: "orgdeleteuser",   store: "org-delete-user" };   // 管理员删除新用户

export const ORG_DEPARTMENT_ADD    = { query: "org-add-dept",      url: "orgadddept",      store: "org-add-dept" };       // 管理员添加新部门
export const ORG_DEPARTMENT_SELECT = { query: "org-select-dept",   url: "orgselectdept",   store: "org-update-dept" };    // 与UPDATE共用一个MMKV, SELECT后作为默认值存储
export const ORG_DEPARTMENT_UPDATE = { query: "org-update-dept",   url: "orgupdatedept",   store: "org-update-dept" };    // 管理员更新部门
export const ORG_DEPARTMENT_DELETE = { query: "org-delete-dept",   url: "orgdeletedept",   store: "org-delete-dept" };    // 管理员删除部门
export const DEPT_MEMBERS_GET      = { query: "dept-members-get",  url: "deptmemberget",  store: "dept-members-get" };    // 拉取部门成员

export const ORG_POSITION_ADD      = { query: "org-add-pos",       url: "orgaddpos",       store: "org-add-pos" };       // 管理员添加新职位
export const ORG_POSITION_SELECT   = { query: "org-select-pos",    url: "orgselectpos",    store: "org-update-pos" };    // 与UPDATE共用一个MMKV, SELECT后作为默认值存储
export const ORG_POSITION_UPDATE   = { query: "org-update-pos",    url: "orgupdatepos",    store: "org-update-pos" };    // 管理员更新职位
export const ORG_POSITION_DELETE   = { query: "org-delete-pos",    url: "orgdeletepos",    store: "org-delete-pos" };    // 管理员删除职位

export const ORG_ROLE_ADD          = { query: "org-add-role",      url: "orgaddrole",      store: "org-add-role" };      // 管理员添加新角色
export const ORG_ROLE_SELECT       = { query: "org-select-role",   url: "orgselectrole",   store: "org-update-role" };   // 与UPDATE共用一个MMKV, SELECT后作为默认值存储
export const ORG_ROLE_UPDATE       = { query: "org-update-role",   url: "orgupdaterole",   store: "org-update-role" };   // 管理员更新角色
export const ORG_ROLE_DELETE       = { query: "org-delete-role",   url: "orgdeleterole",   store: "org-delete-role" };   // 管理员删除角色
export const STD_ROLES_GET         = { query: "std-roles-get",     url: "stdrolesget",     store: "std-roles-get" };     // 拉取标准角色权限

export const ORG_CLIENT_ADD        = { query: "org-add-client",    url: "orgaddclient",    store: "org-add-client" };    // 管理员添加新客户
export const ORG_CLIENT_SELECT     = { query: "org-select-client", url: "orgselectclient", store: "org-update-client" }; // 与UPDATE共用一个MMKV, SELECT后作为默认值存储
export const ORG_CLIENT_UPDATE     = { query: "org-update-client", url: "orgupdateclient", store: "org-update-client" }; // 管理员更新客户
export const ORG_CLIENT_DELETE     = { query: "org-delete-client", url: "orgdeleteclient", store: "org-delete-client" }; // 管理员删除客户

export const ORG_PROJ_BASE_ADD    = { query: "org-add-proj-base",    url: "orgaddprojbase",    store: "org-add-proj-base" };    // 管理员添加项目库
export const ORG_PROJ_BASE_SELECT = { query: "org-select-proj-base", url: "orgselectprojbase", store: "org-update-proj-base" }; // 与UPDATE共用一个MMKV, SELECT后作为默认值存储
export const ORG_PROJ_BASE_UPDATE = { query: "org-update-proj-base", url: "orgupprojbase",     store: "org-update-proj-base" }; // 管理员更新项目库
export const ORG_PROJ_BASE_DELETE = { query: "org-delete-proj-base", url: "orgdelprojbase",    store: "org-delete-proj-base" }; // 管理员删除项目库

// WB = Water Balance
export const LIST_WB_BY_CREATER   = { query: "list-wb-by-creator", url: "listwbbycreator", store: "list-wb-by-creator" }; // 列出某人创建的所有水平衡项目
export const WATER_BALANCE_ADD    = { query: "add-water-balance",  url: "addwaterbalance", store: "add-water-balance" };  // 管理员添加项目库
export const WATER_BALANCE_SELECT = { query: "sel-water-balance",  url: "selwaterbalance", store: "sel-water-balance" };  // 与UPDATE共用一个MMKV, SELECT后作为默认值存储
export const WATER_BALANCE_UPDATE = { query: "upd-water-balance",  url: "updwaterbalance", store: "upd-water-balance" };  // 管理员更新项目库
export const WATER_BALANCE_DELETE = { query: "del-water-balance",  url: "delwaterbalance", store: "del-water-balance" };  // 管理员删除项目库

// ZC = Zero Carbon
export const LIST_ZC_BY_CREATER   = { query: "list-zc-by-creator", url: "listzcbycreator", store: "list-zc-by-creator" }; // 列出某人创建的所有水平衡项目
export const ZERO_CARBON_ADD      = { query: "add-zero-carbon",    url: "addzerocarbon",   store: "add-zero-carbon" };    // 管理员添加项目库
export const ZERO_CARBON_SELECT   = { query: "sel-zero-carbon",    url: "selzerocarbon",   store: "sel-zero-carbon" };    // 与UPDATE共用一个MMKV, SELECT后作为默认值存储
export const ZERO_CARBON_UPDATE   = { query: "upd-zero-carbon",    url: "updzerocarbon",   store: "upd-zero-carbon" };    // 管理员更新项目库
export const ZERO_CARBON_DELETE   = { query: "del-zero-carbon",    url: "delzerocarbon",   store: "del-zero-carbon" };    // 管理员删除项目库

// 列出与自己相关的所有项目
export const COPY_MY_PROJ         = { query: "copy-my-proj",        url: "copymyproj",      store: "copy-my-proj" };        // 拷贝项目
export const LIST_MY_PROJ_ALL     = { query: "list-my-proj-all",    url: "listmyprojall",   store: "list-my-proj-all" };    // 所有项目
export const LIST_MY_PROJ_WB      = { query: "list-my-proj-wb",     url: "listmyprojwb",    store: "list-my-proj-wb" };     // 水平衡项目
export const LIST_MY_PROJ_ZC      = { query: "list-my-proj-zc",     url: "listmyprojzc",    store: "list-my-proj-zc" };     // 零碳诊断项目

// message screen
export const LIST_PROJ_MESSAGES   = { query: "list-proj-messages", url: "api/list/projmsgs", store: "list-proj-messages" }; // 列出与某人相关的所有项目消息


// Project records apis for lst, get, upd, del.
// url规范: api/type/proj/:record/:op/:pubid
// 其中, record是在各项目的ProjectIndex.jsx页面赋予, op在更下一层页面赋予(包括lst, get, upd, del四种), pubid通常是项目的pubid
// 例如, 某个水平衡项目关于水源表的lst操作: "/api/proj/wb/watersource/lst/:pubid"

// Water Balance
export const LST_WATER_BALANCE_RECORDS = { query: "lst-water-balance-records", url: "api/proj/wb", store: "lst-water-balance-records" };
export const ADD_WATER_BALANCE_RECORDS = { query: "add-water-balance-records", url: "api/proj/wb", store: "add-water-balance-records" };
export const GET_WATER_BALANCE_RECORDS = { query: "get-water-balance-records", url: "api/proj/wb", store: "get-water-balance-records" };
export const UPD_WATER_BALANCE_RECORDS = { query: "upd-water-balance-records", url: "api/proj/wb", store: "upd-water-balance-records" };
export const DEL_WATER_BALANCE_RECORDS = { query: "del-water-balance-records", url: "api/proj/wb", store: "del-water-balance-records" };
export const SUM_WATER_BALANCE_RECORDS = { query: "sum-water-balance-records", url: "api/proj/wb", store: "sum-water-balance-records" };
export const GEN_WATER_BALANCE_DOC     = { query: "gen-water-balance-doc", url: "api/proj/wb/gendoc", store: "gen-water-balance-doc" };

// Zero Carbon
export const LST_ZERO_CARBON_RECORDS = { query: "lst-zero-carbon-records", url: "api/proj/zc", store: "lst-zero-carbon-records" };
export const ADD_ZERO_CARBON_RECORDS = { query: "add-zero-carbon-records", url: "api/proj/zc", store: "add-zero-carbon-records" };
export const GET_ZERO_CARBON_RECORDS = { query: "get-zero-carbon-records", url: "api/proj/zc", store: "get-zero-carbon-records" };
export const UPD_ZERO_CARBON_RECORDS = { query: "upd-zero-carbon-records", url: "api/proj/zc", store: "upd-zero-carbon-records" };
export const DEL_ZERO_CARBON_RECORDS = { query: "del-zero-carbon-records", url: "api/proj/zc", store: "del-zero-carbon-records" };
export const SUM_ZERO_CARBON_RECORDS = { query: "sum-zero-carbon-records", url: "api/proj/zc", store: "sum-zero-carbon-records" };

// 使用useQuery的配置
export const WATER_BALANCE_USE_QUERY = { query: "water-balance-query", url: "api/proj/wb" };
export const ZERO_CARBON_USE_QUERY   = { query: "zero-carbon-query",   url: "api/proj/zc" };

// 全局的App Feedback
export const APP_FEEDBACK_GLOBL = { url: "util/fdbk" };
