import React, { useState } from "react";
import { Image, ImageBackground, Pressable, StyleSheet, useWindowDimensions, View } from "react-native";
import { IconButton, Modal, Portal, useTheme } from "react-native-paper";
import { DEFAULT_IMAGE_URI } from "../config";
import { requestCameraWithPermission, requestGalleryWithPermission } from "../services/system/permissions";
//import PropTypes from "prop-types";

// Dimensions文档建议使用useWindowDimensions: https://reactnative.dev/docs/dimensions

/**
 * 在界面上显示一张图片, 点击后全屏显示
 * 注意, Image组件的缓存, 目前只有ios有效: https://reactnative.dev/docs/images#cache-control-ios-only
 * @param {Object} args
 * @param {string} args.imageUri
 * @param {number | null} args.width
 * @param {number | null} args.height
 * @param {Object} imageStyle 传递本参数的意图主要是处理默认图片的背景色问题
 * @returns
 */
const ImageCellView = ({ imageUri, width = null, height = 150, imageStyle}) => {
    const {height: windowHeight, width: windowWidth} = useWindowDimensions();
    const theme = useTheme();
    const styles = StyleSheet.create({
        image: {
            width: width ? width : null,
            height: height ? height : 150,
            margin: 1,
            //alignSelf: "center", // 使用此参数会导致奇怪的问题, image尺寸会发生改变
            resizeMode: "center",
            backgroundColor: theme.colors.surface,
        },
        fullScreen:
        {
            justifyContent: "center",
            //width: Dimensions.get("window").width,
            //height: Dimensions.get("window").height,
            width: windowWidth,
            height: windowHeight,
            resizeMode:  "center" // "contain "
        },
    });

    const [showfullScreen, setShowfullScreen] = useState(false);

    return (
        <View style={{marginVertical: 0}}>
            <Pressable onPress={()=>{ // 如果不能点击, 就要添加zIndex: style={{zIndex: 0.5, }}, https://reactnative.dev/docs/layout-props#zindex
                setShowfullScreen(true);
            }}>
                <Image
                    source={{uri: imageUri, cache: "reload"}} //
                    resizeMode="center"
                    //backgroundColor="black"
                    style={imageStyle?.image || styles.image}>
                </Image>
            </Pressable>

            <Portal>
                <Modal visible={showfullScreen} onDismiss={() => setShowfullScreen(false)}>
                    <Pressable
                        onPress={()=>{
                            setShowfullScreen(false);
                        }}>
                        <ImageBackground
                            source={{uri: imageUri, cache: "reload"}}
                            resizeMode="center"
                            backgroundColor="black"
                            style={imageStyle?.fullScreen || styles.fullScreen}>
                        </ImageBackground>
                    </Pressable>
                </Modal>
            </Portal>
        </View>
    );
};


/**
 * Image组件
 * resizeMode参数区别:
 * https://medium.com/@nima-ahmadi/react-native-image-resizemode-a-visual-guide-f1958d27c615
 * https://mehrankhandev.medium.com/understanding-resizemode-in-react-native-dd0e455ce63
 * 官方文档: https://reactnative.dev/docs/image#resizemode
 * @param {Object} arg
 * @param {function} arg.onPickImageCB
 * @param {function} arg.onDeleteImageCB
 * @param {boolean} arg.disabled
 * @param {number} arg.pickNum 默认为1, 表示最多选择1张图片, 2表示最多选择2张图片, 依此类推, 0表示无限制(由全局上限限制).
 * @param {int | null} arg.width 界面上图像框的宽度, 默认null, 表示使用最大宽度.
 * @param {int} arg.height 图像高度, 默认150.
 * @param {boolean} arg.crop 是否剪裁, false不剪裁, 使用原图
 * @param {number | false | undefined | null} arg.compress 压缩程度, 0~1, 为假值时(包括0)表示不压缩
 */
const ControlledCellImageInput = ({ uriArray, placeholder, onPickImageCB, onDeleteImageCB, onBlur, pickNum = 1, width = null, height = 150, crop = false, compress = 0.8, editable = false, ...props }) => {
    const {height: windowHeight, width: windowWidth} = useWindowDimensions();
    const theme = useTheme();
    const styles = StyleSheet.create({
        error: {
            fontSize: 13,
            color: theme.colors.error,
            paddingTop: 8,
        },
        image: {
            width: width ? width : null,
            height: height ? height : 150,
            margin: 1,
            //alignSelf: "center", // 使用此参数会导致奇怪的问题, image尺寸会发生改变
            resizeMode: "center",
            backgroundColor: theme.colors.surface,
        },
        defaultImage: {
            //width: 200,
            height: 150,
            margin: 1,
            resizeMode: "center",
            backgroundColor: "#d0d0d0", // 与默认图片相同的背景色
        },
        fullScreenImage:
        {
            justifyContent: "center",
            //width: Dimensions.get("window").width,
            //height: Dimensions.get("window").height,
            width: windowWidth,
            height: windowHeight,
            resizeMode:  "center" // "contain "
        },
    });

    return (
        <View>
            { uriArray?.length > 0 ?
                uriArray.map((uri, i) => {
                    return (
                        <ImageCellView
                            key={`image-cell-${i}`}
                            imageUri={uri}
                            width={width}
                            height={height}
                            imageStyle={{image: styles.image, fullScreen: styles.fullScreenImage}}
                        />);
                })
                :
                <ImageCellView
                    imageUri={DEFAULT_IMAGE_URI}
                    width={width}
                    height={height}
                    imageStyle={{image: styles.defaultImage, fullScreen: styles.fullScreenImage}}
                />
            }

            <View style={{flexDirection: "row", width: "100%", justifyContent: "flex-begin", }}>
                <View>
                    {/** 删除按钮 */}
                    <IconButton
                        icon="image-off-outline"
                        size={24}
                        selected
                        disabled={uriArray?.length <= 0 ? true : false}
                        onPress={()=>{
                            //setImageUri([]);
                            //pickResult.current = null;
                            onDeleteImageCB();
                        }}>
                    </IconButton>
                </View>
                <View style={{flexDirection: "row", flexGrow: 1, justifyContent: "flex-end", }}>
                    {/** 拍摄按钮 */}
                    <IconButton
                        icon="camera"
                        selected
                        // /iconColor={theme.colors.primary}
                        disabled={uriArray?.length >= pickNum ? true : false}
                        size={24}
                        onPress={async () => {
                            await requestCameraWithPermission(onPickImageCB, crop, compress, pickNum);}}>
                    </IconButton>

                    {/** 相册按钮 */}
                    <IconButton
                        icon="image"
                        selected
                        //iconColor={theme.colors.primary}
                        disabled={uriArray?.length >= pickNum ? true : false}
                        size={24}
                        onPress={async () => {
                            await requestGalleryWithPermission(onPickImageCB, crop, compress, pickNum);
                        }}>
                    </IconButton>
                </View>
            </View>
        </View>
    );
};

/*
ControlledCellImageInput.propTypes = {
    //uriArray: PropTypes.array,
    //label: PropTypes.string,
    placeholder: PropTypes.string,
    onClearText: PropTypes.func,
    onChangeText: PropTypes.func,
    editable: PropTypes.bool,
    mode: PropTypes.string,
};
*/

export default ControlledCellImageInput;
