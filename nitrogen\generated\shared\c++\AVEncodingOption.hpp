///
/// AVEncodingOption.hpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON> @ Margelo
///

#pragma once

#if __has_include(<NitroModules/NitroHash.hpp>)
#include <NitroModules/NitroHash.hpp>
#else
#error NitroModules cannot be found! Are you sure you installed NitroModules properly?
#endif
#if __has_include(<NitroModules/JSIConverter.hpp>)
#include <NitroModules/JSIConverter.hpp>
#else
#error NitroModules cannot be found! Are you sure you installed NitroModules properly?
#endif
#if __has_include(<NitroModules/NitroDefines.hpp>)
#include <NitroModules/NitroDefines.hpp>
#else
#error NitroModules cannot be found! Are you sure you installed NitroModules properly?
#endif

namespace margelo::nitro::react_native_audio_recorder_player {

  /**
   * An enum which can be represented as a JavaScript union (AVEncodingOption).
   */
  enum class AVEncodingOption {
    LPCM      SWIFT_NAME(lpcm) = 0,
    IMA4      SWIFT_NAME(ima4) = 1,
    AAC      SWIFT_NAME(aac) = 2,
    MAC3      SWIFT_NAME(mac3) = 3,
    MAC6      SWIFT_NAME(mac6) = 4,
    ULAW      SWIFT_NAME(ulaw) = 5,
    ALAW      SWIFT_NAME(alaw) = 6,
    MP1      SWIFT_NAME(mp1) = 7,
    MP2      SWIFT_NAME(mp2) = 8,
    MP4      SWIFT_NAME(mp4) = 9,
    ALAC      SWIFT_NAME(alac) = 10,
    AMR      SWIFT_NAME(amr) = 11,
    FLAC      SWIFT_NAME(flac) = 12,
    OPUS      SWIFT_NAME(opus) = 13,
  } CLOSED_ENUM;

} // namespace margelo::nitro::react_native_audio_recorder_player

namespace margelo::nitro {

  using namespace margelo::nitro::react_native_audio_recorder_player;

  // C++ AVEncodingOption <> JS AVEncodingOption (union)
  template <>
  struct JSIConverter<AVEncodingOption> final {
    static inline AVEncodingOption fromJSI(jsi::Runtime& runtime, const jsi::Value& arg) {
      std::string unionValue = JSIConverter<std::string>::fromJSI(runtime, arg);
      switch (hashString(unionValue.c_str(), unionValue.size())) {
        case hashString("lpcm"): return AVEncodingOption::LPCM;
        case hashString("ima4"): return AVEncodingOption::IMA4;
        case hashString("aac"): return AVEncodingOption::AAC;
        case hashString("MAC3"): return AVEncodingOption::MAC3;
        case hashString("MAC6"): return AVEncodingOption::MAC6;
        case hashString("ulaw"): return AVEncodingOption::ULAW;
        case hashString("alaw"): return AVEncodingOption::ALAW;
        case hashString("mp1"): return AVEncodingOption::MP1;
        case hashString("mp2"): return AVEncodingOption::MP2;
        case hashString("mp4"): return AVEncodingOption::MP4;
        case hashString("alac"): return AVEncodingOption::ALAC;
        case hashString("amr"): return AVEncodingOption::AMR;
        case hashString("flac"): return AVEncodingOption::FLAC;
        case hashString("opus"): return AVEncodingOption::OPUS;
        default: [[unlikely]]
          throw std::invalid_argument("Cannot convert \"" + unionValue + "\" to enum AVEncodingOption - invalid value!");
      }
    }
    static inline jsi::Value toJSI(jsi::Runtime& runtime, AVEncodingOption arg) {
      switch (arg) {
        case AVEncodingOption::LPCM: return JSIConverter<std::string>::toJSI(runtime, "lpcm");
        case AVEncodingOption::IMA4: return JSIConverter<std::string>::toJSI(runtime, "ima4");
        case AVEncodingOption::AAC: return JSIConverter<std::string>::toJSI(runtime, "aac");
        case AVEncodingOption::MAC3: return JSIConverter<std::string>::toJSI(runtime, "MAC3");
        case AVEncodingOption::MAC6: return JSIConverter<std::string>::toJSI(runtime, "MAC6");
        case AVEncodingOption::ULAW: return JSIConverter<std::string>::toJSI(runtime, "ulaw");
        case AVEncodingOption::ALAW: return JSIConverter<std::string>::toJSI(runtime, "alaw");
        case AVEncodingOption::MP1: return JSIConverter<std::string>::toJSI(runtime, "mp1");
        case AVEncodingOption::MP2: return JSIConverter<std::string>::toJSI(runtime, "mp2");
        case AVEncodingOption::MP4: return JSIConverter<std::string>::toJSI(runtime, "mp4");
        case AVEncodingOption::ALAC: return JSIConverter<std::string>::toJSI(runtime, "alac");
        case AVEncodingOption::AMR: return JSIConverter<std::string>::toJSI(runtime, "amr");
        case AVEncodingOption::FLAC: return JSIConverter<std::string>::toJSI(runtime, "flac");
        case AVEncodingOption::OPUS: return JSIConverter<std::string>::toJSI(runtime, "opus");
        default: [[unlikely]]
          throw std::invalid_argument("Cannot convert AVEncodingOption to JS - invalid value: "
                                    + std::to_string(static_cast<int>(arg)) + "!");
      }
    }
    static inline bool canConvert(jsi::Runtime& runtime, const jsi::Value& value) {
      if (!value.isString()) {
        return false;
      }
      std::string unionValue = JSIConverter<std::string>::fromJSI(runtime, value);
      switch (hashString(unionValue.c_str(), unionValue.size())) {
        case hashString("lpcm"):
        case hashString("ima4"):
        case hashString("aac"):
        case hashString("MAC3"):
        case hashString("MAC6"):
        case hashString("ulaw"):
        case hashString("alaw"):
        case hashString("mp1"):
        case hashString("mp2"):
        case hashString("mp4"):
        case hashString("alac"):
        case hashString("amr"):
        case hashString("flac"):
        case hashString("opus"):
          return true;
        default:
          return false;
      }
    }
  };

} // namespace margelo::nitro
