/**
 * 服务端返回消息状态码解析, 如果服务端附带了状态码信息, 就使用服务端的, 否则使用这里定义的.
 * 原则上服务端的状态码信息与本函数里的状态码信息是一致的, 使用客户端解析的意义在于语言的国际化, 但目前不需要, 为方便调试, 直接使用服务端解析.
 * 服务端定义位于函数: encode-http-response.
 * 文件: /src/utils.lisp
 * @param {int} stateCode 状态码
 * @param {string} info 状态信息
 * @returns
 */
export const parseServerState = (stateCode, info) => {
    switch (stateCode) {
        case  0: return info ? `服务端(${stateCode}): ${info}` : `服务端: OK(${stateCode})`;
        case  1: return info ? `服务端(${stateCode}): ${info}` : `服务端: 未登录(${stateCode})`;
        case  2: return info ? `服务端(${stateCode}): ${info}` : `服务端: 未找到记录(${stateCode})`;
        case  3: return info ? `服务端(${stateCode}): ${info}` : `服务端: 变量错误(${stateCode})`;
        case  4: return info ? `服务端(${stateCode}): ${info}` : `服务端: 格式错误(${stateCode})`;
        case  5: return info ? `服务端(${stateCode}): ${info}` : `服务端: 未授权(${stateCode})`;
        case  6: return info ? `服务端(${stateCode}): ${info}` : `服务端: 输入错误(${stateCode})`;
        case  7: return info ? `服务端(${stateCode}): ${info}` : `服务端: 未实现(${stateCode})`;
        case  9: return info ? `服务端(${stateCode}): ${info}` : `服务端: 未处理异常(${stateCode})`;
        case 10: return info ? `服务端(${stateCode}): ${info}` : `服务端: 数据版本错误(${stateCode})`;
        case 11: return info ? `服务端(${stateCode}): ${info}` : `服务端: 禁止操作(${stateCode})`;
        default: return info ? `服务端(${stateCode}): ${info}` : `服务端: ${stateCode}`;
    }
};
