import { useEffect } from "react";
import NetInfo from "@react-native-community/netinfo";
import { onlineManager } from "@tanstack/react-query";
import { Platform } from "react-native";


// https://tanstack.com/query/latest/docs/react/react-native#online-status-management
// https://codesandbox.io/p/sandbox/github/tanstack/query/tree/main/examples/react/react-native?file=%2Fsrc%2Fhooks%2FuseOnlineManager.ts%3A1%2C31

/**
 * Online status management
 */
export function useOnlineManager() {
    useEffect(() => {
        // React Query already supports on reconnect auto refetch in web browser
        if (Platform.OS !== "web") {
            return NetInfo.addEventListener((state) => {
                onlineManager.setOnline(
                    state.isConnected != null &&
                    state.isConnected &&
                    Boolean(state.isInternetReachable),
                );
            });
        }
    }, []);
}
