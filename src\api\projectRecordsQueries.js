// This file contains the query maker function for the projects' record,
// including record listing, inserting, updating, deleting


import { makeRecordsAddingClient as addQueryClient } from "./makeRecordsAddingClient";
import { makeRecordsDeletingClient as delQueryClient } from "./makeRecordsDeletingClient";
import { makeRecordsGenDocClient as genDocQueryClient } from "./makeRecordsGenDocClient";
import { makeRecordsGettingClient as getQueryClient } from "./makeRecordsGettingClient";
import { makeRecordsListingClient as lstQueryClient } from "./makeRecordsListingClient";
import { makeRecordsUpdatingClient as updQueryClient } from "./makeRecordsUpdatingClient";

import { default as makeUseQueryClient } from "./makeUseQueryClient";

import * as keys from "../config/keysConfig";
import { makeMutationClientGenerator } from "./makeMutationClient";

// for water balance project
export const makeReqLstWaterBalanceRecords = lstQueryClient(keys.LST_WATER_BALANCE_RECORDS);
export const makeReqSumWaterBalanceRecords = lstQueryClient(keys.SUM_WATER_BALANCE_RECORDS);
export const makeReqAddWaterBalanceRecords = addQueryClient(keys.ADD_WATER_BALANCE_RECORDS);
export const makeReqGetWaterBalanceRecords = getQueryClient(keys.GET_WATER_BALANCE_RECORDS);
export const makeReqUpdWaterBalanceRecords = updQueryClient(keys.UPD_WATER_BALANCE_RECORDS);
export const makeReqDelWaterBalanceRecords = delQueryClient(keys.DEL_WATER_BALANCE_RECORDS);
export const makeReqGenWaterBalanceDoc     = genDocQueryClient(keys.GEN_WATER_BALANCE_DOC);

// for zero carbon project
export const makeReqLstZeroCarbonRecords = lstQueryClient(keys.LST_ZERO_CARBON_RECORDS);
export const makeReqSumZeroCarbonRecords = lstQueryClient(keys.SUM_ZERO_CARBON_RECORDS);
export const makeReqAddZeroCarbonRecords = addQueryClient(keys.ADD_ZERO_CARBON_RECORDS);
export const makeReqGetZeroCarbonRecords = getQueryClient(keys.GET_ZERO_CARBON_RECORDS);
export const makeReqUpdZeroCarbonRecords = updQueryClient(keys.UPD_ZERO_CARBON_RECORDS);
export const makeReqDelZeroCarbonRecords = delQueryClient(keys.DEL_ZERO_CARBON_RECORDS);

// 使用useQuery执行的查询
export const waterBalanceUseQueryClient = makeUseQueryClient(keys.WATER_BALANCE_USE_QUERY);

// 用于通用的简单查询, 返回一个函数, 与上面用法一样, 但http请求函数必须显示调用， 用法见SketchingScreen.jsx
export const makeSimpleMutationClient = makeMutationClientGenerator();
