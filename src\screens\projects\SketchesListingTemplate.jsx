import React, { useEffect, useRef, useState } from "react";
import { StyleSheet, View } from "react-native";
import { Appbar, DataTable, Divider, Snackbar, useTheme } from "react-native-paper";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import {
    makeReqAddWaterBalanceRecords as addRecordsQueryMaker,
    makeReqLstWaterBalanceRecords as listRecordsQueryMaker,
} from "../../api/projectRecordsQueries";
import BottomBarButton from "../../components/BottomBarButton";
import HeaderBar from "../../components/HeaderBar";
import ImageCard from "../../components/ImageCard";
import { useRefreshOnFocus } from "../../hooks/useRefreshOnFocus";
import { assembleUrl } from "../../utils";
import { parseServerState } from "../../utils/messages";
import EmptyScreen from "../EmptyScreen";
import ScreenWrapper from "../ScreenWrapper";


/**
 * @param {object} args
 * @param {string} args.pubid 本项目的pubid
 * @param {string} args.wholeNaviTo 汇总表的导航名称, 如果为假就表示没有汇总表
 * @param {bool} args.displaySumup 用户控制统计表单的栏目是否显示
 * @returns
 */
const SketchesListingTemplate = ({ title, queryKwd, pubid, listItemNavigateTo, bottomBarLabel, emptyScreenText, snackBarDefaultText, addButtonIcon, addButtonLabel, navigation, projMeta, displayAppbar = true, ...props }) => {

    const { bottom, left, right } = useSafeAreaInsets();
    const theme = useTheme();
    const height = theme.isV3 ? 80 : 56;

    const [snackBarMessage, setSnackBarMessage] = useState(snackBarDefaultText);

    const [showSnackbar, setShowSnackbar] = useState(false);
    const [dataArray, setDataArray] = useState([]);
    const dataArrayBuf = useRef([]);

    // query data record to fill the list content of current page
    const onSketchesListSuccess = (data) => {
        console.log("onSketchesListSuccess data:", data);
        if (data.STATUS === 0) {
            dataArrayBuf.current = [];
            dataArrayBuf.current = dataArrayBuf.current.concat(data.DATA.filter(item => item?.id !== 0));
            setDataArray(dataArrayBuf.current);
        } else {
            setSnackBarMessage(parseServerState(data.STATUS || data.status, data.DATA.info));
            setShowSnackbar(true);
        }
    };
    const onSketchesListError = (err) => { console.log(title, "onSketchesListError", err); };
    const onSketchesListSettled = () => {};
    const pageDataQuery = listRecordsQueryMaker([queryKwd, "lst", pubid], onSketchesListSuccess, onSketchesListError, onSketchesListSettled);

    // query to add a new empty record
    const onRecordAddSuccess = (data) => {
        console.log("onRecordAddSuccess data:", data);
        if (data.STATUS === 0) {
            pageDataQuery.mutate();
        } else {
            setSnackBarMessage(parseServerState(data.STATUS || data.status, data.DATA.info));
            setShowSnackbar(true);
        }
    };
    const onRecordAddError = (err) => { console.log(title, "onRecordAddError", err);};
    const onRecordAddSettled = () => {};
    const recordAddQuery = addRecordsQueryMaker([queryKwd, "add", pubid], onRecordAddSuccess, onRecordAddError, onRecordAddSettled);

    useEffect(() => {
        pageDataQuery.mutate();
    }, []);

    useRefreshOnFocus(pageDataQuery.mutate); // 通过返回按钮返回时刷新数据

    return (
        <>
            <HeaderBar
                title={title}
                navigation={navigation}
            //goBackCallback={() => {}}
            // 获得外链需要根据权限开启
            //menuItemArray={[{ title: "获得外链", action: ()=>{} }]}
            />
            {dataArray.length === 0 ? <EmptyScreen text={emptyScreenText} /> :
                <ScreenWrapper contentContainerStyle={styles.container}>
                    <DataTable style={styles.dataTable}>
                        <Divider />

                        {dataArray.map((item, i) => {
                            return (
                                <View key={`cardview-${i}-${Math.floor(Math.random() * 1000000)}`}>
                                    <ImageCard
                                        imageURL={item.picture ? assembleUrl(item.picture) : null}
                                        title={item.title || ""}
                                        subtitle={item.subtitle || ""}
                                        content={item.content || ""}
                                        onPress={() => {
                                            navigation.navigate(listItemNavigateTo, { pageMeta: { queryKwd: queryKwd, ...item }, projMeta:projMeta });
                                        }}
                                        theme={theme}
                                    />

                                </View>);
                        })}


                    </DataTable>
                    {/* Give a margin, or the Appbar will be on top of the last ListingItem. */}

                </ScreenWrapper>}

            {displayAppbar && <Appbar
                style={[
                    styles.bottom,
                    {
                        height: height + bottom,
                    },
                    theme.isV3 && {
                        backgroundColor: theme.colors.elevation.level2,
                    },
                ]}
                safeAreaInsets={{ bottom, left, right }}
                theme={{ mode: "adaptive" }}
            //mode='center-aligned'
            >

                <View style={styles.bottomBarContainer}>
                    <View style={styles.bottomBarComponents}>
                        <BottomBarButton
                            label={bottomBarLabel}
                            icon={addButtonIcon}
                            onPress={() => {
                                recordAddQuery.mutate();
                            }}
                        />
                    </View>
                </View>
            </Appbar>}

            <Snackbar
                style={styles.snackBar}
                visible={showSnackbar}
                onDismiss={() => {
                    setShowSnackbar(false);
                }}
                onIconPress={() => {
                    setShowSnackbar(false);
                }}
                duration={Snackbar.DURATION_LONG}
            >
                {snackBarMessage}
            </Snackbar>

        </>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    card: {
        margin: 4,
    },
    dataTable: {
        fontSize: 24,
        //marginTop: 18,
        //borderColor: "black"
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        flex: 1,
        marginHorizontal: 6
    },
    snackBar: {
        flexDirection: "column",
    },
});

export default SketchesListingTemplate;
