///
/// RecordBackType.hpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc R<PERSON> @ Margelo
///

#pragma once

#if __has_include(<NitroModules/JSIConverter.hpp>)
#include <NitroModules/JSIConverter.hpp>
#else
#error NitroModules cannot be found! Are you sure you installed NitroModules properly?
#endif
#if __has_include(<NitroModules/NitroDefines.hpp>)
#include <NitroModules/NitroDefines.hpp>
#else
#error NitroModules cannot be found! Are you sure you installed NitroModules properly?
#endif



#include <optional>

namespace margelo::nitro::react_native_audio_recorder_player {

  /**
   * A struct which can be represented as a JavaScript object (RecordBackType).
   */
  struct RecordBackType {
  public:
    std::optional<bool> isRecording     SWIFT_PRIVATE;
    double currentPosition     SWIFT_PRIVATE;
    std::optional<double> currentMetering     SWIFT_PRIVATE;
    std::optional<double> recordSecs     SWIFT_PRIVATE;

  public:
    RecordBackType() = default;
    explicit RecordBackType(std::optional<bool> isRecording, double currentPosition, std::optional<double> currentMetering, std::optional<double> recordSecs): isRecording(isRecording), currentPosition(currentPosition), currentMetering(currentMetering), recordSecs(recordSecs) {}
  };

} // namespace margelo::nitro::react_native_audio_recorder_player

namespace margelo::nitro {

  using namespace margelo::nitro::react_native_audio_recorder_player;

  // C++ RecordBackType <> JS RecordBackType (object)
  template <>
  struct JSIConverter<RecordBackType> final {
    static inline RecordBackType fromJSI(jsi::Runtime& runtime, const jsi::Value& arg) {
      jsi::Object obj = arg.asObject(runtime);
      return RecordBackType(
        JSIConverter<std::optional<bool>>::fromJSI(runtime, obj.getProperty(runtime, "isRecording")),
        JSIConverter<double>::fromJSI(runtime, obj.getProperty(runtime, "currentPosition")),
        JSIConverter<std::optional<double>>::fromJSI(runtime, obj.getProperty(runtime, "currentMetering")),
        JSIConverter<std::optional<double>>::fromJSI(runtime, obj.getProperty(runtime, "recordSecs"))
      );
    }
    static inline jsi::Value toJSI(jsi::Runtime& runtime, const RecordBackType& arg) {
      jsi::Object obj(runtime);
      obj.setProperty(runtime, "isRecording", JSIConverter<std::optional<bool>>::toJSI(runtime, arg.isRecording));
      obj.setProperty(runtime, "currentPosition", JSIConverter<double>::toJSI(runtime, arg.currentPosition));
      obj.setProperty(runtime, "currentMetering", JSIConverter<std::optional<double>>::toJSI(runtime, arg.currentMetering));
      obj.setProperty(runtime, "recordSecs", JSIConverter<std::optional<double>>::toJSI(runtime, arg.recordSecs));
      return obj;
    }
    static inline bool canConvert(jsi::Runtime& runtime, const jsi::Value& value) {
      if (!value.isObject()) {
        return false;
      }
      jsi::Object obj = value.getObject(runtime);
      if (!JSIConverter<std::optional<bool>>::canConvert(runtime, obj.getProperty(runtime, "isRecording"))) return false;
      if (!JSIConverter<double>::canConvert(runtime, obj.getProperty(runtime, "currentPosition"))) return false;
      if (!JSIConverter<std::optional<double>>::canConvert(runtime, obj.getProperty(runtime, "currentMetering"))) return false;
      if (!JSIConverter<std::optional<double>>::canConvert(runtime, obj.getProperty(runtime, "recordSecs"))) return false;
      return true;
    }
  };

} // namespace margelo::nitro
