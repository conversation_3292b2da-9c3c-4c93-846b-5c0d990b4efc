import React, { useState } from "react";
import { Appbar, Menu } from "react-native-paper";
import { Platform } from "react-native";


const MORE_ICON = Platform.OS === "ios" ? "dots-horizontal" : "dots-vertical";

const HeaderBar = ({ title, navigation, menuItemArray, goBackCallback, titleMode = "center-aligned" }) => {
    const [menuVisible, setMenuVisible] = useState(false);

    //const [sceneAnimation, setSceneAnimation] = useState();
    return (
        <Appbar.Header elevated mode={titleMode}>
            <Appbar.BackAction onPress={() => {
                goBackCallback?.();
                navigation.goBack();
            }} />
            <Appbar.Content title={title} />
            {menuItemArray &&
            <Menu
                visible={menuVisible}
                onDismiss={() => setMenuVisible(false)}
                anchor={
                    <Appbar.Action
                        icon={MORE_ICON}
                        onPress={() => setMenuVisible(true)}
                        //{...{ color: "white" }}
                    />
                }
            >
                {menuItemArray.map((item, index) => {
                    return (
                        <Menu.Item
                            key={index}
                            title={item.title}
                            onPress={() => {
                                item.action();
                                setMenuVisible(false);
                            }}
                        />
                    );
                })}

            </Menu>}
        </Appbar.Header>
    );
};

export default HeaderBar;


export const HeaderMenu = ({ menuItemArray, icon = "dots-vertical" }) => {
    const [menuVisible, setMenuVisible] = useState(false);
    return (
        menuItemArray &&
            <Menu
                visible={menuVisible}
                onDismiss={() => setMenuVisible(false)}
                anchor={
                    <Appbar.Action
                        icon={icon}
                        onPress={() => setMenuVisible(true)}
                        //{...{ color: "white" }}
                    />
                }
            >
                {menuItemArray.map((item, index) => {
                    return (
                        <Menu.Item
                            key={index}
                            trailingIcon={item.checked ? "check" : ""}
                            title={item.title}
                            onPress={() => {
                                item.action();
                                setMenuVisible(false);
                            }}
                        />
                    );
                })}
            </Menu>
    );
};
