import debounce from "lodash/debounce";
import React, { useEffect, useState } from "react";
import { Controller, useForm, useWatch } from "react-hook-form";
import { Dimensions, ScrollView, StyleSheet, View } from "react-native";
import {
    Button,
    Dialog,
    Portal,
    RadioButton,
    Text,
    TextInput,
    useTheme
} from "react-native-paper";
import { isFunction } from "../utils";


/*
dialogItemArray = [{id: 1, name: "Option1"}]
*/
export const DialogWithRadioButton = ({ title, visible, onOK, onCancel, okBtnLabel = "确定", cancelBtnLabel = "取消", dialogState, setDialogState, dialogItemArray, nullOptionsTip, ...props }) => {
    const [checked, setChecked] = useState(dialogState); // checked should be: {"id": 1, "name": "DEPT-01"}
    const [filteredArray, setFilteredArray] = useState(dialogItemArray);
    const theme = useTheme();

    const {
        control,
    } = useForm({
        defaultValues: {
            filterWith: "",
        },
    });

    const filterWith = useWatch({ control, name: "filterWith"});

    const updateItemArray = debounce((filterWith) => {
        setFilteredArray(filterWith ? dialogItemArray.filter(item => item.name.includes(filterWith)) : dialogItemArray);
    }, 100);

    useEffect(() => {
        updateItemArray(filterWith);
    }, [filterWith, dialogItemArray]);

    useEffect(() => {
        setChecked(dialogState);
    }, [dialogState]);

    return (
        <Portal>
            <Dialog
                onDismiss={() => { setChecked(dialogState); onCancel();}}
                visible={visible}
                style={{ maxHeight: 0.8 * Dimensions.get("window").height }}
            >
                <Dialog.Title>{title}</Dialog.Title>
                <View style={styles.searchBarContainer}>
                    <Controller
                        control={control}
                        //rules={{ required: true, }}
                        render={({ field: { onChange, onBlur, value } }) => (
                            <TextInput
                                label=""
                                mode="outlined"
                                onBlur={onBlur}
                                onChangeText={onChange}
                                value={value}
                                right={<TextInput.Icon icon="magnify" />}
                                style={{
                                    flex: 1,
                                    marginHorizontal: "5%",
                                    backgroundColor: theme.colors.surface,
                                }}
                            />
                        )}
                        name="filterWith"
                    />
                </View>

                <Dialog.ScrollArea style={styles.container}>
                    { !dialogItemArray || dialogItemArray.length === 0 ?
                        <Text variant="titleMedium" style={{ textAlign: "center", }}>{isFunction(nullOptionsTip) ? nullOptionsTip() : nullOptionsTip || "发生未知错误, 请联系管理员!"}</Text>
                        :
                        <ScrollView>
                            {filteredArray?.map((item, index) => {
                                return (
                                    <RadioButton.Item
                                        key={index}
                                        label={item.name}
                                        value={item.name}
                                        status={checked?.id === item?.id ? "checked" : "unchecked"}
                                        onPress={()=>setChecked({ id: item?.id, name: item?.name })}
                                    />
                                /*
                                    <TouchableRipple key={index} onPress={() => {
                                        setChecked({ id: item?.id, name: item?.name });

                                    }}>
                                        <View style={styles.row}>
                                            <View pointerEvents="none">
                                                <RadioButton
                                                    //value={item.name}
                                                    status={checked?.name === item?.name ? "checked" : "unchecked"}
                                                />
                                            </View>
                                            <DialogTextComponent isSubheading style={styles.text}>
                                                {item.name}
                                            </DialogTextComponent>
                                        </View>
                                    </TouchableRipple>
                                */
                                );
                            })}
                        </ScrollView>}
                </Dialog.ScrollArea>
                <Dialog.Actions>
                    <Button onPress={() => {
                        if (!dialogItemArray || dialogItemArray.length === 0) {
                            onCancel();
                        } else {
                            setChecked(dialogState);
                            onCancel();
                        }
                    }}>{cancelBtnLabel}</Button>
                    <Button onPress={() => {
                        if (!dialogItemArray || dialogItemArray.length === 0){
                            onCancel();
                        } else {
                            setDialogState(checked);
                            onOK(checked);
                        }
                    }}>{okBtnLabel}</Button>
                </Dialog.Actions>
            </Dialog>
        </Portal>
    );
};

const styles = StyleSheet.create({
    container: {
        //maxHeight: 270,
        paddingHorizontal: 0,
    },
    row: { // 没使用
        flexDirection: "row",
        alignItems: "center",
        paddingHorizontal: 16,
        paddingVertical: 8,
    },
    text: {
        paddingLeft: 8,
    },
    searchBarContainer: {
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
        width: "100%",
        //paddingVertical: 8,
    },
});
