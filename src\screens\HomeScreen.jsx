import { getHeader<PERSON>itle } from "@react-navigation/elements";
import { CardStyleInterpolators, createStackNavigator } from "@react-navigation/stack";
import React from "react";
import { Platform, StyleSheet, View } from "react-native";
import { Appbar, Text } from "react-native-paper";
import { LoginScreen, RegisterScreen, ResetPasswordScreen } from ".";
import EmptyScreen from "./EmptyScreen";
import { HomeTabs } from "./HomeTabs";
import {
    BasicInfoRecordsListing,
    BasicInfoRecordsUpdating,
    ClientInfoRecordsListing,
    ClientInfoRecordsUpdating,
    ClientMaterialConsumeRecordsListing,
    ClientMaterialConsumeRecordsUpdating,
    ClientProductScaleRecordsListing,
    ClientProductScaleRecordsUpdating,
    ClientPublicUtilityRecordsListing,
    ClientPublicUtilityRecordsUpdating,
    ClientRequiredDeviceRecordsListing,
    ClientRequiredDeviceRecordsUpdating,
    ComOpStatsRecordsListing,
    ComOpStatsRecordsUpdating,
    ComProdStatsRecordsListing,
    ComProdStatsRecordsUpdating,
    ComWaterMeterEquipRecordsListing,
    ComWaterMeterEquipRecordsUpdating,
    ComWaterMeterEquipStatsRecordsListing,
    ComWaterMeterEquipStatsRecordsUpdating,
    EnergySystemInfoRecordsListing,
    EnergySystemInfoRecordsUpdating,
    SketchesListing,
    SketchingScreen,
    SketchingTest,
    WaterBalanceIndex,
    WaterBalanceInspectStatsRecordsListing,
    WaterBalanceInspectStatsRecordsUpdating,
    WaterBalanceOverview,
    WaterMeterSampleRecordsListing,
    WaterMeterSampleRecordsUpdating,
    WaterQuotaInfoRecordsListing,
    WaterQuotaInfoRecordsUpdating,
    WaterSavingEquipsRecordsListing,
    WaterSavingEquipsRecordsListing2,
    WaterSavingEquipsRecordsUpdating,
    WaterSavingEquipsRecordsUpdating2,
    WaterSourceRecordsListing,
    WaterSourceRecordsUpdating,
    //WaterUsageRecordsListing,
    //WaterUsageRecordsUpdating,
    WaterUsageAnnualRecordsListing,
    WaterUsageAnnualRecordsUpdating,
    WaterUsageCalcListing,
    WaterUsageCalcUpdating,
    //WaterBalanceInspectStatsRecordsWhole,
    WaterUsageInspectResultsRecordsListing,
    WaterUsageInspectResultsRecordsUpdating,
    WaterUsageInstAnalyzeRecordsListing,
    //WaterUsageInstAnalyzeRecordsUpdating,
    WaterUsageInstAnalyzeRecordsWhole,
    WaterUsageUnitsFirstRecordsListing,
    WaterUsageUnitsFirstRecordsUpdating,
    WaterUsageUnitsFourthRecordsListing,
    WaterUsageUnitsFourthRecordsUpdating,
    WaterUsageUnitsRecordsAdding,
    WaterUsageUnitsSecondRecordsListing,
    WaterUsageUnitsSecondRecordsUpdating,
    WaterUsageUnitsThirdRecordsListing,
    WaterUsageUnitsThirdRecordsUpdating,
    WaterUsingEquipsRecordsListing,
    WaterUsingEquipsRecordsUpdating,
    ZCBasicInfoRecordsListing,
    ZCBasicInfoRecordsUpdating,
    ZCEnergyBillsRecordsListing,
    ZCEnergyBillsRecordsUpdating,
    ZCEnergyConsumersRecordsListing,
    ZCEnergyConsumersRecordsUpdating,
    ZCHeatingBillsRecordsListing,
    ZCHeatingBillsRecordsUpdating,
    ZCScreensIndex,
} from "./projects";
import {
    ClientsInserting, ClientsListing, ClientsUpdating, DepartmentsInserting, DepartmentsListing, DepartmentsUpdating,
    OrgInserting, OrgListing, OrgUpdating, PositionsInserting, PositionsListing, PositionsUpdating, ProjectBaseInserting, ProjectBaseListing, ProjectBaseUpdating,
    ProjectCopying,
    RolesInserting, RolesListing, RolesUpdating,
    TestBottomSheet, TestImageInput, TestInputTypes, TestReactHookFormMMKV, TestReactHookFormZustand, TestUploadFiles,
    UserInserting, UserListing, UserUpdating, WaterBalanceInserting, WaterBalanceListing, WaterBalanceUpdating,
    ZeroCarbonInserting,
    ZeroCarbonListing,
    ZeroCarbonUpdating,
} from "./workbench";

const Stack = createStackNavigator();

// 需求: 仅在各个Tab页面的顶层显示标签, 其它更深层次的页面只显示各自的header(甚至自己的header都不显示), 不显示Tab页签
// 方法是, 在根屏幕(Home)创建一个栈导航,
// 这个栈导航的第一个屏幕实现标签导航, 其它所有二级及更深的屏幕都属于这个栈导航的屏幕.
// 这样, Tab页面的内容定义在各Tab页面上, 深入页面并不受到Tab导航的影响, 因此不会显示Tab页签.
// https://reactnavigation.org/docs/hiding-tabbar-in-screens/
/**
 * 使用Stack.Navigator定义主页面,
 * 所有与功能相关且需要导航的屏幕都必须放在这里
 */
const HomeScreen = () => {
    const cardStyleInterpolator =
        Platform.OS === "android"
            ? CardStyleInterpolators.forFadeFromBottomAndroid
            : CardStyleInterpolators.forHorizontalIOS;

    return (
        <>
            <Stack.Navigator
                screenOptions={({ navigation }) => {
                    return {
                        detachPreviousScreen: !navigation.isFocused(),
                        cardStyleInterpolator,
                        header: ({ navigation, route, options, back }) => {
                            const title = getHeaderTitle(options, route.name);
                            return (
                                <Appbar.Header elevated mode={"center-aligned"}>
                                    {back ? (
                                        <Appbar.BackAction onPress={() => navigation.goBack()} />
                                    ) : navigation.openDrawer ? (
                                        <Appbar.Action
                                            icon="menu"
                                            //icon={() => <Avatar.Icon size={28} icon="menu" />}
                                            isLeading
                                            onPress={() => navigation.openDrawer()}
                                        />
                                    ) : null}
                                    <Appbar.Content title={title}/>
                                </Appbar.Header>
                            );
                        },
                    };
                }}
            >

                {/** 主页屏幕 */}
                <Stack.Screen name="MainScreen" component={HomeTabs} options={{ headerShown: false }} />

                {/** 抽屉菜单点击退出后的屏幕 */}
                <Stack.Screen name="LoginScreen"         component={LoginScreen}         options={{ headerShown: false }} />
                <Stack.Screen name="RegisterScreen"      component={RegisterScreen}      options={{ headerShown: false }} />
                <Stack.Screen name="ResetPasswordScreen" component={ResetPasswordScreen} options={{ headerShown: false }} />

                {/** 工作台屏幕 APP用户 */}
                <Stack.Screen name="WorkbenchOrgListing"   component={OrgListing}   options={{ title: "管理App客户" }} />
                <Stack.Screen name="WorkbenchOrgInserting" component={OrgInserting} options={{ headerShown: false }} />
                <Stack.Screen name="WorkbenchOrgUpdating"  component={OrgUpdating}  options={{ headerShown: false }} />

                {/** 工作台屏幕 用户 */}
                <Stack.Screen name="WorkbenchUserListing"   component={UserListing}   options={{ title:"管理项目成员" }} />
                <Stack.Screen name="WorkbenchUserInserting" component={UserInserting} options={{ headerShown: false }} />
                <Stack.Screen name="WorkbenchUserUpdating"  component={UserUpdating}  options={{ headerShown: false }} />

                {/** 工作台屏幕 部门 */}
                <Stack.Screen name="WorkbenchDepartmentsListing"   component={DepartmentsListing}   options={{ title: "部门管理" }} />
                <Stack.Screen name="WorkbenchDepartmentsInserting" component={DepartmentsInserting} options={{ headerShown: false }} />
                <Stack.Screen name="WorkbenchDepartmentsUpdating"  component={DepartmentsUpdating}  options={{ headerShown: false }} />

                {/** 工作台屏幕 职位 */}
                <Stack.Screen name="WorkbenchPositionsListing"   component={PositionsListing}   options={{ title: "岗位管理" }} />
                <Stack.Screen name="WorkbenchPositionsInserting" component={PositionsInserting} options={{ headerShown: false }} />
                <Stack.Screen name="WorkbenchPositionsUpdating"  component={PositionsUpdating}  options={{ headerShown: false }} />

                {/** 工作台屏幕 角色 */}
                <Stack.Screen name="WorkbenchRolesListing"   component={RolesListing}   options={{ title: "角色权限管理" }} />
                <Stack.Screen name="WorkbenchRolesInserting" component={RolesInserting} options={{ headerShown: false }} />
                <Stack.Screen name="WorkbenchRolesUpdating"  component={RolesUpdating}  options={{ headerShown: false }} />

                {/** 工作台屏幕 客户 */}
                <Stack.Screen name="WorkbenchClientsListing"   component={ClientsListing}   options={{ title: "客户管理" }} />
                <Stack.Screen name="WorkbenchClientsInserting" component={ClientsInserting} options={{ headerShown: false }} />
                <Stack.Screen name="WorkbenchClientsUpdating"  component={ClientsUpdating}  options={{ headerShown: false }} />

                {/** 工作台屏幕 项目 */}
                <Stack.Screen name="WorkbenchProjectBaseListing"   component={ProjectBaseListing}   options={{ title: "项目库管理" }} />
                <Stack.Screen name="WorkbenchProjectBaseInserting" component={ProjectBaseInserting} options={{ headerShown: false }} />
                <Stack.Screen name="WorkbenchProjectBaseUpdating"  component={ProjectBaseUpdating}  options={{ headerShown: false }} />

                {/** 工作台屏幕 新建水平衡 */}
                <Stack.Screen name="WorkbenchWaterBalanceListing"   component={WaterBalanceListing}   options={{ title: "水平衡项目" }} />
                <Stack.Screen name="WorkbenchWaterBalanceInserting" component={WaterBalanceInserting} options={{ headerShown: false }} />
                <Stack.Screen name="WorkbenchWaterBalanceUpdating"  component={WaterBalanceUpdating}  options={{ headerShown: false }} />

                {/** 工作台屏幕 新建零碳诊断 */}
                <Stack.Screen name="WorkbenchZeroCarbonListing"   component={ZeroCarbonListing}   options={{ title: "零碳诊断" }} />
                <Stack.Screen name="WorkbenchZeroCarbonInserting" component={ZeroCarbonInserting} options={{ headerShown: false }} />
                <Stack.Screen name="WorkbenchZeroCarbonUpdating"  component={ZeroCarbonUpdating}  options={{ headerShown: false }} />

                {/** 工作台屏幕 项目拷贝 */}
                <Stack.Screen name="ProjectCopying"               component={ProjectCopying}      options={{ headerShown: false }} />

                {/** 消息屏点击链接, 水平衡项目 */}
                <Stack.Screen name="WaterBalanceIndex"                       component={WaterBalanceIndex}                       options={{ headerShown: false }} />
                <Stack.Screen name="WaterBalanceOverview"                    component={WaterBalanceOverview}                    options={{ headerShown: false }} />
                <Stack.Screen name="ClientInfoRecordsListing"                component={ClientInfoRecordsListing}                options={{ headerShown: false }} />
                <Stack.Screen name="ClientInfoRecordsUpdating"               component={ClientInfoRecordsUpdating}               options={{ headerShown: false }} />
                <Stack.Screen name="ClientPublicUtilityRecordsListing"       component={ClientPublicUtilityRecordsListing}       options={{ headerShown: false }} />
                <Stack.Screen name="ClientPublicUtilityRecordsUpdating"      component={ClientPublicUtilityRecordsUpdating}      options={{ headerShown: false }} />
                <Stack.Screen name="ClientProductScaleRecordsListing"        component={ClientProductScaleRecordsListing}        options={{ headerShown: false }} />
                <Stack.Screen name="ClientProductScaleRecordsUpdating"       component={ClientProductScaleRecordsUpdating}       options={{ headerShown: false }} />
                <Stack.Screen name="ClientMaterialConsumeRecordsListing"     component={ClientMaterialConsumeRecordsListing}     options={{ headerShown: false }} />
                <Stack.Screen name="ClientMaterialConsumeRecordsUpdating"    component={ClientMaterialConsumeRecordsUpdating}    options={{ headerShown: false }} />
                <Stack.Screen name="ClientRequiredDeviceRecordsListing"      component={ClientRequiredDeviceRecordsListing}      options={{ headerShown: false }} />
                <Stack.Screen name="ClientRequiredDeviceRecordsUpdating"     component={ClientRequiredDeviceRecordsUpdating}     options={{ headerShown: false }} />
                <Stack.Screen name="BasicInfoRecordsListing"                 component={BasicInfoRecordsListing}                 options={{ headerShown: false }} />
                <Stack.Screen name="BasicInfoRecordsUpdating"                component={BasicInfoRecordsUpdating}                options={{ headerShown: false }} />
                <Stack.Screen name="WaterQuotaInfoRecordsListing"            component={WaterQuotaInfoRecordsListing}            options={{ headerShown: false }} />
                <Stack.Screen name="WaterQuotaInfoRecordsUpdating"           component={WaterQuotaInfoRecordsUpdating}           options={{ headerShown: false }} />
                <Stack.Screen name="WaterUsageCalcListing"                   component={WaterUsageCalcListing}                   options={{ headerShown: false }} />
                <Stack.Screen name="WaterUsageCalcUpdating"                  component={WaterUsageCalcUpdating}                  options={{ headerShown: false }} />
                <Stack.Screen name="WaterSourceRecordsListing"               component={WaterSourceRecordsListing}               options={{ headerShown: false }} />
                <Stack.Screen name="WaterSourceRecordsUpdating"              component={WaterSourceRecordsUpdating}              options={{ headerShown: false }} />
                {/*<Stack.Screen name="WaterUsageRecordsListing"                component={WaterUsageRecordsListing}                options={{ headerShown: false }} />*/}
                {/*<Stack.Screen name="WaterUsageRecordsUpdating"               component={WaterUsageRecordsUpdating}               options={{ headerShown: false }} />*/}
                <Stack.Screen name="WaterUsageAnnualRecordsListing"          component={WaterUsageAnnualRecordsListing}          options={{ headerShown: false }} />
                <Stack.Screen name="WaterUsageAnnualRecordsUpdating"         component={WaterUsageAnnualRecordsUpdating}         options={{ headerShown: false }} />
                <Stack.Screen name="ComProdStatsRecordsListing"              component={ComProdStatsRecordsListing}              options={{ headerShown: false }} />
                <Stack.Screen name="ComProdStatsRecordsUpdating"             component={ComProdStatsRecordsUpdating}             options={{ headerShown: false }} />
                <Stack.Screen name="ComOpStatsRecordsListing"                component={ComOpStatsRecordsListing}                options={{ headerShown: false }} />
                <Stack.Screen name="ComOpStatsRecordsUpdating"               component={ComOpStatsRecordsUpdating}               options={{ headerShown: false }} />
                <Stack.Screen name="ComWaterMeterEquipStatsRecordsListing"   component={ComWaterMeterEquipStatsRecordsListing}   options={{ headerShown: false }} />
                <Stack.Screen name="ComWaterMeterEquipStatsRecordsUpdating"  component={ComWaterMeterEquipStatsRecordsUpdating}  options={{ headerShown: false }} />
                <Stack.Screen name="ComWaterMeterEquipRecordsListing"        component={ComWaterMeterEquipRecordsListing}        options={{ headerShown: false }} />
                <Stack.Screen name="ComWaterMeterEquipRecordsUpdating"       component={ComWaterMeterEquipRecordsUpdating}       options={{ headerShown: false }} />
                <Stack.Screen name="WaterMeterSampleRecordsListing"          component={WaterMeterSampleRecordsListing}          options={{ headerShown: false }} />
                <Stack.Screen name="WaterMeterSampleRecordsUpdating"         component={WaterMeterSampleRecordsUpdating}         options={{ headerShown: false }} />
                <Stack.Screen name="WaterUsageUnitsRecordsAdding"            component={WaterUsageUnitsRecordsAdding}            options={{ headerShown: false }} />
                <Stack.Screen name="WaterUsageUnitsFirstRecordsListing"      component={WaterUsageUnitsFirstRecordsListing}      options={{ headerShown: false }} />
                <Stack.Screen name="WaterUsageUnitsFirstRecordsUpdating"     component={WaterUsageUnitsFirstRecordsUpdating}     options={{ headerShown: false }} />
                <Stack.Screen name="WaterUsageUnitsSecondRecordsListing"     component={WaterUsageUnitsSecondRecordsListing}     options={{ headerShown: false }} />
                <Stack.Screen name="WaterUsageUnitsSecondRecordsUpdating"    component={WaterUsageUnitsSecondRecordsUpdating}    options={{ headerShown: false }} />
                <Stack.Screen name="WaterUsageUnitsThirdRecordsListing"      component={WaterUsageUnitsThirdRecordsListing}      options={{ headerShown: false }} />
                <Stack.Screen name="WaterUsageUnitsThirdRecordsUpdating"     component={WaterUsageUnitsThirdRecordsUpdating}     options={{ headerShown: false }} />
                <Stack.Screen name="WaterUsageUnitsFourthRecordsListing"     component={WaterUsageUnitsFourthRecordsListing}     options={{ headerShown: false }} />
                <Stack.Screen name="WaterUsageUnitsFourthRecordsUpdating"    component={WaterUsageUnitsFourthRecordsUpdating}    options={{ headerShown: false }} />
                <Stack.Screen name="WaterSavingEquipsRecordsListing"         component={WaterSavingEquipsRecordsListing}         options={{ headerShown: false }} />
                <Stack.Screen name="WaterSavingEquipsRecordsUpdating"        component={WaterSavingEquipsRecordsUpdating}        options={{ headerShown: false }} />
                <Stack.Screen name="WaterSavingEquipsRecordsListing2"        component={WaterSavingEquipsRecordsListing2}        options={{ headerShown: false }} />
                <Stack.Screen name="WaterSavingEquipsRecordsUpdating2"       component={WaterSavingEquipsRecordsUpdating2}       options={{ headerShown: false }} />
                <Stack.Screen name="WaterUsingEquipsRecordsListing"          component={WaterUsingEquipsRecordsListing}          options={{ headerShown: false }} />
                <Stack.Screen name="WaterUsingEquipsRecordsUpdating"         component={WaterUsingEquipsRecordsUpdating}         options={{ headerShown: false }} />

                <Stack.Screen name="WaterBalanceInspectStatsRecordsListing"  component={WaterBalanceInspectStatsRecordsListing}  options={{ headerShown: false }} />
                <Stack.Screen name="WaterBalanceInspectStatsRecordsUpdating" component={WaterBalanceInspectStatsRecordsUpdating} options={{ headerShown: false }} />
                {/*<Stack.Screen name="WaterBalanceInspectStatsRecordsWhole" component={WaterBalanceInspectStatsRecordsWhole}    options={{ headerShown: false }} />*/}
                <Stack.Screen name="WaterUsageInspectResultsRecordsListing"  component={WaterUsageInspectResultsRecordsListing}  options={{ headerShown: false }} />
                <Stack.Screen name="WaterUsageInspectResultsRecordsUpdating" component={WaterUsageInspectResultsRecordsUpdating} options={{ headerShown: false }} />
                <Stack.Screen name="WaterUsageInstAnalyzeRecordsListing"     component={WaterUsageInstAnalyzeRecordsListing}     options={{ headerShown: false }} />
                {/*<Stack.Screen name="WaterUsageInstAnalyzeRecordsUpdating" component={WaterUsageInstAnalyzeRecordsUpdating}    options={{ headerShown: false }} />*/}
                <Stack.Screen name="WaterUsageInstAnalyzeRecordsWhole"       component={WaterUsageInstAnalyzeRecordsWhole}       options={{ headerShown: false }} />

                {/** 消息屏点击链接, 零碳诊断项目 */}
                <Stack.Screen name="ZCScreensIndex"                          component={ZCScreensIndex}                          options={{ headerShown: false }} />
                <Stack.Screen name="ZCBasicInfoRecordsListing"               component={ZCBasicInfoRecordsListing}               options={{ headerShown: false }} />
                <Stack.Screen name="ZCBasicInfoRecordsUpdating"              component={ZCBasicInfoRecordsUpdating}              options={{ headerShown: false }} />
                <Stack.Screen name="ZCEnergyConsumersRecordsListing"         component={ZCEnergyConsumersRecordsListing}         options={{ headerShown: false }} />
                <Stack.Screen name="ZCEnergyConsumersRecordsUpdating"        component={ZCEnergyConsumersRecordsUpdating}        options={{ headerShown: false }} />
                <Stack.Screen name="ZCEnergyBillsRecordsListing"             component={ZCEnergyBillsRecordsListing}             options={{ headerShown: false }} />
                <Stack.Screen name="ZCEnergyBillsRecordsUpdating"            component={ZCEnergyBillsRecordsUpdating}            options={{ headerShown: false }} />
                <Stack.Screen name="ZCHeatingBillsRecordsListing"            component={ZCHeatingBillsRecordsListing}            options={{ headerShown: false }} />
                <Stack.Screen name="ZCHeatingBillsRecordsUpdating"           component={ZCHeatingBillsRecordsUpdating}           options={{ headerShown: false }} />
                <Stack.Screen name="EnergySystemInfoRecordsListing"          component={EnergySystemInfoRecordsListing}          options={{ headerShown: false }} />
                <Stack.Screen name="EnergySystemInfoRecordsUpdating"         component={EnergySystemInfoRecordsUpdating}         options={{ headerShown: false }} />









                {/** 附件区 */}
                <Stack.Screen name="SketchesListing"         component={SketchesListing}         options={{ headerShown: false }} />
                <Stack.Screen name="SketchingScreen"         component={SketchingScreen}         options={{ headerShown: false }} />

                {/**空屏幕 */}
                <Stack.Screen name="EmptyScreen" component={EmptyScreen} options={{ title: "" }} />

                {/** 功能测试区 */}
                <Stack.Screen name="SketchingTest" component={SketchingTest} options={{ title: "Skia图像" }} />
                <Stack.Screen name="TestInputTypes" component={TestInputTypes} options={{ title: "添加项目成员" }} />
                <Stack.Screen name="TestScreen1"      component={TestScreen1} options={{ title: "水平衡" }} />
                <Stack.Screen name="TestScreen2"      component={TestScreen2} options={{ title: "电平衡" }} />
                <Stack.Screen name="TestReactHookFormZustand" component={TestReactHookFormZustand} options={{ title: "ReactHookForm + Zustand" }} />
                <Stack.Screen name="TestReactHookFormMMKV" component={TestReactHookFormMMKV} options={{ title: "ReactHookForm + MMKV" }} />
                <Stack.Screen name="TestUploadFiles" component={TestUploadFiles} options={{ title: "Upload Files" }} />
                <Stack.Screen name="TestBottomSheet" component={TestBottomSheet} options={{ title: "Bottom Sheet" }} />
                <Stack.Screen name="TestImageInput" component={TestImageInput} options={{ title: "Image Input" }} />

            </Stack.Navigator>

        </>);
};

HomeScreen.title = "Theming With React Navigation";

const TestScreen1 = () => {
    return (
        <View style={styles.container}>
            <Text variant="headlineMedium">水平衡</Text>
        </View>
    );
};

const TestScreen2 = () => {
    return (
        <View style={styles.container}>
            <Text variant="headlineMedium">电平衡</Text>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
    },
});

export default HomeScreen;
