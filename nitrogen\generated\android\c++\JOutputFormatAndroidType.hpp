///
/// JOutputFormatAndroidType.hpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc R<PERSON>y @ Margelo
///

#pragma once

#include <fbjni/fbjni.h>
#include "OutputFormatAndroidType.hpp"

namespace margelo::nitro::react_native_audio_recorder_player {

  using namespace facebook;

  /**
   * The C++ JNI bridge between the C++ enum "OutputFormatAndroidType" and the the Kotlin enum "OutputFormatAndroidType".
   */
  struct JOutputFormatAndroidType final: public jni::JavaClass<JOutputFormatAndroidType> {
  public:
    static auto constexpr kJavaDescriptor = "Lcom/margelo/nitro/reactnativeaudiorecorderplayer/OutputFormatAndroidType;";

  public:
    /**
     * Convert this Java/Kotlin-based enum to the C++ enum OutputFormatAndroidType.
     */
    [[maybe_unused]]
    [[nodiscard]]
    OutputFormatAndroidType toCpp() const {
      static const auto clazz = javaClassStatic();
      static const auto fieldOrdinal = clazz->getField<int>("_ordinal");
      int ordinal = this->getFieldValue(fieldOrdinal);
      return static_cast<OutputFormatAndroidType>(ordinal);
    }

  public:
    /**
     * Create a Java/Kotlin-based enum with the given C++ enum's value.
     */
    [[maybe_unused]]
    static jni::alias_ref<JOutputFormatAndroidType> fromCpp(OutputFormatAndroidType value) {
      static const auto clazz = javaClassStatic();
      static const auto fieldDEFAULT = clazz->getStaticField<JOutputFormatAndroidType>("DEFAULT");
      static const auto fieldTHREE_GPP = clazz->getStaticField<JOutputFormatAndroidType>("THREE_GPP");
      static const auto fieldMPEG_4 = clazz->getStaticField<JOutputFormatAndroidType>("MPEG_4");
      static const auto fieldAMR_NB = clazz->getStaticField<JOutputFormatAndroidType>("AMR_NB");
      static const auto fieldAMR_WB = clazz->getStaticField<JOutputFormatAndroidType>("AMR_WB");
      static const auto fieldAAC_ADIF = clazz->getStaticField<JOutputFormatAndroidType>("AAC_ADIF");
      static const auto fieldAAC_ADTS = clazz->getStaticField<JOutputFormatAndroidType>("AAC_ADTS");
      static const auto fieldOUTPUT_FORMAT_RTP_AVP = clazz->getStaticField<JOutputFormatAndroidType>("OUTPUT_FORMAT_RTP_AVP");
      static const auto fieldMPEG_2_TS = clazz->getStaticField<JOutputFormatAndroidType>("MPEG_2_TS");
      static const auto fieldWEBM = clazz->getStaticField<JOutputFormatAndroidType>("WEBM");
      
      switch (value) {
        case OutputFormatAndroidType::DEFAULT:
          return clazz->getStaticFieldValue(fieldDEFAULT);
        case OutputFormatAndroidType::THREE_GPP:
          return clazz->getStaticFieldValue(fieldTHREE_GPP);
        case OutputFormatAndroidType::MPEG_4:
          return clazz->getStaticFieldValue(fieldMPEG_4);
        case OutputFormatAndroidType::AMR_NB:
          return clazz->getStaticFieldValue(fieldAMR_NB);
        case OutputFormatAndroidType::AMR_WB:
          return clazz->getStaticFieldValue(fieldAMR_WB);
        case OutputFormatAndroidType::AAC_ADIF:
          return clazz->getStaticFieldValue(fieldAAC_ADIF);
        case OutputFormatAndroidType::AAC_ADTS:
          return clazz->getStaticFieldValue(fieldAAC_ADTS);
        case OutputFormatAndroidType::OUTPUT_FORMAT_RTP_AVP:
          return clazz->getStaticFieldValue(fieldOUTPUT_FORMAT_RTP_AVP);
        case OutputFormatAndroidType::MPEG_2_TS:
          return clazz->getStaticFieldValue(fieldMPEG_2_TS);
        case OutputFormatAndroidType::WEBM:
          return clazz->getStaticFieldValue(fieldWEBM);
        default:
          std::string stringValue = std::to_string(static_cast<int>(value));
          throw std::invalid_argument("Invalid enum value (" + stringValue + "!");
      }
    }
  };

} // namespace margelo::nitro::react_native_audio_recorder_player
