import { MMKV } from "react-native-mmkv";
import { isArray, isEmptyObject, isObject, isString } from "../../utils";
//import log from "../logging";


const MMKV_INSTANCE = new MMKV();


/**
 * Get the value of `property` under the `storeKey` of MMKV.
 * @param {string} storeKey MMKV key, should be placed in `keys.js`
 * @param {string} property The key of json which was stored in MMKV under `storeKey`
 * @returns {any} Any value that's parsed by `JSON.parse()`.
 */
export const getMMKV = (storeKey, property) => JSON.parse(MMKV_INSTANCE.getString(storeKey)).state[property];


/**
 * Clear all record in this MMKV instance.
 * Used for login out.
 */
export const clearMMKV = () => {
    MMKV_INSTANCE.getAllKeys().map(key => MMKV_INSTANCE.delete(key));
};


/**
 * Create an MMKV store with name `storeName`,
 * return an array of function to set/get/delete the property of this store.
 * This function is used to store a big form which cell should be stored for each typing
 * and the cells will be stored separately for the sake of performance(or else the big object should be updated and stringfied for each typing).
 * Should make sure different store has different store name!!
 * @param {string} storeName a string used as the prefix of the store, should not conflict with the prefixes of the existed stores.
 * @returns {{
 * setStore: (key:string, value:any)=>{},
 * getStore: (key:string)=>{},
 * deleteStore: (key:string)=>{},
 * getStoreObject: function(): Object,
 * getStoreKeys: function(): string[],
 * clearStore: function(): ingeger,
 * }}
 */
export const creatMMKVStore = (storeName) => {
    const mmkvStoreName = storeName;
    const storeNameStartsWith = `${mmkvStoreName}.`;

    // Check if this store name is existed
    const allStoreKeys = MMKV_INSTANCE.getAllKeys();
    for (const key of allStoreKeys) {
        if (key.startsWith(storeNameStartsWith)) {
            console.log("Find the same store name of %s, they will be treated as the same store!", mmkvStoreName);
        }
    }

    // store the values under the separate keys
    const makeStoreKey = key => `${mmkvStoreName}.${key}`;

    // also store the list of keys of this store
    const keyListStoreName = `${mmkvStoreName}->>ALL-KEYS`;
    const restoreKeyList = MMKV_INSTANCE.getString(keyListStoreName); // restore the key list from mmkv
    let allKeys = restoreKeyList ? JSON.parse(restoreKeyList) : [];

    /**
     * Return a list of keys for this store.
     * @returns {String[]}
     */
    const getStoreKeys = () => allKeys;

    /**
     * Store key/value under the store.
     * @param {string} key the key of the stored object, which can be stringified.
     * @param {any} value the value of the stored object, which can be stringified.
     */
    const setStore = (key, value) => {
        if (!allKeys.includes(key)) {
            allKeys.push(key);
            MMKV_INSTANCE.set(keyListStoreName, JSON.stringify(allKeys));
        }
        MMKV_INSTANCE.set(makeStoreKey(key), JSON.stringify(value));
    };

    /**
     * Get the value of the store for `key`
     * @param {string} key the key of the stored object, which can be stringified.
     */
    const getStore = (key) => {
        const mmkvValue = MMKV_INSTANCE.getString(makeStoreKey(key));
        return mmkvValue ? JSON.parse(mmkvValue) : undefined;
    };

    /**
     * Reset key/value under the store.
     * @param {string} key the key of the stored object, which can be stringified.
     */
    const resetStore = (key) => {
        if (!allKeys.includes(key)) {
            allKeys.push(key);
            MMKV_INSTANCE.set(keyListStoreName, JSON.stringify(allKeys));
        }
        MMKV_INSTANCE.set(makeStoreKey(key), JSON.stringify(""));
    };

    /**
    * Delete the value of the store for `key`
    * @param {string} key the key of the stored object, which can be stringified.
    */
    const deleteStore = (key) => {
        const keyIndex = allKeys.indexOf(key);
        if (keyIndex >= 0) {
            MMKV_INSTANCE.delete(makeStoreKey(key));
            allKeys[keyIndex] = null;
            MMKV_INSTANCE.set(keyListStoreName, JSON.stringify(allKeys));
        } else {
            console.debug("Try to delete the key ~s which is not existed.", key);
        }
    };

    /**
     * Return an object of all keys and values for this store.
     * @returns {Object}
     */
    const getStoreObject = () => {
        const obj = {};
        for (const key of allKeys) {
            key ? obj[key] = getStore(key) : undefined; // key can be null if it's deleted
        }
        return obj;
    };

    /**
     * Set this store with an object, if clearFirst is true, the previous stored data will be cleared first.
     * @returns {Object}
     */
    const setStoreObject = (obj, clearFirst = false) => {
        clearFirst && clearStore();
        for (const [key, value] of Object.entries(obj)) {
            setStore(key, value);
        }
        return obj;
    };

    /**
     * Clear all stored kvs of this store.
     * @returns {integer}
    */
    const clearStore = () => {
        const num = allKeys.length;
        allKeys.map(key => key ? MMKV_INSTANCE.delete(makeStoreKey(key)) : undefined);
        allKeys = [];
        //MMKV_INSTANCE.set(keyListStoreName, JSON.stringify(allKeys));
        MMKV_INSTANCE.delete(keyListStoreName);
        return num;
    };

    /**
     * Reset all stored kvs of this store.
     * @returns {integer}
    */
    const resetStoreObject = () => {
        const num = allKeys.length;
        allKeys.map(key => key ? MMKV_INSTANCE.set(makeStoreKey(key), "") : undefined);
        allKeys = allKeys.filter((item) => !!item);
        MMKV_INSTANCE.set(keyListStoreName, JSON.stringify(allKeys));
        return num;
    };

    return { setStore, getStore, deleteStore, getStoreObject, setStoreObject, getStoreKeys, clearStore, resetStore, resetStoreObject };
};


/**
 * Form data stored in mmkv have 3 types: array([{id,name}*]), object({id, name}), atom type
 * This function accepts the store key and retrieves the value in mmkv
 * and then convert the value to some string that can be displayed in TextInput
 * @param {*} keyName
 * @param {*} defaultTo
 * @param {*} converter
 * @returns
 */
export const makeStoredValueToFormValue = (getStoreFn) => {
    return (keyName, defaultTo = "", converter = String) => {
        const val = getStoreFn(keyName);

        if(val) {
            if (isArray(val)) {                  // for check selector, or a list of files, should be further tested
                return val.map(item => item?.id !== undefined ? item?.id : item);  // item目前有两种情况, 1.用于check的`{id, name}`对象, 2.用于图像拾取的uri字符数组
            } else if (isObject(val)) {          // for radio selector, or the file control object, should be further tested
                return val?.id !== undefined ? val?.id : (isEmptyObject(val) ? defaultTo : val);  // val目前有两种情况, 1.用于check的`{id, name}`对象, 2.用于文件修改情况的fctrl对象
            } else {                             // should be normal TextInput
                return val ? (isString(val) ? val : converter(val)) : defaultTo;
            }
        } else {
            return defaultTo;
        }
    };
};


export default MMKV_INSTANCE;
