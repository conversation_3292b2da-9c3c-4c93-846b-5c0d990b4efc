import { joi<PERSON>esolver } from "@hookform/resolvers/joi";
import "fast-text-encoding";
import <PERSON><PERSON> from "joi";
import React, { useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { StyleSheet, View } from "react-native";
import { Appbar, Divider, Snackbar, Text, useTheme } from "react-native-paper";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import BottomBarButton from "../../components/BottomBarButton";
import ControlledDateTimePicker from "../../components/ControlledDateTimePicker";
import ControlledRadioInputWithQuery from "../../components/ControlledRadioInputWithQuery";
import ControlledTextInput from "../../components/ControlledTextInput";
import { DialogToConfirm } from "../../components/DialogToConfirm";
import HeaderBar from "../../components/HeaderBar";
import ScreenWrapper from "../ScreenWrapper";

import { creatMMKVStore } from "../../services/local-storage";
import { identity, isEmptyObject, isNumber, makeDataFeeder, whenLet } from "../../utils";
import { validatorBase } from "../../utils/validatorBase";

// 新组件需要重新!!
import { makeRequestInsertProjWB as insertQueryClient } from "../../api/insertingQueries";
import { WATER_BALANCE_ADD as pageMainKey } from "../../config/keysConfig"; // 用于页面信息的提交
//import { makeRequestListingDepartments as allDeptsQueryClient } from "../../api/listingQueries";
import { useShallow } from "zustand/shallow";
import { makeRequestListingClients as allClientsQueryClient, makeRequestListingUsers as allUsersQuery, makeRequestListingWBProjBase as projbaseQuery } from "../../api/listingQueries";
import ControlledCheckboxInputWithQuery from "../../components/ControlledCheckboxInputWithQuery";
import { waterBalanceInsertStates as selectorStates } from "../../hooks/selectorStates";
import { onPreSubmitError } from "../../utils/screens";


const templateOptsDataProviderGen = (template) => {
    if (isNumber(template)) {
        if (template === 1) { // 钢铁行业
            return [
                // 主要生产用水
                { id: 1,   name: "烧结厂" },
                { id: 2,   name: "炼铁厂" },
                { id: 3,   name: "炼钢厂" },
                { id: 4,   name: "轧钢厂" },
                { id: 5,   name: "焦化厂" },
                // 辅助生产用水
                { id: 101, name: "动力厂" },
                { id: 102, name: "白灰厂" },
                { id: 103, name: "电厂" },
                { id: 104, name: "机械厂" },
                // 附属生产用水
                { id: 201, name: "后勤" },
            ];
        } else {
            return [];
        }
    } else {
        return undefined;
    }
};

// 在mmkv中存储所有控件的数据, 新组件需要重新设置
const { setStore, getStore, clearStore, getStoreObject, setStoreObject } = creatMMKVStore(pageMainKey.store);
const dataFeeder = makeDataFeeder();
// 数据规范要求服务端数据包含id为0的默认值, 同时mmkv存储的是一个对象, 因此以下几行不再需要
//const superiorReplacer = makeReplacer("superior", "name", "id"); // 数据中superior对应的值, 将它到对象数组中去匹配name键, 若成功, superior的值替换成name所在对象的id的值
//const deptDefaults = { id: 0, name: "无上级部门" };                // 需要添加的默认部门占位
//const deptLookUpKey = "id";                                      // radio按钮根据值方向查找的键值
//const threadFunctions = [superiorReplacer.replace];
//let render = 0;

/**
 * 需要注意其中包含4种数据/状态:
 * 1. InputText手动输入的数据, 在useForm中申明, 直接由react-hook-form的controller处理, 以便减少渲染.
 * 2. 拉选框状态数据, 在selectorStates.js中预先定义, 包括radio和checkbox两种, 需要通过zustand进行全局状态管理, 其中包含的是复合数据, radio的是对象{id, name}, checkbox的是对象数组[{id, name}*], 控件要求包含id和name.
 * 3. mmkv存储数据, 在此通过creatMMKVStore创建, 用于存储当前的控件数据以及载入时恢复原先状态, 其中保存的InputText数据内容与react-form的相同, 拉选框数据内容与zustand的相同.
 * 4. 完整的react-hook-form数据, 其中包含第一种数据, radio拉选框数据只包含其id, checkbox的数据则是id的数组. hook-form数据转换成FormData后由http发送给服务端.
 * @param {Object} navigation
 * @returns
 */
const WaterBalanceInserting = ({ navigation }) => {
    // render++;
    const { bottom, left, right } = useSafeAreaInsets();
    const theme = useTheme();
    const height = theme.isV3 ? 80 : 56;

    // 新组件需要修改!!
    const bottomBarLabels = { ok: "创建", cancel: "取消" };                // 底部按钮显示文字
    const [confimDialogConfig, setConfirmDialogConfig] = useState({});       // 对话框信息, {title, text, okLabel, onOK}, 但显示对话框的变量在showConfirm
    const [snackBarMessage, setSnackBarMessage] = useState("创建水平衡项目遇到错误"); // 下方错误提示文字内容
    // 拉选框组件使用全局状态
    const [
        clientRadioState,
        projbaseRadioState,
        difficultyRadioState, // 难度和项目经理水平暂时未使用, 数据库的难度使用projbase中定义的默认难度
        expectedTimePickerState,
        salesmanRadioState,
        managerRadioState,
        managerLevelRadioState,
        areaPrincipalRadioState,
        courtesyCopiesCheckState,
        templateRadioState,
        templateOptsCheckState,
        startDatePickerState,
        endDatePickerState,
        setClientRadioState,
        setProjbaseRadioState,
        setDifficultyRadioState,
        setExpectedTimePickerState,
        setSalesmanRadioState,
        setManagerRadioState,
        setManagerLevelRadioState,
        setAreaPrincipalRadioState,
        setCourtesyCopiesCheckState,
        setTemplateRadioState,
        setTemplateOptsCheckState,
        setStartDatePickerState,
        setEndDatePickerState,
    ] = selectorStates(useShallow(state => [
        state.clientRadio,
        state.projbaseRadio,
        state.difficultyRadio,
        state.expectedTimePicker,
        state.salesmanRadio,
        state.managerRadio,
        state.managerLevelRadio,
        state.areaPrincipalRadio,
        state.courtesyCopiesCheck,
        state.templateRadio,
        state.templateOptsCheck,
        state.startDatePicker,
        state.endDatePicker,
        state.setClientRadio,
        state.setProjbaseRadio,
        state.setDifficultyRadio,
        state.setExpectedTimePicker,
        state.setSalesmanRadio,
        state.setManagerRadio,
        state.setManagerLevelRadio,
        state.setAreaPrincipalRadio,
        state.setCourtesyCopiesCheck,
        state.setTemplateRadio,
        state.setTemplateOptsCheck,
        state.setStartDatePicker,
        state.setEndDatePicker,
    ])); // radio和check组件状态

    // 新组件不需改动
    const saveButtonIcon = "content-save-all-outline";
    const [okButtonLoading, setOkButtonLoading] = useState(false);           // 右下方按钮图标是否旋转
    const [okButtonDisabled, setOkButtonDisabled] = useState(false);         // 右下方按钮是否禁用
    const [cancelButtonDisabled, setCancelButtonDisabled] = useState(false); // 左下方按钮是否禁用
    const [showSnackbar, setShowSnackbar] = useState(false);                 // 下方是否显示错误通知
    const [showConfirm, setShowConfirm] = useState(false);                   // 是否显示确认对话框

    //const difficultyStateDataProviderRef = useRef([{ id: 1, name: "简单" }, { id: 2, name: "中等" }, { id: 3, name: "困难" }]);  // 用作难度状态选项的数据源
    //const managerLevelDataProviderRef    = useRef([{ id: 1, name: "低" },   { id: 2, name: "中" }, { id: 3, name: "高" }]);     // 用作类型状态选项的数据源
    const templateDataProviderRef = useRef([{ id: 1, name: "钢铁" }, { id: 0, name: "其它" }, ]);

    const formDefaults  = useRef({
        projectName:    "",
        productName:    "",
        client:         "",
        projbase:       "",
        difficulty:     0,
        expectedTime:   0,
        salesman:       "",
        manager:        "",
        managerLevel:   0,
        areaPrincipal:  "",
        courtesyCopies: [],
        template:       0,
        templateOpts:   [],
        startDate:      0,
        endDate:        0,
    });
    const storeDefaults = useRef({
        projectName:    "",
        productName:    "",
        client:         { id: "", name: "" },
        projbase:       { id: "", name: "" },
        difficulty:     { id: 0,  name: "" },
        expectedTime:   0,
        salesman:       { id: "", name: "" },
        manager:        { id: "", name: "" },
        managerLevel:   { id: 0,  name: "" },
        areaPrincipal:  { id: "", name: "" },
        courtesyCopies: [],
        template:       0,
        templateOpts:   [],
        startDate:      0,
        endDate:        0,
    });
    const selectorDefaults = useRef({
        client:         { id: "", name: "" },
        projbase:       { id: "", name: "" },
        difficulty:     { id: 0,  name: "" },
        expectedTime:   0,
        salesman:       { id: "", name: "" },
        manager:        { id: "", name: "" },
        managerLevel:   { id: 0,  name: "" },
        areaPrincipal:  { id: "", name: "" },
        courtesyCopies: [],
        template:       0,
        templateOpts:   [],
        startDate:      0,
        endDate:        0,
    });

    // 新组件需要重新定义!!
    const schema = Joi.object({
        projectName:    validatorBase.wbProjectName.required,
        productName:    validatorBase.wbProductName.unrequired,
        client:         validatorBase.wbClient.unrequired,
        projbase:       validatorBase.wbProjbase.required,
        difficulty:     validatorBase.wbDifficulty.unrequired,
        expectedTime:   validatorBase.universalTime.required,
        salesman:       validatorBase.wbSalesman.unrequired,
        manager:        validatorBase.wbManager.required,
        managerLevel:   validatorBase.wbManagerLevel.unrequired,
        areaPrincipal:  validatorBase.wbAreaPrincipal.unrequired,
        courtesyCopies: validatorBase.wbCourtesyCopies.unrequired,
        template:       validatorBase.wbTemplate.unrequired,
        templateOpts:   validatorBase.wbTemplateOpts.unrequired,
        startDate:      validatorBase.universalTime.unrequired,
        endDate:        validatorBase.universalTime.unrequired,
    });

    // 新组件需要重新定义defaultValues!!
    const {
        handleSubmit,
        control,
        resetField,
        reset,
        formState: { errors },
        getValues,
    } = useForm({
        resolver: joiResolver(schema),
        //reValidateMode: "onBlur", // https://github.com/orgs/react-hook-form/discussions/10327
        defaultValues: {
            projectName:    whenLet(getStore("projectName"),                        identity, ""),
            productName:    whenLet(getStore("productName"),                        identity, ""),
            client:         whenLet(getStore("client")?.id,                         identity, ""),
            projbase:       whenLet(getStore("projbase")?.id,                       identity, "0"),
            difficulty:     whenLet(getStore("difficulty")?.id,                     identity, "0"),
            expectedTime:   whenLet(getStore("expectedTime"),                       identity, "0"),
            salesman:       whenLet(getStore("salesman")?.id,                       identity, ""),
            manager:        whenLet(getStore("manager")?.id,                        identity, ""),
            managerLevel:   whenLet(getStore("managerLevel")?.id,                   identity, "0"),
            areaPrincipal:  whenLet(getStore("areaPrincipal")?.id,                  identity, ""),
            courtesyCopies: whenLet(getStore("courtesyCopies")?.map(item=>item.id), identity, []),
            template:       whenLet(getStore("template")?.id,                       identity, "0"),
            templateOpts:   whenLet(getStore("templateOpts")?.map(item=>item.id),   identity, []),
            startDate:      whenLet(getStore("startDate"),                          identity, "0"),
            endDate:        whenLet(getStore("endDate"),                            identity, "0"),
        },
    });

    // 新组件需要重新定义setDiagText!!
    const onHandleSubmit = (data) => { // data: {"name": "mmm", "superior": 0}
        // 由于这里的data的版本可能先于mmkv的版本, 因此必须在此将data转储到一个引用地址, 以供query使用, 这就是dataFeeder的用处, 另外dataFeeder可以做一些数据预处理
        setCancelButtonDisabled(true);
        setOkButtonDisabled(true);
        setOkButtonLoading(true);
        dataFeeder(data);
        console.log("commit data:", data);
        submitQuery.mutate();
    };
    // 新组件不需改动
    const onCancel = () => {
        clearStore();
        // 每增加一个下拉框都要设置各自的默认值
        reset(formDefaults.current);              // 重置react-form
        setStoreObject(storeDefaults.current);    // 重置mmkv存储
        // 重置selector控件状态
        setProjbaseRadioState(selectorDefaults.current.projbase);
        setClientRadioState(selectorDefaults.current.client);
        setDifficultyRadioState(selectorDefaults.current.difficulty);
        setExpectedTimePickerState(selectorDefaults.current.expectedTime);
        setSalesmanRadioState(selectorDefaults.current.salesman);
        setManagerRadioState(selectorDefaults.current.manager);
        setManagerLevelRadioState(selectorDefaults.current.managerLevel);
        setAreaPrincipalRadioState(selectorDefaults.current.areaPrincipal);
        setCourtesyCopiesCheckState(selectorDefaults.current.courtesyCopies);
        setTemplateRadioState(selectorDefaults.current.template);
        setTemplateOptsCheckState(selectorDefaults.current.templateOpts);
        setStartDatePickerState(selectorDefaults.current.startDate);
        setEndDatePickerState(selectorDefaults.current.endDate);
    };

    // 新组件不需改动
    const commitOnSuccess = (data) => {
        console.log("response success, data: ", data);
        if (data.STATUS !== 0) {
            setSnackBarMessage(`水平衡项目创建发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        } else {
            // 设置对话框数据
            setConfirmDialogConfig({
                title: "水平衡项目",
                text: "新项目创建成功!",
                okLabel: "确定",
                onOK: () => {
                    clearStore();
                    // 每增加一个下拉框都要设置各自的默认值
                    reset(formDefaults.current);                   // 重置react-form
                    setStoreObject(storeDefaults.current);         // 重置mmkv存储
                    // 重置radio状态
                    setProjbaseRadioState(selectorDefaults.current.projbase);
                    setClientRadioState(selectorDefaults.current.client);
                    setDifficultyRadioState(selectorDefaults.current.difficulty);
                    setExpectedTimePickerState(selectorDefaults.current.expectedTime);
                    setSalesmanRadioState(selectorDefaults.current.salesman);
                    setManagerRadioState(selectorDefaults.current.manager);
                    setManagerLevelRadioState(selectorDefaults.current.managerLevel);
                    setAreaPrincipalRadioState(selectorDefaults.current.areaPrincipal);
                    setCourtesyCopiesCheckState(selectorDefaults.current.courtesyCopies);
                    setTemplateRadioState(selectorDefaults.current.template);
                    setTemplateOptsCheckState(selectorDefaults.current.templateOpts);
                    setStartDatePickerState(selectorDefaults.current.startDate);
                    setEndDatePickerState(selectorDefaults.current.endDate);
                    setShowConfirm(false);
                }
            });
            setShowConfirm(true);
        }
    };
    const commitOnError = (error) => {
        setSnackBarMessage(`项目创建发生错误, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const commitOnSettled = (data, error) => {
        setCancelButtonDisabled(false);
        setOkButtonDisabled(false);
        setOkButtonLoading(false);
    };
    const submitQuery = insertQueryClient(dataFeeder, commitOnSuccess, commitOnError, commitOnSettled);

    // 组件载入时从mmkv存储恢复下拉控件状态
    useEffect(()=>{
        const storedObject = getStoreObject();
        isEmptyObject(storedObject.projbase)       ? setProjbaseRadioState(selectorDefaults.current.projbase)             : setProjbaseRadioState(storedObject.projbase);
        isEmptyObject(storedObject.client)         ? setClientRadioState(selectorDefaults.current.client)                 : setClientRadioState(storedObject.client);
        isEmptyObject(storedObject.difficulty)     ? setDifficultyRadioState(selectorDefaults.current.difficulty)         : setDifficultyRadioState(storedObject.difficulty);
        isEmptyObject(storedObject.expectedTime)   ? setExpectedTimePickerState(selectorDefaults.current.expectedTime)    : setExpectedTimePickerState(storedObject.expectedTime);
        isEmptyObject(storedObject.salesman)       ? setSalesmanRadioState(selectorDefaults.current.salesman)             : setSalesmanRadioState(storedObject.salesman);
        isEmptyObject(storedObject.manager)        ? setManagerRadioState(selectorDefaults.current.manager)               : setManagerRadioState(storedObject.manager);
        isEmptyObject(storedObject.managerLevel)   ? setManagerLevelRadioState(selectorDefaults.current.managerLevel)     : setManagerLevelRadioState(storedObject.managerLevel);
        isEmptyObject(storedObject.areaPrincipal)  ? setAreaPrincipalRadioState(selectorDefaults.current.areaPrincipal)   : setAreaPrincipalRadioState(storedObject.areaPrincipal);
        isEmptyObject(storedObject.courtesyCopies) ? setCourtesyCopiesCheckState(selectorDefaults.current.courtesyCopies) : setCourtesyCopiesCheckState(storedObject.courtesyCopies);
        isEmptyObject(storedObject.template)       ? setTemplateRadioState(selectorDefaults.current.template)             : setTemplateRadioState(storedObject.template);
        isEmptyObject(storedObject.templateOpts)   ? setTemplateOptsCheckState(selectorDefaults.current.templateOpts)     : setTemplateOptsCheckState(storedObject.templateOpts);
        isEmptyObject(storedObject.startDate)      ? setStartDatePickerState(selectorDefaults.current.startDate)          : setStartDatePickerState(storedObject.startDate);
        isEmptyObject(storedObject.endDate)        ? setEndDatePickerState(selectorDefaults.current.endDate)              : setEndDatePickerState(storedObject.endDate);
    }, []);
    //console.log("getValues...................", getValues());

    return (
        <View style={{ flex: 1 }}>

            <HeaderBar
                title={"添加新项目"}
                navigation={navigation}
            //goBackCallback={() => {}}
            //menuItemArray={[{ title: "标题", action: ()=>{} }]}
            />

            <ScreenWrapper contentContainerStyle={styles.container} style={{ marginBottom: height+ bottom }}>
                {/*<Text>Render: {render}</Text>*/}

                <View style={styles.formEntry}>
                    <ControlledTextInput
                        rowLabel="项目名称"
                        control={control}
                        name="projectName"
                        placeholder="XXXX项目"
                        onChangeText={(text) => setStore("projectName", text)}
                        onClearText={() => {
                            setStore("projectName", "");
                            resetField("projectName", { defaultValue: "" });
                        }}
                        editable={true}
                        mode="outlined"
                        formError={errors}
                        required={true}
                        multiline={true}
                    />

                    {/*<ControlledTextInput
                        rowLabel="产品名称"
                        control={control}
                        name="productName"
                        placeholder="XXXX产品"
                        onChangeText={(text) => setStore("productName", text)}
                        onClearText={() => {
                            setStore("productName", "");
                            resetField("productName", { defaultValue: "" });
                        }}
                        editable={true}
                        mode="outlined"
                        formError={errors}
                        required={true}
                    />*/}

                    <ControlledRadioInputWithQuery
                        name="client"
                        rowLabel="客户"
                        control={control}
                        placeholder="请拉选客户"
                        onDialogConfirm={(obj) => {
                            console.log("confirm client: ", obj);
                            setStore("client", obj);
                            setClientRadioState(obj);
                        }}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        dataProvider={allClientsQueryClient}
                        setFetchFailMessage={setSnackBarMessage}
                        setDisplayFailBar={setShowSnackbar}
                        dialogState={clientRadioState}
                        setDialogState={setClientRadioState}
                        defaultNullOptionsTip={"未找到客户数据, 请联系管理员!"}
                        multiline={true}
                        required={false}
                    />

                    <ControlledRadioInputWithQuery
                        name="projbase"
                        rowLabel="项目库类型"
                        control={control}
                        placeholder="请拉选项目库类型"
                        onDialogConfirm={(obj) => {
                            console.log("confirm projbase: ", obj);
                            setStore("projbase", obj);
                            setProjbaseRadioState(obj);
                        }}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        dataProvider={projbaseQuery}
                        setFetchFailMessage={setSnackBarMessage}
                        setDisplayFailBar={setShowSnackbar}
                        dialogState={projbaseRadioState}
                        setDialogState={setProjbaseRadioState}
                        defaultNullOptionsTip={"未找到项目类型数据, 请联系管理员!"}
                        required={true}
                    />

                    {/*<ControlledRadioInputWithQuery
                        name="difficulty"
                        rowLabel="难度"
                        control={control}
                        placeholder="请拉选项目难度"
                        onDialogConfirm={(obj) => {
                            console.log("confirm difficulty: ", obj);
                            setStore("difficulty", obj);
                            setDifficultyRadioState(obj);
                        }}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        //dataProvider={allUsersQuery}
                        dataProvider={difficultyStateDataProviderRef}
                        setFetchFailMessage={setSnackBarMessage}
                        setDisplayFailBar={setShowSnackbar}
                        dialogState={difficultyRadioState}
                        setDialogState={setDifficultyRadioState}
                        defaultNullOptionsTip={"未找到难度数据, 请联系管理员!"}
                        required={true}
                    />*/}

                    <ControlledDateTimePicker
                        name="expectedTime"
                        mode={"date"}
                        rowLabel="项目截止日期"
                        placeholder="请拉选截止日期"
                        dialogState={expectedTimePickerState}
                        onDialogConfirm={(obj) => {
                            console.log("confirm expectedTime: ", obj);
                            setStore("expectedTime", obj);
                            setExpectedTimePickerState(obj);
                        }}
                        required={true}
                        formError={errors}
                        control={control}
                    />

                    {/*<ControlledRadioInputWithQuery
                        name="salesman"
                        rowLabel="业务员"
                        control={control}
                        placeholder="请拉选项目难度"
                        onDialogConfirm={(obj) => {
                            console.log("confirm salesman: ", obj);
                            setStore("salesman", obj);
                            setDifficultyRadioState(obj);
                        }}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        dataProvider={allUsersQuery}
                        setFetchFailMessage={setSnackBarMessage}
                        setDisplayFailBar={setShowSnackbar}
                        dialogState={salesmanRadioState}
                        setDialogState={setSalesmanRadioState}
                        defaultNullOptionsTip={"未找到难度数据, 请联系管理员!"}
                        required={true}
                    />*/}

                    <ControlledRadioInputWithQuery
                        name="manager"
                        rowLabel="项目经理"
                        control={control}
                        placeholder="请拉选项目经理"
                        onDialogConfirm={(obj) => {
                            console.log("confirm manager: ", obj);
                            setStore("manager", obj);
                            setManagerRadioState(obj);
                        }}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        dataProvider={allUsersQuery}
                        setFetchFailMessage={setSnackBarMessage}
                        setDisplayFailBar={setShowSnackbar}
                        dialogState={managerRadioState}
                        setDialogState={setManagerRadioState}
                        defaultNullOptionsTip={"未找到项目经理数据, 请联系管理员!"}
                        required={true}
                    />

                    {/*<ControlledRadioInputWithQuery
                        name="managerLevel"
                        rowLabel="项目经理水平"
                        control={control}
                        placeholder="请拉选项目经理水平"
                        onDialogConfirm={(obj) => {
                            console.log("confirm managerLevel: ", obj);
                            setStore("managerLevel", obj);
                            setManagerLevelRadioState(obj);
                        }}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        dataProvider={managerLevelDataProviderRef}
                        setFetchFailMessage={setSnackBarMessage}
                        setDisplayFailBar={setShowSnackbar}
                        dialogState={managerLevelRadioState}
                        setDialogState={setManagerLevelRadioState}
                        defaultNullOptionsTip={"未找到项目经理水平数据, 请联系管理员!"}
                        required={true}
                    />*/}

                    {/*<ControlledRadioInputWithQuery
                        name="areaPrincipal"
                        rowLabel="区域负责人"
                        control={control}
                        placeholder="请拉选项区域负责人"
                        onDialogConfirm={(obj) => {
                            console.log("confirm areaPrincipal: ", obj);
                            setStore("areaPrincipal", obj);
                            setAreaPrincipalRadioState(obj);
                        }}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        dataProvider={allUsersQuery}
                        setFetchFailMessage={setSnackBarMessage}
                        setDisplayFailBar={setShowSnackbar}
                        dialogState={areaPrincipalRadioState}
                        setDialogState={setAreaPrincipalRadioState}
                        defaultNullOptionsTip={"未找到区域负责人数据, 请联系管理员!"}
                        required={false}
                    />*/}

                    {/*<ControlledCheckboxInputWithQuery
                        name="courtesyCopies"
                        rowLabel="抄送"
                        control={control}
                        placeholder="请拉选抄送成员"
                        onDialogConfirm={(obj) => {
                            console.log("confirm courtesyCopies: ", obj);
                            setStore("courtesyCopies", obj);
                            setCourtesyCopiesCheckState(obj);
                        }}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        //queryClient={allUsersQuery}
                        dataProvider={allUsersQuery}
                        setFetchFailMessage={setSnackBarMessage}
                        setDisplayFailBar={setShowSnackbar}
                        dialogState={courtesyCopiesCheckState}
                        setDialogState={setCourtesyCopiesCheckState}
                        defaultNullOptionsTip={"未找到成员数据, 请检查您的网络或联系管理员!"}
                        required={false}
                    />*/}

                    <Divider bold={true} />

                    <View style={{ marginTop: 5 }}>
                        <Text>以下部分用于选择项目模板, 钢铁行业请拉选, 其他行业暂且忽视。</Text>
                        <Text>注意: 提交后不可更改!</Text>
                    </View>

                    <ControlledRadioInputWithQuery
                        name="template"
                        rowLabel="行业类型"
                        control={control}
                        placeholder="请拉选行业类型"
                        onDialogConfirm={(obj) => {
                            console.log("confirm template: ", obj);
                            setStore("template", obj);
                            setTemplateRadioState(obj);
                        }}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        dataProvider={templateDataProviderRef}
                        setFetchFailMessage={setSnackBarMessage}
                        setDisplayFailBar={setShowSnackbar}
                        dialogState={templateRadioState}
                        setDialogState={setTemplateRadioState}
                        defaultNullOptionsTip={"未找到模板数据, 请联系管理员!"}
                        required={false}
                    />
                    <ControlledCheckboxInputWithQuery
                        name="templateOpts"
                        rowLabel="用水单元"
                        control={control}
                        placeholder="请拉选用水单元"
                        onDialogConfirm={(obj) => {
                            console.log("confirm templateOpts: ", obj);
                            setStore("templateOpts", obj);
                            setTemplateOptsCheckState(obj);
                        }}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        dataProvider={() => templateOptsDataProviderGen(getValues("template"))}
                        setFetchFailMessage={setSnackBarMessage}
                        setDisplayFailBar={setShowSnackbar}
                        dialogState={templateOptsCheckState}
                        setDialogState={setTemplateOptsCheckState}
                        defaultNullOptionsTip={"未找到模板选项数据, 请联系管理员!"}
                        required={false}
                    />

                    <ControlledDateTimePicker
                        name="startDate"
                        mode={"date"}
                        rowLabel="用水测试开始日期"
                        placeholder="请拉选开始日期"
                        dialogState={startDatePickerState}
                        onDialogConfirm={(obj) => {
                            console.log("confirm startDate: ", obj);
                            setStore("startDate", obj);
                            setStartDatePickerState(obj);
                        }}
                        required={false}
                        formError={errors}
                        control={control}
                    />

                    <ControlledDateTimePicker
                        name="endDate"
                        mode={"date"}
                        rowLabel="用水测试结束日期(含)"
                        placeholder="请拉选结束日期"
                        dialogState={endDatePickerState}
                        onDialogConfirm={(obj) => {
                            console.log("confirm endDate: ", obj);
                            setStore("endDate", obj);
                            setEndDatePickerState(obj);
                        }}
                        required={false}
                        formError={errors}
                        control={control}
                    />


                </View>

            </ScreenWrapper>

            <Appbar
                style={[
                    styles.bottom,
                    {
                        height: height + bottom,
                    },
                    theme.isV3 && {
                        backgroundColor: theme.colors.elevation.level2,
                    },
                ]}
                safeAreaInsets={{ bottom, left, right }}
                theme={{ mode: "adaptive" }}
            //mode='center-aligned'
            >

                <View style={styles.bottomBarContainer}>
                    <Divider />
                    <View style={styles.bottomBarComponents}>
                        <BottomBarButton
                            label={bottomBarLabels.cancel}
                            disabled={cancelButtonDisabled}
                            onPress={onCancel}
                        />
                        <BottomBarButton
                            label={bottomBarLabels.ok}
                            loading={okButtonLoading}
                            disabled={okButtonDisabled}
                            icon={saveButtonIcon}
                            onPress={handleSubmit(onHandleSubmit, (formErr) => onPreSubmitError(formErr, getStore, "WaterBalanceInserting"))}
                        />
                    </View>
                </View>
            </Appbar>

            <Snackbar
                style={styles.snackBar}
                visible={showSnackbar}
                onDismiss={() => {
                    setShowSnackbar(false);
                }}
                onIconPress={() => {
                    setShowSnackbar(false);
                }}
                duration={Snackbar.DURATION_LONG}
            >
                {snackBarMessage}
            </Snackbar>

            <DialogToConfirm
                visible={showConfirm}
                title={confimDialogConfig.title ? confimDialogConfig.title : ""}
                text={confimDialogConfig.text ? confimDialogConfig.text : ""}
                onOK={confimDialogConfig.onOK ? confimDialogConfig.onOK : () => setShowConfirm(false)}
                okBtnLabel={confimDialogConfig.okLabel ? confimDialogConfig.okLabel : "确认"}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    firstColumn: {
        flex: 2,
        //borderWidth: 1,
        //height: 40,
        content: {
            fontSize: 18,
        }
    },
    secondColumn: {
        flex: 3,
        // /borderWidth: 1,
        // height: 40,
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        flex: 1,
        marginHorizontal: 6
    },
    formEntry: {
        margin: 8,
    },
    snackBar: {
        flexDirection: "column",
    },
});

export default WaterBalanceInserting;
