import { STORE_LOGIN_INFO } from "../config/keysConfig";
import { getMMKV } from "../services/local-storage";


/**
 * A nullary function which return the token of the current user.
 */
export const getToken = () => getMMKV(STORE_LOGIN_INFO, "token");


/**
 * Return an object of getting and resetting the user permits: {getPermit: () => {}, resetPermit: () => {}}
 * @returns
 */
const makeUserPermits = () => {
    let permitList = null;
    return {
        getPermit: () => {
            if (!permitList) {
                permitList = getMMKV(STORE_LOGIN_INFO, "permits");
            }
            return permitList;
        },
        resetPermit: () => {
            permitList = getMMKV(STORE_LOGIN_INFO, "permits");
            return permitList;
        },
        clearPermit: () => {
            permitList = null;
        },
    };
};
/**
 * An object of getting and resetting the user permits: {getPermit: () => {}, resetPermit: () => {}}.
 */
const userPermits = makeUserPermits();
/**
 * A nullary function which return the permit array of the current user.
 */
export const getUserPermits = userPermits.getPermit;
export const clearPermits   = userPermits.clearPermit;


/**
 * A nullary function which return the userType of the current user.
 */
export const getUserType = () => getMMKV(STORE_LOGIN_INFO, "userType");
