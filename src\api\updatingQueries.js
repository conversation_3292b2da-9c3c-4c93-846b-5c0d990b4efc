import * as keys from "../config/keysConfig";
import { makeUpdatingClient as httpClient } from "./makeUpdatingClient";


export const makeRequestUpdateOrg         = httpClient(keys.ORG_UPDATE);            // 更新App客户
export const makeRequestUpdateDepartment  = httpClient(keys.ORG_DEPARTMENT_UPDATE); // 更新部门
export const makeRequestUpdateUser        = httpClient(keys.ORG_USER_UPDATE);       // 更新用户
export const makeRequestUpdatePosition    = httpClient(keys.ORG_POSITION_UPDATE);   // 更新用户
export const makeRequestUpdateClient      = httpClient(keys.ORG_CLIENT_UPDATE);     // 更新客户
export const makeRequestUpdateRole        = httpClient(keys.ORG_ROLE_UPDATE);       // 更新角色
export const makeRequestUpdateProjectBase = httpClient(keys.ORG_PROJ_BASE_UPDATE);  // 更新项目库

// 更新项目: 水平衡, 低碳诊断
export const makeRequestUpdateProjectWB   = httpClient(keys.WATER_BALANCE_UPDATE);
export const makeRequestUpdateProjectZC   = httpClient(keys.ZERO_CARBON_UPDATE);
