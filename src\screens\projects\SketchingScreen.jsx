import { CameraRoll } from "@react-native-camera-roll/camera-roll";
import Slider from "@react-native-community/slider";
import { Canvas, Fill, Group, ImageFormat, Path, Skia, Image as SkiaImage, Paragraph as SkiaParagraph, useCanvasRef, useImage } from "@shopify/react-native-skia";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { Modal, PermissionsAndroid, Platform, Image as ReactNativeImage, StyleSheet, TouchableOpacity, View } from "react-native";
import RNFS from "react-native-fs";
import { Gesture, GestureDetector } from "react-native-gesture-handler";
import { Button, Dialog, Divider, IconButton, Menu, Text as PaperText, TextInput as PaperTextInput, Portal, RadioButton, Snackbar, useTheme } from "react-native-paper";
import { runOnJS, useSharedValue } from "react-native-reanimated";
import {
    makeReqDelWaterBalanceRecords as recordDeleteQueryMaker,
    makeSimpleMutationClient as recordUpdateQueryMaker
} from "../../api/projectRecordsQueries";
import { uploadBase64 } from "../../api/uploadBase64";
import { DialogToConfirm } from "../../components/DialogToConfirm";
import { LoadingIndicator } from "../../components/LoadingIndicator";
import { useRerender } from "../../hooks/useRerender";
import log from "../../services/logging";
import { requestGalleryWithPermission } from "../../services/system/permissions";
import { assembleUrl, makeUrl, parseFileName } from "../../utils";
import { layoutTransform } from "../../utils/image";
import ScreenWrapper from "../ScreenWrapper";

const checkOrientation = (orientation) => {
    const validOrientation = ["PORTRAIT", "PORTRAIT_UPSIDEDOWN", "LANDSCAPE_LEFT", "LANDSCAPE_RIGHT"];
    return validOrientation.includes(orientation);
};

/**
 * 显示/添加本图像信息对话框组件
 *
 * 用于显示和编辑本图像的相关信息，如标题和备注内容。
 * 图像信息不在本组件中显示地修改, 而是通过调用onOKCallback来修改。
 *
 * @param {Object} props - 组件属性
 * @param {boolean} props.visible - 控制对话框是否可见
 * @param {Function} props.setVisible - 设置对话框可见状态的函数
 * @param {Object} props.sketchInfo - 本图像信息对象，包含title、subtitle和content字段,
 * @param {Function} props.onOKCallback - 点击确定按钮时的回调函数，接收编辑后的本图像信息对象
 * @param {Function} props.onCancelCallback - 点击取消按钮时的回调函数
 * @param {string} [props.dialogTitle=""] - 对话框标题
 * @param {string} [props.titleLabel="名称"] - 标题字段的标签文本
 * @param {string} [props.subtitleLabel=""] - 副标题字段的标签文本
 * @param {string} [props.contentLabel="备注信息"] - 内容字段的标签文本
 * @param {string} [props.okBtnLabel="确定"] - 确定按钮的文本
 * @param {string} [props.cancelBtnLabel="取消"] - 取消按钮的文本
 * @returns {React.ReactElement} 本图像信息对话框组件
 */
const SketchInfoDialog = ({ visible, setVisible, sketchInfo, onOKCallback, onCancelCallback, dialogTitle = "", titleLabel = "名称", subtitleLabel = "", contentLabel = "备注信息", okBtnLabel = "确定", cancelBtnLabel = "取消"}) => {
    const {
        control,
        reset,
        handleSubmit,
    } = useForm({
        defaultValues: {
            title: sketchInfo?.title || "",
            subtitle: sketchInfo?.subtitle || "",
            content: sketchInfo?.content || "",
        },
    });

    useEffect(()=>{
        reset({
            title: sketchInfo?.title || "",
            subtitle: sketchInfo?.subtitle || "",
            content: sketchInfo?.content || "",
        });
    }, [sketchInfo]);

    // 提交按钮, data由handleSubmit传入
    const onOK = (data) => {
        if(data) {
            const picInfo = {
                title: data.title,
                subtitle: data.subtitle,
                content: data.content,
            };
            setVisible(false);
            onOKCallback(picInfo);
            reset(); // 恢复默认值
        }
    };
        // 取消按钮
    const onCancel = () => {
        setVisible(false); // 先隐藏后清空的主观感受好些
        onCancelCallback();
        reset(); // 恢复默认值
    };

    return (
        <Portal>
            <Dialog onDismiss={() => { setVisible(false); }} visible={visible} dismissable={false}>
                {dialogTitle && <Dialog.Title>{dialogTitle}</Dialog.Title>}
                <Dialog.Content>
                    <View flexDirection="column" style={{marginTop: 16}}>
                        <View>
                            <PaperText variant="bodyLarge" style={{margin: 6}}>{titleLabel}</PaperText>
                            <Controller
                                control={control}
                                name="title"
                                //rules={{ required: true, }}
                                render={({ field: { onChange, onBlur, value } }) => (
                                    <PaperTextInput
                                        mode="outlined"
                                        multiline={true}
                                        onBlur={onBlur}
                                        onChangeText={onChange}
                                        value={value}
                                        style={{minHeight: 60,}}
                                    />
                                )}
                            />
                        </View>
                        {/*<View>
                            <PaperText variant="bodyLarge" style={{marginTop: 10, marginBottom: 6}}>{subtitleLabel}</PaperText>
                            <Controller
                                control={control}
                                name="subtitle"
                                //rules={{ required: true, }}
                                render={({ field: { onChange, onBlur, value } }) => (
                                    <PaperTextInput
                                        multiline={true}
                                        mode="outlined"
                                        onBlur={onBlur}
                                        onChangeText={onChange}
                                        value={value}
                                        style={{minHeight: 60,}}
                                    />
                                )}
                            />
                        </View>*/}
                        <View>
                            <PaperText variant="bodyLarge" style={{marginTop: 10, marginBottom: 6}}>{contentLabel}</PaperText>
                            <Controller
                                control={control}
                                name="content"
                                //rules={{ required: true, }}
                                render={({ field: { onChange, onBlur, value } }) => (
                                    <PaperTextInput
                                        mode="outlined"
                                        placeholder=""
                                        multiline={true}
                                        onBlur={onBlur}
                                        onChangeText={onChange}
                                        value={value}
                                        style={{minHeight: 100,}}
                                    />
                                )}
                            />
                        </View>
                    </View>
                </Dialog.Content>
                <Dialog.Actions>
                    <Button onPress={onCancel}>{cancelBtnLabel}</Button>
                    <Button onPress={handleSubmit(onOK)}>{okBtnLabel}</Button>
                </Dialog.Actions>
            </Dialog>
        </Portal>
    );
};

const TextInputDialog = ({visible, initTextSize, initTextInputValue, initOrientation, onCancel, onOK}) => {
    const [textSize, setTextSize] = useState(initTextSize || 20);
    const [textInputValue, setTextInputValue] = useState(initTextInputValue || "");
    const [orientation, setOrientation] = useState(checkOrientation(initOrientation) ?  initOrientation : "PORTRAIT");

    const closeModal = () => {
        onCancel(); // 调用父组件的取消回调
        setTextInputValue(""); // 清空输入框
    };

    const confirmModal = () => {
        onOK(textSize, textInputValue, orientation);
        setTextInputValue("");
    };

    return (
        <Modal
            visible={visible}
            transparent={true}
            animationType="fade"
            onShow={() => {setTextInputValue(initTextInputValue || "");}}
            onRequestClose={closeModal}
        >
            <View style={styles.modalOverlay}>
                <View style={[styles. textInputContainer, ]}>
                    <PaperText style={styles.modalTitle}>请输入文字</PaperText>
                    <PaperTextInput
                        mode="outlined"
                        style={styles.textInput}
                        value={textInputValue}
                        onChangeText={setTextInputValue}
                        autoFocus={true}
                        multiline={true}
                        maxLength={200}
                        placeholder="在此输入文字..."
                    />
                    <View style={{borderWidth: 1, borderColor: "#ccc", padding: 10, borderRadius: 5}}>
                        <PaperText style={{ textAlign: "center", marginBottom: 5,}}>旋转角度</PaperText>
                        <RadioButton.Group
                            value={orientation}
                            onValueChange={(value) => setOrientation(value)}
                        >
                            <View style={{flexDirection: "row", justifyContent: "space-around", alignItems: "center"}}>
                                <View style={{flexDirection: "row", alignItems: "center"}}>
                                    <PaperText>0°</PaperText>
                                    <RadioButton value="PORTRAIT" />
                                </View>
                                <View style={{flexDirection: "row", alignItems: "center"}}>
                                    <PaperText>90°</PaperText>
                                    <RadioButton value="LANDSCAPE_LEFT" />
                                </View>
                                <View style={{flexDirection: "row", alignItems: "center"}}>
                                    <PaperText>180°</PaperText>
                                    <RadioButton value="PORTRAIT_UPSIDEDOWN" />
                                </View>
                                <View style={{flexDirection: "row", alignItems: "center"}}>
                                    <PaperText>270°</PaperText>
                                    <RadioButton value="LANDSCAPE_RIGHT" />
                                </View>
                            </View>

                        </RadioButton.Group>
                    </View>
                    <View style={{...styles.textSizeContainer, marginTop: 5, borderColor: "#ccc", padding: 10, borderRadius: 5}}>
                        <PaperText style={styles.label}>文字大小: {textSize}</PaperText>
                        <Slider
                            value={textSize}
                            onSlidingComplete={(value) => {
                                const roundedValue = Math.round(value);
                                if (roundedValue !== textSize) {
                                    setTextSize(roundedValue);
                                }
                            }}
                            minimumValue={10}
                            maximumValue={50}
                            step={1}
                            style={styles.slider}
                        />
                    </View>
                    <View style={styles.modalButtons}>
                        <Button mode="outlined" onPress={closeModal}>取消</Button>
                        <Button mode="contained" onPress={confirmModal}>确定</Button>
                    </View>
                </View>
            </View>
        </Modal>
    );
};

/**
 * 绘制图像的屏幕
 * 关于处理中文, Skia中, 处理中文, 有两种方法:
 *    1. 使用SkiaText, 但要打包中文字体文件, `const font = useFont(require("../../assets/fonts/NotoSansSC-Regular.ttf"), 20)`.
 *    2. 使用Paragraph, 不需要打包字体.
 * 作者建议使用Paragraph而不是Text, https://github.com/Shopify/react-native-skia/issues/2681
 *
 * 注意: colorPicker处, 使用rnpaper的IconButton效果不尽如意, 占位太大, containerColor区域也太大.
 * 注意, 由于历史原因, 本屏幕曾经兼顾add和upd, cversion穿插太多调用混乱, 因此废弃掉, 对使用也不造成影响.
 */
const SketchingScreen = ({navigation, route, ...props}) => {
    //console.log("SketchingScreen pageMeta from nav:", route.params.pageMeta);
    //console.log("SketchingScreen projMeta from nav:", route.params.projMeta);

    const theme = useTheme();

    const rerender = useRerender();
    const tryRerender = useCallback((fn, delay = 20, limit = 10) => {
        if (limit > 0) {
            fn() ? rerender() : setTimeout(() => { tryRerender(fn, delay, limit--);}, delay);
        } else {
            rerender();
        }
    }, [rerender]);

    //const cversion = useRef(route.params.pageMeta?.cversion || 1); // 为简单方便, 此处客户端不使用cversion
    //const queryMode = route.params.pageMeta.id ? "upd" : "add"; // 用于查询. 如果有pubid, 则为更新模式, 否则为添加模式, 注: 更新后没有add模式
    const savedImageURI = route.params.pageMeta.picture || ""; // 老名称, 同步时不改变名称
    const normalFilename = useRef(`sk_${(route.params.pageMeta.id || "").slice(0, 10)}_${Date.now()}.jpg`); // 新名称, 用于第一次同步时使用
    const savedImageFullAddress = savedImageURI ? assembleUrl(savedImageURI, false) : null; // useImage允许null或undefined, 字符串会当作url处理
    const defaultBackgroundImage = useImage(savedImageFullAddress); // 从服务器获得默认图片作为背景

    const apiUri = useRef("api/proj/wb"); // 目前仅用于上传文件
    const queryKwd = route.params?.pageMeta?.queryKwd || "";
    const projPubid = route.params?.projMeta?.contentId || "";
    const recordPubid = route.params?.pageMeta?.id;

    const isModified = useSharedValue(false);
    const [snackBarMessage, setSnackBarMessage]        = useState("更新表单遇到错误");  // 下方错误提示文字内容
    const [showSnackbar,     setShowSnackbar]          = useState(false);            // 下方是否显示错误通知
    const [showConfirm,      setShowConfirm]           = useState(false);            // 是否显示确认对话框
    const [confimDialogConfig, setConfirmDialogConfig] = useState({});               // 对话框信息, {title, text, okLabel, onOK}, 但显示对话框的变量在showConfirm
    const [loadingIndicatorVisible, setLoadingIndicatorVisible] = useState(false); // 是否显示loading

    const requestURL = makeUrl(apiUri.current, [queryKwd, recordPubid ? "upd" : "add", recordPubid ? recordPubid : projPubid]);

    // 设备朝向, 包含4种类型: "PORTRAIT", "PORTRAIT_UPSIDEDOWN", "LANDSCAPE_LEFT", "LANDSCAPE_RIGHT"
    const defaultOrientation = useRef("PORTRAIT");

    const paths = useSharedValue([]);
    const currentPath = useSharedValue(null);
    const [strokeWidth, setStrokeWidth] = useState(2); // 画笔粗细

    // 添加状态记录最后有效的触摸位置
    const lastValidTouchPosition = useSharedValue({ x: 0, y: 0 });

    const [isEraser, setIsEraser] = useState(false); // 是否为橡皮擦模式
    const [toolsMenuVisible, setToolsMenuVisible] = useState(false);
    const [shapesMenuVisible, setShapesMenuVisible] = useState(false);

    const [sketchInfo, setSketchInfo] = useState({
        title: route.params.pageMeta.title,
        subtitle: route.params.pageMeta.subtitle,
        content: route.params.pageMeta.content,
    }); // 存放图像的文字信息
    const [sketchInfoVisible, setSketchInfoVisible] = useState(false); // 图像信息显示与填写界面开关

    const canvasRef = useCanvasRef();

    // 画布背景颜色, 橡皮擦颜色也使用背景颜色, 暂时不需要设置背景颜色
    const [backgroundColor, _setBackgroundColor] = useState("#FFFFFFFF");
    const colors = ["#000000", "#FF0000", "#00FF00", "#0000FF", "#FFA500", "#FFFFFF"]; // 颜色面板
    const [color, setColor] = useState(colors[0]); // 画笔颜色
    const previousColor = useRef(color);

    // 添加绘图模式状态
    const [drawMode, setDrawMode] = useState("freehand"); // freehand, line, rectangle, ellipse, polyline, text
    // 添加一个新的状态来跟踪所有操作
    const operations = useSharedValue([]);

    // 添加文字相关状态
    const textInputVisible = useSharedValue(false);
    const textPosition = useSharedValue({ x: 0, y: 0 });

    const defaultTextSize = useRef(20);
    //const texts = useSharedValue([]);
    const paragraphs = useSharedValue([]);


    // 添加图片相关状态
    const [backgroundImage, setBackgroundImage] = useState(null);
    const [imageLayout, setImageLayout] = useState({ x: 0, y: 0, width: 0, height: 0, rotate: false }); // 用于从相册加载图片作为背景
    const [defaultImageLayout, setDefaultImageLayout] = useState(null); // 用于从服务器获得图片作为背景, 默认为null
    const skiaImage = useImage(backgroundImage);

    // 添加折线相关状态
    const polylinePoints = useSharedValue([]);
    const lastMoveTime = useSharedValue(0);
    const lastMovePosition = useSharedValue({ x: 0, y: 0 });

    // 添加起始点引用
    const startPoint = useSharedValue({ x: 0, y: 0 });

    const absoluteTouchPosition = useRef({ x: 0, y: 0 }); // 记录绝对触摸位置
    const canvasPosition = useSharedValue({ x: 0, y: 0, width: 0, height: 0 }); // 记录画布位置

    // 获取画布位置和尺寸
    const onCanvasLayout = (event) => {
        const { x, y, width, height } = event.nativeEvent.layout;
        if (width > 0 && height > 0) {
            //log.debug("onCanvasLayout, get width and height: ", width, height);
            canvasPosition.value = { x, y, width, height };
        }
    };

    /*
    // 创建椭圆路径的辅助函数
    const createEllipsePath = (cx, cy, rx, ry) => {
        return `M ${cx - rx} ${cy} ` +
                   `A ${rx} ${ry} 0 0 1 ${cx + rx} ${cy} ` +
                   `A ${rx} ${ry} 0 0 1 ${cx - rx} ${cy} Z`;
    };
    */

    // 创建 Pan 手势对象
    const panGesture = Gesture.Pan()
        .minPointers(1)
        .maxPointers(1)
        .onBegin((event) => {
            // 手势开始 - 相当于onTouchStart
            isModified.value = true;

            // 获取触摸位置
            const { x, y } = event;

            // 计算相对于画布的位置
            let canvasX = x - canvasPosition.value.x;
            let canvasY = y - canvasPosition.value.y;

            // 限制坐标在画布范围内
            canvasX = Math.max(0, Math.min(canvasX, canvasPosition.value.width));
            canvasY = Math.max(0, Math.min(canvasY, canvasPosition.value.height));

            // 保存起始点和最后有效触摸位置
            startPoint.value = { x: canvasX, y: canvasY };
            lastValidTouchPosition.value = { x: canvasX, y: canvasY };
            console.log("Gesture begin, canvasPosition: ", canvasPosition.value, "drawMode: ", drawMode);
            console.log("Gesture begin, ", "canvasX:", canvasX, "canvasY:", canvasY, x, y);

            // 如果当前有路径，先保存它
            if (currentPath.value) {
                paths.value = [...paths.value, currentPath.value];
                currentPath.value = null;
                // 记录这个绘图操作
                operations.value = [...operations.value, { type: "path", index: paths.value.length }];
            }

            if (drawMode === "text") {
                // 文字模式：记录点击位置并显示文字输入弹窗
                textPosition.value = { x: canvasX, y: canvasY };
                textInputVisible.value = true;
                runOnJS(rerender)();
                return; // 不创建路径
            }

            if (drawMode === "freehand") {
                currentPath.value = {
                    path: `M ${canvasX} ${canvasY}`,
                    color: isEraser ? backgroundColor : color,
                    strokeWidth: strokeWidth,
                };
            } else if (drawMode === "polyline") {
                // 折线模式：开始一个新的折线或继续现有的折线
                if (polylinePoints.value.length === 0) {
                    // 新的折线
                    polylinePoints.value = [{ x: canvasX, y: canvasY }];

                    currentPath.value = {
                        path: `M ${canvasX} ${canvasY}`,
                        color: isEraser ? backgroundColor : color,
                        strokeWidth: strokeWidth,
                        mode: drawMode
                    };
                } else {
                    // 继续现有的折线
                    const newPoints = [...polylinePoints.value, { x: canvasX, y: canvasY }];
                    polylinePoints.value = newPoints;

                    // 更新路径
                    let pathData = `M ${newPoints[0].x} ${newPoints[0].y}`;
                    for (let i = 1; i < newPoints.length; i++) {
                        pathData += ` L ${newPoints[i].x} ${newPoints[i].y}`;
                    }

                    currentPath.value = {
                        path: pathData,
                        color: isEraser ? backgroundColor : color,
                        strokeWidth: strokeWidth,
                        mode: drawMode
                    };
                }

                // 记录最后移动时间和位置
                lastMoveTime.value = Date.now();
                lastMovePosition.value = { x: canvasX, y: canvasY };
            } else {
                // 对于直线、矩形和椭圆，先创建一个初始路径
                currentPath.value = {
                    path: `M ${canvasX} ${canvasY}`,
                    color: isEraser ? backgroundColor : color,
                    strokeWidth: strokeWidth,
                    mode: drawMode,
                    startX: canvasX,
                    startY: canvasY
                };
            }
            runOnJS(rerender)();
        })
        .onUpdate((event) => {
            // 如果没有当前路径，不处理移动事件
            if (!currentPath.value) return;

            // 获取触摸位置
            const { x, y } = event;

            // 计算相对于画布的位置
            let canvasX = x - canvasPosition.value.x;
            let canvasY = y - canvasPosition.value.y;

            // 限制坐标在画布范围内
            canvasX = Math.max(0, Math.min(canvasX, canvasPosition.value.width));
            canvasY = Math.max(0, Math.min(canvasY, canvasPosition.value.height));

            // 更新最后有效触摸位置
            lastValidTouchPosition.value = { x: canvasX, y: canvasY };

            // 根据绘图模式更新路径
            if (drawMode === "freehand") {
                currentPath.value = (!currentPath.value) ? currentPath.value : {...currentPath.value, path: currentPath.value.path + ` L ${canvasX} ${canvasY}`};
            } else if (drawMode === "polyline") {
                const now = Date.now();
                const { x: lastX, y: lastY } = lastMovePosition.value;
                const distance = Math.sqrt(Math.pow(canvasX - lastX, 2) + Math.pow(canvasY - lastY, 2));

                // 检查是否需要添加新点（停顿检测）
                if (distance > 10 && now - lastMoveTime.value > 300) {
                    // 添加新点
                    const newPoints = [...polylinePoints.value, { x: canvasX, y: canvasY }];
                    polylinePoints.value = newPoints;

                    // 更新路径
                    let pathData = `M ${newPoints[0].x} ${newPoints[0].y}`;
                    for (let i = 1; i < newPoints.length; i++) {
                        pathData += ` L ${newPoints[i].x} ${newPoints[i].y}`;
                    }

                    // 检查是否需要闭合路径
                    if (newPoints.length > 2) {
                        const firstPoint = newPoints[0];
                        const lastPoint = newPoints[newPoints.length - 1];
                        const closeDistance = Math.sqrt(
                            Math.pow(lastPoint.x - firstPoint.x, 2) +
                        Math.pow(lastPoint.y - firstPoint.y, 2)
                        );

                        // 如果最后一点和第一点距离很近，则闭合路径
                        if (closeDistance < 30) {
                            pathData += ` L ${firstPoint.x} ${firstPoint.y} Z`;
                        }
                    }

                    currentPath.value = (!currentPath.value) ? currentPath.value : {...currentPath.value, path: pathData};

                    // 更新最后移动时间和位置
                    lastMoveTime.value = now;
                    lastMovePosition.value = { x: canvasX, y: canvasY };
                } else {
                // 正常移动，更新临时路径显示
                    let pathData = `M ${polylinePoints.value[0].x} ${polylinePoints.value[0].y}`;
                    for (let i = 1; i < polylinePoints.value.length; i++) {
                        pathData += ` L ${polylinePoints.value[i].x} ${polylinePoints.value[i].y}`;
                    }
                    pathData += ` L ${canvasX} ${canvasY}`;

                    currentPath.value = (!currentPath.value) ? currentPath.value : {...currentPath.value, path: pathData};

                    // 更新最后移动位置
                    lastMovePosition.value = { x: canvasX, y: canvasY };
                }
            } else if (drawMode === "line") {
            // 直线：从起点到当前点
                const { startX, startY } = currentPath.value; // 安全获取属性
                if (startX === undefined || startY === undefined) return; // 如果没有起点，不执行
                currentPath.value = (!currentPath.value) ? currentPath.value : {...currentPath.value, path: `M ${startX} ${startY} L ${canvasX} ${canvasY}`};
            } else if (drawMode === "rectangle") {
            // 矩形：基于起点和当前点
                const { startX, startY } = currentPath.value || {}; // 安全获取属性
                if (startX === undefined || startY === undefined) return; // 如果没有起点，不执行
                currentPath.value = (!currentPath.value) ? currentPath.value : {...currentPath.value, path: `M ${startX} ${startY} L ${canvasX} ${startY} L ${canvasX} ${canvasY} L ${startX} ${canvasY} Z`};
            } else if (drawMode === "ellipse") {
            // 椭圆：基于起点和当前点定义的矩形
                const { startX, startY } = currentPath.value || {}; // 安全获取属性
                if (startX === undefined || startY === undefined) return; // 如果没有起点，不执行

                const centerX = (startX + canvasX) / 2;
                const centerY = (startY + canvasY) / 2;
                const radiusX = Math.abs(canvasX - startX) / 2;
                const radiusY = Math.abs(canvasY - startY) / 2;

                // 创建椭圆路径的辅助函数
                const createEllipsePath = (cx, cy, rx, ry) => {
                    return `M ${cx - rx} ${cy} ` +
                   `A ${rx} ${ry} 0 0 1 ${cx + rx} ${cy} ` +
                   `A ${rx} ${ry} 0 0 1 ${cx - rx} ${cy} Z`;
                };

                currentPath.value = (!currentPath.value) ? currentPath.value : {...currentPath.value, path: createEllipsePath(centerX, centerY, radiusX, radiusY)};
            }
            runOnJS(rerender)();
        })
        .onEnd((event) => {
            // 如果没有当前路径，不需要处理
            if (!currentPath.value) return;

            // 获取触摸位置
            const { x, y } = event;

            // 计算相对于画布的位置
            let canvasX = x - canvasPosition.value.x;
            let canvasY = y - canvasPosition.value.y;

            // 限制坐标在画布范围内
            canvasX = Math.max(0, Math.min(canvasX, canvasPosition.value.width));
            canvasY = Math.max(0, Math.min(canvasY, canvasPosition.value.height));

            if (drawMode === "polyline") {
                // 折线模式下，触摸结束时完成绘制
                if (polylinePoints.value.length > 0) {
                    // 记录最后一个点（手指离开屏幕的位置）
                    let finalPoints = [...polylinePoints.value, { x: canvasX, y: canvasY }];

                    // 检查是否需要闭合路径
                    let shouldClose = false;
                    if (finalPoints.length > 2) {
                        const firstPoint = finalPoints[0];
                        const lastPoint = finalPoints[finalPoints.length - 1];
                        const closeDistance = Math.sqrt(
                            Math.pow(lastPoint.x - firstPoint.x, 2) +
                            Math.pow(lastPoint.y - firstPoint.y, 2)
                        );

                        // 如果最后一点和第一点距离很近，则直接让最后一点等于第一点
                        if (closeDistance < 30) {
                            finalPoints[finalPoints.length - 1] = { ...firstPoint };
                            shouldClose = true;
                        }
                    }

                    // 创建最终路径
                    let pathData = `M ${finalPoints[0].x} ${finalPoints[0].y}`;
                    for (let i = 1; i < finalPoints.length; i++) {
                        pathData += ` L ${finalPoints[i].x} ${finalPoints[i].y}`;
                    }

                    // 如果需要闭合，添加Z命令
                    if (shouldClose) {
                        pathData += " Z";
                    }

                    // 添加到路径列表
                    const finalPath = {
                        ...currentPath.value,
                        path: pathData
                    };
                    paths.value = [...paths.value, finalPath];

                    // 记录这个绘图操作并重置状态
                    operations.value = [...operations.value, { type: "path", index: paths.value.length }];
                    currentPath.value = null;
                    polylinePoints.value = [];
                }
            } else {
                // 其他模式下，触摸结束时完成绘制
                paths.value = [...paths.value, currentPath.value];
                currentPath.value = null;

                // 记录这个绘图操作
                operations.value = [...operations.value, { type: "path", index: paths.value.length }];
            }
            runOnJS(rerender)();
        });




    const clearCanvas = () => {
        isModified.value || (isModified.value = true);

        // 清空所有路径和文字
        paths.value = [];
        //texts.value = [];
        paragraphs.value = [];
        currentPath.value = null;
        //setPolylinePoints([]);
        polylinePoints.value = [];
        //setOperations([]);
        operations.value = [];

        // 清除背景图片
        setBackgroundImage(null);
        setImageLayout({ x: 0, y: 0, width: 0, height: 0, rotate: false });
        setDefaultImageLayout(null);

        // 重置绘图模式为自由绘制
        setDrawMode("freehand");

        // 重置折线相关状态
        //setLastMoveTime(0);
        lastMoveTime.value = 0;
        //tLastMovePosition({ x: 0, y: 0 });
        lastMovePosition.value = { x: 0, y: 0 };

        // 重置文字输入状态
        //setTextInputVisible(false);
        textInputVisible.value = false;
        //setTextInputValue("");

        // 充值橡皮擦
        isEraser && setColor(previousColor.current);
        setIsEraser(false);

        // 重置触摸位置引用
        lastValidTouchPosition.value = { x: 0, y: 0 };
        startPoint.value = { x: 0, y: 0 };
        absoluteTouchPosition.current = { x: 0, y: 0 };

        rerender();
    };

    const undo = () => {
        if (operations.value.length === 0) return;

        // 获取最后一个操作
        const lastOperation = operations.value[operations.value.length - 1];

        // 根据操作类型撤销
        if (lastOperation.type === "path") {
            //setPaths(prev => prev.slice(0, -1));
            paths.value = paths.value.slice(0, -1);
        } else if (lastOperation.type === "text") {
            //texts.value = texts.value.slice(0, -1);
            paragraphs.value = paragraphs.value.slice(0, -1);
        } else if (lastOperation.type === "image") {
            // 恢复之前的图片状态
            if (lastOperation.previousState.uri) {
                setBackgroundImage(lastOperation.previousState.uri);
                setImageLayout(lastOperation.previousState.layout);
            } else {
                // 如果之前没有图片，则清除当前图片
                setBackgroundImage(null);
                setImageLayout({ x: 0, y: 0, width: 0, height: 0, rotate: false });
            }
        }

        // 从操作历史中移除这个操作
        operations.value = operations.value.slice(0, -1);
        rerender();
    };

    const drawModeBeforeEraser = useRef("freehand");
    const toggleEraser = () => {
        if (isEraser) {
            setDrawMode(drawModeBeforeEraser.current);
            setColor(previousColor.current);
        } else {
            drawModeBeforeEraser.current = drawMode;
            drawMode !== "freehand" && setDrawMode("freehand");
            previousColor.current = color;
            setColor(backgroundColor);
        }
        setIsEraser(!isEraser);
    };

    // 设置绘制模式图标
    const setShapeMenuIcon = useCallback(() => {
        if (isEraser){
            return "eraser";
        } else if (drawMode === "freehand") {
            return "draw";
        } else if (drawMode === "line") {
            return "slash-forward";
        } else if (drawMode === "polyline") {
            return "chart-line-variant";
        } else if (drawMode === "rectangle") {
            return "square-outline";
        } else if (drawMode === "ellipse") {
            return "ellipse-outline";
        } else if (drawMode === "text") {
            return "format-text-variant";
        } else {
            log.error("Invalid drawMode:", drawMode);
            return "draw";
        }
    }, [drawMode, isEraser]);

    // 添加设置绘图模式的函数
    const setDrawingMode = (mode) => {
        if(isEraser) {
            setIsEraser(false);
            setColor(previousColor.current);
        }
        setDrawMode(mode);
        setShapesMenuVisible(false);
    };

    // 添加文字确认函数
    const handleTextConfirm = (textSize, textInputValue, orientation) => {
        const textPreProcessed = textInputValue; // 不改动
        //const textPreProcessed = textInputValue.replace(/\r\n|\n|\r/g, "").trim(); // 去除换行符, 然后裁剪, 顺序不能反了
        if (textPreProcessed) {
            isModified.value || (isModified.value = true);
            // 使用SkiaText渲染, 添加新文字到文字列表
            /*texts.value = [...texts.value, {
                text: textPreProcessed,
                x: textPosition.value.x,
                y: textPosition.value.y,
                color: color,
                size: textSize,
                createdOrientation: orientation, // 记录创建时的方向, 由于Skia的旋转和React组件的旋转不同, 因此这里使用"PORTRAIT"这种表示
            }];*/

            // 使用SkiaParagraph渲染
            paragraphs.value = [...paragraphs.value, {
                content: Skia.ParagraphBuilder.Make()
                    .pushStyle({
                        color: Skia.Color(color),
                        fontSize: textSize,
                    })
                    .addText(textPreProcessed)
                    .build(),
                x: textPosition.value.x,
                y: textPosition.value.y,
                createdOrientation: orientation, // 记录创建时的方向, 由于Skia的旋转和React组件的旋转不同, 因此这里使用"PORTRAIT"这种表示
            }];
            // 记录这个文字操作
            //operations.value = [...operations.value, { type: "text", index: texts.value.length }];
            operations.value = [...operations.value, { type: "text", index: paragraphs.value.length }];

        }
        textInputVisible.value = false;
        tryRerender(()=> textInputVisible.value === false); // 延迟重新渲染
    };

    // 添加文字取消函数
    const handleTextCancel = () => {
        textInputVisible.value = false;
        rerender();
    };

    const requestStoragePermission = async () => {
        try {
            console.log("Android Version:", Platform.Version);
            // 检查当前权限状态
            const checkPermission = async (permission) => {
                const result = await PermissionsAndroid.check(permission);
                console.log(`Permission ${permission}:`, result);
                return result;
            };

            if (Platform.Version >= 33) {
                // Android 13+ 只需要 READ_MEDIA_IMAGES 权限
                const hasReadImages = await checkPermission(PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES);

                if (hasReadImages) {
                    console.log("已有读取媒体权限");
                    return true;
                }

                console.log("请求读取媒体权限");
                const granted = await PermissionsAndroid.request(
                    PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES,
                    {
                        title: "媒体访问权限",
                        message: "需要访问媒体权限来保存绘图",
                        buttonNeutral: "稍后询问",
                        buttonNegative: "取消",
                        buttonPositive: "确定"
                    }
                );
                console.log("媒体权限请求结果:", granted);
                return granted === PermissionsAndroid.RESULTS.GRANTED;
            } else {
                // Android 12 及以下版本需要 WRITE_EXTERNAL_STORAGE 权限
                const hasWritePermission = await checkPermission(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE);

                if (hasWritePermission) {
                    console.log("已有写入权限");
                    return true;
                }

                console.log("请求写入权限");
                const granted = await PermissionsAndroid.request(
                    PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
                    {
                        title: "存储权限",
                        message: "需要存储权限来保存绘图",
                        buttonNeutral: "稍后询问",
                        buttonNegative: "取消",
                        buttonPositive: "确定"
                    }
                );
                console.log("写入权限请求结果:", granted);
                return granted === PermissionsAndroid.RESULTS.GRANTED;
            }
        } catch (err) {
            console.warn("权限请求错误:", err);
            return false;
        }
    };

    const saveDrawing = async () => {
        try {
            const hasPermission = await requestStoragePermission();

            if (!hasPermission) {
                //Alert.alert("错误", "需要存储权限来保存图片");
                setConfirmDialogConfig({
                    title: "错误",
                    text: "需要存储权限来保存图片",
                    okLabel: "确定",
                    onOK: () => {
                        setShowConfirm(false);
                    }});
                setShowConfirm(true);
                return;
            }

            if (canvasRef.current) {
                // https://shopify.github.io/react-native-skia/docs/canvas/overview#getting-a-canvas-snapshot
                const image = await canvasRef.current?.makeImageSnapshotAsync();

                const bytes = image.encodeToBase64(ImageFormat.JPEG);
                if (!bytes) {
                    throw new Error("图片编码失败");
                }

                const tempFilePath = `${RNFS.CachesDirectoryPath}/lcmp_${Date.now()}.jpg`;  // 生成临时文件路径，扩展名为jpg
                await RNFS.writeFile(tempFilePath, bytes, "base64"); // 将base64数据写入临时文件

                // 保存到相册
                await CameraRoll.saveAsset(`file://${tempFilePath}`, {
                    type: "photo"
                });

                await RNFS.unlink(tempFilePath); // 删除临时文件

                //Alert.alert("成功", "图片已保存到相册");
                setConfirmDialogConfig({
                    title: "错误",
                    text: "图片已保存到相册",
                    okLabel: "确定",
                    onOK: () => {
                        setShowConfirm(false);
                    }});
                setShowConfirm(true);
            }
        } catch (error) {
            console.error("保存图片错误:", error);
            let errorMessage = error.message;
            // 处理一些常见错误
            if (errorMessage.includes("Permission")) {
                errorMessage = "没有足够的权限保存图片";
            } else if (errorMessage.includes("storage")) {
                errorMessage = "存储空间不足";
            } else if (errorMessage.includes("encode")) {
                errorMessage = "图片编码失败";
            }
            //Alert.alert("错误", "保存图片失败: " + errorMessage);
            setConfirmDialogConfig({
                title: "错误",
                text: "保存图片失败: " + errorMessage,
                okLabel: "确定",
                onOK: () => {
                    setShowConfirm(false);
                }});
            setShowConfirm(true);
        }
    };

    const uploadDrawing = async () => {
        try {
            if (canvasRef.current) {
                // https://shopify.github.io/react-native-skia/docs/canvas/overview#getting-a-canvas-snapshot
                const image = await canvasRef.current?.makeImageSnapshotAsync();

                const bytes = image.encodeToBase64(ImageFormat.JPEG);
                if (!bytes) {
                    throw new Error("图片编码失败");
                }

                const fileName = savedImageURI ? parseFileName(savedImageURI) : normalFilename.current;
                const response = await uploadBase64(requestURL, fileName, "image/jpeg", bytes, { ...sketchInfo });

                return response;
            }
        } catch (error) {
            console.log("上传图片失败: ", error);
            //Alert.alert("错误", "上传图片失败");
            setConfirmDialogConfig({
                title: "错误",
                text: "上传图片失败",
                okLabel: "确定",
                onOK: () => {
                    setShowConfirm(false);
                }});
            setShowConfirm(true);
            return {"STATUS": -1, "info": error.message};
        }
    };

    // 添加加载图片的函数, 图片变换参考SketchingTest.jsx
    const loadImage = async () => {
        try {
            const file = await requestGalleryWithPermission(null, true, 0, 1); // 第三个参数为0表示不走压缩流程
            if (file && file.length > 0) {
                // 保存当前图片状态，用于撤销
                const previousImageState = {
                    uri: backgroundImage,
                    layout: { ...imageLayout }
                };

                const imageFile = file[0];
                const imageSize = await ReactNativeImage.getSize(imageFile);
                const imageWidth = imageSize.width;
                const imageHeight = imageSize.height;

                const {layout} = layoutTransform(imageWidth, imageHeight, canvasPosition.value.width, canvasPosition.value.height);
                setImageLayout(layout);

                // 设置背景图片
                setBackgroundImage(imageFile);

                // 记录这个图片操作
                operations.value = [...operations.value, {
                    type: "image",
                    previousState: previousImageState,
                    currentState: {
                        uri: imageFile,
                        layout: { ...imageLayout },
                    }
                }];
                isModified.value || (isModified.value = true);
            }
        } catch (error) {
            console.error("加载图片错误:", error);
            setConfirmDialogConfig({
                title: "错误",
                text: "加载图片失败: " + error.message,
                okLabel: "确定",
                onOK: () => {
                    setShowConfirm(false);
                }});
            setShowConfirm(true);
        }
    };

    // isModified的判断要非常仔细, 因为涉及到确认框的逻辑, onRecordUpdateSuccess对此也有修改!
    const goBack = () => {
        if (isModified.value) {
            setConfirmDialogConfig({
                title: "图片有改动",
                text: "是否同步图片到服务器？",
                okLabel: "同步并退出",
                onOK: () => {
                    isModified.value = false;
                    setShowConfirm(false);
                    setLoadingIndicatorVisible(true);
                    recordUpdateQuery.mutate();
                },
                cancelLabel: "直接退出",
                onCancel: () => {
                    setShowConfirm(false);
                    isModified.value && (isModified.value = false);
                    navigation.goBack();
                }
            });
            setShowConfirm(true);
        } else {
            showConfirm && setShowConfirm(false);
            isModified.value && (isModified.value = false);
            navigation.goBack();
        }
    };

    // 新组件不需改动
    const onRecordUpdateSuccess = (data) => {
        console.log("Record update query success with response data: ", data);
        if(data.STATUS !== 0) {
            setSnackBarMessage(`图片同步发生错误, 状态码: ${data?.STATUS}, 请截图并联系管理员!`);
            setShowSnackbar(true);
        } else {
            console.log("图片更新成功！");
            // 设置确认框数据
            setConfirmDialogConfig({
                title: "成功",
                text: "图片同步成功!",
                okLabel: "确定",
                onOK: () => {
                    if(isModified.value) {
                        isModified.value = false;
                        setShowConfirm(false);
                    } else {
                        isModified.value = false;
                        setShowConfirm(false);
                        goBack();
                    }

                }});
            setShowConfirm(true);
        }
    };
    const onRecordUpdateError = (error) => {
        console.log("同步错误", error.message);
        setSnackBarMessage(`图片同步发生错误, 错误消息: ${error.message}, 请重试或联系管理员`);
        setShowSnackbar(true);
    };
    const onRecordUpdateSettled = (_data, _error) => {
        setLoadingIndicatorVisible(false);
        //setOkButtonDisabled(false);
        //setOkButtonLoading(false);
    };
    const recordUpdateQuery = recordUpdateQueryMaker([queryKwd, "upd", recordPubid], uploadDrawing, onRecordUpdateSuccess, onRecordUpdateError, onRecordUpdateSettled);

    const syncToServer = () => {
        setLoadingIndicatorVisible(true);
        recordUpdateQuery.mutate();
    };
    // Query: delete record
    const recordDeleteOnSuccess = () => {
        navigation.goBack();
    };
    const recordDeleteOnError = (error) => {
        //setSnackBarMessage(`表单删除失败, 错误消息: ${error.message}`);
        //setShowSnackbar(true);
        console.log("Record deleting error: ", error);
    };
    const recordDeleteQuery = recordDeleteQueryMaker([queryKwd, "del", recordPubid], recordDeleteOnSuccess, recordDeleteOnError);

    // 当图片加载完成或容器宽度变化时，计算画布高度和图片布局
    useEffect(() => {
        if (defaultBackgroundImage) {
            const {layout} = layoutTransform(defaultBackgroundImage.width(), defaultBackgroundImage.height(), canvasPosition.value.width, canvasPosition.value.height);
            setDefaultImageLayout(layout);
        }
    }, [defaultBackgroundImage]);


    return (
        <View style={{ flex: 1 }}>
            {/*<HeaderBar
                //title={screenTitle}
                navigation={navigation}
                goBackCallback={() => {
                    callOneByOne(
                        //() => { _mainCversionGetter=undefined; _subCversionGetter=undefined;},
                        //resetFieldsStates,
                    );
                    // callOneByOne(clearStore, resetFieldsStates)  // 返回前清空mmkv存储因为临时数据不再需要, 重置状态使得页面切换后不会使用前面的数据
                }}
                //menuItemArray={formType === 0 ? [{ title: "删除表单", action: deleteAlert }, { title: "清空缓存", action: clearCache }] : []}
            />*/}
            <ScreenWrapper contentContainerStyle={styles.container}>

                <GestureDetector gesture={panGesture}>
                    <View
                        style={styles.canvas}
                        onLayout={onCanvasLayout}
                    >
                        <Canvas
                            ref={canvasRef}
                            style={StyleSheet.absoluteFill}
                            pointerEvents="box-only"
                        >
                            <Fill color={backgroundColor} />

                            {/* 渲染从服务器获得的默认背景图片 */}
                            {defaultBackgroundImage && defaultImageLayout && (
                                (defaultImageLayout.rotate ? (<Group
                                    transform={
                                        [
                                            { translateX: defaultImageLayout.translateX },
                                            { translateY: defaultImageLayout.translateY },
                                            { rotate: defaultImageLayout.rotate },
                                            { scale: defaultImageLayout.scale }
                                        ]
                                    }
                                >
                                    <SkiaImage
                                        image={defaultBackgroundImage}
                                        x={defaultImageLayout.x}
                                        y={defaultImageLayout.y}
                                        width={defaultImageLayout.width}
                                        height={defaultImageLayout.height}
                                        //fit="cover"
                                    />
                                </Group>) : (<SkiaImage
                                    image={defaultBackgroundImage}
                                    x={defaultImageLayout.x}
                                    y={defaultImageLayout.y}
                                    width={defaultImageLayout.width}
                                    height={defaultImageLayout.height}
                                    //fit="cover"
                                />))
                            )}

                            {/* 渲染背景图片 */}
                            {skiaImage && (
                                (imageLayout.rotate ? (<Group
                                    transform={
                                        [
                                            { translateX: imageLayout.translateX },
                                            { translateY: imageLayout.translateY },
                                            { rotate: imageLayout.rotate },
                                            { scale: imageLayout.scale }
                                        ]
                                    }
                                >
                                    <SkiaImage
                                        image={skiaImage}
                                        x={imageLayout.x}
                                        y={imageLayout.y}
                                        width={imageLayout.width}
                                        height={imageLayout.height}
                                        //fit="cover"
                                    />
                                </Group>) : (<SkiaImage
                                    image={skiaImage}
                                    x={imageLayout.x}
                                    y={imageLayout.y}
                                    width={imageLayout.width}
                                    height={imageLayout.height}
                                    //fit="cover"
                                />))
                            )}

                            { /* 绘制历史路径 */
                                paths.value.map((p, index) => (
                                    <Path
                                        key={index}
                                        path={p.path}
                                        strokeWidth={p.strokeWidth}
                                        style="stroke"
                                        color={p.color}
                                    />
                                ))}
                            { /* 绘制当前路径 */
                                currentPath.value && (
                                    <Path
                                        path={currentPath.value.path}
                                        strokeWidth={currentPath.value.strokeWidth}
                                        style="stroke"
                                        color={currentPath.value.color}
                                    />
                                )}
                            {/*texts.value.map((item, index) => ( // Text不能处理中文字体问题, 改为Paragraph
                                <Group
                                    key={`text-${index}`}
                                    transform={[
                                        { translateX: item.x },
                                        { translateY: item.y },
                                        // 使用文本创建时的方向
                                        { rotate: item.createdOrientation === "LANDSCAPE_LEFT" ? Math.PI / 2 :
                                            item.createdOrientation === "LANDSCAPE_RIGHT" ? -Math.PI / 2 :
                                                item.createdOrientation === "PORTRAIT_UPSIDEDOWN" ? Math.PI : 0 },
                                        { scale: item.size / 20 },
                                        { translateX: -item.x },
                                        { translateY: -item.y }
                                    ]}
                                >
                                    <SkiaText
                                        x={item.x}
                                        y={item.y + 20} // 使用基础大小20
                                        text={item.text}
                                        color={item.color}
                                        size={20} // 使用固定大小，通过Group缩放
                                        font={font}
                                    />
                                </Group>
                            ))*/}

                            {paragraphs.value.map((item, index) => (
                                <Group
                                    key={`paragraph-${index}`}
                                    transform={[
                                        { translateX: item.x },
                                        { translateY: item.y },
                                        // 使用文本创建时的方向
                                        { rotate: item.createdOrientation === "LANDSCAPE_LEFT" ? Math.PI / 2 :
                                            item.createdOrientation === "LANDSCAPE_RIGHT" ? -Math.PI / 2 :
                                                item.createdOrientation === "PORTRAIT_UPSIDEDOWN" ? Math.PI : 0 },
                                        // SkiaParagraph不需要放缩, 创建时已经制定了大小
                                        //{ scale: item.size / 20 },
                                        { translateX: -item.x },
                                        { translateY: -item.y }
                                    ]}
                                >
                                    <SkiaParagraph
                                        paragraph={item.content}
                                        x={item.x}
                                        y={item.y}
                                        width={canvasPosition.value.width}
                                        height={canvasPosition.value.height}
                                    />
                                </Group>
                            ))}

                        </Canvas>
                    </View>
                </GestureDetector>

                {/** 下部UI */}
                <Divider bold={true} />
                <View style={{...styles.controls, backgroundColor:theme.colors.elevation.level5}}>
                    <View style={{flexDirection: "row", justifyContent: "space-between", alignItems: "center", height: 50 }}>
                        <View style={{justifyContent: "center", alignSelf: "center"}}>
                            <Menu
                                visible={shapesMenuVisible}
                                onDismiss={() => setShapesMenuVisible(false)}
                                anchor={
                                    <IconButton icon={setShapeMenuIcon()} size={28} onPress={() => setShapesMenuVisible(true)} />
                                }
                            >
                                <View>
                                    <Menu.Item leadingIcon={"format-text-variant"} title="文字" onPress={() => { setDrawingMode("text"); }} />
                                    <Divider />
                                    <Menu.Item leadingIcon={"ellipse-outline"} title="圆形" onPress={() => { setDrawingMode("ellipse"); }} />
                                    <Divider />
                                    <Menu.Item leadingIcon={"square-outline"} title="方形" onPress={() => { setDrawingMode("rectangle"); }} />
                                    <Divider />
                                    <Menu.Item leadingIcon={"chart-line-variant"} title="折线" onPress={() => { setDrawingMode("polyline"); }} />
                                    <Divider />
                                    <Menu.Item leadingIcon={"slash-forward"} title="直线" onPress={() => { setDrawingMode("line"); }} />
                                    <Divider />
                                    <Menu.Item leadingIcon={"draw"} title="铅笔" onPress={() => { setDrawingMode("freehand"); }} />
                                    <Divider />
                                </View>
                            </Menu>
                        </View>

                        <View style={[styles.colorPicker, {marginLeft: 0, marginRight: 0, height: 50, alignSelf: "center"}]}>
                            {colors.map((c) => (
                                <TouchableOpacity
                                    key={c}
                                    style={[
                                        styles.colorButton,
                                        { backgroundColor: c },
                                        color === c && styles.selectedColor,
                                        isEraser && c === color && styles.disabled
                                    ]}
                                    onPress={() => {
                                        setColor(c);
                                        setIsEraser(false);
                                    }}
                                    disabled={isEraser}
                                />
                            ))}
                        </View>

                        <View style={{justifyContent: "center", alignSelf: "center"}}>
                            <Menu
                                visible={toolsMenuVisible}
                                onDismiss={() => setToolsMenuVisible(false)}
                                anchor={
                                    <IconButton icon="cog-outline" size={28} onPress={() => setToolsMenuVisible(true)} />
                                }
                            >
                                <View>
                                    <Menu.Item leadingIcon={"arrow-left"} title={"退出"} onPress={() => { setToolsMenuVisible(false); goBack();}} />
                                    <Divider bold={true}/>
                                    <Menu.Item leadingIcon={"cloud-sync-outline"} title={"同步到服务器"} onPress={() => {
                                        isModified.value || (isModified.value = true); // 这个赋值的目的是当未修改但同步时，点击弹框确定按钮不会返回上一界面
                                        syncToServer();
                                        setToolsMenuVisible(false);
                                    }} />
                                    <Divider/>
                                    <Menu.Item leadingIcon={"file-image-remove-outline"} title={"删除图片"} onPress={() => {  setToolsMenuVisible(false); recordDeleteQuery.mutate();}} />
                                    <Divider/>
                                    <Menu.Item leadingIcon={"content-save-outline"} title={"保存到相册"} onPress={() => { saveDrawing(); setToolsMenuVisible(false); }} />
                                    <Divider bold={true}/>
                                    <Menu.Item leadingIcon={"playlist-edit"} title={"图像信息"} onPress={() => { setSketchInfoVisible(true); setToolsMenuVisible(false); }} />
                                    <Divider/>
                                    <Menu.Item leadingIcon={"image-outline"} title={"相册"} onPress={() => { loadImage(); setToolsMenuVisible(false); }} />
                                    <Divider/>
                                    {/*<Menu.Item leadingIcon={"image-size-select-large"} title={"扩大画布"} onPress={() => { setToolsMenuVisible(false); }} />
                                    <Divider/>*/}
                                    <Menu.Item leadingIcon={"close-outline"} title={"清空画布"} onPress={() => { clearCanvas(); setToolsMenuVisible(false); }} />
                                    <Divider/>
                                    {/*<Menu.Item leadingIcon={"format-color-fill"} title={"填充"} onPress={() => { }} />
                                    <Divider/>*/}
                                    <Menu.Item leadingIcon={isEraser ? "draw" : "eraser"} title={isEraser ? "绘制" : "擦除"} onPress={() => { toggleEraser(); setToolsMenuVisible(false); }} />
                                    <Divider/>
                                    <Menu.Item leadingIcon={"undo"} title={"撤销"} onPress={() => { undo(); setToolsMenuVisible(false); }} />
                                    <Divider bold={true}/>
                                </View>
                            </Menu>
                        </View>
                    </View>

                    <View style={styles.sliderContainer}>
                        <PaperText style={styles.label}>笔触大小: {strokeWidth}</PaperText>
                        <Slider
                            value={strokeWidth}
                            onSlidingComplete={(value) => {
                                if (value !== strokeWidth) {
                                    setStrokeWidth(value);
                                }
                            }}
                            minimumValue={1}
                            maximumValue={50}
                            step={1}
                            style={styles.slider}
                        />
                    </View>
                </View>

                {/* 添加文字输入弹窗 */}
                <TextInputDialog
                    visible={textInputVisible.value}
                    initTextSize={defaultTextSize.current}
                    initTextInputValue={""}
                    initOrientation={defaultOrientation.current}
                    onCancel={handleTextCancel}
                    onOK={handleTextConfirm}
                />

                {/* 图像信息弹窗 */}
                <SketchInfoDialog
                    visible={sketchInfoVisible}
                    setVisible={setSketchInfoVisible}
                    sketchInfo={sketchInfo}
                    onOKCallback={setSketchInfo}
                    onCancelCallback={() => {}}
                    titleLabel={"名称"}
                    contentLabel={"备注"}
                />

                <Snackbar
                    style={styles.snackBar}
                    visible={showSnackbar}
                    onDismiss={() => setShowSnackbar(false)}
                    onIconPress={() => setShowSnackbar(false)}
                    duration={Snackbar.DURATION_LONG}
                >
                    {snackBarMessage}
                </Snackbar>

                <DialogToConfirm
                    visible={showConfirm}
                    title={confimDialogConfig.title ? confimDialogConfig.title : ""}
                    text={confimDialogConfig.text ? confimDialogConfig.text : ""}
                    onOK={confimDialogConfig.onOK ? confimDialogConfig.onOK : () => setShowConfirm(false)}
                    okBtnLabel={confimDialogConfig.okLabel ? confimDialogConfig.okLabel : "确认"}
                    onCancel={confimDialogConfig.onCancel ? confimDialogConfig.onCancel : () => setShowConfirm(false)}
                    cancelBtnLabel={confimDialogConfig.cancelLabel ? confimDialogConfig.cancelLabel : "取消"}
                />

                <LoadingIndicator
                    title={""}
                    message={"上传中....."}
                    visible={loadingIndicatorVisible}
                    onClose={()=>setLoadingIndicatorVisible(false)}
                />
            </ScreenWrapper>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: "#fff",
    },
    header: {
        flexDirection: "row",
        justifyContent: "space-between",
        padding: 10,
        backgroundColor: "#fff",
        borderBottomWidth: 1,
        borderBottomColor: "#eee",
    },
    headerButton: {
        padding: 8,
    },
    canvas: {
        flex: 1,
        backgroundColor: "#fff",
        //borderColor:"red",
        //borderWidth: 1,
        width: "100%",
        height: "100%"
    },
    controls: {
        padding: 10,
        backgroundColor: "#fff",
    },
    colorPicker: {
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
        // marginBottom: 10,  // 移除这一行
        flex: 1,
        height: 50,  // 添加固定高度
    },
    colorButton: {
        width: 30,
        height: 30,
        borderRadius: 15,
        margin: 5,
        borderWidth: 1,
        borderColor: "#ccc",
    },
    selectedColor: {
        borderWidth: 3,
        borderColor: "#666",
    },
    disabled: {
        opacity: 0.5,
    },
    sliderContainer: {
        marginBottom: 10,
    },
    label: {
        textAlign: "center",
        marginBottom: 5,
    },
    slider: {
        width: "100%",
    },
    modalOverlay: {
        flex: 1,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        justifyContent: "center",
        alignItems: "center",
    },
    menuContainer: {
        backgroundColor: "#fff",
        borderRadius: 10,
        padding: 20,
        minWidth: 200,
    },
    menuItem: {
        flexDirection: "row",
        alignItems: "center",
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: "#eee",
    },
    menuText: {
        marginLeft: 10,
        fontSize: 16,
    },
    textInputContainer: {
        backgroundColor: "#fff",
        borderRadius: 10,
        padding: 20,
        width: "80%",
        maxWidth: 400,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: "bold",
        marginBottom: 15,
        textAlign: "center",
    },
    textInput: {
        borderWidth: 1,
        borderColor: "#ccc",
        borderRadius: 5,
        padding: 10,
        minHeight: 100,
        textAlignVertical: "top",
        marginBottom: 15,
    },
    textSizeContainer: {
        borderWidth: 1,
        marginBottom: 15,
    },
    modalButtons: {
        flexDirection: "row",
        justifyContent: "space-around",
    },
});

export default SketchingScreen;
