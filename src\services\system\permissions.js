import Geolocation from "@react-native-community/geolocation";
import { <PERSON><PERSON>, PermissionsAndroid, Platform, } from "react-native";
import { Image as CompressImage } from "react-native-compressor";
import { getSystemVersion } from "react-native-device-info";
import RNFS from "react-native-fs";
import ImagePicker from "react-native-image-crop-picker";
import { check, PERMISSIONS, request, RESULTS } from "react-native-permissions";
import { MAX_IMAGE_SIZE, REQUEST_LOCATION_MAX_RETRY, REQUEST_LOCATION_MAXIMUM_AGE, REQUEST_LOCATION_TIMEOUT } from "../../config";
import { ensureArray } from "../../utils";
import log from "../logging";

/**
 * Image组件
 * resizeMode参数区别:
 * https://medium.com/@nima-ahmadi/react-native-image-resizemode-a-visual-guide-f1958d27c615
 * https://mehrankhandev.medium.com/understanding-resizemode-in-react-native-dd0e455ce63
 * 官方文档: https://reactnative.dev/docs/image#resizemode
 */

/**
 *
 * @param {Object} pickedImage
 * @param {function} onPickImageCB
 * @param {number | false | undefined | null} compress
 * @returns
 */
const compressImage = async (pickedImage, compress = 1) => {
    try {
        if (pickedImage) {
            if(compress && compress > 0) {
                // will be compressed
                const pathCompressed = await CompressImage.compress(pickedImage?.path, {
                    maxWidth: 1500,
                    maxHeight: 1000,
                    quality: compress,
                });
                const imageCompressed = await RNFS.stat(pathCompressed);

                //console.log("Image compressed:", imageCompressed);
                if (imageCompressed.size > MAX_IMAGE_SIZE) {
                    Alert.alert("File size Limit Exceeded.", `Please select a file up to ${MAX_IMAGE_SIZE / 1024 / 1024} MB.`);
                } else {
                    return imageCompressed.path;
                }
            } else {
                // not been compressed
                //console.log("Image uncompressed:", pickedImage);
                if (pickedImage.size > MAX_IMAGE_SIZE) {
                    Alert.alert("File size Limit Exceeded.", `Please select a file up to ${MAX_IMAGE_SIZE / 1024 / 1024} MB.`);
                } else {
                    return pickedImage.path;
                }
            }
        }
    } catch (error) {
        console.log("Error compressing an image, file will not compressed, error:", error);
        return pickedImage?.path;
    }
};

/**
 * Pick an image from camera, return the file of the image.
 * @param {function} onPickImageCB
 * @param {boolean} crop
 * @param {number | false | undefined | null} compress
 * @param {int} pickNum 注: 摄像头似乎不需要此参数
 * @returns
 */
const pickImageFromCamera = async (onPickImageCB, crop=true, compress = 1, pickNum = 1) => {
    try {
        const pickMultiple = pickNum === 1 ? false : true;
        const pickedImage = await ImagePicker.openCamera({
            // width: 300,
            // height: 400,
            cropping: crop,
            multiple: pickMultiple,
            maxFiles: pickNum,  // 根据文档, 目前只对ios有效
            mediaType: "photo",
        });

        // openPicker的multiple选项为true时返回一个对象数组, 为false时返回一个对象, 因此统一成数组方便处理
        const compressedImagePaths = await Promise.all(ensureArray(pickedImage).map(async (img) => {
            const compressedImagePath = await compressImage(img, compress);
            onPickImageCB && onPickImageCB(compressedImagePath);
            return compressedImagePath;
        }));

        return compressedImagePaths;
    } catch (error) {
        console.log("Error picking image from camera:", error);
        return [];
    }
};

/**
 * Pick an image from gallery, return the file of the image.
 * @param {function} onPickImageCB
 * @param {boolean} crop
 * @param {number | false | undefined | null} compress
 * @param {int} pickNum
 * @returns
 */
const pickImageFromGallery = async (onPickImageCB, crop=true, compress = 1, pickNum = 1) => {
    try {
        const pickMultiple = pickNum === 1 ? false : true;
        const pickedImage = await ImagePicker.openPicker({
            // width: 300,
            // height: 400,
            cropping: crop,
            multiple: pickMultiple,
            maxFiles: pickNum,  // 根据文档, 目前只对ios有效
            mediaType: "photo",
        });

        // openPicker的multiple选项为true时返回一个对象数组, 为false时返回一个对象, 因此统一成数组方便处理
        const compressedImagePaths = await Promise.all(ensureArray(pickedImage).map(async (img) => {
            const compressedImagePath = await compressImage(img, compress);
            onPickImageCB && onPickImageCB(compressedImagePath);
            return compressedImagePath;
        }));

        return compressedImagePaths;
    } catch (error) {
        console.log("Error picking image from gallery:", error);
        return [];
    }
};

/**
 * Pick an image from camera, return the file of the image.
 * @param {function} onPickImageCB
 * @param {boolean} crop
 * @param {number} compress
 * @param {int} pickNum
 * @returns
 */
export const requestCameraWithPermission = async (onPickImageCB, crop, compress = 1, pickNum = 1) => {
    try {
        const cameraPermission = await check(PERMISSIONS.ANDROID.CAMERA);

        if (cameraPermission === RESULTS.GRANTED) {
            console.log("Camera permission already granted");
            return pickImageFromCamera(onPickImageCB, crop, compress, pickNum);
        }
        const cameraPermissionResult = await request(PERMISSIONS.ANDROID.CAMERA);

        if (cameraPermissionResult === RESULTS.GRANTED) {
            console.log("Camera permission granted");
            return pickImageFromCamera(onPickImageCB, crop, compress, pickNum);
        }
        console.log("Camera permission denied");
    } catch (error) {
        console.log("Error checking/requesting camera permission:", error);
        return null;
    }
};

/**
 *
 * @param {function} onPickImageCB
 * @param {boolean} crop
 * @param {number} compress
 * @param {int} pickNum
 * @returns
 */
export const requestGalleryWithPermission = async (onPickImageCB, crop, compress, pickNum = 1) => {
    try {
        if (Platform.OS === "android") {
            const deviceVersion = getSystemVersion();
            let granted = PermissionsAndroid.RESULTS.DENIED;
            if (deviceVersion >= 13) {
                granted = PermissionsAndroid.RESULTS.GRANTED;
            } else {
                granted = await PermissionsAndroid.request(
                    PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE
                );
            }
            if (granted) {
                return pickImageFromGallery(onPickImageCB, crop, compress, pickNum);
            }
        } else {
        // On iOS, permissions are typically not required for accessing the photo library
            console.log("iOS platform: No specific permissions required for media library");
            return pickImageFromGallery(onPickImageCB, crop, compress, pickNum);
        }
    } catch (error) {
        console.log("Error checking/requesting storage permission:", error);
        return null;
    }
};


const MAX_LOCATION_RETRY_TIMES = REQUEST_LOCATION_MAX_RETRY;
/**
 * Request GPS location permission and return result
 * @param {function} onSuccessCB Callback function to execute when permission is granted
 * @returns {Promise<boolean>} Returns true if permission is granted, false otherwise
 */
export const requestLocationWithPermission = async (onSuccessCB, onErrorCB, options, retryTimes = 0) => {
    Geolocation.requestAuthorization(
        Geolocation.getCurrentPosition(
            pos => {
                log.debug("Get location with default options: %s, retry: %s, pos: %s", JSON.stringify(options), retryTimes, JSON.stringify(pos));
                onSuccessCB && onSuccessCB(pos);
            },
            err => {
                // 考虑使用换成你调用requestLocationWithPermission
                log.info("Retry to get location with option: %s, retry: %s, err: %s", JSON.stringify(options), retryTimes, JSON.stringify(err));
                if (retryTimes < MAX_LOCATION_RETRY_TIMES) {
                    requestLocationWithPermission(onSuccessCB, onErrorCB, { enableHighAccuracy: false, timeout: REQUEST_LOCATION_TIMEOUT, maximumAge: REQUEST_LOCATION_MAXIMUM_AGE }, ++retryTimes);
                } else {
                    log.info("Failed to get location with option: %s, retry: %s, err: %s", JSON.stringify(options), retryTimes, JSON.stringify(err));
                    onErrorCB && onErrorCB(err);
                    //Alert.alert("定位失败", err.message);
                }
            },
            options || {
                enableHighAccuracy: true,                  // 使用高精度GPS
                timeout:    REQUEST_LOCATION_TIMEOUT,      // 超时时间, 单位毫秒
                maximumAge: REQUEST_LOCATION_MAXIMUM_AGE,  // 允许缓存时间内的位置信息, 单位毫秒
            }
        ),
        err => {
            log.info("Error requesting location permission: %s", JSON.stringify(err));
            onErrorCB && onErrorCB(err);
            Alert.alert("获取定位权限失败", "请前往手机设置中开启：\n1. 打开「设置」→「应用管理」\n2. 找到本应用(智能低碳) → 点击「权限」\n3. 启用「位置信息」");
        }
    );
};
