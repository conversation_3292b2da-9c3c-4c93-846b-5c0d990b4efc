import React from "react";
//import { StyleSheet } from "react-native";
import { List, Divider } from "react-native-paper";
import { CardWorkbench } from "../components/CardWorkbench";
import ScreenWrapper from "./ScreenWrapper";


// Divider: https://callstack.github.io/react-native-paper/docs/components/Divider
const KnowledgeBaseScreen = ({ navigation }) => {
    //console.log("navigation ListWorbench", navigation);
    return (
        <ScreenWrapper>
            <Divider bold={true} />

            {<>
                <List.Section title={"标准查询"}>
                    {/* Will not navi to any screens yet */}
                    <CardWorkbench title="水平衡标准查询" desc="" naviTo="" icon="water-check-outline" navigation={navigation} />
                    {/*<CardWorkbench title="电平衡" desc="发起电平衡项目" naviTo="TestScreen2" icon="lightning-bolt-outline" navigation={navigation} />*/}
                </List.Section>
                <Divider bold={true} />
            </>}

            {/*<>
                <List.Section title={"App管理"}>
                    <CardWorkbench title="管理App客户" naviTo="WorkbenchOrgListing" icon="account-plus-outline" navigation={navigation} />
                </List.Section>
                <Divider bold={true} />
            </>*/}


        </ScreenWrapper>
    );
};

/*
const styles = StyleSheet.create({
    container: {
        flex: 1,
        marginTop: 4,
        marginBottom: 4,
        marginHorizontal: 10,
        //marginVertical: 10,
    },
    card: {
        borderWidth: 1,
        marginVertical: 2,
    }
});
*/

export default KnowledgeBaseScreen;
