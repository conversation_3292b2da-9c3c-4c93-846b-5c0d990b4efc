///
/// ReactNativeAudioRecorderPlayer+autolinking.gradle
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON> @ Margelo
///

/// This is a Gradle file that adds all files generated by Nitrogen
/// to the current Gradle project.
///
/// To use it, add this to your build.gradle:
/// ```gradle
/// apply from: '../nitrogen/generated/android/ReactNativeAudioRecorderPlayer+autolinking.gradle'
/// ```

logger.warn("[NitroModules] 🔥 ReactNativeAudioRecorderPlayer is boosted by nitro!")

android {
  sourceSets {
    main {
      java.srcDirs += [
        // Nitrogen files
        "${project.projectDir}/../nitrogen/generated/android/kotlin"
      ]
    }
  }
}
