import { joi<PERSON>esolver } from "@hookform/resolvers/joi";
import "fast-text-encoding";
import <PERSON><PERSON> from "joi";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { Alert, StyleSheet, View } from "react-native";
import { Divider, Snackbar } from "react-native-paper";
import Button from "../../../components/Button";
import { DialogToConfirm } from "../../../components/DialogToConfirm";
import HeaderBar from "../../../components/HeaderBar";
import { LoadingIndicator } from "../../../components/LoadingIndicator";
import { creatMMKVStore, makeStoredValueToFormValue } from "../../../services/local-storage";
import { callOneByOne, ifTruthLet, isNullness, makeDataFeeder } from "../../../utils";
import { checkPermits } from "../../../utils/permits";
import { isServerDataNewer, makeGetClientCversion, radioIdToObject } from "../../../utils/records";
import { validatorBase } from "../../../utils/validatorBase";
import ScreenWrapper from "../../ScreenWrapper";

// 新组件需要重新!!
import { UPD_WATER_BALANCE_RECORDS as pageMainKey } from "../../../config/keysConfig";
import { wbComOpStatStates as selectorStates } from "../../../hooks/selectorStates";
// 本表单无下拉输入
//import { wbWaterUsageUpdateStates as selectorStates } from "../../../hooks/selectorStates";
import { useShallow } from "zustand/shallow";
import {
    makeReqDelWaterBalanceRecords as recordDeleteQueryMaker,
    makeReqGetWaterBalanceRecords as recordSelectQueryMaker,
    makeReqUpdWaterBalanceRecords as recordUpdateQueryMaker,
} from "../../../api/projectRecordsQueries";
import { FormListNodesMapper } from "../../../components/FormListNodesMapper";
import { serviceTypeEnum } from "../../../config/waterBalance";
import { roundNearestTo } from "../../../utils/numeric";
import { onPreSubmitError } from "../../../utils/screens";


const dataFeeder = makeDataFeeder();

/**
 * 单位运营情况统计表, 服务业专用
 * 工业对应表的是ComProdStatsRecordsUpdating.jsx, 工业表: 生产情况统计表, 工业书: 企业近三年生产情况统计
 * 注意, 项目数据表单与工作台管理表单的数据有一个显著的区别:
 * 项目数据不需要像用户编号那样脱敏, 两边传输的数据是直接的, 不需要做{id, name}这种映射, 部分需要映射的可以在客户端本地完成,
 * @param {object} args
 * @param {object} args.navigation
 * @param {object} args.route
 * @returns
 */
const RecordsUpdating = ({ navigation, route }) => {
    console.log("RecordsUpdating pageMeta from nav:", route.params.pageMeta);
    console.log("RecordsUpdating projMeta from nav:", route.params.projMeta);
    const projSubclass = route.params.projMeta.subclass; // 1表, 2书
    //const projIndustry = parseIndustryCode(route.params.projMeta.industry); // 1 -> industry, 2 -> service

    const { queryKwd, id: recordPubid, name: recordName, formType } = route.params.pageMeta; // recordPubid is the alias of id
    const saveButtonIcon = "content-save-all-outline";
    //const formDisabledGlobal = checkPermits() ? false : true; // old marked
    const formEditableGlobal = checkPermits() ? (formType === 0 ? true : false) : false;

    // 使用memoize来优化
    const { setStore, getStore, clearStore, setStoreObject } = useMemo(() => {
        console.log("creatMMKVStore memo:", pageMainKey.store, queryKwd, recordPubid);
        return creatMMKVStore(`${pageMainKey.store}/${queryKwd}/${recordPubid}`);
    }, [queryKwd, recordPubid]);

    const storedValueToFormValue = useRef(makeStoredValueToFormValue(getStore));
    const getClientCversion = useRef(makeGetClientCversion(() => getStore("mainCversion"), () => getStore("subCversion")));

    // 客户端cversion = `${mainCversion}.${subCversion}`
    (getStore("mainCversion") === undefined) ? setStore("mainCversion", 0) : undefined; // 客户端主版本号
    (getStore("subCversion")  === undefined) ? setStore("subCversion",  0) : undefined; // 客户端次版本号, 修改一次自增一次

    // 次版本号, 表示本地更改次数, 用于程序上对子版本号的增加, 用于判断与服务端版本的差异, 同时可用于判断避免重复从MMKV获取数据
    const subCversionRef = useRef(getStore("subCversion"));

    // 新组件需要修改!!
    const bottomBarLabels = { ok: "提交" };                    // 底部按钮显示文字
    const [snackBarMessage, setSnackBarMessage] = useState("更新表单遇到错误"); // 下方错误提示文字内容

    const [
        serviceTypeRadioState,
        testDatePickerState,
        setServiceTypeRadioState,
        setTestDatePickerState,
        resetSelectorStates
    ] = selectorStates(useShallow(state => [
        state.serviceTypeRadio,
        state.testDatePicker,
        state.setServiceTypeRadio,
        state.setTestDatePicker,
        state.resetStates,
    ])); // radio组件状态


    const [confimDialogConfig, setConfirmDialogConfig] = useState({});       // 对话框信息, {title, text, okLabel, onOK}, 但显示对话框的变量在showConfirm

    // 新组件不需改动
    const [screenTitle,      setScreenTitle]      = useState(route.params.pageMeta.name); // 屏幕上方导航栏标题
    const [okButtonLoading,  setOkButtonLoading]  = useState(false);            // 右下方按钮图标是否旋转
    const [okButtonDisabled, setOkButtonDisabled] = useState(!checkPermits());  // 右下方按钮是否禁用
    const [showSnackbar,     setShowSnackbar]     = useState(false);            // 下方是否显示错误通知
    const [showConfirm,      setShowConfirm]      = useState(false);            // 是否显示确认对话框
    const [loadingIndicatorVisible, setLoadingIndicatorVisible] = useState(false); // 是否显示loading
    const delQueryInfo = useRef({ id: recordPubid, name: recordName });         // 用于删除本记录的查询变量

    const serviceTypeStateDataProviderRef    = useRef(serviceTypeEnum);

    const fieldByServiceType = useRef({
        101: [ "serviceObjNum", "buildArea", "testWaterIntake", "aopDays", "avgBuildWater", "rjqsY", ],             // 机关
        102: [ "testWaterIntake", "fwssLhmj", "wateringDays", "dwffSmmj", ],                                        // 机关(附加)-绿化
        201: [ "serviceObjNum", "buildArea", "testWaterIntake", "aopDays", "fwssCzcw", "dwffCwY", ],                // 酒店-住宿业
        301: [ "serviceObjNum", "buildArea", "testWaterIntake", "aopDays", "fwssBzrs", "rjqsY", "avgBuildWater", ], // 学校类
        401: [ "serviceObjNum", "buildArea", "testWaterIntake", "aopDays", "fwssBzcw", "dwffCwD", ],                // 医院类-住院
        402: [ "serviceObjNum", "buildArea", "testWaterIntake", "aopDays", "fwssYymz", "dwffRs", ],                 // 医院类-门诊
        501: [ "serviceObjNum", "buildArea", "testWaterIntake", "aopDays", "rjqsD", ],                              // 写字楼
        601: [ "serviceObjNum", "buildArea", "testWaterIntake", "aopDays", "avgBuildWater", ],                      // 餐饮
        701: [ "serviceObjNum", "buildArea", "testWaterIntake", "aopDays", "avgBuildWater", ],                      // 商场/超市
        801: [ "serviceObjNum", "buildArea", "testWaterIntake", "aopDays", "avgBuildWater", ],                      // 文化娱乐
    });

    const validFieldsArray = fieldByServiceType.current[serviceTypeRadioState.id];
    /**
     * 返回“真值”时对应配置的表单不会显示出来
     * @param {string} fieldName
     * @returns
     */
    const inputFilterIf = (fieldName) =>  {
        return !validFieldsArray?.includes(fieldName);
    };

    // Define form
    // 新组件需要重新定义!!
    const schema = Joi.object({
        serviceType:     validatorBase.waterBalance.commons.intFieldUnrequired,
        aopDays:         validatorBase.waterBalance.commons.intFieldUnrequired,
        wateringDays:    validatorBase.waterBalance.commons.intFieldUnrequired,
        testDate:        validatorBase.waterBalance.commons.dateFieldUnrequired,
        serviceObjNum:   validatorBase.waterBalance.commons.intFieldUnrequired,
        buildArea:       validatorBase.waterBalance.commons.floatFieldUnrequired,
        testWaterIntake: validatorBase.waterBalance.commons.floatFieldUnrequired,
        fwssLhmj:        validatorBase.waterBalance.commons.floatFieldUnrequired,
        fwssCzcw:        validatorBase.waterBalance.commons.floatFieldUnrequired,
        fwssBzrs:        validatorBase.waterBalance.commons.floatFieldUnrequired,
        fwssBzcw:        validatorBase.waterBalance.commons.floatFieldUnrequired,
        fwssYymz:        validatorBase.waterBalance.commons.floatFieldUnrequired,
        rjqsY:           validatorBase.waterBalance.commons.floatFieldUnrequired,
        rjqsD:           validatorBase.waterBalance.commons.floatFieldUnrequired,
        dwffSmmj:        validatorBase.waterBalance.commons.floatFieldUnrequired,
        dwffCwY:         validatorBase.waterBalance.commons.floatFieldUnrequired,
        dwffCwD:         validatorBase.waterBalance.commons.floatFieldUnrequired,
        dwffRs:          validatorBase.waterBalance.commons.floatFieldUnrequired,
        facilityNum:     validatorBase.waterBalance.commons.floatFieldUnrequired,
        recipientNum:    validatorBase.waterBalance.commons.floatFieldUnrequired,
        staffNum:        validatorBase.waterBalance.commons.floatFieldUnrequired,
        avgWaterUsage:   validatorBase.waterBalance.commons.floatFieldUnrequired,
        avgBuildWater:   validatorBase.waterBalance.commons.floatFieldUnrequired,
        unitWaterUsage:  validatorBase.waterBalance.commons.floatFieldUnrequired,
        // other fields
        waterScale:   validatorBase.waterBalance.commons.waterScale,
        others:       validatorBase.waterBalance.commons.textField,
        remarks:      validatorBase.waterBalance.commons.longTextField,
    });

    // 新组件需要重新定义defaultValues!!
    const {
        handleSubmit,
        control,
        resetField,
        reset,
        formState: { errors },
        //setValue,
        //getValues,
    } = useForm({
        resolver: joiResolver(schema),
        //reValidateMode: "onBlur", // https://github.com/orgs/react-hook-form/discussions/10327
        defaultValues: { // 默认值在组件载入时会被更新, 这里的初始值正常情况下没有用处
            serviceType:     "",
            aopDays:         "",
            wateringDays:    "",
            testDate:        "",
            serviceObjNum:   "",
            buildArea:       "",
            testWaterIntake: "",
            fwssLhmj:        "",
            fwssCzcw:        "",
            fwssBzrs:        "",
            fwssBzcw:        "",
            fwssYymz:        "",
            rjqsY:           "",
            rjqsD:           "",
            dwffSmmj:        "",
            dwffCwY:         "",
            dwffCwD:         "",
            dwffRs:          "",
            facilityNum:     "",
            recipientNum:    "",
            staffNum:        "",
            avgWaterUsage:   "",
            avgBuildWater:   "",
            unitWaterUsage:  "",
            // other fields
            waterScale:      "0",
            others: "",
            remarks: "",
        },
    });

    // Query: select record
    const onRecordSelectSuccess = (data) => { // data: {"id": 3, "leader": 0, "name": "DEPT-03", "superior": 0}
        // 注意, 进入onSuccess之前就意味着服务端版本比客户端版本新, 因此要更新客户端主版本号
        console.log("onRecordSelectSuccess:", data);
        if (data.STATUS === 0) {
            setStore("mainCversion", data.DATA.cversion);
            setStore("subCversion", 0);
            //const factor = 10 ** (data.DATA.waterScale || 0);

            const formObjects  = {
                serviceType:     String(data.DATA.serviceType),
                aopDays:         String(data.DATA.aopDays),
                wateringDays:    String(data.DATA.wateringDays),
                testDate:        String(data.DATA.testDate),
                serviceObjNum:   String(data.DATA.serviceObjNum),
                buildArea:       String(data.DATA.buildArea),
                testWaterIntake: String(data.DATA.testWaterIntake),
                fwssLhmj:        String(data.DATA.fwssLhmj),
                fwssCzcw:        String(data.DATA.fwssCzcw),
                fwssBzrs:        String(data.DATA.fwssBzrs),
                fwssBzcw:        String(data.DATA.fwssBzcw),
                fwssYymz:        String(data.DATA.fwssYymz),
                rjqsY:           String(roundNearestTo(data.DATA.rjqsY, 3)),
                rjqsD:           String(roundNearestTo(data.DATA.rjqsD, 3)),
                dwffSmmj:        String(roundNearestTo(data.DATA.dwffSmmj, 3)),
                dwffCwY:         String(roundNearestTo(data.DATA.dwffCwY, 3)),
                dwffCwD:         String(roundNearestTo(data.DATA.dwffCwD, 3)),
                dwffRs:          String(roundNearestTo(data.DATA.dwffRs, 3)),
                facilityNum:     String(data.DATA.facilityNum),
                recipientNum:    String(data.DATA.recipientNum),
                staffNum:        String(data.DATA.staffNum),
                avgWaterUsage:   String(roundNearestTo(data.DATA.avgWaterUsage, 3)),
                avgBuildWater:   String(roundNearestTo(data.DATA.avgBuildWater, 3)),
                unitWaterUsage:  String(data.DATA.unitWaterUsage), // String(data.DATA.unitWaterUsage  / factor),
                // other fields
                waterScale:      String(data.DATA.waterScale || 0),
                others:          String(data.DATA.others),
                remarks:         String(data.DATA.remarks),
            };
            reset(formObjects);           // 重置react-form

            const storeObjects = checkPermits() && {
                ...formObjects,
                serviceType: radioIdToObject(serviceTypeStateDataProviderRef.current, data.DATA.serviceType),

            };
            storeObjects && setStoreObject(storeObjects, false); // 判断用户权限, 把服务端数据写到mmkv存储, 只有可写用户才需要本地存储, 不会首先清空老数据

            // 设置selector菜单状态
            setServiceTypeRadioState(storeObjects.serviceType);
            setTestDatePickerState(storeObjects.testDate);

            // 设置屏幕标题
            (screenTitle !== formObjects.name) && setScreenTitle(formObjects.name);
        } else  {
            console.log("STATUS !== 0", data.STATUS);
            setSnackBarMessage(`表单数据拉取发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        }
    };
    const onRecordSelectError = (err) => {
        console.log("Record selecting error:", err);
    };
    const onRecordSelectSettled = (err) => {
        loadingIndicatorVisible && setLoadingIndicatorVisible(false);
    };
    const recordSelectQuery = recordSelectQueryMaker([queryKwd, "get", recordPubid], onRecordSelectSuccess, onRecordSelectError, onRecordSelectSettled);

    // Query update
    // 新组件需要重新定义setDiagText!!
    const onHandleSubmit = (data) => {
        //const factor = 10 ** Number(waterUnitScaleRef.current);
        setOkButtonDisabled(true);
        setOkButtonLoading(true);
        dataFeeder({
            ...data,
            //name: projSubclass === 1 ? `${utc1900Format(data.beginTime, "YYYY-MM-DD")}` : data.serviceName,
            cversion: getClientCversion.current(),
            //statType: data.year < 365 ? 1 : 0, // 修改后, 凡是判断为当前所在年度都默认为测试期间数据
            //unitWaterUsage: data.unitWaterUsage * factor,
        }); // append client cversion
        recordUpdateQuery.mutate();
    };

    // 新组件不需改动
    const onRecordUpdateSuccess = (data) => {
        console.log("response success, data: ", data);
        if (data.STATUS !== 0) {
            setSnackBarMessage(`表单更新发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        } else {
            // 设置确认框数据
            setConfirmDialogConfig({
                title: "更新表单",
                text: "表单信息更新成功!",
                okLabel: "确定",
                onOK: () => {
                    setShowConfirm(false);
                    recordSelectQuery.mutate();
                }
            });
            setShowConfirm(true);
        }
    };
    const onRecordUpdateError = (error) => {
        setSnackBarMessage(`表单更新发生错误, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const onRecordUpdateSettled = (data, error) => {
        setOkButtonDisabled(false);
        setOkButtonLoading(false);
    };
    const recordUpdateQuery = recordUpdateQueryMaker(dataFeeder, [queryKwd, "upd", recordPubid], onRecordUpdateSuccess, onRecordUpdateError, onRecordUpdateSettled);

    // query delete
    const recordDeleteOnSuccess = () => {
        clearStore();
        navigation.goBack();
    };
    const recordDeleteOnError = (error) => {
        setSnackBarMessage(`表单删除失败, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const recordDeleteQuery = recordDeleteQueryMaker([queryKwd, "del", recordPubid], recordDeleteOnSuccess, recordDeleteOnError);

    const deleteAlert = () => {
        return (
            Alert.alert("删除表单", `删除后不可撤销, 请确认是否删除: ${delQueryInfo.current?.name}?`, [
                { text: "取消" },
                { text: "确定", onPress: () => recordDeleteQuery.mutate() },
            ])
        );
    };

    const clearCache = () => {
        return (
            Alert.alert("清空本地缓存", "1. 用于未知原因导致数据无法提交的情况\r\n2. 未提交的内容将会丢失, 请做好数据截图备份\r\n3. 已提交到服务端的数据不受影响\r\n4. 确认清空请点击“确认”按钮", [
                { text: "取消" },
                { text: "确定", onPress: () => { clearStore(); navigation.goBack(); } },
            ])
        );
    };

    // Hook: onMount fetch data
    useEffect(() => {
        // 优化方案: 根据用户权限进行mutate, 只有可写用户才需要本地存储
        const localCversion = getClientCversion.current();
        const remoteCversion = route.params.pageMeta.cversion;
        console.log("localCversion:", localCversion, "remoteSversion:", remoteCversion, "subCversionRef:", subCversionRef.current);

        const restoreLocalData = () => {
            const formObjects = { // 默认值用户表单的数据显示
                serviceType:     storedValueToFormValue.current("serviceType"),
                aopDays:         storedValueToFormValue.current("aopDays"),
                wateringDays:    storedValueToFormValue.current("wateringDays"),
                testDate:        storedValueToFormValue.current("testDate"),
                serviceObjNum:   storedValueToFormValue.current("serviceObjNum"),
                buildArea:       storedValueToFormValue.current("buildArea"),
                testWaterIntake: storedValueToFormValue.current("testWaterIntake"),
                fwssLhmj:        storedValueToFormValue.current("fwssLhmj"),
                fwssCzcw:        storedValueToFormValue.current("fwssCzcw"),
                fwssBzrs:        storedValueToFormValue.current("fwssBzrs"),
                fwssBzcw:        storedValueToFormValue.current("fwssBzcw"),
                fwssYymz:        storedValueToFormValue.current("fwssYymz"),
                rjqsY:           storedValueToFormValue.current("rjqsY"),
                rjqsD:           storedValueToFormValue.current("rjqsD"),
                dwffSmmj:        storedValueToFormValue.current("dwffSmmj"),
                dwffCwY:         storedValueToFormValue.current("dwffCwY"),
                dwffCwD:         storedValueToFormValue.current("dwffCwD"),
                dwffRs:          storedValueToFormValue.current("dwffRs"),
                facilityNum:     storedValueToFormValue.current("facilityNum"),
                recipientNum:    storedValueToFormValue.current("recipientNum"),
                staffNum:        storedValueToFormValue.current("staffNum"),
                avgWaterUsage:   storedValueToFormValue.current("avgWaterUsage"),
                avgBuildWater:   storedValueToFormValue.current("avgBuildWater"),
                unitWaterUsage:  storedValueToFormValue.current("unitWaterUsage"),
                // other fields
                waterScale:      storedValueToFormValue.current("waterScale", "0"),
                others:          storedValueToFormValue.current("others"),
                remarks:         storedValueToFormValue.current("remarks"),
            };
            reset(formObjects); // 重置react-form数据

            // 设置selector数据
            setServiceTypeRadioState(ifTruthLet(getStore("serviceType"), isNullness, () => {return {id: 0, name: ""};}, value => value));
            setTestDatePickerState(formObjects.testDate);
        };

        if (formType === 1) {
            recordSelectQuery.mutate();
        } else if (localCversion === 0 && remoteCversion > 0) {
            console.log("Local cversion === 0, serve may have something nontrivial initialized, query mutate!");
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        } else if (isServerDataNewer(localCversion, remoteCversion) && subCversionRef.current > 0) { // 服务端有更新数据, 但本地有修改
            console.log("Client cversion fell behind the server cversion, someone might modified the database, choose to use the data version!");
            Alert.alert("注意", "由于其他人向服务器提交了数据, 服务端数据比本地数据新, 请选择使用哪份数据?", [
                { text: "使用本地数据", onPress: () => { setStore("mainCversion", remoteCversion); restoreLocalData(); } },
                { text: "使用服务端数据", onPress: () => { setLoadingIndicatorVisible(true); recordSelectQuery.mutate(); } },
            ]);
        } else if (isServerDataNewer(localCversion, remoteCversion)) { // localCversion==0时要mutate是因为服务端可能在创建记录时生成了必要数据
            console.log("Server cversion may be newer, query mutate:", localCversion, remoteCversion);
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        }
        else {
            console.log("Local cversion >= server cversion, query will not mutate!");
            restoreLocalData();
        }
    }, []);


    // 统一配置提示文字和
    /*
    const fieldTips = {
        prodCapacity: { toolTip: "单位指具体产品的基础单位, 例如吨, 件等。", placeholder: "" },
        actualOutput: { toolTip: "单位指具体产品的基础单位, 例如吨, 件等。", placeholder: "" },
        wipup:        { toolTip: "单位指具体产品的基础单位, 例如吨, 件等。", placeholder: "" },
    };*/
    const FieldsConfig_1_book = [
        {
            inputs: [
                { name: "serviceType",     label: "服务类型",         unit: "",         type: "RADIO", editable: true, placeholder: "", filterIf: null, selectorState: serviceTypeRadioState,  setSelectorState: setServiceTypeRadioState, dataProvider: serviceTypeEnum, props: {multiline: true}, },
                { name: "aopDays",         label: "年运营天数",       unit: "天",        type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                { name: "wateringDays",    label: "浇水频次",         unit: "",         type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                { name: "serviceObjNum",   label: "服务对象的数量",    unit: "人",        type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                { name: "buildArea",       label: "建筑面积",         unit: "m²",        type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                { name: "testWaterIntake", label: "测试期间取水量",    unit: "m³",        type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                { name: "fwssLhmj",        label: "绿化面积",         unit: "m²",        type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                { name: "fwssCzcw",        label: "出租床位",         unit: "床",        type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                { name: "fwssBzrs",        label: "标准人数",         unit: "人",        type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                { name: "fwssBzcw",        label: "编制床位",         unit: "床",        type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                { name: "fwssYymz",        label: "医院门诊人数",      unit: "",         type: "PLAIN", editable: true, placeholder: "", filterIf: inputFilterIf, },
                { name: "rjqsY",           label: "年度人均取水量",    unit: "m³/人·a",   type: "PLAIN", editable: false, placeholder: "提交后自动计算", filterIf: inputFilterIf, },
                { name: "rjqsD",           label: "每天人均取水量",    unit: "L/人·d",    type: "PLAIN", editable: false, placeholder: "提交后自动计算", filterIf: inputFilterIf, },
                { name: "avgBuildWater",   label: "单位建筑面积用水量", unit: "m³/m²·a",  type: "PLAIN", editable: false, placeholder: "提交后自动计算", filterIf: inputFilterIf, },
                { name: "dwffSmmj",        label: "单位水面面积取水量", unit: "m³/m²·a",  type: "PLAIN", editable: false, placeholder: "提交后自动计算", filterIf: inputFilterIf, },
                { name: "dwffCwY",         label: "单位床位取水量",    unit: "m³/床·a",   type: "PLAIN", editable: false, placeholder: "提交后自动计算", filterIf: inputFilterIf, },
                { name: "dwffCwD",         label: "单位床位取水量",    unit: "L/床·d",    type: "PLAIN", editable: false, placeholder: "提交后自动计算", filterIf: inputFilterIf, },
                { name: "dwffRs",          label: "单位人数取水量次",   unit: "L/(人·次)", type: "PLAIN", editable: false, placeholder: "提交后自动计算", filterIf: inputFilterIf, },

            ]
        },
    ];
    const FieldsConfig_1_table = [
        {
            inputs: [
                { name: "testDate",        label: "测试时段",        unit: "天",     type: "DATE", editable: true,  selectorState: testDatePickerState, setSelectorState: setTestDatePickerState, },
                { name: "buildArea",       label: "建筑面积",        unit: "m²",    type: "PLAIN", editable: true,  placeholder: "", },
                { name: "staffNum",        label: "人数",           unit: "人",     type: "PLAIN", editable: true,  placeholder: "", },
                { name: "testWaterIntake", label: "测试期间取水量",   unit: "m³",     type: "PLAIN", editable: true,  placeholder: "", },
                { name: "unitWaterUsage",  label: "单位用水量",      unit: "m³",     type: "PLAIN", editable: true,  placeholder: "", },
                { name: "avgWaterUsage",   label: "人均用水量",      unit: "L/人·d",  type: "PLAIN", editable: false, placeholder: "提交后自动计算", },
                { name: "avgBuildWater",   label: "单位建筑面积用水", unit: "m³/m²·a", type: "PLAIN", editable: false, placeholder: "提交后自动计算", },

            ]
        },
    ];

    const FieldsConfig_2_book = [];
    const FieldsConfig_2_table = [];

    const FieldsConfig_3_book = [
        // 其它部分
        {
            inputs: [
                //{ name: "others",  label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "", },
                //{ name: "remarks", label: "备注", unit: "", type: "PLAIN", editable: true, placeholder: "", },
            ]
        },
    ];
    const FieldsConfig_3_table = [
        // 其它部分
        {
            inputs: [
                //{ name: "others",  label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "", },
                //{ name: "remarks", label: "备注", unit: "", type: "PLAIN", editable: true, placeholder: "", },
            ]
        },
    ];

    //const FieldsConfig = [...FieldsConfig_1, ...FieldsConfig_2, ...FieldsConfig_3];
    let FieldsConfig = [];
    switch(projSubclass) {
        case 1: // 表
            FieldsConfig = [...FieldsConfig_1_table, ...FieldsConfig_2_table, ...FieldsConfig_3_table];
            break;
        case 2: // 书
            FieldsConfig = [...FieldsConfig_1_book, ...FieldsConfig_2_book, ...FieldsConfig_3_book];
            break;
        default:
            console.warn("Unknown subclass:", projSubclass);
            FieldsConfig = [];
            break;
    }

    return (
        <View style={{ flex: 1 }}>

            <HeaderBar
                //title={screenTitle}
                navigation={navigation}
                goBackCallback={() => {
                    callOneByOne(
                        //() => { _mainCversionGetter=undefined; _subCversionGetter=undefined;},
                        resetSelectorStates,
                    );
                    /*callOneByOne(clearStore, resetSelectorStates)*/  // 返回前清空mmkv存储因为临时数据不再需要, 重置状态使得页面切换后不会使用前面的数据
                }}
                menuItemArray={[{ title: "删除表单", action: deleteAlert }, { title: "清空缓存", action: clearCache }]}
            />

            <ScreenWrapper contentContainerStyle={styles.container}>

                <View style={styles.formEntry}>

                    <FormListNodesMapper
                        fieldsConfig={FieldsConfig}
                        subCversionRef={subCversionRef}
                        control={control}
                        setStore={setStore}
                        resetField={resetField}
                        errors={errors}
                        setSnackBarMessage={setSnackBarMessage}
                        setShowSnackbar={setShowSnackbar}
                        editable={formEditableGlobal}
                        rowStyle={styles.rowContainer}
                    />

                    <Divider bold={true}/>

                </View>

                <View style={styles.button}>
                    <Button mode="contained"
                        disabled={okButtonDisabled}
                        loading={okButtonLoading}
                        icon={saveButtonIcon}
                        onPress={handleSubmit(onHandleSubmit, (formErr) => onPreSubmitError(formErr, getStore, "ComProdStatsRecordsUpdating"))}
                    >{bottomBarLabels.ok}</Button>
                </View>
            </ScreenWrapper>

            <LoadingIndicator
                title={""}
                message={"加载中....."}
                visible={loadingIndicatorVisible}
                onClose={()=>setLoadingIndicatorVisible(false)}
            />

            <Snackbar
                style={styles.snackBar}
                visible={showSnackbar}
                onDismiss={() => setShowSnackbar(false)}
                onIconPress={() => setShowSnackbar(false)}
                duration={Snackbar.DURATION_LONG}
            >
                {snackBarMessage}
            </Snackbar>

            <DialogToConfirm
                visible={showConfirm}
                title={confimDialogConfig.title ? confimDialogConfig.title : ""}
                text={confimDialogConfig.text ? confimDialogConfig.text : ""}
                onOK={confimDialogConfig.onOK ? confimDialogConfig.onOK : () => setShowConfirm(false)}
                okBtnLabel={confimDialogConfig.okLabel ? confimDialogConfig.okLabel : "确认"}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    rowContainer:{
        firstColumn: {
            flex: 5,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 6,
        // /borderWidth: 1,
        // height: 40,
        },
    },
    rowContainerLongText: {
        firstColumn: {
            flex: 1,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 4,
            // /borderWidth: 1,
            // height: 40,
        },
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        width: "80%",
        alignSelf: "center",
    },
    formEntry: {
        margin: 8,
    },
    snackBar: {
        flexDirection: "column",
    },
});

export default RecordsUpdating;
