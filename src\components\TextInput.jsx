import PropTypes from "prop-types";
import React from "react";
import { StyleSheet, Text, View } from "react-native";
import { TextInput as PaperTextInput, useTheme } from "react-native-paper";

/**
 * @param {Object} arg
 * @param {string} arg.errorText
 * @param {string} arg.description
 */
const TextInput = ({ errorText, description, ...props }) => {
    const theme = useTheme();
    const styles = StyleSheet.create({
        container: {
            width: "100%",
            marginVertical: 8,
        },
        input: {
            backgroundColor: theme.colors.surface,
        },
        description: {
            fontSize: 13,
            color: theme.colors.secondary,
            paddingTop: 8,
        },
        error: {
            fontSize: 13,
            color: theme.colors.error,
            paddingTop: 8,
        },
    });

    return (
        <View style={styles.container}>
            <PaperTextInput
                style={styles.input}
                selectionColor={theme.colors.primary}
                underlineColor="transparent"
                mode={props.mode || "outlined"}
                right={props.onClearText ?
                    <PaperTextInput.Icon
                        icon="close"
                        color={isTextInputFocused => isTextInputFocused ? (errorText ? theme.colors.error : theme.colors.primary) : "transparent"}
                        onPress={() => props.onClearText()}
                    />
                    : undefined}
                {...props}
            />
            {description && !errorText ? (
                <Text style={styles.description}>{description}</Text>
            ) : null}
            {errorText ? <Text style={styles.error}>{errorText}</Text> : null}
        </View>
    );
};

TextInput.propTypes = {
    errorText: PropTypes.string,
    description: PropTypes.string,
};

export default TextInput;
