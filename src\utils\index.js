import cloneDeep from "lodash/cloneDeep";
import { BASE_URL_NO_SLASH } from "../config";
import { RangeRegex } from "./regex";

/**
 * Returns the first argument it receives.
 * @param {*} value Any value.
 * @returns {*} Returns `value`.
 * @example
 * const object = { 'a': 1 };
 * console.log(identity(object) === object); // => true
 */
export const identity = value => value;


/**
 * When `value` is true, return the result of calling `trueCall` with `value`, or return the result of calling `falseCall`.
 * @param {any} value
 * @param {(value) => {}} trueCall Call if value is true and return the result
 * @param {(value)=>{}} falseCall Call if value is false and return the result
 */
export const ifLet = (value, trueCall = identity, falseCall = identity) => value ? trueCall(value) : falseCall(value);

/**
 * When `truthTest(value)` is true, return the result of calling `trueCall` with `value`, or return the result of calling `falseCall`.
 * @param {any} value
 * * @param {(value) => {}} truthTest Test if value is true in the context
 * @param {(value) => {}} trueCall Call if value is true and return the result
 * @param {(value)=>{}} falseCall Call if value is false and return the result
 */
export const ifTruthLet = (value, truthTest = identity, trueCall = identity, falseCall = identity) => truthTest(value) ? trueCall(value) : falseCall(value);


/**
 * When `value` is true, return the result of calling `trueCall` with `value`, or return `defaultRet`.
 * @param {any} value
 * @param {(value) => {}} trueCall Call if value is true and return the result.
 * @param {any} defaultRet Return if `value` is false.
 */
export const whenLet = (value, trueCall = identity, defaultRet = value) => value ? trueCall(value) : defaultRet;


/**
 * Return a FormData object which is filled with an object.
 * 注意, 对于简单数组值, formData会转换成逗号分隔的字符串, 在服务端解析数据需要注意
 * 对于其它复合类型, 例如对象, 复合数组等, 在调用前需要先调用JSON.stringify转换成字符串
 * 例如: formData.append("x", [1,2,3]) -> { [Symbol(state)]: [ { name: 'x', value: '1,2,3' } ] }
 * @param {object} obj
 */
export const makeFormData = (obj) => {
    const formData = new FormData();
    for (const [key, value] of Object.entries(obj)) {
        formData.append(key, value);
    }
    return formData;
};


/**
 * Return a closure function,
 * if this function is called with a non-undefined arg, this arg will be cloned and stored in it's closure var,
 * if this function is called with none args (arg undefined), the value stored in the closure var will be returned.
 * @returns {Function(any)}
 */
export const makeDataFeeder = () => {
    const stack = [];
    return (data) => data === undefined ? stack[0] : stack[0] = cloneDeep(data);
};


/**
 * Append a default value to dataArray.
 * The `dataArray` has such shape as [{id:integer, name:string, ...}*].
 * The original `dataArray` will not deep cloned first so that this function is pure.
 * The result will be sorted according to the value of `keyName`, from least to most.
 * @param {Object[]} dataArray
 * @param {Object} defaults
 * @param {string} keyName
 * @param {any} keyValue
 * @returns
 */
export const radioItemMakeSureDefault = (dataArray, defaults = { id: 0, name: "无" }, keyName = "id") => {
    const arr = cloneDeep(dataArray);
    const keyValue = defaults[keyName];
    if (arr.find((value) => value[keyName] === keyValue)) {
        return arr.sort((a, b) => a[keyName] - b[keyName]);
    } else {
        arr[arr.length] = defaults;
        return arr.sort((a, b) => a[keyName] - b[keyName]);
    }
};

const plusp = (a, b) => a + b;
[plusp, plusp, plusp].reduce((accu, curr)=> curr(accu), 1);

/**
 * Convert nested function calls into a linear flow of function calls.
 * Idea from clojure's threading macro `->`, https://clojure.org/guides/threading_macros
 * `funArray` is an array of unary functions,
 * from the left to the right, each function accepts the value returned by the previous function,
 * and returns a value which can be accepted by the next function.
 * `obj` is the initial value which is passed to the first `funArray`.
 * Return the result of the last function in `funArray`.
 * eg.
 * const plusp = (a) => a + 1;
 * [plusp, plusp, plusp].reduce((accu, curr)=> curr(accu), 1);
 *
 * @param {any} obj
 * @param {Function[any]} funArray
 * @returns
 */
export const callThreads = (obj, funArray) => {
    return funArray.reduce((accu, curr) => curr(accu), obj);
};

/**
 * Call a list of nullary functions one by one, return an array of the results of those functions.
 * @param  {...function()} fns
 * @returns
 */
export const callOneByOne = (...fns) => {
    return fns.map(fn => fn());
};

/**
 * Look up the second arg, whose value under the key `lookUpKey` matches the value of the first arg under the key `reversedKey`,
 * If found, the value of the first arg under the key `reversedKey`, will be replaced with the value of the looked up result under the key `getValKey`.
 * If not found, return the original `reversedObj`.
 * `reversedObj` will be deep copied so that this function is pure.
 * The `dataArray` has such shape as [{id:integer, name:string, ...}*].
 * eg.
 * findAndReplace({ "name": "ab1112", "superior": "DEPT-02" }, [{ id: 123, name: "DEPT-02" }], "superior", "name", "id");
 * -> { name: 'ab1112', superior: 123 }
 * @param {Object[]} dataArray
 * @param {Object} defaults
 * @param {string} keyName
 * @param {any} keyValue
 * @returns
 */
export const findAndReplace = (reversedObj, lookUpObjArray, reversedKey, lookUpKey = "name", getValKey = "id") => {
    const dataCopy = cloneDeep(reversedObj);
    const targetObj = lookUpObjArray.find(value => value[lookUpKey] === dataCopy[reversedKey]);
    targetObj ? dataCopy[reversedKey] = targetObj[getValKey] : undefined;
    return dataCopy;
};


/**
 * eg.
 * const replacer = makeReplacer("superior", "name", "id");
 * replacer.setLookup = [{ id: 123, name: "DEPT-02" }];
 * replacer.replace({ "name": "ab1112", "superior": "DEPT-02" });
 * @param {string} reversedKey
 * @param {string} lookUpKey
 * @param {string} getValKey
 * @returns
 */
export const makeReplacer = (reversedKey, lookUpKey = "name", getValKey = "id") => {
    let lookUpObjArray = [];
    return {
        getter: () => lookUpObjArray,
        replace: (reversedObj) => findAndReplace(reversedObj, lookUpObjArray, reversedKey, lookUpKey , getValKey),
        setLookup: (newLookUpObjArray) => {
            lookUpObjArray = cloneDeep(newLookUpObjArray);
        },
    };
};


/**
 * Make sure the function will be called only once.
 * @param {function} fn
 * @returns
 */
export const onceOnly = (fn, enabled=true) => {
    let isCalled = !enabled;
    const reset = () => isCalled = false;
    const call = () => !isCalled ? fn() : null;
    return {call, reset};
};


/**
 * mergeQueryPath(1) => '/1'
 * mergeQueryPath([1]) => '/1'
 * mergeQueryPath(["name", "id", 1]) => '/name/id/1'
 * @param {Array | String | Number} varOrVars 查询变量或者一组查询量组合成一个路径
 * @returns "/xxx/yy"格式, 以斜杠开头
 */
export const mergeQueryPath = (varOrVars) => {
    if (Array.isArray(varOrVars)) {
        return varOrVars.map(item => `/${item}`).reduce((accu, cur) => `${accu}${cur}`);
    } else if (varOrVars) {
        return `/${varOrVars}`;
    } else {
        return "";
    }
};

/**
 * eg. makeUrl("api/proj/wb", [api, method, pubid]);
 * @param {string} apiPath
 * @param {string | number | Array} varOrVars
 * @returns
 */
export const makeUrl = (apiPath, varOrVars) => {
    return `${apiPath}${mergeQueryPath(varOrVars)}`;
};

/**
 * Parse the file name from the uri.
 * eg. parseFileName("http://localhost:8080/api/proj/wb/upfile/2022-01-01/2022-01-01-01.jpg?token=123"); // => "2022-01-01-01.jpg"
 * @param {string} uri
 * @returns
 */
export const parseFileName = uri =>  uri.split("/").pop().split("?")[0];

/**
 * eg. makeFileUploadUrl("api/proj/wb", [queryKwd, "upfile", field, recordPubid]);
 * @param {string} apiPath
 * @param {string | number | Array} varOrVars
 * @returns
 */
export const makeFileUploadUrl = (apiPath, varOrVars) => {
    return `${apiPath}${mergeQueryPath(varOrVars)}`;
};

/**
 * Ensure a tail "/" at the end of the path.
 * @param {string} path
 * @returns
 */

export const ensureTailSlash = (path) => path.endsWith("/") ? path : `${path}/`;
/**
 * Ensure no tail "/" at the end of the path.
 * @param {string} path
 * @returns
 */
export const ensureNoTailSlash = (path) => path.endsWith("/") ? path.slice(0, -1) : path;

/**
 * Ensure a head "/" at the start of the path.
 * @param {string} path
 * @returns
 */
export const ensureHeadSlash = (path) => path.startsWith("/") ? path : `/${path}`;

/**
 * Ensure no head "/" at the start of the path.
 * @param {string} path
 * @returns
 */
export const ensureNoHeadSlash = (path) => path.startsWith("/") ? path.slice(1) : path;

/**
 * Assemble a full url with `queryPath` and `baseUrl`.
 * @param {string} queryPath  "/xxx/yy.ext"格式, 以斜杠开头, 表示服务端传过来的文件路径
 * @param {boolean} withQuery 后面是否附带随机参数, 用于消除图像缓存
 * @param {string} baseUrl 在服务器的根地址
 * @returns
 */
export const assembleUrl = (queryPath, withQuery = true, baseUrl = BASE_URL_NO_SLASH) => {
    return `${baseUrl}${ensureHeadSlash(queryPath)}${withQuery ? `?t=${new Date().getTime()}` : ""}`;
};

/**
 * Return the data type of `val`.
 * @param {*} val
 * @returns
 */
export const ofType = (val) => {
    return typeof(val);
};

/**
 * Check if `val` is of type function.
 * @param {*} val
 * @returns
 */
export const isFunction = (val) => {
    return ofType(val) === "function";
};

/**
 * Check if `val` is of type string.
 * @param {*} val
 * @returns
 */
export const isString = (val) => {
    return ofType(val) === "string";
};

/**
 * Check if `val` is of type number.
 * @param {*} val
 * @returns
 */
export const isNumber = (val) => {
    return ofType(val) === "number";
};

/**
 * Check if `val` is of type number.
 * @param {*} val
 * @returns
 */
export const isNumberSpecifier = (val) => {
    return !isNaN(Number(val));
};

/**
 * Check if `val` is of type integer.
 * @param {*} val
 * @returns
 */
export const isInteger = (val) => {
    return Number.isInteger(val);
};

/**
 * Check if `val` is of type Array.
 * @param {*} val
 * @returns
 */
export const isArray = (val) => {
    return Array.isArray(val);
};

/**
 * 检查一个数组是否为空
 *
 * @param {any} val - 待检查的值
 * @returns {boolean} - 如果val是一个空数组，则返回true；否则返回false
 */
export const isEmptyArray = (val) => {
    return isArray(val) && val.length === 0;
};


/**
 * Check if `val` is an Error object.
 * https://stackoverflow.com/questions/30469261/checking-for-typeof-error-in-js
 * @param {any} val
 * @returns
 */
export const isError = (val) => {
    return val &&
           val.stack &&
           val.message &&
           typeof val.stack === "string" &&
           typeof val.message === "string";
};

/**
 * If val is an array, return val, otherwise return [val].
 * @param {*} val
 * @returns
 */
export const ensureArray = (val) => {
    return isArray(val) ? val : [val];
};

/**
 * Check if `val` is of type Object.
 * @param {*} val
 * @returns
 */
export const isObject = (val) => {
    return ofType(val) === "object";
};

/**
 * Check if `obj` is empty, return true if obj is {}.
 * 注意: 使用isEmptyObject, 对于非object的类型会返回false, 对于空数字, 会返回true
 * @param {object} objectName
 * @returns boolean
 */
export const isEmptyObject = (obj) => {
    return obj && Object.keys(obj).length === 0 && obj.constructor === Object;
};

/**
 * Check if `obj` is an object but not empty, Return true if obj is not {}.
 * 注意: 使用isNonEmptyObject, 对于非object的类型会返回false, 很容易造成语义理解的歧义, 不建议使用, 废除!!
export const isNonEmptyObject = (obj) => {
    return isObject(obj) && (!isEmptyObject(obj));
};
*/

/**
 * 检查给定值是否为空
 *
 * 此函数用于判断一个给定的值是否为空它不仅仅检查值是否为null，
 * 还会检查值是否为空数组、空对象或者NaN对于这些情况，函数都会返回true，
 * 表示值在某种意义上是“空”的这对于数据验证和默认值设置等场景非常有用
 *
 * @param {any} val - 待检查的值可以是任何类型
 * @returns {boolean} - 如果给定的值被认为是“空”，则返回true；否则返回false
 */
export const isNullness = (val) => {
    return !val || isEmptyArray(val) || isEmptyObject(val) || (isNumber(val) && isNaN(val));
};

/**
 * 检查给定值是否为非零空值
 *
 * @param {any} val - 待检查的值可以是任何类型
 * @returns {boolean} - 如果给定的值被认为是空值但非零，则返回true；否则返回false
 */
export const isNonZeroNull = (val) => {
    return isNullness(val) && val !== 0;
};

/**
 * Check if `rangeStr` is a mathatical range.
 * Valid ranges: '(-inf, inf)', '[-inf, +inf]', '(-1, 2]', '[1.1, inf)', '(.1, 1.)', '(0,0)', ...
 * @param {string} rangeStr
 * @returns boolean
 */
export const isRange = (rangeStr) => {
    const normalRange = rangeStr.replace(/\s/g, "");
    const match = RangeRegex.exec(normalRange);
    if (match) {
        const [_, min, max] = match;
        if ((min.toUpperCase() === "-INF") || (max.toUpperCase() === "INF") || (max.toUpperCase() === "+INF")) {
            return true;
        } else {
            return Number(min) <= Number(max);
        }
    } else {
        return false;
    }
};


/**
 * 以数组形式返回区间最小最大值
 * @param {string} rangeStr
 * @returns
 */
export const parseRangeVals = (rangeStr) => {
    if(rangeStr === "empty") {
        return ["", ""];
    } else if (isRange(rangeStr)) {
        const normalRange = rangeStr.replace(/\s/g, "");
        const match = RangeRegex.exec(normalRange);
        const [_, min, max] = match;
        return [min, max];
    } else {
        return undefined;
    }
};


/**
 * Return a generator function, this function returns `start` for the first call, and increaces `step` for each successive call.
 * @param {integer} start The first return integer of this generator
 * @param {integer} step  The increacement of this generator
 * @returns {()=>integer}
 */
export const makeCounter = (start=0, step=1) => {
    const counter = {id: start - step};
    return () => {
        return counter.id += step;
    };
};

/**
 * Is version string ver1 older than ver2.
 * versionOlderThan(currVer, latestVer) will return true if currVer is older than latestVer.
 * @param {string} ver1 version string has such format as "d.d.d".
 * @param {string} ver2 version string has such format as "d.d.d".
 */
export const versionOlderThan = (ver1, ver2) => {
    const verNums1 = ver1.split(".");
    const verNums2 = ver2.split(".");
    if(verNums1.length === verNums2.length){
        for (let i = 0; i < verNums1.length; i++) {
            if(parseInt(verNums1[i]) < parseInt(verNums2[i])) {
                return true;
            }
            if(parseInt(verNums1[i]) > parseInt(verNums2[i])) {
                return false;
            }
        }
    } else {
        return false;
    }
    return false;
};

/**
 * Parse industry code to industry type.
 * @param {number} code industry code, 1 for industry, 2 for service, others for other.
 * @param {number} projClass 项目类型， 1水平衡, 11零碳诊断
 * @returns industry type, "industry", "service" or "other".
 */
export const parseIndustryCode = (code, projClass = 1) => {
    if(projClass === 1) {
        if(code === 1) {
            return "industry";
        } else if (code === 2) {
            return "service";
        } else {
            return "other";
        }
    } else if (projClass === 11) {
        // 目前只有公共机构一种
        return "publicInst";
    } else {
        return "other";
    }
};
