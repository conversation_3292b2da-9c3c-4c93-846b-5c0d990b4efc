import React from "react";
import ListingTemplate from "./ListingTemplate";
import { makeRequestListingClients as queryClient } from "../../api/listingQueries";
import { allClientsState as pageDataState } from "../../hooks/globalStates";


const ClientsListing = ({ navigation }) => {
    return (
        <ListingTemplate
            bottomBarLabel={"添加客户"}
            saveButtonIcon={"account-plus-outline"}
            emptyScreenText={"请点击下方按钮添加客户"}
            snackBarDefaultText={"添加角色遇到客户"}
            queryClient={queryClient}
            pageDataState={pageDataState}
            listItemNavigateTo={"WorkbenchClientsUpdating"}
            addingButtonNavigateTo={"WorkbenchClientsInserting"}
            navigation={navigation}
        />
    );
};

export default ClientsListing;
