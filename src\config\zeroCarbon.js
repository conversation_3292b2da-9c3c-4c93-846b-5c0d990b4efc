import { cities } from "./common";

/**
 * Find in an object array, where `id` matches the id of some object in the array, return that object if the match succeeds.
 * @param {int} id
 * @param {[{id: int, name: string}]} enumArray
 * @returns
 */
export const findEnumItem = (id, enumArray) => {
    return enumArray.find(item => id === item.id);
};

const genYearEnum = (years, thisYearTips = "") => {
    const currDate = new Date();
    const currYear = currDate.getFullYear();
    let yearEnum = [];

    for (let i = 0; i < years; i++) {
        yearEnum.push({ id: currYear - i, name: `${currYear - i}` });
    }

    return yearEnum;
};

const hebeiPrivinceId = 13;
export const hebeiRegionEnum = cities[hebeiPrivinceId];

export const industryTypeEnum = [
    {id: 1, name: "党政机关"},
    {id: 2, name: "教育类"},
    {id: 3, name: "卫生医疗类"},
    {id: 4, name: "场馆类"},
    {id: 0, name: "其他机构"},
];

export const eQuotaTypeEnum = [
    {id: 1, name: "省直"},
    {id: 2, name: "市直"},
    {id: 3, name: "区县及以下"},
    {id: 11, name: "高等教育"},
    {id: 12, name: "中等教育"},
    {id: 13, name: "初等教育"},
    {id: 14, name: "学前教育"},
    {id: 10, name: "其他教育"},
    {id: 21, name: "综合医院"},
    {id: 22, name: "专科医院"},
    {id: 23, name: "基层医疗"},
    {id: 20, name: "其他医疗"},
    {id: 31, name: "场馆类"},
    {id: 0, name: "其他机构"},
];

export const startYearEnum = genYearEnum(10);

export const statisticsYearEnum = genYearEnum(10);

export const durationEnum = [
    {id: 1, name: "1"},
    {id: 2, name: "2"},
    {id: 3, name: "3"},
    {id: 4, name: "4"},
    {id: 5, name: "5"},
];

// 11电力, 21水, 31天然气, 32液化石油气, 41采暖, 51汽油, 52柴油
// 基本情况表
export const energyListEnum = [
    {id: 11, name: "电力"},
    {id: 21, name: "水"},
    {id: 31, name: "天然气"},
    {id: 32, name: "液化石油气"},
    {id: 41, name: "采暖"},
    {id: 51, name: "汽油"},
    {id: 52, name: "柴油"},
];


// 能源账单表, 相比energyListEnum少了采暖
export const energyBillsEnergyListEnum = [
    {id: 11, name: "电力"},
    {id: 21, name: "水"},
    {id: 31, name: "天然气"},
    {id: 32, name: "液化石油气"},
    //{id: 41, name: "采暖"},
    {id: 51, name: "汽油"},
    {id: 52, name: "柴油"},
];

// 主要用于能源账单表
export const energyInfo = {
    11: { energyName: "电力",     energyLabel: "月总耗电量",  energyUnit: "kW·h", costType: 0, costLabel: "月总电费", costUnit: "元", billType: 1, billName: "建筑能源账单" },
    21: { energyName: "水",       energyLabel: "月总水量",   energyUnit: "m³",   costType: 0, costLabel: "月总水费", costUnit: "元", billType: 1, billName: "建筑能源账单" },
    31: { energyName: "天然气",    energyLabel: "月总用量",   energyUnit: "m³",   costType: 0, costLabel: "月总费用", costUnit: "元", billType: 1, billName: "建筑能源账单" },
    32: { energyName: "液化石油气", energyLabel: "月总用量",   energyUnit: "kg",   costType: 0, costLabel: "月总费用", costUnit: "元", billType: 1, billName: "建筑能源账单" },
    41: { energyName: "采暖",      energyLabel: "",         energyUnit: "m²",   costType: 0, costLabel: "采暖费用", costUnit: "元", billType: 3, billName: "采暖费用信息" },
    51: { energyName: "汽油",      energyLabel: "汽油消耗量", energyUnit: "L",    costType: 0, costLabel: "费用",    costUnit: "元", billType: 2, billName: "交通能耗账单" },
    52: { energyName: "柴油",      energyLabel: "柴油消耗量", energyUnit: "L",    costType: 0, costLabel: "费用",    costUnit: "元", billType: 2, billName: "交通能耗账单" },
};

/**
* 枚举数值规范:
* 0: app用户尚未填写.
* 1: 反应被考察机构某项目无效, 无此项等含义.
* 2~9: 预留.
* 10: 专指枚举的其它项
* 11, 12…表示逻辑中定义的非其它类型的枚举.
* 对于布尔类型, 11表示是, 12表示否.
*/

// 用能系统基本情况字段枚举
// 形式, 11集中采暖, 12自主采暖
export const hsTypeEnum = [
    {id: 11, name: "集中采暖"},
    {id: 12, name: "自主采暖"},
];

// 具体名称, 11市政采暖, 12空气源热泵, 13电锅炉, 14燃气锅炉, 15水源热泵, 16地源热泵, 17分体空调, 18内燃机空调, 10其他
export const hsNameEnum = [
    {id: 11, name: "市政采暖"},
    {id: 12, name: "空气源热泵"},
    {id: 13, name: "电锅炉"},
    {id: 14, name: "燃气锅炉"},
    {id: 15, name: "水源热泵"},
    {id: 16, name: "地源热泵"},
    {id: 17, name: "分体空调"},
    {id: 18, name: "内燃机空调"},
    {id: 10, name: "其他"},
];

// 换热站, 10否, 11是
export const hsHeatStationEnum = [ {id: 11, name: "是"}, {id: 12, name: "否"}, ];

// 使用形式, 11电热水器, 12电锅炉, 13燃气锅炉, 14空气源热泵热水机, 15太阳能热水器, 10其他
export const hwsUseFormEnum = [
    {id: 11, name: "电热水器"},
    {id: 12, name: "电锅炉"},
    {id: 13, name: "燃气锅炉"},
    {id: 14, name: "空气源热泵热水机"},
    {id: 15, name: "太阳能热水器"},
    {id: 10, name: "其他"},
];

// 是否使用变频技术, 11是, 12否
export const liftFreqConvUsedEnum = [ {id: 11, name: "是"}, {id: 12, name: "否"}, ];

// 曳引电梯, 11是, 12否
export const liftTractionEnum = [ {id: 11, name: "是"}, {id: 12, name: "否"}, ];

// 液压电梯, 11是, 12否
export const liftHydraulicEnum = [ {id: 11, name: "是"}, {id: 12, name: "否"}, ];

// 分类, 11干式变压器, 12油浸式变压器, 10其他类型变压器
export const psdsClassEnum = [ {id: 11, name: "干式变压器"}, {id: 12, name: "油浸式变压器"}, {id: 10, name: "其他类型变压器"}, ];

// 水源类型, 11公共供水管网, 12地下水, 13外购水
export const wusWaterTypeEnum = [ {id: 11, name: "公共供水管网"}, {id: 12, name: "地下水"}, {id: 13, name: "外购水"}, ];

// 卫生间用水器具, 11普通水龙头, 12节水型水龙头
export const wusBathroomFixtureEnum = [ {id: 11, name: "普通水龙头"}, {id: 12, name: "节水型水龙头"}, ];

// 饮用水, 11净水机组, 12开水器, 13饮水机
export const wusDrinkWaterEnum = [ {id: 11, name: "净水机组"}, {id: 12, name: "开水器"}, {id: 13, name: "饮水机"}, ];

// 浴室用水器具, 11普通花洒, 12节水花洒
export const wusShowerFixtureEnum = [ {id: 11, name: "普通花洒"}, {id: 12, name: "节水花洒"}, ];

// 小便器, 11非节能, 12按压式, 13感应式
export const wusUrinalEnum = [ {id: 11, name: "非节能"}, {id: 12, name: "按压式"}, {id: 13, name: "感应式"}, ];

// 蹲便器, 11脚踏式, 12感应式
export const wusSquatToiletEnum = [ {id: 11, name: "脚踏式"}, {id: 12, name: "感应式"}, ];

// 是否使用加压泵, 11是, 12否
export const wusPressPumpUsedEnum = [ {id: 11, name: "是"}, {id: 12, name: "否"}, ];

// 是否是透水路面, 11是, 12否
export const wusRoadPermeableEnum = [ {id: 11, name: "是"}, {id: 12, name: "否"}, ];

// 绿化使用水源, 11地下水, 12自来水, 13雨水, 14废水, 15尾水, 10其他
export const wusGreenWaterTypeEnum = [
    {id: 11, name: "地下水"},
    {id: 12, name: "自来水"},
    {id: 13, name: "雨水"},
    {id: 14, name: "废水"},
    {id: 15, name: "尾水"},
    {id: 10, name: "其他"},
];

// 是否有废水再利用, 11是, 12否
export const wusWasteReuseEnum = [ {id: 11, name: "是"}, {id: 12, name: "否"}, ];

// 是否有雨水、冷凝水、尾水回用, 11是, 12否
export const wusReuseRctEnum = [ {id: 11, name: "是"}, {id: 12, name: "否"}, ];

// 形式, 11分体空调, 12中央空调
export const acsTypeEnum = [ {id: 11, name: "分体空调"}, {id: 12, name: "中央空调"}, ];

// 具体名称, 11分体空调, 12空气源热泵, 13水源热泵, 14内燃气空调, 15风冷模块机组, 16螺杆机冷水机组, 17离心式冷水机组, 18挂式分体式空调, 19立式分体式空调, 10其他
export const acsNameEnum = [
    {id: 11, name: "分体空调"},
    {id: 12, name: "空气源热泵"},
    {id: 13, name: "水源热泵"},
    {id: 14, name: "内燃气空调"},
    {id: 15, name: "风冷模块机组"},
    {id: 16, name: "螺杆机冷水机组"},
    {id: 17, name: "离心式冷水机组"},
    {id: 18, name: "挂式分体式空调"},
    {id: 19, name: "立式分体式空调"},
    {id: 10, name: "其他"},
];

// 是否是节能插座, 11是, 12否
export const skEnergySaveEnum = [ {id: 11, name: "是"}, {id: 12, name: "否"}, ];

// 室内照明, 11白炽灯, 12荧光灯, 13LED灯, 14感应灯, 10其他
export const lsIndoorEnum = [
    {id: 11, name: "白炽灯"},
    {id: 12, name: "荧光灯"},
    {id: 13, name: "LED灯"},
    {id: 14, name: "感应灯"},
    {id: 10, name: "其他"},
];

// 开关类型, 11感应开关, 12普通按钮开关
export const lsSwitchTypeEnum = [ {id: 11, name: "感应开关"}, {id: 12, name: "普通按钮开关"},];

// 室外照明, 11普通路灯, 12LED路灯, 13太阳能路灯
export const lsOutdoorEnum = [ {id: 11, name: "普通路灯"}, {id: 12, name: "LED路灯"}, {id: 13, name: "太阳能路灯"}, ];

// 外墙是否有保温, 11是, 12否
export const beWallInsulationEnum = [ {id: 11, name: "是"}, {id: 12, name: "否"}, ];

// 外窗, 11普通单层玻璃, 12普通双层玻璃, 13塑钢中空玻璃
export const beWindowEnum = [ {id: 11, name: "普通单层玻璃"}, {id: 12, name: "普通双层玻璃"}, {id: 13, name: "塑钢中空玻璃"},];

// 屋顶是否有保温, 11是, 12否
export const beRootInsulationEnum = [ {id: 11, name: "是"}, {id: 12, name: "否"}, ];

// 公务用车, 11汽油车, 12柴油车, 13新能源汽车全电, 14新能源汽车混动
export const vtsOfficeVehicleEnum = [
    {id: 11, name: "汽油车"},
    {id: 12, name: "柴油车"},
    {id: 13, name: "新能源汽车全电"},
    {id: 14, name: "新能源汽车混动"},
];

// 水表是否安装齐全, 11是, 12否
export const msWmeterFullInstallEnum = [ {id: 11, name: "是"}, {id: 12, name: "否"}, ];

// 电表是否安装齐全, 11是, 12否
export const msEmeterFullInstallEnum = [ {id: 11, name: "是"}, {id: 12, name: "否"}, ];

// 建设情况, 11电力监测平台, 12水资源监测平台, 13综合能源监测平台
export const ecpConstrStatusEnum = [ {id: 11, name: "电力监测平台"}, {id: 12, name: "水资源监测平台"}, {id: 13, name: "综合能源监测平台"}, ];

// 运行情况, 11正常, 12异常
export const ecpOpStatusEnum = [ {id: 11, name: "正常"}, {id: 12, name: "异常"}, ];
