///
/// AVEncodingOption.swift
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON> @ Margelo
///

/**
 * Represents the JS union `AVEncodingOption`, backed by a C++ enum.
 */
public typealias AVEncodingOption = margelo.nitro.react_native_audio_recorder_player.AVEncodingOption

public extension AVEncodingOption {
  /**
   * Get a AVEncodingOption for the given String value, or
   * return `nil` if the given value was invalid/unknown.
   */
  init?(fromString string: String) {
    switch string {
      case "lpcm":
        self = .lpcm
      case "ima4":
        self = .ima4
      case "aac":
        self = .aac
      case "MAC3":
        self = .mac3
      case "MAC6":
        self = .mac6
      case "ulaw":
        self = .ulaw
      case "alaw":
        self = .alaw
      case "mp1":
        self = .mp1
      case "mp2":
        self = .mp2
      case "mp4":
        self = .mp4
      case "alac":
        self = .alac
      case "amr":
        self = .amr
      case "flac":
        self = .flac
      case "opus":
        self = .opus
      default:
        return nil
    }
  }

  /**
   * Get the String value this AVEncodingOption represents.
   */
  var stringValue: String {
    switch self {
      case .lpcm:
        return "lpcm"
      case .ima4:
        return "ima4"
      case .aac:
        return "aac"
      case .mac3:
        return "MAC3"
      case .mac6:
        return "MAC6"
      case .ulaw:
        return "ulaw"
      case .alaw:
        return "alaw"
      case .mp1:
        return "mp1"
      case .mp2:
        return "mp2"
      case .mp4:
        return "mp4"
      case .alac:
        return "alac"
      case .amr:
        return "amr"
      case .flac:
        return "flac"
      case .opus:
        return "opus"
    }
  }
}
