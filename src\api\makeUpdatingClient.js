import { useMutation } from "@tanstack/react-query";
import { httpClient } from "../services/http";
import log from "../services/logging";
import { makeFormData, mergeQueryPath } from "../utils";


const RETRY_CODES = [401, 501, 502, 503, 504];
const RETRY_METHODS = ["POST", "GET", "PUT", "HEAD", "DELETE", "OPTIONS", "TRACE"];
const RETRY_INTERVALS = [2000, 3000, 5000];
const RETRY_LIMIT = RETRY_INTERVALS.length;

/**
 * Return a `useMutation` query object. The query result will be stored in MMKV
 * @param {Object} arg
 * @param {string} arg.query the query key for react-query
 * @param {string} arg.url the http request path which will be responed by the server
 * @returns
 */
export const makeUpdatingClient = ({ query, url }) => {
    /**
     * 返回一个函数, 这个返回的函数创建一个useMutation查询, 用于发起update请求.
     * @param {Function} dataGetter function
     * @param {Number | Number[] | String | String[]} varOrVars
     * @param {()=>{}} onSuccess callback function
     * @param {()=>{}} onError callback function
     * @param {()=>{}} onSettled callback function
     */
    return (dataGetter, varOrVars, onSuccess, onError, onSettled) => {
        const appendUrl = `${url}${mergeQueryPath(varOrVars)}`; // url/sub_dir
        return useMutation({
            mutationKey: [query],
            mutationFn: async () => {
                const formData = makeFormData(dataGetter());
                log.debug("makeUpdatingClient post, query: %s, urlPath: %s, data: %s", query, appendUrl, formData);
                const response = await httpClient.post(appendUrl, { body: formData, retry: { limit: 0 } });
                const json = await response.json();
                log.debug("updating query client %s on %s, mutationFn receive data: %s", query, appendUrl, json);
                return json;
            },
            onSuccess: (data, variables, context) => {
                console.log("Call onSuccess");
                onSuccess?.(data);
            },
            onError: (error, variables, context) => {
                console.log("Call onError");
                onError?.(error);
            },
            onSettled: (data, error, variables, context) => {
                console.log("Call onSettled");
                onSettled?.(data, error);
            },


            // 逻辑上, 首次失败后, 立即检测retryDelay, 经延迟后再检测retry触发重试
            // 注意, 有Ky的retry有bug, 这里使用React Query的retry!!!!!!
            retry: (failureCount, error) => { // failureCount from 0
                log.debug("Retry query: %s, urlPath: %s, error: %s, failureCount: %s", query, url, error, failureCount);
                if (failureCount < RETRY_LIMIT
                    && error.name === "HTTPError"
                    && RETRY_CODES.includes(error.response.status)
                    && RETRY_METHODS.includes(error.request.method)) {
                    return true;
                } else {
                    return false;
                }
            },
            retryDelay: (attemptIndex) => RETRY_INTERVALS[attemptIndex], // attemptIndex from 0
        });
    };
};
