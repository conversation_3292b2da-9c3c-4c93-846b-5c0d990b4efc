///
/// AVEncoderAudioQualityIOSType.kt
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON>av<PERSON> @ Margelo
///

package com.margelo.nitro.reactnativeaudiorecorderplayer

import androidx.annotation.Keep
import com.facebook.proguard.annotations.DoNotStrip

/**
 * Represents the JavaScript enum/union "AVEncoderAudioQualityIOSType".
 */
@DoNotStrip
@Keep
enum class AVEncoderAudioQualityIOSType {
  MIN,
  LOW,
  MEDIUM,
  HIGH,
  MAX;

  @DoNotStrip
  @Keep
  private val _ordinal = ordinal
}
