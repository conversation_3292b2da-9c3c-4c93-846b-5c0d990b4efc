import { joi<PERSON>esolver } from "@hookform/resolvers/joi";
import "fast-text-encoding";
import <PERSON><PERSON> from "joi";
import debounce from "lodash/debounce";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { useForm, useWatch } from "react-hook-form";
import { Alert, StyleSheet, View } from "react-native";
import { Divider, Snackbar } from "react-native-paper";
import Button from "../../../components/Button";
import ControlledTextInput from "../../../components/ControlledTextInput";
import { DialogToConfirm } from "../../../components/DialogToConfirm";
import HeaderBar from "../../../components/HeaderBar";
import { LoadingIndicator } from "../../../components/LoadingIndicator";
import { creatMMKVStore, makeStoredValueToFormValue } from "../../../services/local-storage";
import { callOneByOne, makeDataFeeder } from "../../../utils";
import { checkPermits } from "../../../utils/permits";
import { isServerDataNewer, makeGetClientCversion, radioIdToObject } from "../../../utils/records";
import { validatorBase } from "../../../utils/validatorBase";
import ScreenWrapper from "../../ScreenWrapper";

// 新组件需要重新!!
import { useShallow } from "zustand/shallow";
import {
    makeReqDelWaterBalanceRecords as recordDeleteQueryMaker,
    makeReqGetWaterBalanceRecords as recordSelectQueryMaker,
    makeReqUpdWaterBalanceRecords as recordUpdateQueryMaker,
} from "../../../api/projectRecordsQueries";
import { FormListNodesMapper } from "../../../components/FormListNodesMapper";
import { UPD_WATER_BALANCE_RECORDS as pageMainKey } from "../../../config/keysConfig";
import { meterDeviceTypeEnum } from "../../../config/waterBalance";
import { wbWaterMeterEquipStatStates as selectorStates } from "../../../hooks/selectorStates";
import { onPreSubmitError } from "../../../utils/screens";


const dataFeeder = makeDataFeeder();

/**
 * 用水单位水计量器具配备统计
 * 注意, 项目数据表单与工作台管理表单的数据有一个显著的区别:
 * 项目数据不需要像用户编号那样脱敏, 两边传输的数据是直接的, 不需要做{id, name}这种映射, 部分需要映射的可以在客户端本地完成,
 * @param {object} args
 * @param {object} args.navigation
 * @param {object} args.route
 * @returns
 */
const RecordsUpdating = ({ navigation, route }) => {
    console.log("ComWaterMeterEquipStatsRecordsUpdating pageMeta from nav:", route.params.pageMeta);
    console.log("ComWaterMeterEquipStatsRecordsUpdating projMeta from nav:", route.params.projMeta);
    const projSubclass = route.params.projMeta.subclass; // 1表, 2书

    const { queryKwd, id: recordPubid, name: recordName, formType } = route.params.pageMeta; // recordPubid is the alias of id
    const saveButtonIcon = "content-save-all-outline";
    const formEditableGlobal = checkPermits() ? (formType === 0 ? true : false) : false;

    // 使用memoize来优化
    const { setStore, getStore, clearStore, setStoreObject } = useMemo(() => {
        console.log("creatMMKVStore memo:", pageMainKey.store, queryKwd, recordPubid);
        return creatMMKVStore(`${pageMainKey.store}/${queryKwd}/${recordPubid}`);
    }, [queryKwd, recordPubid]);

    const storedValueToFormValue = useRef(makeStoredValueToFormValue(getStore));
    const getClientCversion = useRef(makeGetClientCversion(() => getStore("mainCversion"), () => getStore("subCversion")));

    // 客户端cversion = `${mainCversion}.${subCversion}`
    (getStore("mainCversion") === undefined) ? setStore("mainCversion", 0) : undefined; // 客户端主版本号
    (getStore("subCversion")  === undefined) ? setStore("subCversion",  0) : undefined; // 客户端次版本号, 修改一次自增一次
    //let mainCversion = getStore("mainCversion"); // 主版本号, 与服务端cversion对应
    //let subCversion  = getStore("subCversion");  // 次版本号, 表示本地更改次数, 用于程序上对子版本号的增加, 用于判断与服务端版本的差异, 同时可用于判断避免重复从MMKV获取数据
    const subCversionRef = useRef(getStore("subCversion"));

    // 新组件需要修改!!
    const bottomBarLabels = { ok: "提交" };                    // 底部按钮显示文字
    const [snackBarMessage, setSnackBarMessage] = useState("更新表单遇到错误"); // 下方错误提示文字内容

    const [
        deviceTypeState,
        setDeviceTypeState,
        resetSelectorStates
    ] = selectorStates(useShallow(state => [
        state.deviceType,
        state.setDeviceType,
        state.resetStates,
    ])); // radio组件状态

    const [confimDialogConfig, setConfirmDialogConfig] = useState({});       // 对话框信息, {title, text, okLabel, onOK}, 但显示对话框的变量在showConfirm

    // 新组件不需改动
    const [screenTitle,      setScreenTitle]      = useState(route.params.pageMeta.name); // 屏幕上方导航栏标题
    const [okButtonLoading,  setOkButtonLoading]  = useState(false);            // 右下方按钮图标是否旋转
    const [okButtonDisabled, setOkButtonDisabled] = useState(!checkPermits());  // 右下方按钮是否禁用
    const [showSnackbar,     setShowSnackbar]     = useState(false);            // 下方是否显示错误通知
    const [showConfirm,      setShowConfirm]      = useState(false);            // 是否显示确认对话框
    const [loadingIndicatorVisible, setLoadingIndicatorVisible] = useState(false); // 是否显示loading
    const delQueryInfo = useRef({ id: recordPubid, name: recordName });         // 用于删除本记录的查询变量

    const deviceTypeStateDataProviderRef = useRef(meterDeviceTypeEnum);
    //const accuracyLevelStateDataProviderRef = useRef([{ id: 1, name: "一级" }, { id: 2, name: "二级" }, ]);
    //const equipStateStateDataProviderRef    = useRef([{ id: 1, name: "合格" }, { id: 2, name: "不合格" },]);

    // Define form
    // 新组件需要重新定义!!
    const schema = Joi.object({
        name:          validatorBase.waterBalance.comWaterMeterEquipStats.name,
        deviceType:    validatorBase.waterBalance.comWaterMeterEquipStats.deviceType,
        shouldEquip1:  validatorBase.waterBalance.comWaterMeterEquipStats.intField,
        haveEquipped1: validatorBase.waterBalance.comWaterMeterEquipStats.intField,
        intactDevice1: validatorBase.waterBalance.comWaterMeterEquipStats.intFieldUnrequired,
        equippedRate1: validatorBase.waterBalance.comWaterMeterEquipStats.percentField,
        intactRate1:   validatorBase.waterBalance.comWaterMeterEquipStats.percentFieldUnrequired,
        meteredRate1:  validatorBase.waterBalance.comWaterMeterEquipStats.percentFieldUnrequired,
        shouldEquip2:  validatorBase.waterBalance.comWaterMeterEquipStats.intField,
        haveEquipped2: validatorBase.waterBalance.comWaterMeterEquipStats.intField,
        intactDevice2: validatorBase.waterBalance.comWaterMeterEquipStats.intFieldUnrequired,
        equippedRate2: validatorBase.waterBalance.comWaterMeterEquipStats.percentField,
        intactRate2:   validatorBase.waterBalance.comWaterMeterEquipStats.percentFieldUnrequired,
        meteredRate2:  validatorBase.waterBalance.comWaterMeterEquipStats.percentFieldUnrequired,
        shouldEquip3:  validatorBase.waterBalance.comWaterMeterEquipStats.intField,
        haveEquipped3: validatorBase.waterBalance.comWaterMeterEquipStats.intField,
        intactDevice3: validatorBase.waterBalance.comWaterMeterEquipStats.intFieldUnrequired,
        equippedRate3: validatorBase.waterBalance.comWaterMeterEquipStats.percentField,
        intactRate3:   validatorBase.waterBalance.comWaterMeterEquipStats.percentFieldUnrequired,
        meteredRate3:  validatorBase.waterBalance.comWaterMeterEquipStats.percentFieldUnrequired,
        shouldEquip:   validatorBase.waterBalance.comWaterMeterEquipStats.intFieldUnrequired,
        haveEquipped:  validatorBase.waterBalance.comWaterMeterEquipStats.intFieldUnrequired,
        intactDevice:  validatorBase.waterBalance.comWaterMeterEquipStats.intFieldUnrequired,
        equippedRate:  validatorBase.waterBalance.comWaterMeterEquipStats.percentFieldUnrequired,
        intactRate:    validatorBase.waterBalance.comWaterMeterEquipStats.percentFieldUnrequired,
        meteredRate:   validatorBase.waterBalance.comWaterMeterEquipStats.percentFieldUnrequired,
        others:        validatorBase.waterBalance.comWaterMeterEquipStats.textField,
        remarks:       validatorBase.waterBalance.comWaterMeterEquipStats.longTextField,
    });

    // 新组件需要重新定义defaultValues!!
    const {
        handleSubmit,
        control,
        resetField,
        reset,
        formState: { errors },
        setValue,
        //getValues,
    } = useForm({
        resolver: joiResolver(schema),
        //reValidateMode: "onBlur", // https://github.com/orgs/react-hook-form/discussions/10327
        defaultValues: { // 默认值在组件载入时会被更新, 这里的初始值正常情况下没有用处
            name:          "",
            deviceType:    "",
            shouldEquip1:  "",
            haveEquipped1: "",
            intactDevice1: "",
            equippedRate1: "",
            intactRate1:   "",
            meteredRate1:  "",
            shouldEquip2:  "",
            haveEquipped2: "",
            intactDevice2: "",
            equippedRate2: "",
            intactRate2:   "",
            meteredRate2:  "",
            shouldEquip3:  "",
            haveEquipped3: "",
            intactDevice3: "",
            equippedRate3: "",
            intactRate3:   "",
            meteredRate3:  "",
            shouldEquip:   "",
            haveEquipped:  "",
            intactDevice:  "",
            equippedRate:  "",
            intactRate:    "",
            meteredRate:   "",
            others:        "",
            remarks:       "",
        },
    });

    // Query: select record
    const onRecordSelectSuccess = (data) => { // data: {"id": 3, "leader": 0, "name": "DEPT-03", "superior": 0}
        // 注意, 进入onSuccess之前就意味着服务端版本比客户端版本新, 因此要更新客户端主版本号
        console.log("onRecordSelectSuccess:", data);
        if (data.STATUS === 0) {
            setStore("mainCversion", data.DATA.cversion);
            setStore("subCversion", 0);
            const formObject  = {
                name:          String(data.DATA.name),
                deviceType:    String(data.DATA.deviceType),
                shouldEquip1:  String(data.DATA.shouldEquip1),
                haveEquipped1: String(data.DATA.haveEquipped1),
                intactDevice1: String(data.DATA.intactDevice1),
                equippedRate1: String(data.DATA.equippedRate1),
                intactRate1:   String(data.DATA.intactRate1),
                meteredRate1:  String(data.DATA.meteredRate1),
                shouldEquip2:  String(data.DATA.shouldEquip2),
                haveEquipped2: String(data.DATA.haveEquipped2),
                intactDevice2: String(data.DATA.intactDevice2),
                equippedRate2: String(data.DATA.equippedRate2),
                intactRate2:   String(data.DATA.intactRate2),
                meteredRate2:  String(data.DATA.meteredRate2),
                shouldEquip3:  String(data.DATA.shouldEquip3),
                haveEquipped3: String(data.DATA.haveEquipped3),
                intactDevice3: String(data.DATA.intactDevice3),
                equippedRate3: String(data.DATA.equippedRate3),
                intactRate3:   String(data.DATA.intactRate3),
                meteredRate3:  String(data.DATA.meteredRate3),
                shouldEquip:   String(data.DATA.shouldEquip),
                haveEquipped:  String(data.DATA.haveEquipped),
                intactDevice:  String(data.DATA.intactDevice),
                equippedRate:  String(data.DATA.equippedRate),
                intactRate:    String(data.DATA.intactRate),
                meteredRate:   String(data.DATA.meteredRate),
                others:        String(data.DATA.others),
                remarks:       String(data.DATA.remarks),
            };
            reset(formObject);           // 重置react-form

            const storeObjects = checkPermits() && {
                ...formObject,
                deviceType: radioIdToObject(deviceTypeStateDataProviderRef.current, data.DATA.deviceType),
                //waterType:     radioIdToObject(waterTypeStateDataProviderRef.current,     data.DATA.waterType),
                //accuracyLevel: radioIdToObject(accuracyLevelStateDataProviderRef.current, data.DATA.accuracyLevel),
                //equipState:    radioIdToObject(equipStateStateDataProviderRef.current,    data.DATA.equipState),
            };
            storeObjects && setStoreObject(storeObjects, false); // 判断用户权限, 把服务端数据写到mmkv存储, 只有可写用户才需要本地存储, 不会首先清空老数据

            // 设置selector菜单状态
            setDeviceTypeState(radioIdToObject(deviceTypeStateDataProviderRef.current, data.DATA.deviceType));

            // 设置屏幕标题
            (screenTitle !== formObject.name) && setScreenTitle(formObject.name);
        } else  {
            console.log("STATUS !== 0", data.STATUS);
            setSnackBarMessage(`表单数据拉取发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        }
    };
    const onRecordSelectError = (err) => {
        console.log("Record selecting error:", err);
    };
    const onRecordSelectSettled = (err) => {
        loadingIndicatorVisible && setLoadingIndicatorVisible(false);
    };
    const recordSelectQuery = recordSelectQueryMaker([queryKwd, "get", recordPubid], onRecordSelectSuccess, onRecordSelectError, onRecordSelectSettled);

    // query update
    // 新组件需要重新定义setDiagText!!
    const onHandleSubmit = (data) => {
        setOkButtonDisabled(true);
        setOkButtonLoading(true);
        dataFeeder({
            ...data,
            cversion: getClientCversion.current(),
            name: radioIdToObject(deviceTypeStateDataProviderRef.current, data.deviceType)?.name || "",
        }); // append client cversion
        recordUpdateQuery.mutate();
    };

    // 新组件不需改动
    const onRecordUpdateSuccess = (data) => {
        console.log("response success, data: ", data);
        if(data.STATUS !== 0) {
            setSnackBarMessage(`表单更新发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        } else {
            // 设置确认框数据
            setConfirmDialogConfig({
                title: "更新表单",
                text: "表单信息更新成功!",
                okLabel: "确定",
                onOK: () => {
                    setShowConfirm(false);
                    recordSelectQuery.mutate();
                }});
            setShowConfirm(true);
        }
    };
    const onRecordUpdateError = (error) => {
        setSnackBarMessage(`表单更新发生错误, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const onRecordUpdateSettled = (data, error) => {
        setOkButtonDisabled(false);
        setOkButtonLoading(false);
    };
    const recordUpdateQuery = recordUpdateQueryMaker(dataFeeder, [queryKwd, "upd", recordPubid], onRecordUpdateSuccess, onRecordUpdateError, onRecordUpdateSettled);

    // query delete
    const recordDeleteOnSuccess = () => {
        clearStore();
        navigation.goBack();
    };
    const recordDeleteOnError = (error) => {
        setSnackBarMessage(`表单删除失败, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const recordDeleteQuery = recordDeleteQueryMaker([queryKwd, "del", recordPubid], recordDeleteOnSuccess, recordDeleteOnError);

    const deleteAlert = () => {
        return (
            Alert.alert("删除表单", `删除后不可撤销, 请确认是否删除: ${delQueryInfo.current?.name}?`, [
                { text: "取消" },
                { text: "确定", onPress: () => recordDeleteQuery.mutate() },
            ])
        );
    };

    const clearCache = () => {
        return (
            Alert.alert("清空本地缓存", "1. 用于未知原因导致数据无法提交的情况\r\n2. 未提交的内容将会丢失, 请做好数据截图备份\r\n3. 已提交到服务端的数据不受影响\r\n4. 确认清空请点击“确认”按钮", [
                { text: "取消" },
                { text: "确定", onPress: () => { clearStore(); navigation.goBack(); } },
            ])
        );
    };

    // Hooks: refresh data
    // useWatch must be initialized before their values set remotely or locally.
    const shouldEquip1  = Number(useWatch({ control, name: "shouldEquip1" }));
    const haveEquipped1 = Number(useWatch({ control, name: "haveEquipped1" }));
    const intactDevice1 = Number(useWatch({ control, name: "intactDevice1" }));
    const shouldEquip2  = Number(useWatch({ control, name: "shouldEquip2" }));
    const haveEquipped2 = Number(useWatch({ control, name: "haveEquipped2" }));
    const intactDevice2 = Number(useWatch({ control, name: "intactDevice2" }));
    const shouldEquip3  = Number(useWatch({ control, name: "shouldEquip3" }));
    const haveEquipped3 = Number(useWatch({ control, name: "haveEquipped3" }));
    const intactDevice3 = Number(useWatch({ control, name: "intactDevice3" }));

    // https://stackoverflow.com/questions/76501023/alternate-to-react-hook-form-usewatch
    const updateGlobalState = debounce((should1, have1, intact1, should2, have2, intact2, should3, have3, intact3) => {
        const equippedRate1 = `${should1 === 0 ? 0 : (100.0 * (have1 / should1)).toFixed(2)}`;
        const equippedRate2 = `${should2 === 0 ? 0 : (100.0 * (have2 / should2)).toFixed(2)}`;
        const equippedRate3 = `${should3 === 0 ? 0 : (100.0 * (have3 / should3)).toFixed(2)}`;
        const intactRate1   = `${should1 === 0 ? 0 : 100}`;
        const intactRate2   = `${should2 === 0 ? 0 : 100}`;
        const intactRate3   = `${should3 === 0 ? 0 : 100}`;
        //const meteredRate1  = `${parseFloat(equippedRate1) > 99.9 ? "100.00" : "0"}`;
        //const meteredRate2  = `${parseFloat(equippedRate2) > 99.9 ? "100.00" : "0"}`;
        //const meteredRate3  = `${parseFloat(equippedRate3) > 99.9 ? "100.00" : "0"}`;

        //const shouldEquip   = `${((should1 + should2 + should3))}`;
        //const haveEquipped  = `${((have1 + have2 + have3))}`;
        //const intactDevice  = `${intact1 + intact2 + intact3}`;
        //const equippedRate  = `${should1 + should2 + should3 === 0 ? 0 : (100.0 * (have1   + have2   + have3)   / (should1 + should2 + should3)).toFixed(2)}`;
        //const intactRate    = `${have1   + have2   + have3   === 0 ? 0 : (100.0 * (intact1 + intact2 + intact3) / (have1   + have2   + have3)).toFixed(2)}`;
        //const meteredRate   =
        setValue("equippedRate1", equippedRate1);
        setValue("equippedRate2", equippedRate2);
        setValue("equippedRate3", equippedRate3);
        setValue("intactRate1",   intactRate1);
        setValue("intactRate2",   intactRate2);
        setValue("intactRate3",   intactRate3);
        //(parseFloat(equippedRate1) > 99.9) && setValue("meteredRate1",  meteredRate1);
        //(parseFloat(equippedRate2) > 99.9) && setValue("meteredRate2",  meteredRate2);
        //(parseFloat(equippedRate3) > 99.9) && setValue("meteredRate3",  meteredRate3);

        //setValue("shouldEquip",   shouldEquip);
        //setValue("haveEquipped",  haveEquipped);
        //setValue("intactDevice",  intactDevice);
        //setValue("equippedRate",  equippedRate);
        //setValue("intactRate",    intactRate);
        //setValue("meteredRate",   "100");
        //subCversion++;
        subCversionRef.current++;
        setStore("subCversion", subCversionRef.current);
    }, 100);

    useEffect(() => {
        updateGlobalState(shouldEquip1, haveEquipped1, intactDevice1, shouldEquip2, haveEquipped2, intactDevice2, shouldEquip3, haveEquipped3, intactDevice3);
    }, [shouldEquip1, haveEquipped1, intactDevice1, shouldEquip2, haveEquipped2, intactDevice2, shouldEquip3, haveEquipped3, intactDevice3]);


    // Hook: onMount fetch data
    useEffect(() => {
        // 优化方案: 根据用户权限进行mutate, 只有可写用户才需要本地存储
        const localCversion = getClientCversion.current();
        const remoteCversion = route.params.pageMeta.cversion;
        console.log("localCversion:", localCversion, "remoteSversion:", remoteCversion);

        const restoreLocalData = () => {
            const formObject = { // 默认值用户表单的数据显示
                name:          storedValueToFormValue.current("name"),
                deviceType:    storedValueToFormValue.current("deviceType", 1), // default to 1
                shouldEquip1:  storedValueToFormValue.current("shouldEquip1"),
                haveEquipped1: storedValueToFormValue.current("haveEquipped1"),
                intactDevice1: storedValueToFormValue.current("intactDevice1"),
                equippedRate1: storedValueToFormValue.current("equippedRate1"),
                intactRate1:   storedValueToFormValue.current("intactRate1"),
                meteredRate1:  storedValueToFormValue.current("meteredRate1"),
                shouldEquip2:  storedValueToFormValue.current("shouldEquip2"),
                haveEquipped2: storedValueToFormValue.current("haveEquipped2"),
                intactDevice2: storedValueToFormValue.current("intactDevice2"),
                equippedRate2: storedValueToFormValue.current("equippedRate2"),
                intactRate2:   storedValueToFormValue.current("intactRate2"),
                meteredRate2:  storedValueToFormValue.current("meteredRate2"),
                shouldEquip3:  storedValueToFormValue.current("shouldEquip3"),
                haveEquipped3: storedValueToFormValue.current("haveEquipped3"),
                intactDevice3: storedValueToFormValue.current("intactDevice3"),
                equippedRate3: storedValueToFormValue.current("equippedRate3"),
                intactRate3:   storedValueToFormValue.current("intactRate3"),
                meteredRate3:  storedValueToFormValue.current("meteredRate3"),
                shouldEquip:   storedValueToFormValue.current("shouldEquip"),
                haveEquipped:  storedValueToFormValue.current("haveEquipped"),
                intactDevice:  storedValueToFormValue.current("intactDevice"),
                equippedRate:  storedValueToFormValue.current("equippedRate"),
                intactRate:    storedValueToFormValue.current("intactRate"),
                meteredRate:   storedValueToFormValue.current("meteredRate"),
                others:        storedValueToFormValue.current("others"),
                remarks:       storedValueToFormValue.current("remarks"),
            };
            reset(formObject); // 重置react-form数据

            // 设置selector数据
            const defaultDeviceType = 1; // 默认为1, 水计量器具
            setDeviceTypeState(getStore("deviceType") || radioIdToObject(deviceTypeStateDataProviderRef.current, defaultDeviceType));
        };

        if (localCversion === 0 && remoteCversion > 0) {
            console.log("Local cversion === 0, serve may have something nontrivial initialized, query mutate!");
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        } else if (isServerDataNewer(localCversion, remoteCversion) && subCversionRef.current > 0) { // 服务端有更新数据, 但本地有修改
            console.log("Client cversion fell behind the server cversion, someone might modified the database, choose to use the data version!");
            Alert.alert("注意", "由于其他人向服务器提交了数据, 服务端数据比本地数据新, 请选择使用哪份数据?", [
                { text: "使用本地数据", onPress: () => { setStore("mainCversion", remoteCversion); restoreLocalData(); } },
                { text: "使用服务端数据", onPress: () => { setLoadingIndicatorVisible(true); recordSelectQuery.mutate(); } },
            ]);
        } else if (isServerDataNewer(localCversion, remoteCversion)) { // localCversion==0时要mutate是因为服务端可能在创建记录时生成了必要数据
            console.log("Server cversion may be newer, query mutate:", localCversion, remoteCversion);
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        }
        else {
            console.log("Local cversion >= server cversion, query will not mutate!");
            restoreLocalData();
        }
    }, []);


    // congif for Inputs mapping, type: "PLAIN", "RADIO", "CHECK", "TIME", "DATE", "RANGE"
    // remarks不需配置
    const FieldsConfig_1_book = [
        {
            inputs: [
                //{ name: "name",          label: "表单名称",         unit: "",   type: "PLAIN", editable: true, multiline: true, },
                { name: "deviceType",    label: "计量器具类型",      unit: "",   type: "RADIO", editable: true, placeholder: "", selectorState: deviceTypeState,      setSelectorState: setDeviceTypeState,     dataProvider: meterDeviceTypeEnum, },
            ],
        },
    ];
    const FieldsConfig_1_table = [
        {
            inputs: [
                //{ name: "name",          label: "表单名称",         unit: "",   type: "PLAIN", editable: true, multiline: true, },
                { name: "deviceType",    label: "计量器具类型",      unit: "",   type: "RADIO", editable: true, placeholder: "", selectorState: deviceTypeState,      setSelectorState: setDeviceTypeState,     dataProvider: meterDeviceTypeEnum, },
            ],
        },
    ];

    const FieldsConfig_2_book = [
        {
            nodeType: "Section", title: "一级项目", nodes: [
                {inputs: [
                    { name: "shouldEquip1",  label: "应配备数量", unit: "个", type: "PLAIN", editable: true, },
                    { name: "haveEquipped1", label: "已配备数量", unit: "个", type: "PLAIN", editable: true, },
                    { name: "equippedRate1", label: "配备率",    unit: "%",  type: "PLAIN", editable: false, },
                ]},
            ]
        },
        {
            nodeType: "Section", title: "二级项目", nodes: [
                {inputs: [
                    { name: "shouldEquip2",  label: "应配备数量", unit: "个", type: "PLAIN", editable: true, },
                    { name: "haveEquipped2", label: "已配备数量", unit: "个", type: "PLAIN", editable: true, },
                    { name: "equippedRate2", label: "配备率",     unit: "%", type: "PLAIN", editable: false, },
                ]},
            ]
        },
        {
            nodeType: "Section", title: "三级项目", nodes: [
                {inputs: [
                    { name: "shouldEquip3",  label: "应配备数量", unit: "个", type: "PLAIN", editable: true, },
                    { name: "haveEquipped3", label: "已配备数量", unit: "个", type: "PLAIN", editable: true, },
                    { name: "equippedRate3", label: "配备率",     unit: "%", type: "PLAIN", editable: false, },
                ]},
            ]
        },
        {
            nodeType: "Section", title: "合计", nodes: [
                {inputs: [
                    { name: "shouldEquip",   label: "应配备数量",   unit: "个", type: "PLAIN", editable: false, },
                    { name: "haveEquipped",  label: "已配备数量",   unit: "个", type: "PLAIN", editable: false, },
                    { name: "equippedRate",  label: "配备率",      unit: "%",  type: "PLAIN", editable: false, },
                ]},
            ]
        },
    ];
    const FieldsConfig_2_table = [
        {
            nodeType: "Section", title: "用水单位", nodes: [
                {inputs: [
                    { name: "shouldEquip1",  label: "应安装数量",   unit: "个", type: "PLAIN", editable: true, },
                    { name: "haveEquipped1", label: "已安装数量",   unit: "个", type: "PLAIN", editable: true, },
                    { name: "intactDevice1", label: "正常运行数量", unit: "个", type: "PLAIN", editable: true, },
                    { name: "meteredRate1",  label: "计量率",      unit: "%",  type: "PLAIN", editable: true, },
                    { name: "equippedRate1", label: "配备率",      unit: "%",  type: "PLAIN", editable: false, },
                    { name: "intactRate1",   label: "完好率",      unit: "%",  type: "PLAIN", editable: false, },
                ]},
            ]
        },
        {
            nodeType: "Section", title: "次级用水单位", nodes: [
                {inputs: [
                    { name: "shouldEquip2",  label: "应安装数量",   unit: "个", type: "PLAIN", editable: true, },
                    { name: "haveEquipped2", label: "已安装数量",   unit: "个", type: "PLAIN", editable: true, },
                    { name: "intactDevice2", label: "正常运行数量", unit: "个", type: "PLAIN", editable: true, },
                    { name: "meteredRate2",  label: "计量率",      unit: "%",  type: "PLAIN", editable: true, },
                    { name: "equippedRate2", label: "配备率",      unit: "%",  type: "PLAIN", editable: false, },
                    { name: "intactRate2",   label: "完好率",      unit: "%",  type: "PLAIN", editable: false, },
                ]},
            ]
        },
        {
            nodeType: "Section", title: "主要用水设备", nodes: [
                {inputs: [
                    { name: "shouldEquip3",  label: "应安装数量",   unit: "个", type: "PLAIN", editable: true, },
                    { name: "haveEquipped3", label: "已安装数量",   unit: "个", type: "PLAIN", editable: true, },
                    { name: "intactDevice3", label: "正常运行数量", unit: "个", type: "PLAIN", editable: true, },
                    { name: "meteredRate3",  label: "计量率",      unit: "%",  type: "PLAIN", editable: true, },
                    { name: "equippedRate3", label: "配备率",      unit: "%",  type: "PLAIN", editable: false, },
                    { name: "intactRate3",   label: "完好率",      unit: "%",  type: "PLAIN", editable: false, },
                ]},
            ]
        },
        /*{
            nodeType: "Section", title: "合计", nodes: [
                {inputs: [
                    { name: "shouldEquip",   label: "应配备数量",   unit: "个", type: "PLAIN", editable: false, },
                    { name: "haveEquipped",  label: "已配备数量",   unit: "个", type: "PLAIN", editable: false, },
                    { name: "equippedRate",  label: "配备率",      unit: "%",  type: "PLAIN", editable: false, },
                ]},
            ]
        },*/
    ];

    const FieldsConfig_3_book = [
        // 其它部分
        {
            inputs: [
                //{ name: "others",  label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "", },
            ]
        },
    ];
    const FieldsConfig_3_table = [
        // 其它部分
        {
            inputs: [
                //{ name: "others",  label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "", },
            ]
        },
    ];

    //const FieldsConfig = [...FieldsConfig_1, ...FieldsConfig_2, ...FieldsConfig_3];
    let FieldsConfig = [];
    switch(projSubclass) {
        case 1: // 表
            FieldsConfig = [...FieldsConfig_1_table, ...FieldsConfig_2_table, ...FieldsConfig_3_table];
            break;
        case 2: // 书
            FieldsConfig = [...FieldsConfig_1_book, ...FieldsConfig_2_book, ...FieldsConfig_3_book];
            break;
        default:
            console.warn("Unknown subclass:", projSubclass);
            FieldsConfig = [];
            break;
    }

    return (
        <View style={{ flex: 1 }}>

            <HeaderBar
                //title={screenTitle}
                navigation={navigation}
                goBackCallback={() => {
                    callOneByOne(
                        //() => { _mainCversionGetter=undefined; _subCversionGetter=undefined;},
                        resetSelectorStates,
                    );
                    /*callOneByOne(clearStore, resetSelectorStates)*/  // 返回前清空mmkv存储因为临时数据不再需要, 重置状态使得页面切换后不会使用前面的数据
                }}
                menuItemArray={[{ title: "删除表单", action: deleteAlert }, { title: "清空缓存", action: clearCache }]}
            />

            <ScreenWrapper contentContainerStyle={styles.container}>

                <View style={styles.formEntry}>

                    <FormListNodesMapper
                        fieldsConfig={FieldsConfig}
                        subCversionRef={subCversionRef}
                        control={control}
                        setStore={setStore}
                        resetField={resetField}
                        errors={errors}
                        setSnackBarMessage={setSnackBarMessage}
                        setShowSnackbar={setShowSnackbar}
                        editable={formEditableGlobal}
                        rowStyle={styles.rowContainer}
                    />

                    <Divider/>

                    {formType === 0 && <ControlledTextInput
                        name="remarks"
                        rowLabel="备注"
                        control={control}
                        placeholder=""
                        onChangeText={(text) => {
                            subCversionRef.current++;
                            setStore("subCversion", subCversionRef.current);
                            setStore("remarks", text);
                        }}
                        onClearText={() => {
                            subCversionRef.current++;
                            setStore("subCversion", subCversionRef.current);
                            setStore("remarks", "");
                            resetField("remarks", { defaultValue: "" });
                        }}
                        editable={formEditableGlobal}
                        mode="flat"
                        multiline={true}
                        formError={errors}
                        style={styles.rowContainerLongText}
                        //disabled={formDisabledGlobal}
                        required={false}
                    />}

                    <Divider bold={true}/>

                </View>

                <View style={styles.button}>
                    <Button mode="contained"
                        disabled={okButtonDisabled}
                        loading={okButtonLoading}
                        icon={saveButtonIcon}
                        onPress={handleSubmit(onHandleSubmit, (formErr) => onPreSubmitError(formErr, getStore, "ComWaterMeterEquipStatsRecordsUpdating"))}
                    >{bottomBarLabels.ok}</Button>
                </View>
            </ScreenWrapper>

            <LoadingIndicator
                title={""}
                message={"加载中....."}
                visible={loadingIndicatorVisible}
                onClose={()=>setLoadingIndicatorVisible(false)}
            />

            <Snackbar
                style={styles.snackBar}
                visible={showSnackbar}
                onDismiss={() => setShowSnackbar(false)}
                onIconPress={() => setShowSnackbar(false)}
                duration={Snackbar.DURATION_LONG}
            >
                {snackBarMessage}
            </Snackbar>

            <DialogToConfirm
                visible={showConfirm}
                title={confimDialogConfig.title ? confimDialogConfig.title : ""}
                text={confimDialogConfig.text ? confimDialogConfig.text : ""}
                onOK={confimDialogConfig.onOK ? confimDialogConfig.onOK : () => setShowConfirm(false)}
                okBtnLabel={confimDialogConfig.okLabel ? confimDialogConfig.okLabel : "确认"}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    rowContainer: {
        firstColumn: {
            flex: 1,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 1,
            // borderWidth: 1,
            // height: 40,
        },
    },
    rowContainerLongText: {
        firstColumn: {
            flex: 1,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 4,
            // /borderWidth: 1,
            // height: 40,
        },
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        width: "80%",
        alignSelf: "center",
    },
    formEntry: {
        margin: 8,
    },
    snackBar: {
        flexDirection: "column",
    },
});

export default RecordsUpdating;
