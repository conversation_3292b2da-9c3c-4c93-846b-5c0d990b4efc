///
/// AVEncodingOption.kt
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON> @ Margelo
///

package com.margelo.nitro.reactnativeaudiorecorderplayer

import androidx.annotation.Keep
import com.facebook.proguard.annotations.DoNotStrip

/**
 * Represents the JavaScript enum/union "AVEncodingOption".
 */
@DoNotStrip
@Keep
enum class AVEncodingOption {
  LPCM,
  IMA4,
  AAC,
  MAC3,
  MAC6,
  ULAW,
  ALAW,
  MP1,
  MP2,
  MP4,
  ALAC,
  AMR,
  FLAC,
  OPUS;

  @DoNotStrip
  @Keep
  private val _ordinal = ordinal
}
