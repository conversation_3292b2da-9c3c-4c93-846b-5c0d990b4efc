import React from "react";
import ListingTemplate from "./ListingTemplate";
import { makeRequestListingUsers as queryClient } from "../../api/listingQueries";
import { allUsersState as pageDataState } from "../../hooks/globalStates";


const UserListing = ({ navigation }) => {
    return (
        <ListingTemplate
            bottomBarLabel={"添加用户"}
            emptyScreenText={"请点击下方按钮添加用户"}
            snackBarDefaultText={"添加用户遇到错误"}
            saveButtonIcon={"account-plus-outline"}
            queryClient={queryClient}
            pageDataState={pageDataState}
            listItemNavigateTo={"WorkbenchUserUpdating"}
            addingButtonNavigateTo={"WorkbenchUserInserting"}
            navigation={navigation}
        />
    );
};

export default UserListing;
