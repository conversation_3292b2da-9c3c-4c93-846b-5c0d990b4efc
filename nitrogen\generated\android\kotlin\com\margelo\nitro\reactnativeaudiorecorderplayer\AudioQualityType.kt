///
/// AudioQualityType.kt
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON> @ Margelo
///

package com.margelo.nitro.reactnativeaudiorecorderplayer

import androidx.annotation.Keep
import com.facebook.proguard.annotations.DoNotStrip

/**
 * Represents the JavaScript enum/union "AudioQualityType".
 */
@DoNotStrip
@Keep
enum class AudioQualityType {
  LOW,
  MEDIUM,
  HIGH;

  @DoNotStrip
  @Keep
  private val _ordinal = ordinal
}
