import { Platform } from "react-native";
import { version } from "../../../package.json";

// for system version, see:
// https://stackoverflow.com/questions/38240859/how-to-get-a-versionname-in-react-native-app-on-android
// https://www.npmjs.com/package/react-native-device-info

export const APP_VERSION = version;

/**
 * @type {"ios" | "android" | "windows" | "macos" | "web"} CLIENT_TYPE
 */
export const CLIENT_OS = Platform.OS;

/**
 * Android: API version, not the Android OS version
 * iOS: [UIDevice systemVersion]
 * https://reactnative.dev/docs/platform-specific-code
 * https://en.wikipedia.org/wiki/Android_version_history#Overview
 */
export const CLIENT_OS_VERSION =  Platform.Version;
