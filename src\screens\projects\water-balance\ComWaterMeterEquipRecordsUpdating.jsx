import { joi<PERSON>esolver } from "@hookform/resolvers/joi";
import "fast-text-encoding";
import <PERSON><PERSON> from "joi";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { Alert, StyleSheet, View } from "react-native";
import { Divider, Snackbar } from "react-native-paper";
import Button from "../../../components/Button";
import ControlledTextInput from "../../../components/ControlledTextInput";
import { DialogToConfirm } from "../../../components/DialogToConfirm";
import HeaderBar from "../../../components/HeaderBar";
import { LoadingIndicator } from "../../../components/LoadingIndicator";
import { creatMMKVStore, makeStoredValueToFormValue } from "../../../services/local-storage";
import { callOneByOne, ifTruthLet, isNullness, makeDataFeeder } from "../../../utils";
import { checkPermits } from "../../../utils/permits";
import { isServerDataNewer, makeGetClientCversion, radioIdToObject } from "../../../utils/records";
import { validatorBase } from "../../../utils/validatorBase";
import ScreenWrapper from "../../ScreenWrapper";

// 新组件需要重新!!
import { useShallow } from "zustand/shallow";
import {
    makeReqDelWaterBalanceRecords as recordDeleteQueryMaker,
    makeReqGetWaterBalanceRecords as recordSelectQueryMaker,
    makeReqUpdWaterBalanceRecords as recordUpdateQueryMaker,
} from "../../../api/projectRecordsQueries";
import { FormListNodesMapper } from "../../../components/FormListNodesMapper";
import { UPD_WATER_BALANCE_RECORDS as pageMainKey } from "../../../config/keysConfig";
import { monitoredStateEnum, waterMeterAccuracyLevelEnum, waterMeterCalibrateUnitEnum, waterMeterEquipStateEnum, waterMeterEquipStateEnum4Table, waterSourceTypeEnum } from "../../../config/waterBalance";
import { wbComWaterMeterEquipStates as selectorStates } from "../../../hooks/selectorStates";
import { onPreSubmitError } from "../../../utils/screens";


const dataFeeder = makeDataFeeder();

/**
 * 用水单位水计量器具配备情况
 * 注意, 项目数据表单与工作台管理表单的数据有一个显著的区别:
 * 项目数据不需要像用户编号那样脱敏, 两边传输的数据是直接的, 不需要做{id, name}这种映射, 部分需要映射的可以在客户端本地完成,
 * @param {object} args
 * @param {object} args.navigation
 * @param {object} args.route
 * @returns
 */
const RecordsUpdating = ({ navigation, route }) => {
    console.log("ComWaterMeterEquipRecordsUpdating pageMeta from nav:", route.params.pageMeta);
    console.log("ComWaterMeterEquipRecordsUpdating projMeta from nav:", route.params.projMeta);
    const projSubclass = route.params.projMeta.subclass; // 1表, 2书

    const { queryKwd, id: recordPubid, name: recordName, formType } = route.params.pageMeta; // recordPubid is the alias of id
    const saveButtonIcon = "content-save-all-outline";
    const formEditableGlobal = checkPermits() ? (formType === 0 ? true : false) : false;

    // 使用memoize来优化
    const { setStore, getStore, clearStore, setStoreObject } = useMemo(() => {
        console.log("creatMMKVStore memo:", pageMainKey.store, queryKwd, recordPubid);
        return creatMMKVStore(`${pageMainKey.store}/${queryKwd}/${recordPubid}`);
    }, [queryKwd, recordPubid]);

    const storedValueToFormValue = useRef(makeStoredValueToFormValue(getStore));
    const getClientCversion = useRef(makeGetClientCversion(() => getStore("mainCversion"), () => getStore("subCversion")));

    // 客户端cversion = `${mainCversion}.${subCversion}`
    (getStore("mainCversion") === undefined) ? setStore("mainCversion", 0) : undefined; // 客户端主版本号
    (getStore("subCversion")  === undefined) ? setStore("subCversion",  0) : undefined; // 客户端次版本号, 修改一次自增一次
    //let mainCversion = getStore("mainCversion"); // 主版本号, 与服务端cversion对应
    const subCversionRef = useRef(getStore("subCversion"));

    // 新组件需要修改!!
    const bottomBarLabels = { ok: "提交" };                    // 底部按钮显示文字
    const [snackBarMessage, setSnackBarMessage] = useState("更新表单遇到错误"); // 下方错误提示文字内容
    const [
        waterTypeRadioState,
        accuracyLevelRadioState,
        equipStateRadioState,
        calibrateUnitRadioState,
        monitoredRadioState,
        setWaterTypeRadioState,
        setAccuracyLevelRadioState,
        setEquipStateRadioState,
        setCalibrateUnitRadioState,
        setMonitoredRadioState,
        resetSelectorStates
    ] = selectorStates(useShallow(state => [
        state.waterTypeRadio,
        state.accuracyLevelRadio,
        state.equipStateRadio,
        state.calibrateUnit,
        state.monitoredRadio,
        state.setWaterTypeRadio,
        state.setAccuracyLevelRadio,
        state.setEquipStateRadio,
        state.setCalibrateUnit,
        state.setMonitoredRadio,
        state.resetStates,
    ])); // radio组件状态

    const [confimDialogConfig, setConfirmDialogConfig] = useState({});       // 对话框信息, {title, text, okLabel, onOK}, 但显示对话框的变量在showConfirm

    // 新组件不需改动
    const [screenTitle,      setScreenTitle]      = useState(route.params.pageMeta.name); // 屏幕上方导航栏标题
    const [okButtonLoading,  setOkButtonLoading]  = useState(false);            // 右下方按钮图标是否旋转
    const [okButtonDisabled, setOkButtonDisabled] = useState(!checkPermits());  // 右下方按钮是否禁用
    const [showSnackbar,     setShowSnackbar]     = useState(false);            // 下方是否显示错误通知
    const [showConfirm,      setShowConfirm]      = useState(false);            // 是否显示确认对话框
    const [loadingIndicatorVisible, setLoadingIndicatorVisible] = useState(false); // 是否显示loading
    const delQueryInfo = useRef({ id: recordPubid, name: recordName });         // 用于删除本记录的查询变量

    // 根据子类型选择不同的枚举
    const equipStateEnum = projSubclass === 1 ? waterMeterEquipStateEnum4Table : waterMeterEquipStateEnum;

    const waterTypeStateDataProviderRef = useRef(waterSourceTypeEnum);
    const accuracyLevelStateDataProviderRef = useRef(waterMeterAccuracyLevelEnum);
    const equipStateStateDataProviderRef = useRef(equipStateEnum);
    const monitoredStateDataProviderRef = useRef(monitoredStateEnum);
    const calibrateUnitStateDataProviderRef = useRef(waterMeterCalibrateUnitEnum);

    const onRecordSelectSuccess = (data) => { // data: {"id": 3, "leader": 0, "name": "DEPT-03", "superior": 0}
        // 注意, 进入onSuccess之前就意味着服务端版本比客户端版本新, 因此要更新客户端主版本号
        console.log("onRecordSelectSuccess:", data);
        if (data.STATUS === 0) {
            setStore("mainCversion", data.DATA.cversion);
            setStore("subCversion", 0);
            const formObjects  = {
                name:           String(data.DATA.name),
                manageId:       String(data.DATA.manageId),
                place:          String(data.DATA.place),
                waterType:      String(data.DATA.waterType),
                meterRange:     String(data.DATA.meterRange),
                typeSpec:       String(data.DATA.typeSpec),
                accuracyLevel:  String(data.DATA.accuracyLevel),
                factorySerial:  String(data.DATA.factorySerial),
                pipeDiameter:   String(data.DATA.pipeDiameter),
                calibrateUnit:  String(data.DATA.calibrateUnit),
                calibrateCycle: String(data.DATA.calibrateCycle),
                equipState:     String(data.DATA.equipState),
                monitored:      String(data.DATA.monitored || ""),
                others:         String(data.DATA.others),
                remarks:        String(data.DATA.remarks),
            };
            reset(formObjects);           // 重置react-form

            const storeObjects = checkPermits() && {
                ...formObjects,
                waterType:     radioIdToObject(waterTypeStateDataProviderRef.current,     data.DATA.waterType,     { id: 0, name: "" }),
                accuracyLevel: radioIdToObject(accuracyLevelStateDataProviderRef.current, data.DATA.accuracyLevel, { id: 0, name: "" }),
                calibrateUnit: radioIdToObject(calibrateUnitStateDataProviderRef.current, data.DATA.calibrateUnit, { id: 0, name: "" }),
                equipState:    radioIdToObject(equipStateStateDataProviderRef.current,    data.DATA.equipState,    { id: 0, name: "" }),
                monitored:     radioIdToObject(monitoredStateDataProviderRef.current,     data.DATA.monitored,     { id: 0, name: "" }),
            };
            storeObjects && setStoreObject(storeObjects, false); // 判断用户权限, 把服务端数据写到mmkv存储, 只有可写用户才需要本地存储, 不会首先清空老数据

            // 设置selector菜单状态
            setWaterTypeRadioState(storeObjects.waterType);
            setAccuracyLevelRadioState(storeObjects.accuracyLevel);
            setCalibrateUnitRadioState(storeObjects.calibrateUnit);
            setEquipStateRadioState(storeObjects.equipState);
            setMonitoredRadioState(storeObjects.monitored);

            // 设置屏幕标题
            (screenTitle !== formObjects.name) && setScreenTitle(formObjects.name);
        } else  {
            console.log("STATUS !== 0", data.STATUS);
            setSnackBarMessage(`表单数据拉取发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        }
    };
    const onRecordSelectError = (err) => {
        console.log("Record selecting error:", err);
    };
    const onRecordSelectSettled = (err) => {
        loadingIndicatorVisible && setLoadingIndicatorVisible(false);
    };
    const recordSelectQuery = recordSelectQueryMaker([queryKwd, "get", recordPubid], onRecordSelectSuccess, onRecordSelectError, onRecordSelectSettled);

    // 新组件需要重新定义!!
    const schema = Joi.object({
        name:           validatorBase.waterBalance.comWaterMeterEquip.name,
        manageId:       validatorBase.waterBalance.comWaterMeterEquip.textField,
        place:          validatorBase.waterBalance.comWaterMeterEquip.textField,
        waterType:      projSubclass === 1 ? validatorBase.waterBalance.comWaterMeterEquip.waterTypeUnrequired : validatorBase.waterBalance.comWaterMeterEquip.waterType,
        meterRange:     validatorBase.waterBalance.comWaterMeterEquip.meterRange,
        typeSpec:       validatorBase.waterBalance.comWaterMeterEquip.textField,
        accuracyLevel:  validatorBase.waterBalance.comWaterMeterEquip.accuracyLevel,
        factorySerial:  validatorBase.waterBalance.comWaterMeterEquip.textField,
        pipeDiameter:   projSubclass === 1 ? validatorBase.waterBalance.comWaterMeterEquip.intFieldUnrequired : validatorBase.waterBalance.comWaterMeterEquip.intField,
        calibrateUnit:  validatorBase.waterBalance.comWaterMeterEquip.calibrateUnit,
        calibrateCycle: validatorBase.waterBalance.comWaterMeterEquip.intField,
        equipState:     validatorBase.waterBalance.comWaterMeterEquip.equipState,
        monitored:      validatorBase.waterBalance.comWaterMeterEquip.monitored,
        others:         validatorBase.waterBalance.comWaterMeterEquip.textField,
        remarks:        validatorBase.waterBalance.comWaterMeterEquip.longTextField,
    });

    // 新组件需要重新定义defaultValues!!
    const {
        handleSubmit,
        control,
        resetField,
        reset,
        formState: { errors },
        //getValues,
    } = useForm({
        resolver: joiResolver(schema),
        //reValidateMode: "onBlur", // https://github.com/orgs/react-hook-form/discussions/10327
        defaultValues: { // 默认值在组件载入时会被更新, 这里的初始值正常情况下没有用处
            name:           "",
            manageId:       "",
            place:          "",
            waterType:      "",
            meterRange:     "",
            typeSpec:       "",
            accuracyLevel:  "",
            factorySerial:  "",
            pipeDiameter:   "",
            calibrateUnit:  "",
            calibrateCycle: "",
            equipState:     "",
            monitored:      "",
            others:         "",
            remarks:        "",
        },
    });

    // 新组件需要重新定义setDiagText!!
    const onHandleSubmit = (data) => {
        setOkButtonDisabled(true);
        setOkButtonLoading(true);
        dataFeeder({
            ...data,
            cversion: getClientCversion.current(),
            //name: `${data.manageId}(${data.place}/${radioIdToObject(waterTypeStateDataProviderRef.current, data.waterType)?.name || ""})`,
            //name: `${data.manageId}(${data.meterRange})`, // 服务端自动生成
        }); // append client cversion
        recordUpdateQuery.mutate();
    };

    // 新组件不需改动
    const onRecordUpdateSuccess = (data) => {
        console.log("response success, data: ", data);
        if(data.STATUS !== 0) {
            setSnackBarMessage(`表单更新发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        } else {
            // 设置确认框数据
            setConfirmDialogConfig({
                title: "更新表单",
                text: "表单信息更新成功!",
                okLabel: "确定",
                onOK: () => {
                    setShowConfirm(false);
                    recordSelectQuery.mutate();
                }});
            setShowConfirm(true);
        }
    };
    const onRecordUpdateError = (error) => {
        setSnackBarMessage(`表单更新发生错误, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const onRecordUpdateSettled = (data, error) => {
        setOkButtonDisabled(false);
        setOkButtonLoading(false);
    };
    const recordUpdateQuery = recordUpdateQueryMaker(dataFeeder, [queryKwd, "upd", recordPubid], onRecordUpdateSuccess, onRecordUpdateError, onRecordUpdateSettled);

    const recordDeleteOnSuccess = () => {
        clearStore();
        navigation.goBack();
    };
    const recordDeleteOnError = (error) => {
        setSnackBarMessage(`表单删除失败, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const recordDeleteQuery = recordDeleteQueryMaker([queryKwd, "del", recordPubid], recordDeleteOnSuccess, recordDeleteOnError);

    const deleteAlert = () =>{
        return (
            Alert.alert("删除表单", `删除后不可撤销, 请确认是否删除: ${delQueryInfo.current?.name}?`, [
                { text: "取消" },
                { text: "确定", onPress: () => recordDeleteQuery.mutate() },
            ])
        );
    };

    const clearCache = () => {
        return (
            Alert.alert("清空本地缓存", "1. 用于未知原因导致数据无法提交的情况\r\n2. 未提交的内容将会丢失, 请做好数据截图备份\r\n3. 已提交到服务端的数据不受影响\r\n4. 确认清空请点击“确认”按钮", [
                { text: "取消" },
                { text: "确定", onPress: () => { clearStore(); navigation.goBack(); } },
            ])
        );
    };

    useEffect(() => {
        // 优化方案: 根据用户权限进行mutate, 只有可写用户才需要本地存储
        const localCversion = getClientCversion.current();
        const remoteCversion = route.params.pageMeta.cversion;
        console.log("localCversion:", localCversion, "remoteSversion:", remoteCversion);

        const restoreLocalData = () => {
            const formObjects = { // 默认值用户表单的数据显示
                name:           storedValueToFormValue.current("name"),
                manageId:       storedValueToFormValue.current("manageId"),
                place:          storedValueToFormValue.current("place"),
                waterType:      storedValueToFormValue.current("waterType"), // 偶然遇到一次默认值变成空对象, 但不知道怎么来的
                meterRange:     storedValueToFormValue.current("meterRange"),
                typeSpec:       storedValueToFormValue.current("typeSpec"),
                accuracyLevel:  storedValueToFormValue.current("accuracyLevel"),
                factorySerial:  storedValueToFormValue.current("factorySerial"),
                pipeDiameter:   storedValueToFormValue.current("pipeDiameter"),
                calibrateUnit:  storedValueToFormValue.current("calibrateUnit", 2), // 2是初始化赋予的默认值
                calibrateCycle: storedValueToFormValue.current("calibrateCycle"),
                equipState:     storedValueToFormValue.current("equipState"),
                monitored:      storedValueToFormValue.current("monitored"),
                others:         storedValueToFormValue.current("others"),
                remarks:        storedValueToFormValue.current("remarks"),
            };
            reset(formObjects); // 重置react-form数据
            // 设置selector数据
            setWaterTypeRadioState(ifTruthLet(getStore("waterType"), isNullness, () => {return {id: 0, name: ""};}, value => value));
            setAccuracyLevelRadioState(ifTruthLet(getStore("accuracyLevel"), isNullness, () => {return {id: 0, name: ""};}, value => value));
            // 注意: calibrateUnit使用了默认值2
            setCalibrateUnitRadioState(ifTruthLet(getStore("calibrateUnit"), isNullness, () => {return {id: 2, name: "月"};}, value => value));
            setEquipStateRadioState(ifTruthLet(getStore("equipState"), isNullness, () => {return {id: 0, name: ""};}, value => value));
            setMonitoredRadioState(ifTruthLet(getStore("monitored"), isNullness, () => {return {id: 0, name: ""};}, value => value));

        };

        if (localCversion === 0 && remoteCversion > 0) {
            console.log("Local cversion === 0, serve may have something nontrivial initialized, query mutate!");
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        } else if (isServerDataNewer(localCversion, remoteCversion) && subCversionRef.current > 0) { // 服务端有更新数据, 但本地有修改
            console.log("Client cversion fell behind the server cversion, someone might modified the database, choose to use the data version!");
            Alert.alert("注意", "由于其他人向服务器提交了数据, 服务端数据比本地数据新, 请选择使用哪份数据?", [
                { text: "使用本地数据", onPress: () => { setStore("mainCversion", remoteCversion); restoreLocalData(); } },
                { text: "使用服务端数据", onPress: () => { setLoadingIndicatorVisible(true); recordSelectQuery.mutate(); } },
            ]);
        } else if (isServerDataNewer(localCversion, remoteCversion)) { // localCversion==0时要mutate是因为服务端可能在创建记录时生成了必要数据
            console.log("Server cversion may be newer, query mutate:", localCversion, remoteCversion);
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        }
        else {
            console.log("Local cversion >= server cversion, query will not mutate!");
            restoreLocalData();
        }
    }, []);

    // config for Inputs mapping, type: "PLAIN", "RADIO", "CHECK", "TIME", "DATE", "RANGE"
    // remarks不需配置
    const FieldsConfig_1_book = [
        {
            inputs: [
                //{ name: "name",           label: "表单名称",        unit: "",   type: "PLAIN", editable: true, placeholder: "", multiline: true,  },
                { name: "manageId",       label: "管理编号",        unit: "",   type: "PLAIN", editable: true, placeholder: "", },
                { name: "place",          label: "所在位置",        unit: "",   type: "PLAIN", editable: true, placeholder: "", },
                { name: "waterType",      label: "水源类别",        unit: "",   type: "RADIO", editable: true, placeholder: "", dataProvider: waterTypeStateDataProviderRef, selectorState: waterTypeRadioState, setSelectorState: setWaterTypeRadioState, },
                { name: "meterRange",     label: "计量范围",        unit: "",   type: "PLAIN", editable: true, placeholder: "", },
                { name: "typeSpec",       label: "型号规格",        unit: "",   type: "PLAIN", editable: true, placeholder: "", },
                { name: "accuracyLevel",  label: "准确度等级",      unit: "",   type: "RADIO", editable: true, placeholder: "", dataProvider: accuracyLevelStateDataProviderRef, selectorState: accuracyLevelRadioState, setSelectorState: setAccuracyLevelRadioState, },
                { name: "factorySerial",  label: "出厂编号",        unit: "",   type: "PLAIN", editable: true, placeholder: "", },
                { name: "pipeDiameter",   label: "安装管道口径",     unit: "mm", type: "PLAIN", editable: true, placeholder: "", },
                { name: "calibrateUnit",  label: "检定/校准时间单位", unit: "",   type: "RADIO", editable: true, placeholder: "", dataProvider: calibrateUnitStateDataProviderRef, selectorState: calibrateUnitRadioState, setSelectorState: setCalibrateUnitRadioState, },
                { name: "calibrateCycle", label: "检定周期/校准间隔", unit: "",   type: "PLAIN", editable: true, placeholder: "", },
                { name: "equipState",     label: "状态",            unit: "",   type: "RADIO", editable: true, placeholder: "", dataProvider: equipStateStateDataProviderRef, selectorState: equipStateRadioState, setSelectorState: setEquipStateRadioState, },
                //{ name: "monitored",      label: "是否接入企业用水在线监测平台", unit: "", type: "RADIO", editable: true, placeholder: "", dataProvider: monitoredStateDataProviderRef, selectorState: monitoredRadioState, setSelectorState: setMonitoredRadioState, },
            ],
        }
    ];
    const FieldsConfig_1_table = [
        {
            inputs: [
                //{ name: "name",           label: "表单名称",        unit: "",   type: "PLAIN", editable: true, placeholder: "", multiline: true,  },
                { name: "manageId",       label: "水表编号",        unit: "",   type: "PLAIN", editable: true, placeholder: "", },
                //{ name: "place",          label: "所在位置",        unit: "",   type: "PLAIN", editable: true, placeholder: "", },
                //{ name: "waterType",      label: "水源类别",        unit: "",   type: "RADIO", editable: true, placeholder: "", dataProvider: waterTypeStateDataProviderRef, selectorState: waterTypeRadioState, setSelectorState: setWaterTypeRadioState, },
                { name: "meterRange",     label: "计量范围",        unit: "",   type: "PLAIN", editable: true, placeholder: "", },
                { name: "typeSpec",       label: "水表型号",        unit: "",   type: "PLAIN", editable: true, placeholder: "", },
                { name: "accuracyLevel",  label: "水表精度",        unit: "",   type: "RADIO", editable: true, placeholder: "", dataProvider: accuracyLevelStateDataProviderRef, selectorState: accuracyLevelRadioState, setSelectorState: setAccuracyLevelRadioState, },
                //{ name: "factorySerial",  label: "出厂编号",        unit: "",   type: "PLAIN", editable: true, placeholder: "", },
                //{ name: "pipeDiameter",   label: "安装管道口径",     unit: "mm", type: "PLAIN", editable: true, placeholder: "", },
                { name: "calibrateUnit",  label: "校验时间单位",     unit: "", type: "RADIO", editable: true, placeholder: "", dataProvider: calibrateUnitStateDataProviderRef, selectorState: calibrateUnitRadioState, setSelectorState: setCalibrateUnitRadioState, },
                { name: "calibrateCycle", label: "校验时间",        unit: "", type: "PLAIN", editable: true, placeholder: "", },
                { name: "equipState",     label: "运行是否正常",            unit: "",   type: "RADIO", editable: true, placeholder: "", dataProvider: equipStateStateDataProviderRef, selectorState: equipStateRadioState, setSelectorState: setEquipStateRadioState, },
                { name: "monitored",      label: "是否接入企业用水在线监测平台", unit: "", type: "RADIO", editable: true, placeholder: "", dataProvider: monitoredStateDataProviderRef, selectorState: monitoredRadioState, setSelectorState: setMonitoredRadioState, },
            ],
        }
    ];

    const FieldsConfig_2_book = [];
    const FieldsConfig_2_table = [];

    const FieldsConfig_3_book = [
        {
            inputs: [
                { name: "others", label: "其它", unit: "", type: "PLAIN", editable: true, },
            ]
        },
    ];
    const FieldsConfig_3_table = [
        {
            inputs: [
                //{ name: "others", label: "其它", unit: "", type: "PLAIN", editable: true, },
            ]
        },
    ];

    //const FieldsConfig = [...FieldsConfig_1, ...FieldsConfig_2, ...FieldsConfig_3];
    let FieldsConfig = [];
    switch(projSubclass) {
        case 1: // 表
            FieldsConfig = [...FieldsConfig_1_table, ...FieldsConfig_2_table, ...FieldsConfig_3_table];
            break;
        case 2: // 书
            FieldsConfig = [...FieldsConfig_1_book, ...FieldsConfig_2_book, ...FieldsConfig_3_book];
            break;
        default:
            console.warn("Unknown subclass:", projSubclass);
            FieldsConfig = [];
            break;
    }

    return (
        <View style={{ flex: 1 }}>

            <HeaderBar
                //title={screenTitle}
                navigation={navigation}
                goBackCallback={() => {
                    callOneByOne(
                        //() => { _mainCversionGetter=undefined; _subCversionGetter=undefined;},
                        resetSelectorStates);
                    /*callOneByOne(clearStore, resetSelectorStates)*/  // 返回前清空mmkv存储因为临时数据不再需要, 重置状态使得页面切换后不会使用前面的数据
                }}
                menuItemArray={[{ title: "删除表单", action: deleteAlert }, { title: "清空缓存", action: clearCache }]}
            />

            <ScreenWrapper contentContainerStyle={styles.container}>

                <View style={styles.formEntry}>
                    <FormListNodesMapper
                        fieldsConfig={FieldsConfig}
                        subCversionRef={subCversionRef}
                        control={control}
                        setStore={setStore}
                        resetField={resetField}
                        errors={errors}
                        setSnackBarMessage={setSnackBarMessage}
                        setShowSnackbar={setShowSnackbar}
                        //formDisabledGlobal={formDisabledGlobal}
                        editable={formEditableGlobal}
                        rowStyle={styles.rowContainer}
                    />

                    <Divider/>
                    {formType === 0 && <ControlledTextInput
                        name="remarks"
                        rowLabel="备注"
                        control={control}
                        placeholder=""
                        onChangeText={(text) => {
                            subCversionRef.current++;
                            setStore("subCversion", subCversionRef.current);
                            setStore("remarks", text);
                        }}
                        onClearText={() => {
                            subCversionRef.current++;
                            setStore("subCversion", subCversionRef.current);
                            setStore("remarks", "");
                            resetField("remarks", { defaultValue: "" });
                        }}
                        editable={formEditableGlobal}
                        mode="flat"
                        multiline={true}
                        formError={errors}
                        style={styles.rowContainerLongText}
                        //disabled={formDisabledGlobal}
                        required={false}
                    />}

                    <Divider bold={true}/>

                </View>

                <View style={styles.button}>
                    <Button mode="contained"
                        disabled={okButtonDisabled}
                        loading={okButtonLoading}
                        icon={saveButtonIcon}
                        onPress={handleSubmit(onHandleSubmit, (formErr) => onPreSubmitError(formErr, getStore, "ComWaterMeterEquipRecordsUpdating"))}
                    >{bottomBarLabels.ok}</Button>
                </View>
            </ScreenWrapper>

            <LoadingIndicator
                title={""}
                message={"加载中....."}
                visible={loadingIndicatorVisible}
                onClose={()=>setLoadingIndicatorVisible(false)}
            />

            <Snackbar
                style={styles.snackBar}
                visible={showSnackbar}
                onDismiss={() => setShowSnackbar(false)}
                onIconPress={() => setShowSnackbar(false)}
                duration={Snackbar.DURATION_LONG}
            >
                {snackBarMessage}
            </Snackbar>

            <DialogToConfirm
                visible={showConfirm}
                title={confimDialogConfig.title ? confimDialogConfig.title : ""}
                text={confimDialogConfig.text ? confimDialogConfig.text : ""}
                onOK={confimDialogConfig.onOK ? confimDialogConfig.onOK : () => setShowConfirm(false)}
                okBtnLabel={confimDialogConfig.okLabel ? confimDialogConfig.okLabel : "确认"}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    rowContainer:{
        firstColumn: {
            flex: 1,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 1,
        // /borderWidth: 1,
        // height: 40,
        },
    },
    rowContainerLongText: {
        firstColumn: {
            flex: 1,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 4,
            // /borderWidth: 1,
            // height: 40,
        },
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        width: "80%",
        alignSelf: "center",
    },
    formEntry: {
        margin: 8,
    },
    snackBar: {
        flexDirection: "column",
    },
});

export default RecordsUpdating;
