import { <PERSON>ert, PermissionsAndroid, Platform, } from "react-native";
import ReactNativeB<PERSON>bUtil from "react-native-blob-util";
import { getSystemVersion } from "react-native-device-info";
import RNFS from "react-native-fs";
import { DOWNLOAD_URL_ANDROID } from "../../config";
import log from "../logging";

export const requestWriteStoragePermission = async () => {
    try {
        if (Platform.OS === "android") {
            const deviceVersion = getSystemVersion();
            let granted = PermissionsAndroid.RESULTS.DENIED;
            if (deviceVersion >= 13) {
                granted = PermissionsAndroid.RESULTS.GRANTED;
            } else {
                granted = await PermissionsAndroid.request(
                    PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE
                );
            }
            if (granted) {
                return true;
            }
        } else {
        // On iOS, permissions are typically not required for accessing the photo library
            log.debug("iOS platform: iOS app cannot be sideloaded.");
            return false;
        }
    } catch (error) {
        log.error("Error checking/requesting storage permission:", error);
        return null;
    }
};


export const downloadAPK = async (onUpdateProgress, onDownloadSuccess, onDownloadFailed, onPermisionNotAllowed) => {
    const hasPermission = await requestWriteStoragePermission();
    if (!hasPermission) {
        onPermisionNotAllowed();
        log.debug("WRITE_EXTERNAL_STORAGE permission not allowed to download the APK!");
        return;
    }

    const apkUrl = DOWNLOAD_URL_ANDROID; // 替换为你的APK下载链接
    const localApkPath = `${ReactNativeBlobUtil.fs.dirs.DownloadDir}/lcmp.apk`; // 下载路径
    const android = ReactNativeBlobUtil.android;
    RNFS.exists(localApkPath).then(() =>{
        RNFS.unlink(localApkPath)
            .then(() => {
                log.debug("File deleted:", localApkPath);
            })
            .catch((err) => {
                log.error(err.message);
            });
    });
    log.debug(`Begin to download the updating apk from ${apkUrl}`);

    ReactNativeBlobUtil.config({
        addAndroidDownloads: {
            useDownloadManager: true,
            title: "App Downloading",
            description: "An APK that will be installed",
            mime: "application/vnd.android.package-archive",
            mediaScannable: true,
            notification: true,
            path: localApkPath,
            fileCache: false,
        }
    })
        .fetch("GET", apkUrl)
        // update every 1%
        .progress({count: 1}, (received, total) => {
            //console.log("Downloading progress: ", received / total);
            onUpdateProgress(received / total);
        })
        .then((res) => {
            /*Alert.alert(
                "下载完毕",
                "更新包下载完毕, 请点击安装!",
                [
                    {
                        text: "安装",
                        onPress: () => {
                            onDownloadSuccess();
                            android.actionViewIntent(res.path(), "application/vnd.android.package-archive");
                        },
                    },
                    { text: "取消", style: "cancel" },
                ],
            );*/
            onDownloadSuccess();
            log.debug("APK downloaded successfully, local path:", res.path());
            android.actionViewIntent(res.path(), "application/vnd.android.package-archive");
        })
        .catch((err) => {
            onDownloadFailed();
            log.error(err);
            Alert.alert("更新失败", "下载更新包失败, 请联系客服或者重试!");
        });
};
