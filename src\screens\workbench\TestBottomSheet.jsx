import React, { useCallback, useRef } from "react";
import { Text } from "react-native-paper";
import { StyleSheet, View } from "react-native";
import ScreenWrapper from "../ScreenWrapper";

import BottomSheet, { BottomSheetView } from "@gorhom/bottom-sheet";

/**
 * https://ui.gorhom.dev/components/bottom-sheet/usage
 * @param {Object} args
 * @returns
 */
const TestBottomSheet = ({ navigation }) => {

    const bottomSheetRef = useRef(null);
    const handleSheetChanges = useCallback((index) => {
        console.log("handleSheetChanges", index);
    }, []);

    return (
        <ScreenWrapper contentContainerStyle={styles.container}>

            <View style={styles.container}>
                <BottomSheet
                    ref={bottomSheetRef}
                    snapPoints={["30%", "50%", "70%"]}
                    onChange={handleSheetChanges}
                >
                    <BottomSheetView style={styles.contentContainer}>
                        <Text>Awesome 🎉</Text>
                    </BottomSheetView>
                </BottomSheet>
            </View>

        </ScreenWrapper>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        width: "100%", // 关键
        padding: 24,
        backgroundColor: "grey",
    },
    contentContainer: {
        flex: 1,
        alignItems: "center",
    }
});

export default TestBottomSheet;
