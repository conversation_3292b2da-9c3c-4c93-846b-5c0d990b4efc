import { useMutation } from "@tanstack/react-query";
import { httpClient } from "../services/http";
import log from "../services/logging";


const RETRY_CODES = [401, 501, 502, 503, 504];
const RETRY_METHODS = ["DELETE"];
const RETRY_INTERVALS = [2000, 3000, 5000];
const RETRY_LIMIT = RETRY_INTERVALS.length;

/**
 * Return a `useMutation` query object. The query result will be stored in MMKV
 * @param {Object} arg
 * @param {string} arg.query the query key for react-query
 * @param {string} arg.url the http request path which will be responed by the server
 * @returns
 */
export const makeDeletingClient = ({ query, url }) => {
    /**
     * 向服务端发起一个delete请求.
     * 注意: 这个版本的ky客户端含有重连,
     * 并且在客户端中将token设置到标头: request.headers.set("Authorization", token)
     * @param {()=>{}} onSuccess token string
     * @param {()=>{}} onError callback function
     * @param {()=>{}} onSettled callback function
     */
    return (delKey, onSuccess, onError, onSettled) => {
        return useMutation({
            mutationKey: [query, delKey],
            mutationFn: async () => {
                const response = await httpClient.delete(`${url}/${delKey}`);
                const json = await response.json();
                log.debug("Query %s on %s/%s, mutationFn receive data: %s", query, url, delKey, json);
                return json; // 注意不要return await!
            },

            onSuccess: (data, variables, context) => {
                log.debug("makeDeletingClient onSuccess, query: %s, url: %s, data: %s, vars, %s, context: %s", query, url, data, variables, context);
                onSuccess?.(data);
            },
            onError: (error, variables, context) => {
                log.debug("makeDeletingClient onError, error: %s, query: %s, url: %, vars, %s, context: %s", error, query, url, variables, context);
                onError?.(error);
            },
            onSettled: (data, error, variables, context) => {
                log.debug("makeDeletingClient onSettled, query: %s, url: %s, var: %s, context: %s", query, url, variables, context);
                onSettled?.(data, error);
            },

            // 逻辑上, 首次失败后, 立即检测retryDelay, 经延迟后再检测retry触发重试
            // 注意, 有Ky的retry有bug, 这里使用React Query的retry!!!!!!
            retry: (failureCount, error) => { // failureCount from 0
                log.debug("failureCount: %s", failureCount);
                if (failureCount < RETRY_LIMIT
                    && error.name === "HTTPError"
                    && RETRY_CODES.includes(error.response.status)
                    && RETRY_METHODS.includes(error.request.method)) {
                    return true;
                } else {
                    return false;
                }
            },
            retryDelay: (attemptIndex) => RETRY_INTERVALS[attemptIndex], // attemptIndex from 0
        });
    };
};
