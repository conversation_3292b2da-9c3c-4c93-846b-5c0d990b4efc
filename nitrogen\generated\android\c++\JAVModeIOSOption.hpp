///
/// JAVModeIOSOption.hpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON> @ Margelo
///

#pragma once

#include <fbjni/fbjni.h>
#include "AVModeIOSOption.hpp"

namespace margelo::nitro::react_native_audio_recorder_player {

  using namespace facebook;

  /**
   * The C++ JNI bridge between the C++ enum "AVModeIOSOption" and the the Kotlin enum "AVModeIOSOption".
   */
  struct JAVModeIOSOption final: public jni::JavaClass<JAVModeIOSOption> {
  public:
    static auto constexpr kJavaDescriptor = "Lcom/margelo/nitro/reactnativeaudiorecorderplayer/AVModeIOSOption;";

  public:
    /**
     * Convert this Java/Kotlin-based enum to the C++ enum AVModeIOSOption.
     */
    [[maybe_unused]]
    [[nodiscard]]
    AVModeIOSOption toCpp() const {
      static const auto clazz = javaClassStatic();
      static const auto fieldOrdinal = clazz->getField<int>("_ordinal");
      int ordinal = this->getFieldValue(fieldOrdinal);
      return static_cast<AVModeIOSOption>(ordinal);
    }

  public:
    /**
     * Create a Java/Kotlin-based enum with the given C++ enum's value.
     */
    [[maybe_unused]]
    static jni::alias_ref<JAVModeIOSOption> fromCpp(AVModeIOSOption value) {
      static const auto clazz = javaClassStatic();
      static const auto fieldGAMECHATAUDIO = clazz->getStaticField<JAVModeIOSOption>("GAMECHATAUDIO");
      static const auto fieldVIDEORECORDING = clazz->getStaticField<JAVModeIOSOption>("VIDEORECORDING");
      static const auto fieldVOICECHAT = clazz->getStaticField<JAVModeIOSOption>("VOICECHAT");
      static const auto fieldVIDEOCHAT = clazz->getStaticField<JAVModeIOSOption>("VIDEOCHAT");
      
      switch (value) {
        case AVModeIOSOption::GAMECHATAUDIO:
          return clazz->getStaticFieldValue(fieldGAMECHATAUDIO);
        case AVModeIOSOption::VIDEORECORDING:
          return clazz->getStaticFieldValue(fieldVIDEORECORDING);
        case AVModeIOSOption::VOICECHAT:
          return clazz->getStaticFieldValue(fieldVOICECHAT);
        case AVModeIOSOption::VIDEOCHAT:
          return clazz->getStaticFieldValue(fieldVIDEOCHAT);
        default:
          std::string stringValue = std::to_string(static_cast<int>(value));
          throw std::invalid_argument("Invalid enum value (" + stringValue + "!");
      }
    }
  };

} // namespace margelo::nitro::react_native_audio_recorder_player
