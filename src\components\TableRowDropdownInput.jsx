import React, { useState } from "react";
import { StyleSheet } from "react-native";
import { DataTable, Text, TextInput, useTheme } from "react-native-paper";
import CellTextInput from "./CellTextInput";
import PropTypes from "prop-types";
import RowContainer from "./RowContainer";
import DropdownMenu from "./DropdownMenu";

/**
 * DataTable中一个2列的记录行
 * @param {Object} arg
 * @param {string} arg.value
 * @param {function} arg.onClearText
 * @param {boolean} arg.disabled
 * @param {"flat" | "outlined"} arg.mode
 */

//// 改名: TableRowKeyboardInput, 其它的还有DropdownInput等
const TableRowDropdownInput = ({ rowLabel, inputValue, placeholder, onChangeText, onClearText, editable, dropdownConfig, ...props }) => {
    const theme = useTheme();
    const styles = StyleSheet.create({
        container: {
            //paddingHorizontal: 6,
            //borderWidth: 1,
            height: 60,
            //borderColor: "black",

        },
        firstColumn: {
            flex: 2,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 3,
            //borderWidth: 1,
            // height: 40,
        },
        input: {
            flex:1,
            width: "100%",
            backgroundColor: "transparent", //theme.colors.surface,
            fontSize: 18,
            margin: 1,
            //borderWidth: 1,
            //borderColor: "red",
        },
        error: {
            fontSize: 13,
            color: theme.colors.error,
            paddingTop: 8,
        },
    });

    return (
        <DataTable.Row style={styles.container}>
            <DataTable.Cell style={styles.firstColumn}>
                <Text style={styles.firstColumn.content}>
                    {rowLabel}
                </Text>
            </DataTable.Cell>
            <DataTable.Cell style={styles.secondColumn}>
                <RowContainer>
                    {/*<Text style={{ flex: 1, fontSize: 18, borderWidth: 1, textAlign: "left", textAlignVertical: "center", paddingLeft: 10}}>
                        {rowLabel}
                    </Text>*/}
                    <TextInput
                        returnKeyType="next"
                        mode="outlined" // 不能使用flat, 否则disable状态时下横向不能隐藏
                        value={inputValue}
                        //placeholder={placeholder}
                        //onChangeText={onChangeText}
                        underlineColor="transparent"
                        outlineColor={theme.colors.elevation.level0}
                        placeholderTextColor={theme.colors.elevation.level5}
                        backgroundColor={theme.colors.surface}
                        editable={false}
                        autoCapitalize="none"
                        autoComplete="off"
                        textContentType="none"
                        //onClearText={onClearText}
                        style={styles.input}
                        {...props}
                    />
                    <DropdownMenu style={{ flex: 1 }} editable={editable} dropdownConfig={dropdownConfig}/>
                </RowContainer>
            </DataTable.Cell>
        </DataTable.Row>
    );
};



export default TableRowDropdownInput;
