///
/// AVEncoderAudioQualityIOSType.hpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc Rousavy @ Margelo
///

#pragma once

#include <cmath>
#if __has_include(<NitroModules/JSIConverter.hpp>)
#include <NitroModules/JSIConverter.hpp>
#else
#error NitroModules cannot be found! Are you sure you installed NitroModules properly?
#endif
#if __has_include(<NitroModules/NitroDefines.hpp>)
#include <NitroModules/NitroDefines.hpp>
#else
#error NitroModules cannot be found! Are you sure you installed NitroModules properly?
#endif

namespace margelo::nitro::react_native_audio_recorder_player {

  /**
   * An enum which can be represented as a JavaScript enum (AVEncoderAudioQualityIOSType).
   */
  enum class AVEncoderAudioQualityIOSType {
    MIN      SWIFT_NAME(min) = 0,
    LOW      SWIFT_NAME(low) = 32,
    MEDIUM      SWIFT_NAME(medium) = 64,
    HIGH      SWIFT_NAME(high) = 96,
    MAX      SWIFT_NAME(max) = 127,
  } CLOSED_ENUM;

} // namespace margelo::nitro::react_native_audio_recorder_player

namespace margelo::nitro {

  using namespace margelo::nitro::react_native_audio_recorder_player;

  // C++ AVEncoderAudioQualityIOSType <> JS AVEncoderAudioQualityIOSType (enum)
  template <>
  struct JSIConverter<AVEncoderAudioQualityIOSType> final {
    static inline AVEncoderAudioQualityIOSType fromJSI(jsi::Runtime& runtime, const jsi::Value& arg) {
      int enumValue = JSIConverter<int>::fromJSI(runtime, arg);
      return static_cast<AVEncoderAudioQualityIOSType>(enumValue);
    }
    static inline jsi::Value toJSI(jsi::Runtime& runtime, AVEncoderAudioQualityIOSType arg) {
      int enumValue = static_cast<int>(arg);
      return JSIConverter<int>::toJSI(runtime, enumValue);
    }
    static inline bool canConvert(jsi::Runtime&, const jsi::Value& value) {
      if (!value.isNumber()) {
        return false;
      }
      double integer;
      double fraction = modf(value.getNumber(), &integer);
      if (fraction != 0.0) {
        // It is some kind of floating point number - our enums are ints.
        return false;
      }
      // Check if we are within the bounds of the enum.
      return integer >= 0 && integer <= 4;
    }
  };

} // namespace margelo::nitro
