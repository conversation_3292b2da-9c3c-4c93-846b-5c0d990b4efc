import { joi<PERSON>esolver } from "@hookform/resolvers/joi";
import "fast-text-encoding";
import <PERSON><PERSON> from "joi";
import React, { useEffect, useRef, useState } from "react";
import { useForm, useWatch } from "react-hook-form";
import { StyleSheet, View } from "react-native";
import { Appbar, Divider, Snackbar, useTheme } from "react-native-paper";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import BottomBarButton from "../../components/BottomBarButton";
import ControlledRadioInputWithQuery from "../../components/ControlledRadioInputWithQuery";
import ControlledTextInput from "../../components/ControlledTextInput";
import { DialogToConfirm } from "../../components/DialogToConfirm";
import HeaderBar from "../../components/HeaderBar";
import ScreenWrapper from "../ScreenWrapper";
//import ControlledCheckboxInputWithQuery from "../../components/ControlledCheckboxInputWithQuery";

import { creatMMKVStore } from "../../services/local-storage";
import { identity, isEmptyObject, makeDataFeeder, whenLet } from "../../utils";
import { validatorBase } from "../../utils/validatorBase";

// 新组件需要重新!!
import { makeRequestInsertProjbase as insertQueryClient } from "../../api/insertingQueries";
import { ORG_PROJ_BASE_ADD as pageMainKey } from "../../config/keysConfig"; // 用于页面信息的提交
//import { makeRequestListingDepartments as allDeptsQueryClient } from "../../api/listingQueries";
//import { makeRequestListingUsers       as allUsersQueryClient } from "../../api/listingQueries";
import { useShallow } from "zustand/shallow";
import { projbaseInsertStates as selectorStates } from "../../hooks/selectorStates";
import { useRerender } from "../../hooks/useRerender";
import { onPreSubmitError } from "../../utils/screens";


// 在mmkv中存储所有控件的数据, 新组件需要重新设置
const { setStore, getStore, clearStore, setStoreObject } = creatMMKVStore(pageMainKey.store);
const dataFeeder = makeDataFeeder();
// 数据规范要求服务端数据包含id为0的默认值, 同时mmkv存储的是一个对象, 因此以下几行不再需要
//const superiorReplacer = makeReplacer("superior", "name", "id"); // 数据中superior对应的值, 将它到对象数组中去匹配name键, 若成功, superior的值替换成name所在对象的id的值
//const deptDefaults = { id: 0, name: "无上级部门" };                // 需要添加的默认部门占位
//const deptLookUpKey = "id";                                      // radio按钮根据值方向查找的键值
//const threadFunctions = [superiorReplacer.replace];
//let render = 0;

/**
 * 暂时包含三个字段: 姓名, 手机, 状态
 * 需要注意其中包含4种数据/状态:
 * 1. InputText手动输入的数据, 在useForm中申明, 直接由react-hook-form的controller处理, 以便减少渲染.
 * 2. 拉选框状态数据, 在selectorStates.js中预先定义, 包括radio和checkbox两种, 需要通过zustand进行全局状态管理, 其中包含的是复合数据, radio的是对象{id, name}, checkbox的是对象数组[{id, name}*], 控件要求包含id和name.
 * 3. mmkv存储数据, 在此通过creatMMKVStore创建, 用于存储当前的控件数据以及载入时恢复原先状态, 其中保存的InputText数据内容与react-form的相同, 拉选框数据内容与zustand的相同.
 * 4. 完整的react-hook-form数据, 其中包含第一种数据, radio拉选框数据只包含其id, checkbox的数据则是id的数组. hook-form数据转换成FormData后由http发送给服务端.
 * @param {Object} navigation
 * @returns
 */
const ProjectBaseInserting = ({ navigation }) => {
    // render++;
    const { bottom, left, right } = useSafeAreaInsets();
    const theme = useTheme();
    const height = theme.isV3 ? 80 : 56;

    const rerender = useRerender();

    // 新组件需要修改!!
    const bottomBarLabels = { ok: "添加项目库", cancel: "取消" };                // 底部按钮显示文字
    const [confimDialogConfig, setConfirmDialogConfig] = useState({});       // 对话框信息, {title, text, okLabel, onOK}, 但显示对话框的变量在showConfirm
    const [snackBarMessage, setSnackBarMessage] = useState("添加项目库遇到错误"); // 下方错误提示文字内容
    // 拉选框组件使用全局状态
    const [
        classRadioState,
        subClassRadioState,
        industryRadioState,
        difficultyRadioState,
        setClassRadioState,
        setSubClassRadioState,
        setIndustryRadioState,
        setDifficultyRadioState,
    ] = selectorStates(useShallow(state => [state.classRadio, state.subClassRadio, state.industryRadio, state.difficultyRadio, state.setClassRadio, state.setSubClassRadio, state.setIndustryRadio, state.setDifficultyRadio])); // radio和check组件状态

    // 新组件不需改动
    const saveButtonIcon = "content-save-all-outline";
    const [okButtonLoading, setOkButtonLoading] = useState(false);           // 右下方按钮图标是否旋转
    const [okButtonDisabled, setOkButtonDisabled] = useState(false);         // 右下方按钮是否禁用
    const [cancelButtonDisabled, setCancelButtonDisabled] = useState(false); // 左下方按钮是否禁用
    const [showSnackbar, setShowSnackbar] = useState(false);                 // 下方是否显示错误通知
    const [showConfirm, setShowConfirm] = useState(false);                   // 是否显示确认对话框

    const classStateDataProviderRef      = useRef([{ id: 1, name: "水平衡" }, { id: 11, name: "零碳诊断" }]); // 用作类型状态选项的数据源
    const subClassStateDataProviderRef   = useRef([{ id: 1, name: "表" }, { id: 2, name: "书" },]);
    const industryStateDataProviderRef   = useRef([]); // 在useEffect中根据projClass填写
    const difficultyStateDataProviderRef = useRef([{ id: 1, name: "简单" }, { id: 2, name: "中等" }, { id: 3, name: "困难" }]); // 用作难度状态选项的数据源

    const classChecked = useRef(false); // 流程: 先拉选项目类型, 根据项目类型显示下方输入框

    const formDefaults  = useRef({
        name:       "",
        class:      0,
        subClass:   0,
        industry:   0,
        difficulty: 0,
    });
    const storeDefaults = useRef({
        name:       "",
        class:      { id: 0, name: ""},
        subClass:   { id: 0, name: ""},
        industry:   { id: 0, name: ""},
        difficulty: { id: 0, name: "" },
    });
    const selectorDefaults = useRef({
        class:      { id: 0, name: ""},
        subClass:   { id: 0, name: "" },
        industry:   { id: 0, name: ""},
        difficulty: { id: 0, name: "" },
    });

    // 新组件需要重新定义!!
    const schema = Joi.object({
        name:       validatorBase.projbaseName.required,
        class:      validatorBase.projbaseClass.unrequired,
        subClass:   validatorBase.projbaseSubClass.unrequired,
        industry:   validatorBase.projbaseIndustry.unrequired,
        difficulty: validatorBase.projbaseDifficulty.unrequired,
    });

    // 新组件需要重新定义defaultValues!!
    const {
        handleSubmit,
        control,
        resetField,
        reset,
        formState: { errors },
        //getValues,
    } = useForm({
        resolver: joiResolver(schema),
        //reValidateMode: "onBlur", // https://github.com/orgs/react-hook-form/discussions/10327
        defaultValues: {
            name:       whenLet(getStore("name"),           identity, ""),
            class:      whenLet(getStore("class")?.id,      identity, 0),
            subClass:   whenLet(getStore("subClass")?.id,   identity, 0),
            industry:   whenLet(getStore("industry")?.id,   identity, 0),
            difficulty: whenLet(getStore("difficulty")?.id, identity, 0),
        },
    });

    const projClass = Number(useWatch({ control, name: "class" }));

    useEffect(() => {
        if (projClass === 1) {
            industryStateDataProviderRef.current = ([{ id: 1, name: "工业" }, { id: 2, name: "服务业" },]);
        } else if (projClass === 11) {
            industryStateDataProviderRef.current = ([{ id: 3, name: "公共机构"} ,]); // 暂时只有公共机构 { id: 1, name: "工业" }
        }
        projClass > 0 && (classChecked.current = true);
        rerender();
    }, [projClass]);


    // 新组件需要重新定义setDiagText!!
    const onHandleSubmit = (data) => { // data: {"name": "mmm", "superior": 0}
        // 由于这里的data的版本可能先于mmkv的版本, 因此必须在此将data转储到一个引用地址, 以供query使用, 这就是dataFeeder的用处, 另外dataFeeder可以做一些数据预处理
        setCancelButtonDisabled(true);
        setOkButtonDisabled(true);
        setOkButtonLoading(true);
        dataFeeder(data);
        console.log("commit data:", data);
        submitQuery.mutate();
    };
    // 新组件不需改动
    const onCancel = () => {
        clearStore();
        // 每增加一个下拉框都要设置各自的默认值
        classChecked.current = false;
        reset(formDefaults.current);              // 重置react-form
        setStoreObject(storeDefaults.current);    // 重置mmkv存储
        setClassRadioState(selectorDefaults.current.class);
        setSubClassRadioState(selectorDefaults.current.subClass);
        setIndustryRadioState(selectorDefaults.current.industry);
        setDifficultyRadioState(selectorDefaults.current.difficulty);  // 重置radio状态
    };

    // 新组件不需改动
    const commitOnSuccess = (data) => {
        console.log("response success, data: ", data);
        if (data.STATUS !== 0) {
            setSnackBarMessage(`项目库添加发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        } else {
            // 设置对话框数据
            setConfirmDialogConfig({
                title: "添加项目库",
                text: "新项目库添加成功!",
                okLabel: "确定",
                onOK: () => {
                    clearStore();
                    // 每增加一个下拉框都要设置各自的默认值
                    reset(formDefaults.current);                   // 重置react-form
                    setStoreObject(storeDefaults.current);         // 重置mmkv存储
                    setClassRadioState(selectorDefaults.current.class);
                    setSubClassRadioState(selectorDefaults.current.subClass);
                    setIndustryRadioState(selectorDefaults.current.industry);
                    setDifficultyRadioState(selectorDefaults.current.difficulty);  // 重置radio状态
                    setShowConfirm(false);
                }
            });
            setShowConfirm(true);
        }
    };
    const commitOnError = (error) => {
        setSnackBarMessage(`项目库添加发生错误, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const commitOnSettled = (data, error) => {
        setCancelButtonDisabled(false);
        setOkButtonDisabled(false);
        setOkButtonLoading(false);
    };
    const submitQuery = insertQueryClient(dataFeeder, commitOnSuccess, commitOnError, commitOnSettled);

    // 组件载入时从mmkv存储恢复下拉控件状态
    useEffect(()=>{
        const storedDifficultyState = getStore("difficulty");
        const storedClassState = getStore("class");
        const storedSubClassState = getStore("subClass");
        const storedIndustryState = getStore("industry");
        isEmptyObject(storedDifficultyState) ? setDifficultyRadioState(selectorDefaults.current.difficulty) : setDifficultyRadioState(storedDifficultyState);
        isEmptyObject(storedClassState)      ? setClassRadioState(selectorDefaults.current.class)           : setClassRadioState(storedClassState);
        isEmptyObject(storedSubClassState)   ? setSubClassRadioState(selectorDefaults.current.subClass)     : setSubClassRadioState(storedSubClassState);
        isEmptyObject(storedIndustryState)   ? setIndustryRadioState(selectorDefaults.current.industry)     : setIndustryRadioState(storedIndustryState);

        return () => classChecked.current = false;
    }, []);

    return (
        <View style={{ flex: 1 }}>

            <HeaderBar
                title={"添加项目库"}
                navigation={navigation}
            //goBackCallback={() => {}}
            //menuItemArray={[{ title: "标题", action: ()=>{} }]}
            />

            <ScreenWrapper contentContainerStyle={styles.container} style={{marginBottom: height+ bottom}}>
                {/*<Text>Render: {render}</Text>*/}

                <View style={styles.formEntry}>
                    <ControlledTextInput
                        rowLabel="项目库名称"
                        control={control}
                        name="name"
                        placeholder="XXXX项目"
                        onChangeText={(text) => setStore("name", text)}
                        onClearText={() => {
                            setStore("name", "");
                            resetField("name", { defaultValue: "" });
                        }}
                        editable={true}
                        mode="outlined"
                        formError={errors}
                        required={true}
                        multiline={true}
                    />

                    <ControlledRadioInputWithQuery
                        name="class"
                        rowLabel="项目类型"
                        control={control}
                        placeholder="请拉选项目类型"
                        onDialogConfirm={(obj) => {
                            console.log("confirm state: ", obj);
                            setStore("class", obj);
                            setClassRadioState(obj);
                        }}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        //dataProvider={allUsersQueryClient}
                        dataProvider={classStateDataProviderRef}
                        setFetchFailMessage={setSnackBarMessage}
                        setDisplayFailBar={setShowSnackbar}
                        dialogState={classRadioState}
                        setDialogState={setClassRadioState}
                        defaultNullOptionsTip={"未找到项目类型数据, 请联系管理员!"}
                        required={true}
                    />

                    {projClass === 1 && // 暂时只有水平衡需要拉选子类型
                        <ControlledRadioInputWithQuery
                            name="subClass"
                            rowLabel="项目子类型"
                            control={control}
                            placeholder="请拉选项目子类型"
                            onDialogConfirm={(obj) => {
                                console.log("confirm state: ", obj);
                                setStore("subClass", obj);
                                setSubClassRadioState(obj);
                            }}
                            okBtnLabel={"确定"}
                            cancelBtnLabel={"取消"}
                            formError={errors}
                            //dataProvider={allUsersQueryClient}
                            dataProvider={subClassStateDataProviderRef}
                            setFetchFailMessage={setSnackBarMessage}
                            setDisplayFailBar={setShowSnackbar}
                            dialogState={subClassRadioState}
                            setDialogState={setSubClassRadioState}
                            defaultNullOptionsTip={"未找到项目子类型数据, 请联系管理员!"}
                            required={false}
                        />}

                    {classChecked.current && <ControlledRadioInputWithQuery
                        name="industry"
                        rowLabel="行业类型"
                        control={control}
                        placeholder="请拉选行业类型"
                        onDialogConfirm={(obj) => {
                            console.log("confirm state: ", obj);
                            setStore("industry", obj);
                            setIndustryRadioState(obj);
                        }}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        //dataProvider={allUsersQueryClient}
                        dataProvider={industryStateDataProviderRef}
                        setFetchFailMessage={setSnackBarMessage}
                        setDisplayFailBar={setShowSnackbar}
                        dialogState={industryRadioState}
                        setDialogState={setIndustryRadioState}
                        defaultNullOptionsTip={"未找到项目库难度数据, 请联系管理员!"}
                        required={false}
                    />}

                    {classChecked.current && <ControlledRadioInputWithQuery
                        name="difficulty"
                        rowLabel="默认难度"
                        control={control}
                        placeholder="请拉选项目难度"
                        onDialogConfirm={(obj) => {
                            console.log("confirm state: ", obj);
                            setStore("difficulty", obj);
                            setDifficultyRadioState(obj);
                        }}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        //dataProvider={allUsersQueryClient}
                        dataProvider={difficultyStateDataProviderRef}
                        setFetchFailMessage={setSnackBarMessage}
                        setDisplayFailBar={setShowSnackbar}
                        dialogState={difficultyRadioState}
                        setDialogState={setDifficultyRadioState}
                        defaultNullOptionsTip={"未找到项目库难度数据, 请联系管理员!"}
                        required={false}
                    />}

                </View>

            </ScreenWrapper>

            <Appbar
                style={[
                    styles.bottom,
                    {
                        height: height + bottom,
                    },
                    theme.isV3 && {
                        backgroundColor: theme.colors.elevation.level2,
                    },
                ]}
                safeAreaInsets={{ bottom, left, right }}
                theme={{ mode: "adaptive" }}
            //mode='center-aligned'
            >

                <View style={styles.bottomBarContainer}>
                    <Divider />
                    <View style={styles.bottomBarComponents}>
                        <BottomBarButton
                            label={bottomBarLabels.cancel}
                            disabled={cancelButtonDisabled}
                            onPress={onCancel}
                        />
                        <BottomBarButton
                            label={bottomBarLabels.ok}
                            loading={okButtonLoading}
                            disabled={okButtonDisabled}
                            icon={saveButtonIcon}
                            onPress={handleSubmit(onHandleSubmit, (formErr) => onPreSubmitError(formErr, getStore, "ProjectBaseInserting"))}
                        />
                    </View>
                </View>
            </Appbar>

            <Snackbar
                style={styles.snackBar}
                visible={showSnackbar}
                onDismiss={() => {
                    setShowSnackbar(false);
                }}
                onIconPress={() => {
                    setShowSnackbar(false);
                }}
                duration={Snackbar.DURATION_LONG}
            >
                {snackBarMessage}
            </Snackbar>

            <DialogToConfirm
                visible={showConfirm}
                title={confimDialogConfig.title ? confimDialogConfig.title : ""}
                text={confimDialogConfig.text ? confimDialogConfig.text : ""}
                onOK={confimDialogConfig.onOK ? confimDialogConfig.onOK : () => setShowConfirm(false)}
                okBtnLabel={confimDialogConfig.okLabel ? confimDialogConfig.okLabel : "确认"}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    firstColumn: {
        flex: 2,
        //borderWidth: 1,
        //height: 40,
        content: {
            fontSize: 18,
        }
    },
    secondColumn: {
        flex: 3,
        // /borderWidth: 1,
        // height: 40,
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        flex: 1,
        marginHorizontal: 6
    },
    formEntry: {
        margin: 8,
    },
    snackBar: {
        flexDirection: "column",
    },
});

export default ProjectBaseInserting;
