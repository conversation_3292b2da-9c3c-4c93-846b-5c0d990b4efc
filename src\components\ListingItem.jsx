import React from "react";
import { StyleSheet, Text } from "react-native";
import { List, Card, Avatar, Divider } from "react-native-paper";
import PropTypes from "prop-types";


/**
 * @param {Object} arg
 * @param {string} arg.name
 * @param {string} arg.department
 * @param {string | undefined} arg.title
 * @param {()=>{}} arg.onPress
 * @returns
 */
export const ListingItem = ({ name, department="", position="", onPress }) => {
    return (
        <>
            <List.Item
                title={<Text style={{ fontSize: 18 }}>{name}</Text>}
                description={(department || position) ? <Text style={{ fontSize: 14 }}>{department + " " + position}</Text> : null}
                onPress={onPress}
                left={(props) => <Avatar.Text style={props.style} label={name[0]} size={40} />}
                right={props => <List.Icon {...props} icon="arrow-right" />}
                style={{ borderWidth: 0, marginHorizontal: 10, marginVertical: 2 }}
                titleStyle={{ fontSize: 18 }}
                descriptionStyle={{}}
            />
            <Divider />
        </>
    );
};


export const ListingItem2 = ({ name, department = "", position = "", onPress }) => {
    return (
        <Card
            mode="elevated"
            style={styles.cardContainer}
            onPress={onPress}
        >
            <Card.Title
                title={name}
                subtitle={position ? department + " " + position : department}
                left={() => (<Avatar.Text size= { 40} label={name[0]} />)}
                //right={(props) => <Avatar.Icon {...props} icon="arrow-right" />}
            />
            <Divider />
        </Card>
    );
};

const styles = StyleSheet.create({
    avatarContainer: {
        marginHorizontal: 16,
        width: 64,
    },
});
