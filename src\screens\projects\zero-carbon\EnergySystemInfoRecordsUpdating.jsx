import { joi<PERSON>esolver } from "@hookform/resolvers/joi";
import "fast-text-encoding";
import <PERSON><PERSON> from "joi";
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { Alert, StyleSheet, View } from "react-native";
import { Snackbar } from "react-native-paper";
import Button from "../../../components/Button";
import { DialogToConfirm } from "../../../components/DialogToConfirm";
import { FormListNodesMapper } from "../../../components/FormListNodesMapper";
import HeaderBar from "../../../components/HeaderBar";
import { LoadingIndicator } from "../../../components/LoadingIndicator";
import { creatMMKVStore, makeStoredValueToFormValue } from "../../../services/local-storage";
import { callOneByOne, ifTruthLet, isNullness, makeDataFeeder } from "../../../utils";
import { checkPermits } from "../../../utils/permits";
import { isServerDataNewer, makeGetClientCversion, radioIdToObject } from "../../../utils/records";
import { validatorBase } from "../../../utils/validatorBase";
import ScreenWrapper from "../../ScreenWrapper";


// 新组件需要重新!!
import { useShallow } from "zustand/shallow";
import {
    makeReqDelZeroCarbonRecords as recordDeleteQueryMaker,
    makeReqGetZeroCarbonRecords as recordSelectQueryMaker,
    makeReqUpdZeroCarbonRecords as recordUpdateQueryMaker,
} from "../../../api/projectRecordsQueries";
import { UPD_ZERO_CARBON_RECORDS as pageMainKey } from "../../../config/keysConfig";
import * as projConfig from "../../../config/zeroCarbon";
import { zcEnergySystemInfoStates as selectorStates } from "../../../hooks/selectorStates";
import { onPreSubmitError } from "../../../utils/screens";


const dataFeeder = makeDataFeeder();

/**
 * 用能系统基本情况
 *
 * HS_TYPE INTEGER NOT NULL DEFAULT 0, --形式, 11集中采暖, 12自主采暖
 * HS_NAME INTEGER NOT NULL DEFAULT 0, --具体名称, 11市政采暖, 12空气源热泵, 13电锅炉, 14燃气锅炉, 15水源热泵, 16地源热泵, 17分体空调, 18内燃机空调, 10其他
 * HS_NUM INTEGER NOT NULL DEFAULT 0, --数量(台)(台)
 * HS_EQUIP_MODEL TEXT NOT NULL DEFAULT '', --设备型号
 * HS_HEAT_STATION INTEGER NOT NULL DEFAULT 0, --换热站, 10否, 11是
 * HS_STATION_MODEL TEXT NOT NULL DEFAULT '', --型号
 *
 * HWS_USE_FORM INTEGER NOT NULL DEFAULT 0, --使用形式, 11电热水器, 12电锅炉, 13燃气锅炉, 14空气源热泵热水机, 15太阳能热水器, 10其他
 * HWS_NUM INTEGER NOT NULL DEFAULT 0, --数量(台)(台)
 * HWS_EQUIP_MODEL TEXT NOT NULL DEFAULT '', --设备型号
 *
 * LIFT_FREQ_CONV_USED INTEGER NOT NULL DEFAULT 0, --是否使用变频技术, 11是, 12否
 * LIFT_TRACTION INTEGER NOT NULL DEFAULT 0, --曳引电梯, 11是, 12否
 * LIFT_HYDRAULIC INTEGER NOT NULL DEFAULT 0, --液压电梯, 11是, 12否
 *
 * PSDS_MODEL TEXT NOT NULL DEFAULT '', --型号
 * PSDS_POWER FLOAT NOT NULL DEFAULT 0.0, --功率
 * PSDS_CLASS INTEGER NOT NULL DEFAULT 0, --分类, 11干式变压器, 12油浸式变压器, 10其他类型变压器
 * PSDS_NUM INTEGER NOT NULL DEFAULT 0, --数量(台)(台)
 *
 * WUS_WATER_TYPE INTEGER NOT NULL DEFAULT 0, --水源类型, 11公共供水管网, 12地下水, 13外购水
 * WUS_BATHROOM_FIXTURE INTEGER NOT NULL DEFAULT 0, --卫生间用水器具, 11普通水龙头, 12节水型水龙头
 * WUS_DRINK_WATER INTEGER NOT NULL DEFAULT 0, --饮用水, 11净水机组, 12开水器, 13饮水机
 * WUS_SHOWER_FIXTURE INTEGER NOT NULL DEFAULT 0, --浴室用水器具, 11普通花洒, 12节水花洒
 * WUS_URINAL INTEGER NOT NULL DEFAULT 0, --小便器, 11非节能, 12按压式, 13感应式
 * WUS_SQUAT_TOILET INTEGER NOT NULL DEFAULT 0, --蹲便器, 11脚踏式, 12感应式
 * WUS_PRESS_PUMP_USED INTEGER NOT NULL DEFAULT 0, --是否使用加压泵, 11是, 12否
 * WUS_PRESS_PUMP_MODEL TEXT NOT NULL DEFAULT '', --加压泵型号
 * WUS_PRESS_PUMP_MOTOR TEXT NOT NULL DEFAULT '', --加压泵电机型号
 * WUS_ROAD_PERMEABLE INTEGER NOT NULL DEFAULT 0, --是否是透水路面, 11是, 12否
 * WUS_GREEN_WATER_TYPE INTEGER NOT NULL DEFAULT 0, --绿化使用水源, 11地下水, 12自来水, 13雨水, 14废水, 15尾水, 10其他
 * WUS_WASTE_REUSE INTEGER NOT NULL DEFAULT 0, --是否有废水再利用, 11是, 12否
 * WUS_REUSE_RCT INTEGER NOT NULL DEFAULT 0, --是否有雨水、冷凝水、尾水回用, 11是, 12否
 *
 * ACS_TYPE INTEGER NOT NULL DEFAULT 0, --形式, 11分体空调, 12中央空调
 * ACS_NAME INTEGER NOT NULL DEFAULT 0, --具体名称, 11分体空调, 12空气源热泵, 13水源热泵, 14内燃气空调, 15风冷模块机组, 16螺杆机冷水机组, 17离心式冷水机组, 18挂式分体式空调, 19立式分体式空调, 10其他
 * ACS_NUM INTEGER NOT NULL DEFAULT 0, --数量(台)(台)
 * ACS_EQUIP_MODEL TEXT NOT NULL DEFAULT '', --设备型号
 *
 * SK_ENERGY_SAVE INTEGER NOT NULL DEFAULT 0, --是否是节能插座, 11是, 12否
 * SK_NUM INTEGER NOT NULL DEFAULT 0, --数量(个)(个)
 *
 * LS_INDOOR INTEGER NOT NULL DEFAULT 0, --室内照明, 11白炽灯, 12荧光灯, 13LED灯, 14感应灯, 10其他
 * LS_SWITCH_TYPE INTEGER NOT NULL DEFAULT 0, --开关类型, 11感应开关, 12普通按钮开关
 * LS_OUTDOOR INTEGER NOT NULL DEFAULT 0, --室外照明, 11普通路灯, 12LED路灯, 13太阳能路灯
 *
 * EVCS_OUT_POWER FLOAT NOT NULL DEFAULT 0.0, --输出功率
 * EVCS_NUM INTEGER NOT NULL DEFAULT 0, --数量(台)(台)
 *
 * BE_WALL_INSULATION INTEGER NOT NULL DEFAULT 0, --外墙是否有保温, 11是, 12否
 * BE_WINDOW INTEGER NOT NULL DEFAULT 0, --外窗, 11普通单层玻璃, 12普通双层玻璃, 13塑钢中空玻璃
 * BE_ROOT_INSULATION INTEGER NOT NULL DEFAULT 0, --屋顶是否有保温, 11是, 12否
 *
 * VTS_OFFICE_VEHICLE INTEGER NOT NULL DEFAULT 0, --公务用车, 11汽油车, 12柴油车, 13新能源汽车全电, 14新能源汽车混动
 * VTS_NUM INTEGER NOT NULL DEFAULT 0, --数量(台)(台)
 *
 * SP_ROOF_AREA FLOAT NOT NULL DEFAULT 0.0, --屋顶面积(m²)(m²)
 * SP_INSTALL_CAPACITY FLOAT NOT NULL DEFAULT 0.0, --装机容量(kW)(kW)
 * SP_ANNUAL_ELEC_GEN FLOAT NOT NULL DEFAULT 0.0, --年绿电发电量(kWh)(kWh)
 *
 * MS_WMETER_FULL_INSTALL INTEGER NOT NULL DEFAULT 0, --水表是否安装齐全, 11是, 12否
 * MS_MECHA_WMETER_NUM INTEGER NOT NULL DEFAULT 0, --可改造机械水表数量(块)
 * MS_SMART_WMETER_NUM INTEGER NOT NULL DEFAULT 0, --可改造智能水表数量(块)
 * MS_EMETER_FULL_INSTALL INTEGER NOT NULL DEFAULT 0, --电表是否安装齐全, 11是, 12否
 * MS_ORD_EMETER_NUM INTEGER NOT NULL DEFAULT 0, --可改造普通电表数量(块)
 * MS_SMART_EMETER_NUM INTEGER NOT NULL DEFAULT 0, --可改造智能电表数量(块)
 *
 * ECP_CONSTR_STATUS INTEGER NOT NULL DEFAULT 0, --建设情况, 11电力监测平台, 12水资源监测平台, 13综合能源监测平台
 * ECP_OP_STATUS INTEGER NOT NULL DEFAULT 0, --运行情况, 11正常, 12异常
 *
 * ESTIS_WATER TEXT NOT NULL DEFAULT '', --水
 * ESTIS_ELEC TEXT NOT NULL DEFAULT '', --电
 * ESTIS_GAS TEXT NOT NULL DEFAULT '', --气
 * ESTIS_OFFICE_VEHICLE TEXT NOT NULL DEFAULT '', --公车

 * @param {object} args
 * @param {object} args.navigation
 * @param {object} args.route
 * @returns
 */
const RecordsUpdating = ({ navigation, route }) => {
    console.log("RecordsUpdating pageMeta from nav:", route.params.pageMeta);
    console.log("RecordsUpdating projMeta from nav:", route.params.projMeta);

    //const rerender = useRerender();

    const { title: screenTitle, } = route.params.projMeta;
    //const projClass    = route.params.projMeta.class;     // 项目class, 1水平衡, 11零碳诊断
    //const industryType = route.params.projMeta.industry;  // 行业类型, 1工业, 2服务业
    //const projIndustry = useRef(parseIndustryCode(industryType, projClass)); // "publicInst"

    // formType对应着服务端的form-type, 为0时表示表单是用户填写, 为1表示服务端生成, 为1时只读
    const { queryKwd, id: recordPubid, name: recordName, formType } = route.params.pageMeta; // recordPubid is the alias of id
    const saveButtonIcon = "content-save-all-outline";
    const formEditableGlobal = checkPermits() ? (formType === 0 ? true : false) : false;

    // 使用memoize来优化
    const { setStore, getStore, clearStore, setStoreObject } = useMemo(() => {
        console.log("creatMMKVStore memo:", pageMainKey.store, queryKwd, recordPubid);
        return creatMMKVStore(`${pageMainKey.store}/${queryKwd}/${recordPubid}`);
    }, [queryKwd, recordPubid]);

    const storedValueToFormValue = useRef(makeStoredValueToFormValue(getStore));
    const getClientCversion = useRef(makeGetClientCversion(() => getStore("mainCversion"), () => getStore("subCversion")));

    // 客户端cversion = `${mainCversion}.${subCversion}`
    // 这两行没出重复渲染都要获取, 不放到useEffect中
    (getStore("mainCversion") === undefined) && setStore("mainCversion", 0); // 客户端主版本号
    (getStore("subCversion")  === undefined) && setStore("subCversion",  0); // 客户端次版本号, 修改一次自增一次
    //let mainCversion = getStore("mainCversion"); // 主版本号, 与服务端cversion对应
    // 次版本号, 表示本地更改次数, 用于程序上对子版本号的增加, 用于判断与服务端版本的差异, 同时可用于判断避免重复从MMKV获取数据
    const subCversionRef = useRef(getStore("subCversion"));

    // 新组件需要修改!!
    const bottomBarLabels = { ok: "提交" };                    // 底部按钮显示文字
    const [snackBarMessage, setSnackBarMessage] = useState("更新表单遇到错误"); // 下方错误提示文字内容
    const [confimDialogConfig, setConfirmDialogConfig] = useState({});       // 对话框信息, {title, text, okLabel, onOK}, 但显示对话框的变量在showConfirm
    const [okButtonLoading,  setOkButtonLoading]  = useState(false);            // 右下方按钮图标是否旋转
    const [okButtonDisabled, setOkButtonDisabled] = useState(!checkPermits());  // 右下方按钮是否禁用
    const [showSnackbar,     setShowSnackbar]     = useState(false);            // 下方是否显示错误通知
    const [showConfirm,      setShowConfirm]      = useState(false);            // 是否显示确认对话框
    const [loadingIndicatorVisible, setLoadingIndicatorVisible] = useState(true); // 是否显示loading, 还负责是否渲染ScreenWrapper组件, 为false时渲染.
    const delQueryInfo = useRef({ id: recordPubid, name: recordName });         // 用于删除本记录的查询变量

    const [
        hsTypeRadioState,
        hsNameRadioState,
        hsHeatStationRadioState,
        hwsUseFormRadioState,
        liftFreqConvUsedRadioState,
        liftTractionRadioState,
        liftHydraulicRadioState,
        psdsClassRadioState,
        wusWaterTypeRadioState,
        wusBathroomFixtureRadioState,
        wusDrinkWaterRadioState,
        wusShowerFixtureRadioState,
        wusUrinalRadioState,
        wusSquatToiletRadioState,
        wusPressPumpUsedRadioState,
        wusRoadPermeableRadioState,
        wusGreenWaterTypeRadioState,
        wusWasteReuseRadioState,
        wusReuseRctRadioState,
        acsTypeRadioState,
        acsNameRadioState,
        skEnergySaveRadioState,
        lsIndoorRadioState,
        lsSwitchTypeRadioState,
        lsOutdoorRadioState,
        beWallInsulationRadioState,
        beWindowRadioState,
        beRootInsulationRadioState,
        vtsOfficeVehicleRadioState,
        msWmeterFullInstallRadioState,
        msEmeterFullInstallRadioState,
        ecpConstrStatusRadioState,
        ecpOpStatusRadioState,

        setHsTypeRadioState,
        setHsNameRadioState,
        setHsHeatStationRadioState,
        setHwsUseFormRadioState,
        setLiftFreqConvUsedRadioState,
        setLiftTractionRadioState,
        setLiftHydraulicRadioState,
        setPsdsClassRadioState,
        setWusWaterTypeRadioState,
        setWusBathroomFixtureRadioState,
        setWusDrinkWaterRadioState,
        setWusShowerFixtureRadioState,
        setWusUrinalRadioState,
        setWusSquatToiletRadioState,
        setWusPressPumpUsedRadioState,
        setWusRoadPermeableRadioState,
        setWusGreenWaterTypeRadioState,
        setWusWasteReuseRadioState,
        setWusReuseRctRadioState,
        setAcsTypeRadioState,
        setAcsNameRadioState,
        setSkEnergySaveRadioState,
        setLsIndoorRadioState,
        setLsSwitchTypeRadioState,
        setLsOutdoorRadioState,
        setBeWallInsulationRadioState,
        setBeWindowRadioState,
        setBeRootInsulationRadioState,
        setVtsOfficeVehicleRadioState,
        setMsWmeterFullInstallRadioState,
        setMsEmeterFullInstallRadioState,
        setEcpConstrStatusRadioState,
        setEcpOpStatusRadioState,

        resetSelectorStates,
    ] = selectorStates(useShallow(state => [
        state.hsTypeRadio,
        state.hsNameRadio,
        state.hsHeatStationRadio,
        state.hwsUseFormRadio,
        state.liftFreqConvUsedRadio,
        state.liftTractionRadio,
        state.liftHydraulicRadio,
        state.psdsClassRadio,
        state.wusWaterTypeRadio,
        state.wusBathroomFixtureRadio,
        state.wusDrinkWaterRadio,
        state.wusShowerFixtureRadio,
        state.wusUrinalRadio,
        state.wusSquatToiletRadio,
        state.wusPressPumpUsedRadio,
        state.wusRoadPermeableRadio,
        state.wusGreenWaterTypeRadio,
        state.wusWasteReuseRadio,
        state.wusReuseRctRadio,
        state.acsTypeRadio,
        state.acsNameRadio,
        state.skEnergySaveRadio,
        state.lsIndoorRadio,
        state.lsSwitchTypeRadio,
        state.lsOutdoorRadio,
        state.beWallInsulationRadio,
        state.beWindowRadio,
        state.beRootInsulationRadio,
        state.vtsOfficeVehicleRadio,
        state.msWmeterFullInstallRadio,
        state.msEmeterFullInstallRadio,
        state.ecpConstrStatusRadio,
        state.ecpOpStatusRadio,

        state.setHsTypeRadio,
        state.setHsNameRadio,
        state.setHsHeatStationRadio,
        state.setHwsUseFormRadio,
        state.setLiftFreqConvUsedRadio,
        state.setLiftTractionRadio,
        state.setLiftHydraulicRadio,
        state.setPsdsClassRadio,
        state.setWusWaterTypeRadio,
        state.setWusBathroomFixtureRadio,
        state.setWusDrinkWaterRadio,
        state.setWusShowerFixtureRadio,
        state.setWusUrinalRadio,
        state.setWusSquatToiletRadio,
        state.setWusPressPumpUsedRadio,
        state.setWusRoadPermeableRadio,
        state.setWusGreenWaterTypeRadio,
        state.setWusWasteReuseRadio,
        state.setWusReuseRctRadio,
        state.setAcsTypeRadio,
        state.setAcsNameRadio,
        state.setSkEnergySaveRadio,
        state.setLsIndoorRadio,
        state.setLsSwitchTypeRadio,
        state.setLsOutdoorRadio,
        state.setBeWallInsulationRadio,
        state.setBeWindowRadio,
        state.setBeRootInsulationRadio,
        state.setVtsOfficeVehicleRadio,
        state.setMsWmeterFullInstallRadio,
        state.setMsEmeterFullInstallRadio,
        state.setEcpConstrStatusRadio,
        state.setEcpOpStatusRadio,

        state.resetStates,
    ])); // radio组件状态

    //const defaultProvince = useRef(13); // 河北
    const hsTypeRadioStateDataProviderRef = useRef(projConfig.hsTypeEnum);
    const hsNameRadioStateDataProviderRef = useRef(projConfig.hsNameEnum);
    const hsHeatStationRadioStateDataProviderRef = useRef(projConfig.hsHeatStationEnum);
    const hwsUseFormRadioStateDataProviderRef = useRef(projConfig.hwsUseFormEnum);
    const liftFreqConvUsedRadioStateDataProviderRef = useRef(projConfig.liftFreqConvUsedEnum);
    const liftTractionRadioStateDataProviderRef = useRef(projConfig.liftTractionEnum);
    const liftHydraulicRadioStateDataProviderRef = useRef(projConfig.liftHydraulicEnum);
    const psdsClassRadioStateDataProviderRef = useRef(projConfig.psdsClassEnum);
    const wusWaterTypeRadioStateDataProviderRef = useRef(projConfig.wusWaterTypeEnum);
    const wusBathroomFixtureRadioStateDataProviderRef = useRef(projConfig.wusBathroomFixtureEnum);
    const wusDrinkWaterRadioStateDataProviderRef = useRef(projConfig.wusDrinkWaterEnum);
    const wusShowerFixtureRadioStateDataProviderRef = useRef(projConfig.wusShowerFixtureEnum);
    const wusUrinalRadioStateDataProviderRef = useRef(projConfig.wusUrinalEnum);
    const wusSquatToiletRadioStateDataProviderRef = useRef(projConfig.wusSquatToiletEnum);
    const wusPressPumpUsedRadioStateDataProviderRef = useRef(projConfig.wusPressPumpUsedEnum);
    const wusRoadPermeableRadioStateDataProviderRef = useRef(projConfig.wusRoadPermeableEnum);
    const wusGreenWaterTypeRadioStateDataProviderRef = useRef(projConfig.wusGreenWaterTypeEnum);
    const wusWasteReuseRadioStateDataProviderRef = useRef(projConfig.wusWasteReuseEnum);
    const wusReuseRctRadioStateDataProviderRef = useRef(projConfig.wusReuseRctEnum);
    const acsTypeRadioStateDataProviderRef = useRef(projConfig.acsTypeEnum);
    const acsNameRadioStateDataProviderRef = useRef(projConfig.acsNameEnum);
    const skEnergySaveRadioStateDataProviderRef = useRef(projConfig.skEnergySaveEnum);
    const lsIndoorRadioStateDataProviderRef = useRef(projConfig.lsIndoorEnum);
    const lsSwitchTypeRadioStateDataProviderRef = useRef(projConfig.lsSwitchTypeEnum);
    const lsOutdoorRadioStateDataProviderRef = useRef(projConfig.lsOutdoorEnum);
    const beWallInsulationRadioStateDataProviderRef = useRef(projConfig.beWallInsulationEnum);
    const beWindowRadioStateDataProviderRef = useRef(projConfig.beWindowEnum);
    const beRootInsulationRadioStateDataProviderRef = useRef(projConfig.beRootInsulationEnum);
    const vtsOfficeVehicleRadioStateDataProviderRef = useRef(projConfig.vtsOfficeVehicleEnum);
    const msWmeterFullInstallRadioStateDataProviderRef = useRef(projConfig.msWmeterFullInstallEnum);
    const msEmeterFullInstallRadioStateDataProviderRef = useRef(projConfig.msEmeterFullInstallEnum);
    const ecpConstrStatusRadioStateDataProviderRef = useRef(projConfig.ecpConstrStatusEnum);
    const ecpOpStatusRadioStateDataProviderRef = useRef(projConfig.ecpOpStatusEnum);

    // Define form
    // 新组件需要重新定义!!
    const schema = Joi.object({
        hsType: validatorBase.zeroCarbon.commons.simpleRadio,
        hsName: validatorBase.zeroCarbon.commons.simpleRadio,
        hsNum: validatorBase.zeroCarbon.commons.intField,
        hsEquipModel: validatorBase.zeroCarbon.commons.textField,
        hsHeatStation: validatorBase.zeroCarbon.commons.simpleRadio,
        hsStationModel: validatorBase.zeroCarbon.commons.textField,

        hwsUseForm: validatorBase.zeroCarbon.commons.simpleRadio,
        hwsNum: validatorBase.zeroCarbon.commons.intField,
        hwsEquipModel: validatorBase.zeroCarbon.commons.textField,

        liftFreqConvUsed: validatorBase.zeroCarbon.commons.simpleRadio,
        liftTraction: validatorBase.zeroCarbon.commons.simpleRadio,
        liftHydraulic: validatorBase.zeroCarbon.commons.simpleRadio,

        psdsModel: validatorBase.zeroCarbon.commons.textField,
        psdsPower: validatorBase.zeroCarbon.commons.floatField,
        psdsClass: validatorBase.zeroCarbon.commons.simpleRadio,
        psdsNum: validatorBase.zeroCarbon.commons.intField,

        wusWaterType: validatorBase.zeroCarbon.commons.simpleRadio,
        wusBathroomFixture: validatorBase.zeroCarbon.commons.simpleRadio,
        wusDrinkWater: validatorBase.zeroCarbon.commons.simpleRadio,
        wusShowerFixture: validatorBase.zeroCarbon.commons.simpleRadio,
        wusUrinal: validatorBase.zeroCarbon.commons.simpleRadio,
        wusSquatToilet: validatorBase.zeroCarbon.commons.simpleRadio,
        wusPressPumpUsed: validatorBase.zeroCarbon.commons.simpleRadio,
        wusPressPumpModel: validatorBase.zeroCarbon.commons.textField,
        wusPressPumpMotor: validatorBase.zeroCarbon.commons.textField,
        wusRoadPermeable: validatorBase.zeroCarbon.commons.simpleRadio,
        wusGreenWaterType: validatorBase.zeroCarbon.commons.simpleRadio,
        wusWasteReuse: validatorBase.zeroCarbon.commons.simpleRadio,
        wusReuseRct: validatorBase.zeroCarbon.commons.simpleRadio,

        acsType: validatorBase.zeroCarbon.commons.simpleRadio,
        acsName: validatorBase.zeroCarbon.commons.simpleRadio,
        acsNum: validatorBase.zeroCarbon.commons.intField,
        acsEquipModel: validatorBase.zeroCarbon.commons.textField,

        skEnergySave: validatorBase.zeroCarbon.commons.simpleRadio,
        skNum: validatorBase.zeroCarbon.commons.intField,

        lsIndoor: validatorBase.zeroCarbon.commons.simpleRadio,
        lsSwitchType: validatorBase.zeroCarbon.commons.simpleRadio,
        lsOutdoor: validatorBase.zeroCarbon.commons.simpleRadio,

        evcsOutPower: validatorBase.zeroCarbon.commons.floatField,
        evcsNum: validatorBase.zeroCarbon.commons.intField,

        beWallInsulation: validatorBase.zeroCarbon.commons.simpleRadio,
        beWindow: validatorBase.zeroCarbon.commons.simpleRadio,
        beRootInsulation: validatorBase.zeroCarbon.commons.simpleRadio,

        vtsOfficeVehicle: validatorBase.zeroCarbon.commons.simpleRadio,
        vtsNum: validatorBase.zeroCarbon.commons.intField,

        spRoofArea: validatorBase.zeroCarbon.commons.floatField,
        spInstallCapacity: validatorBase.zeroCarbon.commons.floatField,
        spAnnualElecGen: validatorBase.zeroCarbon.commons.floatField,

        msWmeterFullInstall: validatorBase.zeroCarbon.commons.simpleRadio,
        msMechaWmeterNum: validatorBase.zeroCarbon.commons.intField,
        msSmartWmeterNum: validatorBase.zeroCarbon.commons.intField,
        msEmeterFullInstall: validatorBase.zeroCarbon.commons.simpleRadio,
        msOrdEmeterNum: validatorBase.zeroCarbon.commons.intField,
        msSmartEmeterNum: validatorBase.zeroCarbon.commons.intField,

        ecpConstrStatus: validatorBase.zeroCarbon.commons.simpleRadio,
        ecpOpStatus: validatorBase.zeroCarbon.commons.simpleRadio,

        estisWater: validatorBase.zeroCarbon.commons.textField,
        estisElec: validatorBase.zeroCarbon.commons.textField,
        estisGas: validatorBase.zeroCarbon.commons.textField,
        estisOfficeVehicle: validatorBase.zeroCarbon.commons.textField,
        // other fields
        others:       validatorBase.waterBalance.waterUsageUnits.textField,
        remarks:      validatorBase.waterBalance.waterUsageUnits.longTextField,
    });

    const defaultFormValues = useMemo(() => ({
        // 注意, 默认为0的是允许默认为0的值, 或者通过计算得到的值
        hsType:  "",
        hsName:  "",
        hsNum:  "",
        hsEquipModel:  "",
        hsHeatStation:  "",
        hsStationModel:  "",
        hwsUseForm:  "",
        hwsNum:  "",
        hwsEquipModel:  "",
        liftFreqConvUsed:  "",
        liftTraction:  "",
        liftHydraulic:  "",
        psdsModel:  "",
        psdsPower:  "",
        psdsClass:  "",
        psdsNum:  "",
        wusWaterType:  "",
        wusBathroomFixture:  "",
        wusDrinkWater:  "",
        wusShowerFixture:  "",
        wusUrinal:  "",
        wusSquatToilet:  "",
        wusPressPumpUsed:  "",
        wusPressPumpModel:  "",
        wusPressPumpMotor:  "",
        wusRoadPermeable:  "",
        wusGreenWaterType:  "",
        wusWasteReuse:  "",
        wusReuseRct:  "",
        acsType:  "",
        acsName:  "",
        acsNum:  "",
        acsEquipModel:  "",
        skEnergySave:  "",
        skNum:  "",
        lsIndoor:  "",
        lsSwitchType:  "",
        lsOutdoor:  "",
        evcsOutPower:  "",
        evcsNum:  "",
        beWallInsulation:  "",
        beWindow:  "",
        beRootInsulation:  "",
        vtsOfficeVehicle:  "",
        vtsNum:  "",
        spRoofArea:  "",
        spInstallCapacity:  "",
        spAnnualElecGen:  "",
        msWmeterFullInstall:  "",
        msMechaWmeterNum:  "",
        msSmartWmeterNum:  "",
        msEmeterFullInstall:  "",
        msOrdEmeterNum:  "",
        msSmartEmeterNum:  "",
        ecpConstrStatus:  "",
        ecpOpStatus:  "",
        estisWater:  "",
        estisElec:  "",
        estisGas:  "",
        estisOfficeVehicle:  "",

        // other fields
        others:       "",
        remarks:      "",
    }), []);

    // 新组件需要重新定义defaultValues!!
    const {
        handleSubmit,
        control,
        resetField,
        reset,
        formState: { errors },
        //setValue,
        //getValues,
    } = useForm({
        resolver: joiResolver(schema),
        //reValidateMode: "onBlur", // https://github.com/orgs/react-hook-form/discussions/10327
        // 默认值在组件载入时会被更新, 这里的初始值正常情况下没有用处
        defaultValues: defaultFormValues
    });

    // Query: select record
    const onRecordSelectSuccess = useCallback((data) => { // data: {"id": 3, "leader": 0, "name": "DEPT-03", "superior": 0}
        // 注意, 进入onSuccess之前就意味着服务端版本比客户端版本新, 因此要更新客户端主版本号
        console.log("onRecordSelectSuccess:", data);
        if (data.STATUS === 0) {
            setStore("mainCversion", data.DATA.cversion);
            setStore("subCversion", 0);

            const formObject = {
                hsType: String(data.DATA.hsType),
                hsName: String(data.DATA.hsName),
                hsNum: String(data.DATA.hsNum),
                hsEquipModel: String(data.DATA.hsEquipModel),
                hsHeatStation: String(data.DATA.hsHeatStation),
                hsStationModel: String(data.DATA.hsStationModel),
                hwsUseForm: String(data.DATA.hwsUseForm),
                hwsNum: String(data.DATA.hwsNum),
                hwsEquipModel: String(data.DATA.hwsEquipModel),
                liftFreqConvUsed: String(data.DATA.liftFreqConvUsed),
                liftTraction: String(data.DATA.liftTraction),
                liftHydraulic: String(data.DATA.liftHydraulic),
                psdsModel: String(data.DATA.psdsModel),
                psdsPower: String(data.DATA.psdsPower),
                psdsClass: String(data.DATA.psdsClass),
                psdsNum: String(data.DATA.psdsNum),
                wusWaterType: String(data.DATA.wusWaterType),
                wusBathroomFixture: String(data.DATA.wusBathroomFixture),
                wusDrinkWater: String(data.DATA.wusDrinkWater),
                wusShowerFixture: String(data.DATA.wusShowerFixture),
                wusUrinal: String(data.DATA.wusUrinal),
                wusSquatToilet: String(data.DATA.wusSquatToilet),
                wusPressPumpUsed: String(data.DATA.wusPressPumpUsed),
                wusPressPumpModel: String(data.DATA.wusPressPumpModel),
                wusPressPumpMotor: String(data.DATA.wusPressPumpMotor),
                wusRoadPermeable: String(data.DATA.wusRoadPermeable),
                wusGreenWaterType: String(data.DATA.wusGreenWaterType),
                wusWasteReuse: String(data.DATA.wusWasteReuse),
                wusReuseRct: String(data.DATA.wusReuseRct),
                acsType: String(data.DATA.acsType),
                acsName: String(data.DATA.acsName),
                acsNum: String(data.DATA.acsNum),
                acsEquipModel: String(data.DATA.acsEquipModel),
                skEnergySave: String(data.DATA.skEnergySave),
                skNum: String(data.DATA.skNum),
                lsIndoor: String(data.DATA.lsIndoor),
                lsSwitchType: String(data.DATA.lsSwitchType),
                lsOutdoor: String(data.DATA.lsOutdoor),
                evcsOutPower: String(data.DATA.evcsOutPower),
                evcsNum: String(data.DATA.evcsNum),
                beWallInsulation: String(data.DATA.beWallInsulation),
                beWindow: String(data.DATA.beWindow),
                beRootInsulation: String(data.DATA.beRootInsulation),
                vtsOfficeVehicle: String(data.DATA.vtsOfficeVehicle),
                vtsNum: String(data.DATA.vtsNum),
                spRoofArea: String(data.DATA.spRoofArea),
                spInstallCapacity: String(data.DATA.spInstallCapacity),
                spAnnualElecGen: String(data.DATA.spAnnualElecGen),
                msWmeterFullInstall: String(data.DATA.msWmeterFullInstall),
                msMechaWmeterNum: String(data.DATA.msMechaWmeterNum),
                msSmartWmeterNum: String(data.DATA.msSmartWmeterNum),
                msEmeterFullInstall: String(data.DATA.msEmeterFullInstall),
                msOrdEmeterNum: String(data.DATA.msOrdEmeterNum),
                msSmartEmeterNum: String(data.DATA.msSmartEmeterNum),
                ecpConstrStatus: String(data.DATA.ecpConstrStatus),
                ecpOpStatus: String(data.DATA.ecpOpStatus),
                estisWater: String(data.DATA.estisWater),
                estisElec: String(data.DATA.estisElec),
                estisGas: String(data.DATA.estisGas),
                estisOfficeVehicle: String(data.DATA.estisOfficeVehicle),

                // other fields
                others:       String(data.DATA.others),
                remarks:      String(data.DATA.remarks),
            };
            reset(formObject);           // 重置react-form

            const storeObjects = checkPermits() && (formType === 0) && {
                ...formObject,

                hsType: radioIdToObject(hsTypeRadioStateDataProviderRef.current, data.DATA.hsType),
                hsName: radioIdToObject(hsNameRadioStateDataProviderRef.current, data.DATA.hsName),
                hsHeatStation: radioIdToObject(hsHeatStationRadioStateDataProviderRef.current, data.DATA.hsHeatStation),
                hwsUseForm: radioIdToObject(hwsUseFormRadioStateDataProviderRef.current, data.DATA.hwsUseForm),
                liftFreqConvUsed: radioIdToObject(liftFreqConvUsedRadioStateDataProviderRef.current, data.DATA.liftFreqConvUsed),
                liftTraction: radioIdToObject(liftTractionRadioStateDataProviderRef.current, data.DATA.liftTraction),
                liftHydraulic: radioIdToObject(liftHydraulicRadioStateDataProviderRef.current, data.DATA.liftHydraulic),
                psdsClass: radioIdToObject(psdsClassRadioStateDataProviderRef.current, data.DATA.psdsClass),
                wusWaterType: radioIdToObject(wusWaterTypeRadioStateDataProviderRef.current, data.DATA.wusWaterType),
                wusBathroomFixture: radioIdToObject(wusBathroomFixtureRadioStateDataProviderRef.current, data.DATA.wusBathroomFixture),
                wusDrinkWater: radioIdToObject(wusDrinkWaterRadioStateDataProviderRef.current, data.DATA.wusDrinkWater),
                wusShowerFixture: radioIdToObject(wusShowerFixtureRadioStateDataProviderRef.current, data.DATA.wusShowerFixture),
                wusUrinal: radioIdToObject(wusUrinalRadioStateDataProviderRef.current, data.DATA.wusUrinal),
                wusSquatToilet: radioIdToObject(wusSquatToiletRadioStateDataProviderRef.current, data.DATA.wusSquatToilet),
                wusPressPumpUsed: radioIdToObject(wusPressPumpUsedRadioStateDataProviderRef.current, data.DATA.wusPressPumpUsed),
                wusRoadPermeable: radioIdToObject(wusRoadPermeableRadioStateDataProviderRef.current, data.DATA.wusRoadPermeable),
                wusGreenWaterType: radioIdToObject(wusGreenWaterTypeRadioStateDataProviderRef.current, data.DATA.wusGreenWaterType),
                wusWasteReuse: radioIdToObject(wusWasteReuseRadioStateDataProviderRef.current, data.DATA.wusWasteReuse),
                wusReuseRct: radioIdToObject(wusReuseRctRadioStateDataProviderRef.current, data.DATA.wusReuseRct),
                acsType: radioIdToObject(acsTypeRadioStateDataProviderRef.current, data.DATA.acsType),
                acsName: radioIdToObject(acsNameRadioStateDataProviderRef.current, data.DATA.acsName),
                skEnergySave: radioIdToObject(skEnergySaveRadioStateDataProviderRef.current, data.DATA.skEnergySave),
                lsIndoor: radioIdToObject(lsIndoorRadioStateDataProviderRef.current, data.DATA.lsIndoor),
                lsSwitchType: radioIdToObject(lsSwitchTypeRadioStateDataProviderRef.current, data.DATA.lsSwitchType),
                lsOutdoor: radioIdToObject(lsOutdoorRadioStateDataProviderRef.current, data.DATA.lsOutdoor),
                beWallInsulation: radioIdToObject(beWallInsulationRadioStateDataProviderRef.current, data.DATA.beWallInsulation),
                beWindow: radioIdToObject(beWindowRadioStateDataProviderRef.current, data.DATA.beWindow),
                beRootInsulation: radioIdToObject(beRootInsulationRadioStateDataProviderRef.current, data.DATA.beRootInsulation),
                vtsOfficeVehicle: radioIdToObject(vtsOfficeVehicleRadioStateDataProviderRef.current, data.DATA.vtsOfficeVehicle),
                msWmeterFullInstall: radioIdToObject(msWmeterFullInstallRadioStateDataProviderRef.current, data.DATA.msWmeterFullInstall),
                msEmeterFullInstall: radioIdToObject(msEmeterFullInstallRadioStateDataProviderRef.current, data.DATA.msEmeterFullInstall),
                ecpConstrStatus: radioIdToObject(ecpConstrStatusRadioStateDataProviderRef.current, data.DATA.ecpConstrStatus),
                ecpOpStatus: radioIdToObject(ecpOpStatusRadioStateDataProviderRef.current, data.DATA.ecpOpStatus),
            };
            storeObjects && setStoreObject(storeObjects, false); // 判断用户权限, 把服务端数据写到mmkv存储, 只有可写用户才需要本地存储, 不会首先清空老数据

            // 设置selector菜单状态
            setHsTypeRadioState(storeObjects.hsType);
            setHsNameRadioState(storeObjects.hsName);
            setHsHeatStationRadioState(storeObjects.hsHeatStation);
            setHwsUseFormRadioState(storeObjects.hwsUseForm);
            setLiftFreqConvUsedRadioState(storeObjects.liftFreqConvUsed);
            setLiftTractionRadioState(storeObjects.liftTraction);
            setLiftHydraulicRadioState(storeObjects.liftHydraulic);
            setPsdsClassRadioState(storeObjects.psdsClass);
            setWusWaterTypeRadioState(storeObjects.wusWaterType);
            setWusBathroomFixtureRadioState(storeObjects.wusBathroomFixture);
            setWusDrinkWaterRadioState(storeObjects.wusDrinkWater);
            setWusShowerFixtureRadioState(storeObjects.wusShowerFixture);
            setWusUrinalRadioState(storeObjects.wusUrinal);
            setWusSquatToiletRadioState(storeObjects.wusSquatToilet);
            setWusPressPumpUsedRadioState(storeObjects.wusPressPumpUsed);
            setWusRoadPermeableRadioState(storeObjects.wusRoadPermeable);
            setWusGreenWaterTypeRadioState(storeObjects.wusGreenWaterType);
            setWusWasteReuseRadioState(storeObjects.wusWasteReuse);
            setWusReuseRctRadioState(storeObjects.wusReuseRct);
            setAcsTypeRadioState(storeObjects.acsType);
            setAcsNameRadioState(storeObjects.acsName);
            setSkEnergySaveRadioState(storeObjects.skEnergySave);
            setLsIndoorRadioState(storeObjects.lsIndoor);
            setLsSwitchTypeRadioState(storeObjects.lsSwitchType);
            setLsOutdoorRadioState(storeObjects.lsOutdoor);
            setBeWallInsulationRadioState(storeObjects.beWallInsulation);
            setBeWindowRadioState(storeObjects.beWindow);
            setBeRootInsulationRadioState(storeObjects.beRootInsulation);
            setVtsOfficeVehicleRadioState(storeObjects.vtsOfficeVehicle);
            setMsWmeterFullInstallRadioState(storeObjects.msWmeterFullInstall);
            setMsEmeterFullInstallRadioState(storeObjects.msEmeterFullInstall);
            setEcpConstrStatusRadioState(storeObjects.ecpConstrStatus);
            setEcpOpStatusRadioState(storeObjects.ecpOpStatus);

        } else {
            console.log("STATUS !== 0", data.STATUS);
            setSnackBarMessage(`表单数据拉取发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        }
    }, [setStore, setStoreObject, reset, formType]);

    const onRecordSelectError = useCallback((err) => {
        console.log("Record selecting error:", err);
    }, []);

    const onRecordSelectSettled = useCallback((data, err) => {
        const loadingDuration = 100;
        const currentTime = Date.now();
        const mutationTime = data?.mutationTime || currentTime;

        //console.log("record selecting time left: ", currentTime - data.mutationTime);
        if (currentTime - mutationTime < loadingDuration) {
            setTimeout(() => {
                loadingIndicatorVisible && setLoadingIndicatorVisible(false);
            }, currentTime - data.mutationTime);
        } else {
            loadingIndicatorVisible && setLoadingIndicatorVisible(false);
        }
    }, [loadingIndicatorVisible]); // 必需要依赖, 不然不会关闭loading

    const getPath = formType === 0 ? "get" : "getsum";
    const recordSelectQuery = recordSelectQueryMaker([queryKwd, getPath, recordPubid], onRecordSelectSuccess, onRecordSelectError, onRecordSelectSettled);

    // Query: update record
    const onHandleSubmit = useCallback((data) => {
        setOkButtonDisabled(true);
        setOkButtonLoading(true);
        dataFeeder({ ...data,
            cversion: getClientCversion.current(),
            name:     "用能系统基本情况",
        }); // append client cversion
        recordUpdateQuery.mutate();
    }, [getClientCversion, recordUpdateQuery]);

    // 新组件不需改动
    const onRecordUpdateSuccess = useCallback((data) => {
        console.log(" Record update success response data: ", data);
        if(data.STATUS !== 0) {
            setSnackBarMessage(`表单更新发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        } else {
            // 设置确认框数据
            setConfirmDialogConfig({
                title: "更新表单",
                text: "表单信息更新成功!",
                okLabel: "确定",
                onOK: () => {
                    setShowConfirm(false);
                    recordSelectQuery.mutate();
                }});
            setShowConfirm(true);
        }
    }, [recordSelectQuery]);

    const onRecordUpdateError = useCallback((error) => {
        setSnackBarMessage(`表单更新发生错误, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    }, []);

    const onRecordUpdateSettled = useCallback((data, error) => {
        setOkButtonDisabled(false);
        setOkButtonLoading(false);
    }, []);

    const recordUpdateQuery = recordUpdateQueryMaker(dataFeeder, [queryKwd, "upd", recordPubid], onRecordUpdateSuccess, onRecordUpdateError, onRecordUpdateSettled);

    // Query: delete record
    const recordDeleteOnSuccess = useCallback(() => {
        clearStore();
        resetSelectorStates();
        navigation.goBack();
    }, [clearStore, resetSelectorStates, navigation]);
    const recordDeleteOnError = useCallback((error) => {
        setSnackBarMessage(`表单删除失败, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    }, []);
    const recordDeleteQuery = recordDeleteQueryMaker([queryKwd, "del", recordPubid], recordDeleteOnSuccess, recordDeleteOnError);

    const deleteAlert = useCallback(() => {
        return (
            Alert.alert("删除表单", `删除后不可撤销, 请确认是否删除: ${delQueryInfo.current?.name}?`, [
                { text: "取消" },
                { text: "确定", onPress: () => recordDeleteQuery.mutate() },
            ])
        );
    }, [recordDeleteQuery]);

    const clearCache = useCallback(() => {
        return (
            Alert.alert("清空本地缓存", "1. 用于未知原因导致数据无法提交的情况\r\n2. 未提交的内容将会丢失, 请做好数据截图备份\r\n3. 已提交到服务端的数据不受影响\r\n4. 确认清空请点击“确认”按钮", [
                { text: "取消" },
                { text: "确定", onPress: () => {
                    clearStore();
                    resetSelectorStates();
                    navigation.goBack();
                }},
            ])
        );
    }, [clearStore, resetSelectorStates, navigation]);

    /**
     * 监测数据与自动计算
    const heatingArea = Number(useWatch({ control, name: "heatingArea" }));
    const unitPrice   = Number(useWatch({ control, name: "unitPrice" }));


    const updateGlobalState = debounce((heatingArea, unitPrice) => {
        if (!isNumber(heatingArea) || !isNumber(unitPrice)) { return; } // energyType未正确初始化时回导致解构错误

        const heatingPrice_ = heatingArea * unitPrice;

        setValue("heatingPrice", `${roundNearest(heatingPrice_)}`);
        setStore("heatingPrice", getValues("heatingPrice"));

        // rerender可能会导致闪屏, 但不rerender对应标签不会显示
        rerender();

        subCversionRef.current++;
    }, 100);

    useEffect(() => {
        updateGlobalState(heatingArea, unitPrice);
    }, [heatingArea, unitPrice]); // 依赖项不能添加updateGlobalState, 否则会无休止更新
     */


    // Hook: onMount fetch data
    useEffect(() => {
        // 优化方案: 根据用户权限进行mutate, 只有可写用户才需要本地存储
        const localCversion = getClientCversion.current();
        const remoteCversion = route.params.pageMeta.cversion;
        console.log("localCversion:", localCversion, "remoteSversion:", remoteCversion, "subCversionRef:", subCversionRef.current);

        const restoreLocalData = () => {
            const formObject = { // 默认值用户表单的数据显示
                hsType: storedValueToFormValue.current("hsType"),
                hsName: storedValueToFormValue.current("hsName"),
                hsNum: storedValueToFormValue.current("hsNum"),
                hsEquipModel: storedValueToFormValue.current("hsEquipModel"),
                hsHeatStation: storedValueToFormValue.current("hsHeatStation"),
                hsStationModel: storedValueToFormValue.current("hsStationModel"),
                hwsUseForm: storedValueToFormValue.current("hwsUseForm"),
                hwsNum: storedValueToFormValue.current("hwsNum"),
                hwsEquipModel: storedValueToFormValue.current("hwsEquipModel"),
                liftFreqConvUsed: storedValueToFormValue.current("liftFreqConvUsed"),
                liftTraction: storedValueToFormValue.current("liftTraction"),
                liftHydraulic: storedValueToFormValue.current("liftHydraulic"),
                psdsModel: storedValueToFormValue.current("psdsModel"),
                psdsPower: storedValueToFormValue.current("psdsPower"),
                psdsClass: storedValueToFormValue.current("psdsClass"),
                psdsNum: storedValueToFormValue.current("psdsNum"),
                wusWaterType: storedValueToFormValue.current("wusWaterType"),
                wusBathroomFixture: storedValueToFormValue.current("wusBathroomFixture"),
                wusDrinkWater: storedValueToFormValue.current("wusDrinkWater"),
                wusShowerFixture: storedValueToFormValue.current("wusShowerFixture"),
                wusUrinal: storedValueToFormValue.current("wusUrinal"),
                wusSquatToilet: storedValueToFormValue.current("wusSquatToilet"),
                wusPressPumpUsed: storedValueToFormValue.current("wusPressPumpUsed"),
                wusPressPumpModel: storedValueToFormValue.current("wusPressPumpModel"),
                wusPressPumpMotor: storedValueToFormValue.current("wusPressPumpMotor"),
                wusRoadPermeable: storedValueToFormValue.current("wusRoadPermeable"),
                wusGreenWaterType: storedValueToFormValue.current("wusGreenWaterType"),
                wusWasteReuse: storedValueToFormValue.current("wusWasteReuse"),
                wusReuseRct: storedValueToFormValue.current("wusReuseRct"),
                acsType: storedValueToFormValue.current("acsType"),
                acsName: storedValueToFormValue.current("acsName"),
                acsNum: storedValueToFormValue.current("acsNum"),
                acsEquipModel: storedValueToFormValue.current("acsEquipModel"),
                skEnergySave: storedValueToFormValue.current("skEnergySave"),
                skNum: storedValueToFormValue.current("skNum"),
                lsIndoor: storedValueToFormValue.current("lsIndoor"),
                lsSwitchType: storedValueToFormValue.current("lsSwitchType"),
                lsOutdoor: storedValueToFormValue.current("lsOutdoor"),
                evcsOutPower: storedValueToFormValue.current("evcsOutPower"),
                evcsNum: storedValueToFormValue.current("evcsNum"),
                beWallInsulation: storedValueToFormValue.current("beWallInsulation"),
                beWindow: storedValueToFormValue.current("beWindow"),
                beRootInsulation: storedValueToFormValue.current("beRootInsulation"),
                vtsOfficeVehicle: storedValueToFormValue.current("vtsOfficeVehicle"),
                vtsNum: storedValueToFormValue.current("vtsNum"),
                spRoofArea: storedValueToFormValue.current("spRoofArea"),
                spInstallCapacity: storedValueToFormValue.current("spInstallCapacity"),
                spAnnualElecGen: storedValueToFormValue.current("spAnnualElecGen"),
                msWmeterFullInstall: storedValueToFormValue.current("msWmeterFullInstall"),
                msMechaWmeterNum: storedValueToFormValue.current("msMechaWmeterNum"),
                msSmartWmeterNum: storedValueToFormValue.current("msSmartWmeterNum"),
                msEmeterFullInstall: storedValueToFormValue.current("msEmeterFullInstall"),
                msOrdEmeterNum: storedValueToFormValue.current("msOrdEmeterNum"),
                msSmartEmeterNum: storedValueToFormValue.current("msSmartEmeterNum"),
                ecpConstrStatus: storedValueToFormValue.current("ecpConstrStatus"),
                ecpOpStatus: storedValueToFormValue.current("ecpOpStatus"),
                estisWater: storedValueToFormValue.current("estisWater"),
                estisElec: storedValueToFormValue.current("estisElec"),
                estisGas: storedValueToFormValue.current("estisGas"),
                estisOfficeVehicle: storedValueToFormValue.current("estisOfficeVehicle"),

                // other fields
                others:       storedValueToFormValue.current("others"),
                remarks:      storedValueToFormValue.current("remarks"),
            };
            reset(formObject); // 重置react-form数据

            // 设置selector数据
            setHsTypeRadioState(ifTruthLet(getStore("hsType"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setHsNameRadioState(ifTruthLet(getStore("hsName"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setHsHeatStationRadioState(ifTruthLet(getStore("hsHeatStation"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setHwsUseFormRadioState(ifTruthLet(getStore("hwsUseForm"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setLiftFreqConvUsedRadioState(ifTruthLet(getStore("liftFreqConvUsed"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setLiftTractionRadioState(ifTruthLet(getStore("liftTraction"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setLiftHydraulicRadioState(ifTruthLet(getStore("liftHydraulic"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setPsdsClassRadioState(ifTruthLet(getStore("psdsClass"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setWusWaterTypeRadioState(ifTruthLet(getStore("wusWaterType"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setWusBathroomFixtureRadioState(ifTruthLet(getStore("wusBathroomFixture"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setWusDrinkWaterRadioState(ifTruthLet(getStore("wusDrinkWater"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setWusShowerFixtureRadioState(ifTruthLet(getStore("wusShowerFixture"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setWusUrinalRadioState(ifTruthLet(getStore("wusUrinal"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setWusSquatToiletRadioState(ifTruthLet(getStore("wusSquatToilet"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setWusPressPumpUsedRadioState(ifTruthLet(getStore("wusPressPumpUsed"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setWusRoadPermeableRadioState(ifTruthLet(getStore("wusRoadPermeable"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setWusGreenWaterTypeRadioState(ifTruthLet(getStore("wusGreenWaterType"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setWusWasteReuseRadioState(ifTruthLet(getStore("wusWasteReuse"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setWusReuseRctRadioState(ifTruthLet(getStore("wusReuseRct"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setAcsTypeRadioState(ifTruthLet(getStore("acsType"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setAcsNameRadioState(ifTruthLet(getStore("acsName"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setSkEnergySaveRadioState(ifTruthLet(getStore("skEnergySave"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setLsIndoorRadioState(ifTruthLet(getStore("lsIndoor"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setLsSwitchTypeRadioState(ifTruthLet(getStore("lsSwitchType"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setLsOutdoorRadioState(ifTruthLet(getStore("lsOutdoor"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setBeWallInsulationRadioState(ifTruthLet(getStore("beWallInsulation"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setBeWindowRadioState(ifTruthLet(getStore("beWindow"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setBeRootInsulationRadioState(ifTruthLet(getStore("beRootInsulation"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setVtsOfficeVehicleRadioState(ifTruthLet(getStore("vtsOfficeVehicle"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setMsWmeterFullInstallRadioState(ifTruthLet(getStore("msWmeterFullInstall"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setMsEmeterFullInstallRadioState(ifTruthLet(getStore("msEmeterFullInstall"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setEcpConstrStatusRadioState(ifTruthLet(getStore("ecpConstrStatus"),  isNullness, () => {return {id: 0, name: ""};}, value => value));
            setEcpOpStatusRadioState(ifTruthLet(getStore("ecpOpStatus"),  isNullness, () => {return {id: 0, name: ""};}, value => value));

            //setYearRadioState(ifTruthLet(getStore("year"),             isNullness, () => {return {id: 0, name: ""};}, value => value));

        };

        // 生成记录时服务端填充了部分有效数据, 因此对于初始版本也必须从服务端拉数据
        if (localCversion === 0) {
            recordSelectQuery.mutate();
            return;
        }

        if(formType === 1) {
            recordSelectQuery.mutate();
        } else if (localCversion === 0 && remoteCversion === 0) { // 初始版本不同步服务器更合理, 更方便输入
            setTimeout(() => {
                setLoadingIndicatorVisible(false);
            }, 10);
            //recordSelectQuery.mutate();
            return;
        } else if (localCversion === 0 && remoteCversion > 0) {
            console.log("Local cversion === 0, serve may have something nontrivial initialized, query mutate!");
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        } else if (isServerDataNewer(localCversion, remoteCversion) && subCversionRef.current > 0) { // 服务端有更新数据, 但本地有修改
            console.log("Client cversion fell behind the server cversion, someone might modified the database, choose to use the data version!");
            Alert.alert("注意", "由于其他人向服务器提交了数据, 服务端数据比本地数据新, 请选择使用哪份数据?", [
                { text: "使用本地数据", onPress: () => { setStore("mainCversion", remoteCversion); restoreLocalData(); } },
                { text: "使用服务端数据", onPress: () => { setLoadingIndicatorVisible(true); recordSelectQuery.mutate(); } },
            ]);
        } else if (isServerDataNewer(localCversion, remoteCversion)) { // localCversion==0时要mutate是因为服务端可能在创建记录时生成了必要数据
            console.log("Server cversion may be newer, query mutate:", localCversion, remoteCversion);
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        }
        else {
            console.log("Local cversion >= server cversion, query will not mutate!");
            setTimeout(() =>{
                console.log("Use local data, delay rendering!");
                restoreLocalData();
                setLoadingIndicatorVisible(false);
            }, 50);
        }
    }, []); // 这里只要求载入时运行一次, 添加依赖项会导致太多重复渲染

    const fieldsConfig = [

        {
            nodeType: "Section", title: "供暖系统", nodes: [
                {
                    inputs: [
                        { name: "hsType", label: "形式", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: hsTypeRadioState, setSelectorState: setHsTypeRadioState, dataProvider: hsTypeRadioStateDataProviderRef, },
                        { name: "hsName",  label: "具体名称", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: hsNameRadioState, setSelectorState: setHsNameRadioState, dataProvider: hsNameRadioStateDataProviderRef, },
                        { name: "hsNum",  label: "数量", unit: "台", type: "PLAIN", editable: true, placeholder: "", },
                        { name: "hsEquipModel",  label: "设备型号", unit: "", type: "PLAIN", editable: true, placeholder: "", },
                        { name: "hsHeatStation",  label: "换热站", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: hsHeatStationRadioState, setSelectorState: setHsHeatStationRadioState, dataProvider: hsHeatStationRadioStateDataProviderRef, },
                        { name: "hsStationModel",  label: "型号", unit: "", type: "PLAIN", editable: true, placeholder: "", },

                    ]
                },
            ]
        },
        {
            nodeType: "Section", title: "热水系统", nodes: [
                {
                    inputs: [
                        { name: "hwsUseForm", label: "使用形式", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: hwsUseFormRadioState, setSelectorState: setHwsUseFormRadioState, dataProvider: hwsUseFormRadioStateDataProviderRef, },
                        { name: "hwsNum",  label: "数量", unit: "台", type: "PLAIN", editable: true, placeholder: "", },
                        { name: "hwsEquipModel",  label: "设备型号", unit: "", type: "PLAIN", editable: true, placeholder: "", },

                    ]
                },
            ]
        },
        {
            nodeType: "Section", title: "电梯", nodes: [
                {
                    inputs: [
                        { name: "liftFreqConvUsed", label: "是否使用变频技术", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: liftFreqConvUsedRadioState, setSelectorState: setLiftFreqConvUsedRadioState, dataProvider: liftFreqConvUsedRadioStateDataProviderRef, },
                        { name: "liftTraction", label: "曳引电梯", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: liftTractionRadioState, setSelectorState: setLiftTractionRadioState, dataProvider: liftTractionRadioStateDataProviderRef, },
                        { name: "liftHydraulic", label: "液压电梯", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: liftHydraulicRadioState, setSelectorState: setLiftHydraulicRadioState, dataProvider: liftHydraulicRadioStateDataProviderRef, },

                    ]
                },
            ]
        },
        {
            nodeType: "Section", title: "供配电系统-变压器", nodes: [
                {
                    inputs: [
                        { name: "psdsModel",  label: "型号", unit: "", type: "PLAIN", editable: true, placeholder: "", },
                        { name: "psdsPower",  label: "功率", unit: "kW", type: "PLAIN", editable: true, placeholder: "", },
                        { name: "psdsClass",  label: "类型", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: psdsClassRadioState, setSelectorState: setPsdsClassRadioState, dataProvider: psdsClassRadioStateDataProviderRef, },
                        { name: "psdsNum",  label: "数量", unit: "台", type: "PLAIN", editable: true, placeholder: "", },

                    ]
                },
            ]
        },
        {
            nodeType: "Section", title: "用水系统", nodes: [
                {
                    inputs: [
                        { name: "wusWaterType", label: "水源类型", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: wusWaterTypeRadioState, setSelectorState: setWusWaterTypeRadioState, dataProvider: wusWaterTypeRadioStateDataProviderRef, },
                        { name: "wusBathroomFixture", label: "卫生间用水器具", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: wusBathroomFixtureRadioState, setSelectorState: setWusBathroomFixtureRadioState, dataProvider: wusBathroomFixtureRadioStateDataProviderRef, },
                        { name: "wusDrinkWater", label: "饮用水", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: wusDrinkWaterRadioState, setSelectorState: setWusDrinkWaterRadioState, dataProvider: wusDrinkWaterRadioStateDataProviderRef, },
                        { name: "wusShowerFixture", label: "浴室用水器具", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: wusShowerFixtureRadioState, setSelectorState: setWusShowerFixtureRadioState, dataProvider: wusShowerFixtureRadioStateDataProviderRef, },
                        { name: "wusUrinal", label: "小便器", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: wusUrinalRadioState, setSelectorState: setWusUrinalRadioState, dataProvider: wusUrinalRadioStateDataProviderRef, },
                        { name: "wusSquatToilet", label: "蹲便器", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: wusSquatToiletRadioState, setSelectorState: setWusSquatToiletRadioState, dataProvider: wusSquatToiletRadioStateDataProviderRef, },
                        { name: "wusPressPumpUsed", label: "是否使用加压泵", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: wusPressPumpUsedRadioState, setSelectorState: setWusPressPumpUsedRadioState, dataProvider: wusPressPumpUsedRadioStateDataProviderRef, },
                        { name: "wusPressPumpModel",  label: "加压泵型号", unit: "", type: "PLAIN", editable: true, placeholder: "", },
                        { name: "wusPressPumpMotor",  label: "加压泵电机型号", unit: "", type: "PLAIN", editable: true, placeholder: "", },
                        { name: "wusRoadPermeable", label: "是否是透水路面", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: wusRoadPermeableRadioState, setSelectorState: setWusRoadPermeableRadioState, dataProvider: wusRoadPermeableRadioStateDataProviderRef, },
                        { name: "wusGreenWaterType", label: "绿化使用水源", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: wusGreenWaterTypeRadioState, setSelectorState: setWusGreenWaterTypeRadioState, dataProvider: wusGreenWaterTypeRadioStateDataProviderRef, },
                        { name: "wusWasteReuse", label: "是否有废水再利用", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: wusWasteReuseRadioState, setSelectorState: setWusWasteReuseRadioState, dataProvider: wusWasteReuseRadioStateDataProviderRef, },
                        { name: "wusReuseRct", label: "是否有雨水、冷凝水、尾水回用", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: wusReuseRctRadioState, setSelectorState: setWusReuseRctRadioState, dataProvider: wusReuseRctRadioStateDataProviderRef, },

                    ]
                },
            ]
        },
        {
            nodeType: "Section", title: "空调系统", nodes: [
                {
                    inputs: [
                        { name: "acsType", label: "形式", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: acsTypeRadioState, setSelectorState: setAcsTypeRadioState, dataProvider: acsTypeRadioStateDataProviderRef, },
                        { name: "acsName", label: "具体名称", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: acsNameRadioState, setSelectorState: setAcsNameRadioState, dataProvider: acsNameRadioStateDataProviderRef, },
                        { name: "acsNum",  label: "数量", unit: "台", type: "PLAIN", editable: true, placeholder: "", },
                        { name: "acsEquipModel",  label: "设备型号", unit: "", type: "PLAIN", editable: true, placeholder: "", },

                    ]
                },
            ]
        },
        {
            nodeType: "Section", title: "插座", nodes: [
                {
                    inputs: [
                        { name: "skEnergySave", label: "是否是节能插座", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: skEnergySaveRadioState, setSelectorState: setSkEnergySaveRadioState, dataProvider: skEnergySaveRadioStateDataProviderRef, },
                        { name: "skNum",  label: "数量", unit: "个", type: "PLAIN", editable: true, placeholder: "", },

                    ]
                },
            ]
        },
        {
            nodeType: "Section", title: "照明系统", nodes: [
                {
                    inputs: [
                        { name: "lsIndoor", label: "室内照明", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: lsIndoorRadioState, setSelectorState: setLsIndoorRadioState, dataProvider: lsIndoorRadioStateDataProviderRef, },
                        { name: "lsSwitchType", label: "开关类型", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: lsSwitchTypeRadioState, setSelectorState: setLsSwitchTypeRadioState, dataProvider: lsSwitchTypeRadioStateDataProviderRef, },
                        { name: "lsOutdoor", label: "室外照明", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: lsOutdoorRadioState, setSelectorState: setLsOutdoorRadioState, dataProvider: lsOutdoorRadioStateDataProviderRef, },

                    ]
                },
            ]
        },
        {
            nodeType: "Section", title: "电动汽车充电桩", nodes: [
                {
                    inputs: [
                        { name: "evcsOutPower",  label: "输出功率", unit: "", type: "PLAIN", editable: true, placeholder: "", },
                        { name: "evcsNum",  label: "数量", unit: "台", type: "PLAIN", editable: true, placeholder: "", },

                    ]
                },
            ]
        },
        {
            nodeType: "Section", title: "围护结构", nodes: [
                {
                    inputs: [
                        { name: "beWallInsulation", label: "外墙是否有保温", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: beWallInsulationRadioState, setSelectorState: setBeWallInsulationRadioState, dataProvider: beWallInsulationRadioStateDataProviderRef, },
                        { name: "beWindow", label: "外窗", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: beWindowRadioState, setSelectorState: setBeWindowRadioState, dataProvider: beWindowRadioStateDataProviderRef, },
                        { name: "beRootInsulation", label: "屋顶是否有保温", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: beRootInsulationRadioState, setSelectorState: setBeRootInsulationRadioState, dataProvider: beRootInsulationRadioStateDataProviderRef, },

                    ]
                },
            ]
        },
        {
            nodeType: "Section", title: "车辆交通系统", nodes: [
                {
                    inputs: [
                        { name: "vtsOfficeVehicle", label: "公务用车", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: vtsOfficeVehicleRadioState, setSelectorState: setVtsOfficeVehicleRadioState, dataProvider: vtsOfficeVehicleRadioStateDataProviderRef, },
                        { name: "vtsNum",  label: "数量", unit: "台", type: "PLAIN", editable: true, placeholder: "", },

                    ]
                },
            ]
        },
        {
            nodeType: "Section", title: "太阳能光伏", nodes: [
                {
                    inputs: [
                        { name: "spRoofArea",  label: "屋顶面积", unit: "m²", type: "PLAIN", editable: true, placeholder: "", },
                        { name: "spInstallCapacity",  label: "装机容量", unit: "kW", type: "PLAIN", editable: true, placeholder: "", },
                        { name: "spAnnualElecGen",  label: "年绿电发电量", unit: "kWh", type: "PLAIN", editable: true, placeholder: "", },

                    ]
                },
            ]
        },
        {
            nodeType: "Section", title: "计量设备情况", nodes: [
                {
                    inputs: [
                        { name: "msWmeterFullInstall", label: "水表是否安装齐全", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: msWmeterFullInstallRadioState, setSelectorState: setMsWmeterFullInstallRadioState, dataProvider: msWmeterFullInstallRadioStateDataProviderRef, },
                        { name: "msMechaWmeterNum",  label: "可改造机械水表数量", unit: "块", type: "PLAIN", editable: true, placeholder: "", },
                        { name: "msSmartWmeterNum",  label: "可改造智能水表数量", unit: "块", type: "PLAIN", editable: true, placeholder: "", },
                        { name: "msEmeterFullInstall", label: "电表是否安装齐全", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: msEmeterFullInstallRadioState, setSelectorState: setMsEmeterFullInstallRadioState, dataProvider: msEmeterFullInstallRadioStateDataProviderRef, },
                        { name: "msOrdEmeterNum",  label: "可改造普通电表数量", unit: "块", type: "PLAIN", editable: true, placeholder: "", },
                        { name: "msSmartEmeterNum",  label: "可改造智能电表数量", unit: "块", type: "PLAIN", editable: true, placeholder: "", },

                    ]
                },
            ]
        },
        {
            nodeType: "Section", title: "能耗平台", nodes: [
                {
                    inputs: [
                        { name: "ecpConstrStatus", label: "建设情况", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: ecpConstrStatusRadioState, setSelectorState: setEcpConstrStatusRadioState, dataProvider: ecpConstrStatusRadioStateDataProviderRef, },
                        { name: "ecpOpStatus", label: "运行情况", unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: ecpOpStatusRadioState, setSelectorState: setEcpOpStatusRadioState, dataProvider: ecpOpStatusRadioStateDataProviderRef, },

                    ]
                },
            ]
        },
        {
            nodeType: "Section", title: "节能技改情况", nodes: [
                {
                    inputs: [
                        { name: "estisWater",  label: "水", unit: "", type: "PLAIN", editable: true, placeholder: "", },
                        { name: "estisElec",  label: "电", unit: "", type: "PLAIN", editable: true, placeholder: "", },
                        { name: "estisGas",  label: "气", unit: "", type: "PLAIN", editable: true, placeholder: "", },
                        { name: "estisOfficeVehicle",  label: "公车", unit: "", type: "PLAIN", editable: true, placeholder: "", },

                    ]
                },
            ]
        },

    ];

    /**
     * 这里不能使用React.memo来优化性能, 否则会导致useWatch字段在输入时失去焦点
     * useWatch 依赖于组件的重新渲染来更新其监听的值, 当用户输入时， useWatch 会捕获值的变化
     * 使用 memo 阻止了这种重新渲染, 由于 MemoizedFormListNodesMapper 没有重新渲染，React 可能会重置 DOM 元素的状态，导致输入框失去焦点
     * 谨慎使用 React.memo：
     * 只在确实需要优化性能且确定不会影响功能的情况下使用
     * 对于包含表单控件的组件，尤其要小心使用 memo
     */
    //const MemoizedFormListNodesMapper = React.memo(FormListNodesMapper); // 使用memo会导致useWatch字段在输入时失去焦点

    return (
        <View style={{ flex: 1 }}>

            <HeaderBar
                title={screenTitle}
                navigation={navigation}
                goBackCallback={() => {
                    callOneByOne(
                        //() => { _mainCversionGetter=undefined; _subCversionGetter=undefined;},
                        resetSelectorStates,
                    );
                    /*callOneByOne(clearStore, resetSelectorStates)*/  // 返回前清空mmkv存储因为临时数据不再需要, 重置状态使得页面切换后不会使用前面的数据
                }}
                menuItemArray={formType === 0 ? [{ title: "删除表单", action: deleteAlert }, { title: "清空缓存", action: clearCache }] : []}
            />

            {!loadingIndicatorVisible && <ScreenWrapper contentContainerStyle={styles.container}>

                <View style={styles.formEntry}>
                    <FormListNodesMapper
                        fieldsConfig={fieldsConfig}
                        subCversionRef={subCversionRef}
                        control={control}
                        setStore={setStore}
                        resetField={resetField}
                        errors={errors}
                        setSnackBarMessage={setSnackBarMessage}
                        setShowSnackbar={setShowSnackbar}
                        //formDisabledGlobal={formDisabledGlobal}
                        editable={formEditableGlobal}
                        rowStyle={styles.rowContainer}
                    />

                    {/*formType === 0 && <ControlledTextInput
                        name="remarks"
                        rowLabel="备注"
                        control={control}
                        placeholder=""
                        onChangeText={(text) => {
                            subCversionRef.current++;
                            setStore("subCversion", subCversionRef.current);
                            setStore("remarks", text);
                        }}
                        onClearText={() => {
                            subCversionRef.current++;
                            setStore("subCversion", subCversionRef.current);
                            setStore("remarks", "");
                            resetField("remarks", { defaultValue: "" });
                        }}
                        editable={formEditableGlobal}
                        mode="flat"
                        multiline={true}
                        formError={errors}
                        style={styles.rowContainerLongText}
                        //disabled={formDisabledGlobal}
                        required={false}
                    />*/}
                </View>

                {formType === 0 && <View style={styles.button}>
                    <Button mode="contained"
                        disabled={okButtonDisabled}
                        loading={okButtonLoading}
                        icon={saveButtonIcon}
                        onPress={handleSubmit(onHandleSubmit, (formErr) => onPreSubmitError(formErr, getStore, "WaterUsageUnitsFirstRecordsUpdating"))}
                    >{bottomBarLabels.ok}</Button>
                </View>}
            </ScreenWrapper>}

            <LoadingIndicator
                title={""}
                message={"加载中....."}
                visible={loadingIndicatorVisible}
                onClose={()=>setLoadingIndicatorVisible(false)}
            />

            <Snackbar
                style={styles.snackBar}
                visible={showSnackbar}
                onDismiss={() => setShowSnackbar(false)}
                onIconPress={() => setShowSnackbar(false)}
                duration={Snackbar.DURATION_LONG}
            >
                {snackBarMessage}
            </Snackbar>

            <DialogToConfirm
                visible={showConfirm}
                title={confimDialogConfig.title ? confimDialogConfig.title : ""}
                text={confimDialogConfig.text ? confimDialogConfig.text : ""}
                onOK={confimDialogConfig.onOK ? confimDialogConfig.onOK : () => setShowConfirm(false)}
                okBtnLabel={confimDialogConfig.okLabel ? confimDialogConfig.okLabel : "确认"}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    rowContainer:{
        firstColumn: {
            flex: 2,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 3,
        // /borderWidth: 1,
        // height: 40,
        },
    },
    rowContainerLongText: {
        firstColumn: {
            flex: 1,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 4,
            // /borderWidth: 1,
            // height: 40,
        },
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        width: "80%",
        alignSelf: "center",
    },
    formEntry: {
        margin: 8,
    },
    snackBar: {
        flexDirection: "column",
    },
});

export default RecordsUpdating;
