import Geolocation from "@react-native-community/geolocation";
import React, { useEffect, useRef, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { View } from "react-native";
import { Button, Dialog, Divider, IconButton, Portal, Text, TextInput, useTheme } from "react-native-paper";
import { DEFAULT_COORDS_PICK_LIMIT, REQUEST_LOCATION_MAXIMUM_AGE, REQUEST_LOCATION_TIMEOUT } from "../config";
import { requestLocationWithPermission } from "../services/system/permissions";
import { showLocationCoordsObj, showLocationInputCoords } from "../utils/location";
import { getCurrentTime } from "../utils/time";


const PickLocationDialog = ({ visible, setVisible, onOKCallback, onCancelCallback, theme, fieldName, title = "", nameLabel = "名称", descLabel = "备注", gpsLabel = "经纬度", okBtnLabel = "确定", cancelBtnLabel = "取消"}) => {
    const coords = useRef([0, 0, 0]); // 经度, 维度, 精确度

    // 使用了useWatch, trigger都不能在打开界面时调用pickLocation触发刷新页面, 最后只能通过useState强制刷新.
    const [_refreshKey, setRefreshKey] = useState(0);

    const {
        control,
        reset,
        handleSubmit,
        setValue,
    } = useForm({
        defaultValues: {
            name: "",
            desc: "",
            gps: "",
        },
    });

    const pickLocation = () => {
        setValue("gps", "正在定位, 请稍等...");
        // https://github.com/michalchudziak/react-native-geolocation?tab=readme-ov-file#details
        Geolocation.setRNConfiguration({
            skipPermissionRequests: false,         // (boolean) - Defaults to false. If true, you must request permissions before using Geolocation APIs.
            authorizationLevel: "auto",            // (string, iOS-only) - Either "whenInUse", "always", or "auto". Changes whether the user will be asked to give "always" or "when in use" location services permission. Any other value or auto will use the default behaviour, where the permission level is based on the contents of your Info.plist.
            enableBackgroundLocationUpdates: true, // (boolean, iOS-only) - When using skipPermissionRequests, toggle wether to automatically enableBackgroundLocationUpdates. Defaults to true.
            locationProvider: "auto",              // (string, Android-only) - Either "playServices", "android", or "auto". Determines wether to use Google’s Location Services API or Android’s Location API. The "auto" mode defaults to android, and falls back to Android's Location API if play services aren't available.
        });
        requestLocationWithPermission(
            pos => {
                const gps = [pos.coords.longitude, pos.coords.latitude, pos.coords.accuracy];
                coords.current = gps;
                setValue("gps", showLocationInputCoords(gps, ", "));
            },
            error => {
                setValue("gps", "定位失败, 请点击右侧图标重试");
                console.log(fieldName, "定位失败", error);
            },
            { enableHighAccuracy: true, timeout: REQUEST_LOCATION_TIMEOUT, maximumAge: REQUEST_LOCATION_MAXIMUM_AGE},
        );
        setRefreshKey(prev => prev + 1); // 更新状态，强制刷新
    };

    // 当对话框显示时自动获取位置, 注意其中异步函数的使用方法!
    useEffect(() => {
        if (visible) {
            pickLocation();
        }
    }, [visible]);

    // 提交按钮, data由handleSubmit传入
    const onOK = (data) => {
        if(data) {
            const coordsObj = {
                name: data.name,
                desc: data.desc,
                x: coords.current[0],
                y: coords.current[1],
                accuracy: coords.current[2],
                nameLabel: nameLabel,
                descLabel: descLabel,
                gpsLabel: gpsLabel,
                time: getCurrentTime() };
            setVisible(false);
            onOKCallback(coordsObj);
            coords.current = [0, 0, 0];
            reset({ name: "", desc: "", gps: "" });
        }
    };
        // 取消按钮
    const onCancel = () => {
        setVisible(false); // 先隐藏后清空的主观感受好些
        onCancelCallback();
        coords.current = [0, 0, 0];
        reset({ name: "", desc: "", gps: "" });
    };

    return (
        <Portal>
            <Dialog onDismiss={() => { setVisible(false); }} visible={visible} dismissable={false}>
                {title && <Dialog.Title>{title}</Dialog.Title>}
                <Dialog.Content>
                    <View flexDirection="column" style={{marginTop: 16}}>
                        <View>
                            <Text variant="bodyLarge" style={{margin: 6}}>{nameLabel}</Text>
                            <Controller
                                control={control}
                                name="name"
                                //rules={{ required: true, }}
                                render={({ field: { onChange, onBlur, value } }) => (
                                    <TextInput
                                        mode="outlined"
                                        onBlur={onBlur}
                                        onChangeText={onChange}
                                        value={value}
                                        style={{}}
                                    />
                                )}
                            />
                        </View>
                        <View>
                            <Text variant="bodyLarge" style={{marginTop: 10, marginBottom: 6}}>{descLabel}</Text>
                            <Controller
                                control={control}
                                name="desc"
                                //rules={{ required: true, }}
                                render={({ field: { onChange, onBlur, value } }) => (
                                    <TextInput
                                        mode="outlined"
                                        onBlur={onBlur}
                                        onChangeText={onChange}
                                        value={value}
                                        style={{}}
                                    />
                                )}
                            />
                        </View>
                        <View>
                            <Text variant="bodyLarge" style={{marginTop: 10, marginBottom: 6}}>{gpsLabel}</Text>
                            <Controller
                                control={control}
                                name="gps"
                                //rules={{ required: true, }}
                                render={({ field: { onChange, onBlur, value } }) => (
                                    <TextInput
                                        mode="outlined"
                                        placeholder="自动定位中, 请稍等..."
                                        onBlur={onBlur}
                                        onChangeText={onChange}
                                        value={value}
                                        editable={false}
                                        style={{}}
                                        right={<TextInput.Icon icon="map-marker-outline" color={theme.colors.primary} onPress={pickLocation} />}
                                    />
                                )}
                            />
                        </View>
                    </View>
                </Dialog.Content>
                <Dialog.Actions>
                    <Button onPress={onCancel}>{cancelBtnLabel}</Button>
                    <Button onPress={handleSubmit(onOK)}>{okBtnLabel}</Button>
                </Dialog.Actions>
            </Dialog>
        </Portal>
    );
};

/**
 * 用于在输入框显示坐标信息
 * @param {Object} args
 * @param {Object} args.coordsObj 坐标对象
 * @param {function} args.onDeleteLocationCB 删除本单元坐标的回调
 * @param {Object} args.theme 主题
 * @param {Object} args.style 样式
 * @param {Object} args.props 属性
 * @returns
 */
const LocationsCellView = ({ coordsObj, onDeleteLocationCB, theme, style, props}) => {
    return (
        <TextInput
            value={showLocationCoordsObj(coordsObj)}
            editable={false}
            style={{...style}}
            selectionColor={theme.colors.primary}
            underlineColor={theme.colors.elevation.level0}
            outlineColor={theme.colors.elevation.level0} // 透明, 文本外框颜色
            placeholderTextColor={theme.colors.elevation.level5} // 占位符文字颜色
            mode={"outlined"}
            multiline={true}
            right={<TextInput.Icon icon="map-marker-minus-outline" color={theme.colors.primary} onPress={() => {
                onDeleteLocationCB(coordsObj);
            }} />}
            {...props}
        />
    );
};


/**
 * Image组件
 * resizeMode参数区别:
 * https://medium.com/@nima-ahmadi/react-native-image-resizemode-a-visual-guide-f1958d27c615
 * https://mehrankhandev.medium.com/understanding-resizemode-in-react-native-dd0e455ce63
 * 官方文档: https://reactnative.dev/docs/image#resizemode
 * @param {Object} arg
 * @param {string} arg.placeholder 占位符
 * @param {Array} arg.locationList 用于保存图像路径的数组
 * @param {function} arg.onClearCoordsCB 清空所有坐标
 * @param {function} arg.onPickCoordsCB 获取一个坐标的回调
 * @param {function} arg.onDeleteLocationCB 删除一个坐标的回调, 注意是删除一个坐标, 而不是删除所有坐标
 * @param {function} arg.onBlur 失去焦点回调, 目前没使用
 * @param {boolean} arg.disabled 是否禁用(整个组件不可点击), 暂时没使用
 * @param {boolean} arg.editable 是否可编辑(输入区域不可编辑)
 * @param {number} arg.pickLimit 默认为1, 表示最多选择1个坐标, 2表示最多选择2个坐标, 依此类推, 0表示由全局上限限制.
 * @param {int | null} arg.width 界面上图像框的宽度, 默认null, 表示使用最大宽度.
 * @param {int} arg.height 图像高度, 默认150.
 * @param {boolean} arg.crop 是否剪裁, false不剪裁, 使用原图
 * @param {number | false | undefined | null} arg.compress 压缩程度, 0~1, 为假值时(包括0)表示不压缩
 */
const ControlledCellLocationsInput = ({ fieldName, placeholder, locationList, onClearCoordsCB,onPickCoordsCB, onDeleteLocationCB, onBlur, pickerNameLabel = "名称", pickerDescLabel = "备注", pickerGpsLabel = "经纬度", pickLimit = DEFAULT_COORDS_PICK_LIMIT, disabled = false, editable = false, ...props }) => {
    const theme = useTheme();
    const [pickerVisible, setPickerVisible] = useState(false); // GPS坐标获取对话框是否显示
    //const locations = [{name: "xx", desc: "des", x: 123, y: 89}, {}]; // {name, desc, x, y}

    return (
        <View>
            { locationList?.length === 0 ?
                <Text
                    style={{fontSize: 18, marginHorizontal: 15, color: theme.colors.elevation.level5 }}
                >{placeholder}</Text>
                :
                locationList.map((coordsObj, index) => {
                    return (
                        <View key={`cell-location-${coordsObj.name || ""}-${index}`}>
                            <LocationsCellView
                                coordsObj={coordsObj}
                                onDeleteLocationCB={onDeleteLocationCB} // 删除后回调
                                theme={theme}
                                style={{marginTop: 10, marginBottom: 10,}} // 外边距
                                props={props} // 其他参数
                            />
                            <Divider />
                        </View>
                    );})
            }

            <View style={{ flexDirection: "row", width: "100%", justifyContent: "flex-begin", }}>
                <View>
                    {/** 删除按钮 */}
                    <IconButton
                        icon="map-marker-remove-outline"
                        size={24}
                        selected
                        disabled={locationList?.length > 0 ? false : true}
                        onPress={() => onClearCoordsCB()}
                    >
                    </IconButton>
                </View>
                <View style={{ flexDirection: "row", flexGrow: 1, justifyContent: "flex-end", }}>
                    {/** 右1按钮
                    <IconButton
                        icon="camera"
                        selected
                        // /iconColor={theme.colors.primary}
                        disabled={uriArray?.length >= pickNum ? true : false}
                        size={24}
                        onPress={async () => {
                            await requestCameraWithPermission(onPickImageCB, crop, compress, pickNum);}}>
                    </IconButton>
                    */}

                    {/** 右2按钮 */}
                    <IconButton
                        icon="map-marker-plus-outline"
                        selected
                        //iconColor={theme.colors.primary}
                        disabled={locationList?.length >= pickLimit ? true : false}
                        size={24}
                        onPress={ () => setPickerVisible(true) }>
                    </IconButton>
                </View>
            </View>
            <PickLocationDialog
                visible={pickerVisible}
                setVisible={setPickerVisible}
                onOKCallback={onPickCoordsCB}
                onCancelCallback={() => {}}
                title={""}
                okBtnLabel={"确定"}
                cancelBtnLabel={"取消"}
                theme={theme}
                fieldName={fieldName}
                nameLabel={pickerNameLabel}
                descLabel={pickerDescLabel}
                gpsLabel={pickerGpsLabel}
            />
        </View>
    );
};

/*
ControlledCellLocationsInput.propTypes = {
    //uriArray: PropTypes.array,
    //label: PropTypes.string,
    placeholder: PropTypes.string,
    onClearText: PropTypes.func,
    onChangeText: PropTypes.func,
    editable: PropTypes.bool,
    mode: PropTypes.string,
};
*/

export default ControlledCellLocationsInput;
