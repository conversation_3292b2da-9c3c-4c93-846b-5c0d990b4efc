import PropTypes from "prop-types";
import React, { useEffect, useState } from "react";
import { Keyboard, StyleSheet } from "react-native";
import { TextInput, useTheme } from "react-native-paper";

/**
 * 由于CellTextInput的左边有固定的文字标签,
 * 因此在这里, 原本用作TextInput的label的内容, 在此被placeholder取代
 * @param {Object} arg
 * @param {string} arg.value
 * @param {function} arg.onClearText
 * @param {boolean} arg.disabled
 * @param {"flat" | "outlined"} arg.mode
 */
const ControlledCellTextInput = ({ value, placeholder, onClearText, onChangeText, onBlur, editable = false, multiline=false, mode = "outlined", ...props }) => {
    const [iconColor, setIconColor] = useState("transparent");
    const [labelText, setLabelText] = useState(props.errorText || placeholder);
    const [placeholderText, setPlaceholderText] = useState(placeholder);

    const theme = useTheme();
    const styles = StyleSheet.create({
        input: {
            width: "100%",
            backgroundColor: theme.colors.surface,
            fontSize: 18,
            //borderWidth: 1,
            //borderColor: "red",
        },
        error: {
            fontSize: 13,
            color: theme.colors.error,
            paddingTop: 8,
        },
    });

    useEffect(()=>{
        setLabelText(""); // set label to empty string, so that the input area will display the placeholder initially
    },
    []);

    return (
        <TextInput
            value={value}
            label={labelText}
            placeholder={placeholderText}
            editable={editable}
            disabled={!editable}
            style={styles.input}
            selectionColor={theme.colors.primary}
            underlineColor={theme.colors.elevation.level0}
            outlineColor={theme.colors.elevation.level0} // 透明, 文本外框颜色
            placeholderTextColor={theme.colors.elevation.level5} // 占位符文字颜色
            mode={mode}
            multiline={multiline}
            right={(onClearText && editable) ? <TextInput.Icon icon="close" color={iconColor} onPress={() => {
                onClearText();
                setLabelText(placeholder);
                setIconColor(theme.colors.primary);
            }} /> : undefined}
            onFocus={() => {
                setLabelText(placeholder);
                setIconColor(theme.colors.primary); // display the icon with color
                setPlaceholderText(" "); // display nothing, for prettier display, should be a true value, or it will display the placeholder text
            }}
            onBlur={() => {
                onBlur(); // react-hook-form的onBlur
                setIconColor("transparent");     // hide the icon
                setPlaceholderText(placeholder); // restore placeholder
                setLabelText("");                // for prettier display
                // 确保键盘隐藏
                setTimeout(() => {
                    if (typeof Keyboard !== "undefined") {
                        Keyboard.dismiss();
                    }
                }, 100);
            }}
            onChangeText={(text) => onChangeText(text)}
            {...props}
        />
    );
};

ControlledCellTextInput.propTypes = {
    value: PropTypes.string,
    //label: PropTypes.string,
    placeholder: PropTypes.string,
    onClearText: PropTypes.func,
    onChangeText: PropTypes.func,
    editable: PropTypes.bool,
    mode: PropTypes.string,
};

export default ControlledCellTextInput;
