///
/// JAVLinearPCMBitDepthKeyIOSType.hpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc Rousavy @ Margelo
///

#pragma once

#include <fbjni/fbjni.h>
#include "AVLinearPCMBitDepthKeyIOSType.hpp"

namespace margelo::nitro::react_native_audio_recorder_player {

  using namespace facebook;

  /**
   * The C++ JNI bridge between the C++ enum "AVLinearPCMBitDepthKeyIOSType" and the the Kotlin enum "AVLinearPCMBitDepthKeyIOSType".
   */
  struct JAVLinearPCMBitDepthKeyIOSType final: public jni::JavaClass<JAVLinearPCMBitDepthKeyIOSType> {
  public:
    static auto constexpr kJavaDescriptor = "Lcom/margelo/nitro/reactnativeaudiorecorderplayer/AVLinearPCMBitDepthKeyIOSType;";

  public:
    /**
     * Convert this Java/Kotlin-based enum to the C++ enum AVLinearPCMBitDepthKeyIOSType.
     */
    [[maybe_unused]]
    [[nodiscard]]
    AVLinearPCMBitDepthKeyIOSType toCpp() const {
      static const auto clazz = javaClassStatic();
      static const auto fieldOrdinal = clazz->getField<int>("_ordinal");
      int ordinal = this->getFieldValue(fieldOrdinal);
      return static_cast<AVLinearPCMBitDepthKeyIOSType>(ordinal);
    }

  public:
    /**
     * Create a Java/Kotlin-based enum with the given C++ enum's value.
     */
    [[maybe_unused]]
    static jni::alias_ref<JAVLinearPCMBitDepthKeyIOSType> fromCpp(AVLinearPCMBitDepthKeyIOSType value) {
      static const auto clazz = javaClassStatic();
      static const auto fieldBIT8 = clazz->getStaticField<JAVLinearPCMBitDepthKeyIOSType>("BIT8");
      static const auto fieldBIT16 = clazz->getStaticField<JAVLinearPCMBitDepthKeyIOSType>("BIT16");
      static const auto fieldBIT24 = clazz->getStaticField<JAVLinearPCMBitDepthKeyIOSType>("BIT24");
      static const auto fieldBIT32 = clazz->getStaticField<JAVLinearPCMBitDepthKeyIOSType>("BIT32");
      
      switch (value) {
        case AVLinearPCMBitDepthKeyIOSType::BIT8:
          return clazz->getStaticFieldValue(fieldBIT8);
        case AVLinearPCMBitDepthKeyIOSType::BIT16:
          return clazz->getStaticFieldValue(fieldBIT16);
        case AVLinearPCMBitDepthKeyIOSType::BIT24:
          return clazz->getStaticFieldValue(fieldBIT24);
        case AVLinearPCMBitDepthKeyIOSType::BIT32:
          return clazz->getStaticFieldValue(fieldBIT32);
        default:
          std::string stringValue = std::to_string(static_cast<int>(value));
          throw std::invalid_argument("Invalid enum value (" + stringValue + "!");
      }
    }
  };

} // namespace margelo::nitro::react_native_audio_recorder_player
