import { joi<PERSON>esolver } from "@hookform/resolvers/joi";
import "fast-text-encoding";
import <PERSON><PERSON> from "joi";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { Alert, StyleSheet, View } from "react-native";
import { Snackbar, Text } from "react-native-paper";
import Button from "../../../components/Button";
import { DialogToConfirm } from "../../../components/DialogToConfirm";
import HeaderBar from "../../../components/HeaderBar";
import { LoadingIndicator } from "../../../components/LoadingIndicator";
import ScreenWrapper from "../../ScreenWrapper";
//import ControlledTextInput from "../../../components/ControlledTextInput";
import { FormListNodesMapper } from "../../../components/FormListNodesMapper";
import { creatMMKVStore, makeStoredValueToFormValue } from "../../../services/local-storage";
import { callOneByOne, makeDataFeeder, parseIndustryCode } from "../../../utils";
import { checkPermits } from "../../../utils/permits";
import { isServerDataNewer, makeGetClientCversion } from "../../../utils/records";
import { validatorBase } from "../../../utils/validatorBase";

// 新组件需要重新!!
import { UPD_WATER_BALANCE_RECORDS as pageMainKey } from "../../../config/keysConfig";
//import { wbWaterUsageUnitsRecordingsStates as selectorStates } from "../../../hooks/selectorStates";
import {
    makeReqDelWaterBalanceRecords as recordDeleteQueryMaker,
    makeReqGetWaterBalanceRecords as recordSelectQueryMaker,
    makeReqUpdWaterBalanceRecords as recordUpdateQueryMaker,
} from "../../../api/projectRecordsQueries";
import { onPreSubmitError } from "../../../utils/screens";

//import { waterUnitScaleEnum } from "../../../config/waterBalance";
//import { roundNearest } from "../../../utils/numeric";
//import log from "../../../services/logging";


const dataFeeder = makeDataFeeder();

/**
 * 人均生活用水量、单位面积绿化用水量计算表(参考计算)
 * 目前只有表有此需求, 因此在表格类型列表中只有表项目会显示出来.
 * 修改记录: 250213, 全部去掉年份字段, 方便用书分析表提取数据, 原来被关联的近三年表不再从此取数据
 * @param {object} args
 * @param {object} args.navigation
 * @param {object} args.route
 * @returns
 */
const RecordsUpdating = ({ navigation, route }) => {
    console.log("RecordsUpdating pageMeta from nav:", route.params.pageMeta);
    console.log("RecordsUpdating projMeta from nav:", route.params.projMeta);
    const projSubclass = route.params.projMeta.subclass; // 1表, 2书
    const projIndustry = parseIndustryCode(route.params.projMeta.industry); // 1 -> industry, 2 -> service

    //const waterClass = 1;

    // formType对应着服务端的form-type, 为0时表示表单是用户填写, 为1表示服务端生成, 为1时只读
    const { queryKwd, id: recordPubid, name: recordName, formType } = route.params.pageMeta; // recordPubid is the alias of id
    const saveButtonIcon = "content-save-all-outline";
    const formEditableGlobal = checkPermits() ? (formType === 0 ? true : false) : false;

    // 使用memoize来优化
    const { setStore, getStore, clearStore, setStoreObject } = useMemo(() => {
        console.log("creatMMKVStore memo:", pageMainKey.store, queryKwd, recordPubid);
        return creatMMKVStore(`${pageMainKey.store}/${queryKwd}/${recordPubid}`);
    }, [queryKwd, recordPubid]);

    const storedValueToFormValue = useRef(makeStoredValueToFormValue(getStore));
    const getClientCversion = useRef(makeGetClientCversion(() => getStore("mainCversion"), () => getStore("subCversion")));

    // 客户端cversion = `${mainCversion}.${subCversion}`
    (getStore("mainCversion") === undefined) ? setStore("mainCversion", 0) : undefined; // 客户端主版本号
    (getStore("subCversion")  === undefined) ? setStore("subCversion",  0) : undefined; // 客户端次版本号, 修改一次自增一次
    //let mainCversion = getStore("mainCversion"); // 主版本号, 与服务端cversion对应
    // 次版本号, 表示本地更改次数, 用于程序上对子版本号的增加, 用于判断与服务端版本的差异, 同时可用于判断避免重复从MMKV获取数据
    const subCversionRef = useRef(getStore("subCversion"));

    // 新组件需要修改!!
    const bottomBarLabels = { ok: "提交" };                    // 底部按钮显示文字
    const [snackBarMessage, setSnackBarMessage] = useState("更新表单遇到错误"); // 下方错误提示文字内容
    /*
    const [
        waterUnitScaleState,
        startDatePickerState,
        endDatePickerState,
        setWaterUnitScaleState,
        setStartDatePickerState,
        setEndDatePickerState,
        resetSelectorStates,
    ] = selectorStates(useShallow(state => [
        state.waterUnitScale,
        state.startDatePicker,
        state.endDatePicker,
        state.setWaterUnitScale,
        state.setStartDatePicker,
        state.setEndDatePicker,
        state.resetStates,
    ])); // radio组件状态
*/
    const [confimDialogConfig, setConfirmDialogConfig] = useState({});       // 对话框信息, {title, text, okLabel, onOK}, 但显示对话框的变量在showConfirm

    // 新组件不需改动
    const [screenTitle,      setScreenTitle]      = useState(route.params.pageMeta.name); // 屏幕上方导航栏标题
    const [okButtonLoading,  setOkButtonLoading]  = useState(false);            // 右下方按钮图标是否旋转
    const [okButtonDisabled, setOkButtonDisabled] = useState(!checkPermits());  // 右下方按钮是否禁用
    const [showSnackbar,     setShowSnackbar]     = useState(false);            // 下方是否显示错误通知
    const [showConfirm,      setShowConfirm]      = useState(false);            // 是否显示确认对话框
    const [loadingIndicatorVisible, setLoadingIndicatorVisible] = useState(false); // 是否显示loading
    const delQueryInfo = useRef({ id: recordPubid, name: recordName });         // 用于删除本记录的查询变量

    //const waterTypeStateDataProviderRef     = useRef([{ id: 1, name: "自来水" }, { id: 2, name: "地表水" }, { id: 3, name: "地下水" }, { id: 4, name: "市政中水" }]);
    //const accuracyLevelStateDataProviderRef = useRef([{ id: 1, name: "一级" }, { id: 2, name: "二级" }, ]);
    //const equipStateStateDataProviderRef    = useRef([{ id: 1, name: "合格" }, { id: 2, name: "不合格" },]);
    //const waterUnitScaleStateDataProviderRef = useRef(waterUnitScaleEnum);

    // 用于存储waterScale的值, 当unitToggleState发生改变时需要通过该值更新表单字段
    //const waterUnitScaleRef = useRef(0);

    // Define form
    // 新组件需要重新定义!!
    const schema = Joi.object({
        name:            validatorBase.waterBalance.commons.name,
        year:            validatorBase.waterBalance.commons.yearFieldUnrequired,
        aopDays:         validatorBase.waterBalance.commons.intField,
        waterUsers:      validatorBase.waterBalance.commons.intField,
        itTotalWater:    validatorBase.waterBalance.commons.floatFieldUnrequired,
        itBuildArea:     validatorBase.waterBalance.commons.floatFieldUnrequired,
        itLifeWater:     validatorBase.waterBalance.commons.floatFieldUnrequired,
        itGreenArea:     validatorBase.waterBalance.commons.floatFieldUnrequired,
        itGreenWater:    validatorBase.waterBalance.commons.floatFieldUnrequired,
        itProdOutput:    validatorBase.waterBalance.commons.longTextField,
        itAvgLifeWater:  validatorBase.waterBalance.commons.floatFieldUnrequired,
        itAvgGreenWater: validatorBase.waterBalance.commons.floatFieldUnrequired,
        itAvgProdWater:  validatorBase.waterBalance.commons.longTextField,
        itAvgBuildWater: validatorBase.waterBalance.commons.floatFieldUnrequired,
        stBuildArea:     validatorBase.waterBalance.commons.floatFieldUnrequired,
        stLifeWater:     validatorBase.waterBalance.commons.floatFieldUnrequired,
        stGreenArea:     validatorBase.waterBalance.commons.floatFieldUnrequired,
        stGreenWater:    validatorBase.waterBalance.commons.floatFieldUnrequired,
        stAvgLifeWater:  validatorBase.waterBalance.commons.floatFieldUnrequired,
        stAvgGreenWater: validatorBase.waterBalance.commons.floatFieldUnrequired,
        stAvgBuildWater: validatorBase.waterBalance.commons.floatFieldUnrequired,
        // 固定字段
        others:           validatorBase.waterBalance.commons.textField,
        remarks:          validatorBase.waterBalance.commons.longTextField,
    });

    // 新组件需要重新定义defaultValues!!
    const {
        handleSubmit,
        control,
        resetField,
        reset,
        formState: { errors },
        //setValue,
        //getValues,
    } = useForm({
        resolver: joiResolver(schema),
        //reValidateMode: "onBlur", // https://github.com/orgs/react-hook-form/discussions/10327
        defaultValues: { // 默认值在组件载入时会被更新, 这里的初始值正常情况下没有用处
            // 注意, 默认为0的是允许默认为0的值, 或者通过计算得到的值
            name:            "",
            year:            "",
            aopDays:         "",
            waterUsers:      "",
            itTotalWater:    "",
            itBuildArea:     "",
            itLifeWater:     "",
            itGreenArea:     "",
            itGreenWater:    "",
            itProdOutput:    "",
            itAvgLifeWater:  "",
            itAvgGreenWater: "",
            itAvgProdWater:  "",
            itAvgBuildWater: "",
            stBuildArea:     "",
            stLifeWater:     "",
            stGreenArea:     "",
            stGreenWater:    "",
            stAvgLifeWater:  "",
            stAvgGreenWater: "",
            stAvgBuildWater: "",
            // static fields
            others:           "",
            remarks:          "",
        },
    });

    // Query: select record
    const onRecordSelectSuccess = (data) => { // data: {"id": 3, "leader": 0, "name": "DEPT-03", "superior": 0}
        // 注意, 进入onSuccess之前就意味着服务端版本比客户端版本新, 因此要更新客户端主版本号
        if (data.STATUS === 0) {
            setStore("mainCversion", data.DATA.cversion);
            setStore("subCversion", 0);
            console.log("onRecordSelectSuccess.........:", data.DATA);

            // 在此置为空串, 使得表单会显示占位文本
            const formObject = {
                name:            String(data.DATA.name            || ""),
                year:            String(data.DATA.year            || ""), // String(data.DATA.year            || thisYear())
                aopDays:         String(data.DATA.aopDays         || ""),
                waterUsers:      String(data.DATA.waterUsers      || ""),
                itTotalWater:    String(data.DATA.itTotalWater    || ""),
                itBuildArea:     String(data.DATA.itBuildArea     || ""),
                itLifeWater:     String(data.DATA.itLifeWater     || ""),
                itGreenArea:     String(data.DATA.itGreenArea     || ""),
                itGreenWater:    String(data.DATA.itGreenWater    || ""),
                itProdOutput:    String(data.DATA.itProdOutput    || ""),
                itAvgLifeWater:  String(data.DATA.itAvgLifeWater  || ""),
                itAvgGreenWater: String(data.DATA.itAvgGreenWater || ""),
                itAvgProdWater:  String(data.DATA.itAvgProdWater  || ""),
                itAvgBuildWater: String(data.DATA.itAvgBuildWater || ""),
                stBuildArea:     String(data.DATA.stBuildArea     || ""),
                stLifeWater:     String(data.DATA.stLifeWater     || ""),
                stGreenArea:     String(data.DATA.stGreenArea     || ""),
                stGreenWater:    String(data.DATA.stGreenWater    || ""),
                stAvgLifeWater:  String(data.DATA.stAvgLifeWater  || ""),
                stAvgGreenWater: String(data.DATA.stAvgGreenWater || ""),
                stAvgBuildWater: String(data.DATA.stAvgBuildWater || ""),
                others:          String(data.DATA.others          || ""),
                remarks:         String(data.DATA.remarks         || ""),
            };
            reset(formObject);           // 重置react-form

            const storeObjects = checkPermits() && (formType === 0) && {
                ...formObject,
                //waterType:     radioIdToObject(waterTypeStateDataProviderRef.current,     data.DATA.waterType),
                //accuracyLevel: radioIdToObject(accuracyLevelStateDataProviderRef.current, data.DATA.accuracyLevel),
                //equipState:    radioIdToObject(equipStateStateDataProviderRef.current,    data.DATA.equipState),
            };
            storeObjects && setStoreObject(storeObjects, false); // 判断用户权限, 把服务端数据写到mmkv存储, 只有可写用户才需要本地存储, 不会首先清空老数据

            // 设置selector菜单状态
            //setWaterUnitScaleState(radioIdToObject(waterUnitScaleStateDataProviderRef.current, data.DATA.waterScale));
            //setStartDatePickerState(formObject.startDate);
            //setEndDatePickerState(formObject.endDate);

            //waterUnitScaleRef.current = data.DATA.waterScale || 0; // 根据服务端数据更新
            // 设置屏幕标题
            (screenTitle !== formObject.name) && setScreenTitle(formObject.name);
        } else {
            console.log("STATUS !== 0", data.STATUS);
            setSnackBarMessage(`表单数据拉取发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        }
    };
    const onRecordSelectError = (err) => {
        console.log("Record selecting error:", err);
    };
    const onRecordSelectSettled = (err) => {
        loadingIndicatorVisible && setLoadingIndicatorVisible(false);
    };
    const getPath = formType === 0 ? "get" : "getsum";
    const recordSelectQuery = recordSelectQueryMaker([queryKwd, getPath, recordPubid], onRecordSelectSuccess, onRecordSelectError, onRecordSelectSettled);

    // Query: update record
    const onHandleSubmit = (data) => {
        setOkButtonDisabled(true);
        setOkButtonLoading(true);
        dataFeeder({ ...data,
            cversion: getClientCversion.current(),
            name: `用水量计算(年运营${data.aopDays}天, 用水${data.waterUsers}人)`,
            // unifify data units
            // ...
        }); // append client cversion
        recordUpdateQuery.mutate();
    };
    // 新组件不需改动
    const onRecordUpdateSuccess = (data) => {
        console.log(" Record update success response data: ", data);
        if(data.STATUS !== 0) {
            setSnackBarMessage(`表单更新发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        } else {
            // 设置确认框数据
            setConfirmDialogConfig({
                title: "更新表单",
                text: "表单信息更新成功!",
                okLabel: "确定",
                onOK: () => {
                    setShowConfirm(false);
                    recordSelectQuery.mutate();
                }});
            setShowConfirm(true);
        }
    };
    const onRecordUpdateError = (error) => {
        setSnackBarMessage(`表单更新发生错误, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const onRecordUpdateSettled = (data, error) => {
        setOkButtonDisabled(false);
        setOkButtonLoading(false);
    };
    const recordUpdateQuery = recordUpdateQueryMaker(dataFeeder, [queryKwd, "upd", recordPubid], onRecordUpdateSuccess, onRecordUpdateError, onRecordUpdateSettled);

    // Query: delete record
    const recordDeleteOnSuccess = () => {
        clearStore();
        navigation.goBack();
    };
    const recordDeleteOnError = (error) => {
        setSnackBarMessage(`表单删除失败, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const recordDeleteQuery = recordDeleteQueryMaker([queryKwd, "del", recordPubid], recordDeleteOnSuccess, recordDeleteOnError);

    const deleteAlert = () => {
        return (
            Alert.alert("删除表单", `删除后不可撤销, 请确认是否删除: ${delQueryInfo.current?.name}?`, [
                { text: "取消" },
                { text: "确定", onPress: () => recordDeleteQuery.mutate() },
            ])
        );
    };

    const clearCache = () => {
        return (
            Alert.alert("清空本地缓存", "1. 用于未知原因导致数据无法提交的情况\r\n2. 未提交的内容将会丢失, 请做好数据截图备份\r\n3. 已提交到服务端的数据不受影响\r\n4. 确认清空请点击“确认”按钮", [
                { text: "取消" },
                { text: "确定", onPress: () => { clearStore(); navigation.goBack(); } },
            ])
        );
    };

    /*
    const aopDays =       Number(useWatch({ control, name: "aopDays" }));
    const employees =     Number(useWatch({ control, name: "employees" }));
    const greenArea =     Number(useWatch({ control, name: "greenArea" }));
    const domesticWater = Number(useWatch({ control, name: "domesticWater" }));
    const greeningWater = Number(useWatch({ control, name: "greeningWater" }));

    //工业: 计算单位绿化面积用水量, 服务业: 计算单位建筑面积用水量
    const caclAvgGreenWaterConsume = (aopDays, employees, greenArea, domesticWater, greeningWater) => {
        if (projIndustry === "industry") {
            return greenArea ? greeningWater / greenArea : 0;
        } else if (projIndustry === "service") {
            return greenArea ? domesticWater / greenArea *  aopDays : 0;
        } else {
            return 0;
        }
    };

    const updateGlobalState = debounce((aopDays, employees, greenArea, domesticWater, greeningWater) => {
        const avgDomesticWater_ = employees ? domesticWater * aopDays / employees : 0;
        const avgGreeningWater_ = caclAvgGreenWaterConsume(aopDays, employees, greenArea, domesticWater, greeningWater);
        setValue("avgDomesticWater", `${roundNearestTo(avgDomesticWater_)}`);
        setValue("avgGreeningWater", `${roundNearestTo(avgGreeningWater_)}`);
        setStore("avgDomesticWater", getValues("avgDomesticWater"));
        setStore("avgGreeningWater", getValues("avgGreeningWater"));
        subCversionRef.current++;
    }, 100);

    // 注意, 这个useEffect必需在恢复本地存储之前运行, 否则初始化时不会触发
    useEffect(() => {

        (formType === 0) && updateGlobalState(aopDays, employees, greenArea, domesticWater, greeningWater);
    }, [aopDays, employees, greenArea, domesticWater, greeningWater]);
*/

    // Hook: onMount fetch data
    useEffect(() => {
        // 优化方案: 根据用户权限进行mutate, 只有可写用户才需要本地存储
        const localCversion = getClientCversion.current();
        const remoteCversion = route.params.pageMeta.cversion;
        console.log("localCversion:", localCversion, "remoteSversion:", remoteCversion, "subCversionRef:", subCversionRef.current);

        const restoreLocalData = () => {
            const formObject = { // 默认值用户表单的数据显示
                name:            storedValueToFormValue.current("name"),
                year:            storedValueToFormValue.current("year"),
                aopDays:         storedValueToFormValue.current("aopDays"),
                waterUsers:      storedValueToFormValue.current("waterUsers"),
                itTotalWater:    storedValueToFormValue.current("itTotalWater"),
                itBuildArea:     storedValueToFormValue.current("itBuildArea"),
                itLifeWater:     storedValueToFormValue.current("itLifeWater"),
                itGreenArea:     storedValueToFormValue.current("itGreenArea"),
                itGreenWater:    storedValueToFormValue.current("itGreenWater"),
                itProdOutput:    storedValueToFormValue.current("itProdOutput"),
                itAvgLifeWater:  storedValueToFormValue.current("itAvgLifeWater"),
                itAvgGreenWater: storedValueToFormValue.current("itAvgGreenWater"),
                itAvgProdWater:  storedValueToFormValue.current("itAvgProdWater"),
                itAvgBuildWater: storedValueToFormValue.current("itAvgBuildWater"),
                stBuildArea:     storedValueToFormValue.current("stBuildArea"),
                stLifeWater:     storedValueToFormValue.current("stLifeWater"),
                stGreenArea:     storedValueToFormValue.current("stGreenArea"),
                stGreenWater:    storedValueToFormValue.current("stGreenWater"),
                stAvgLifeWater:  storedValueToFormValue.current("stAvgLifeWater"),
                stAvgGreenWater: storedValueToFormValue.current("stAvgGreenWater"),
                stAvgBuildWater: storedValueToFormValue.current("stAvgBuildWater"),
                others:           storedValueToFormValue.current("others"),
                remarks:          storedValueToFormValue.current("remarks"),
            };
            reset(formObject); // 重置react-form数据

            // 设置selector数据
            //const defaultWaterUnitScale = 0;
            //setWaterUnitScaleState(getStore("waterScale") || radioIdToObject(waterUnitScaleStateDataProviderRef.current, defaultWaterUnitScale));
            //setStartDatePickerState(formObject.startDate);
            //setEndDatePickerState(formObject.endDate);

            //waterUnitScaleRef.current = getStore("waterScale")?.id || defaultWaterUnitScale; // 根据本地存储更新
        };

        // 根据反馈, 要求默认显示0
        if (localCversion === 0) {
            recordSelectQuery.mutate();
            return;
        }

        if(formType === 1) {
            recordSelectQuery.mutate();
        } else if (localCversion === 0 && remoteCversion > 0) {
            console.log("Local cversion === 0, serve may have something nontrivial initialized, query mutate!");
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        } else if (isServerDataNewer(localCversion, remoteCversion) && subCversionRef.current > 0) { // 服务端有更新数据, 但本地有修改
            console.log("Client cversion fell behind the server cversion, someone might modified the database, choose to use the data version!");
            Alert.alert("注意", "由于其他人向服务器提交了数据, 服务端数据比本地数据新, 请选择使用哪份数据?", [
                { text: "使用本地数据", onPress: () => { setStore("mainCversion", remoteCversion); restoreLocalData(); } },
                { text: "使用服务端数据", onPress: () => { setLoadingIndicatorVisible(true); recordSelectQuery.mutate(); } },
            ]);
        } else if (isServerDataNewer(localCversion, remoteCversion)) { // localCversion==0时要mutate是因为服务端可能在创建记录时生成了必要数据
            console.log("Server cversion may be newer, query mutate:", localCversion, remoteCversion);
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        }
        else {
            console.log("Local cversion >= server cversion, query will not mutate!");
            restoreLocalData();
        }
    }, []);


    // remarks不需配置
    const FieldsConfig_1_book = {
        industry: [
            {
                inputs: [
                    //{ name: "name",       label: "表单名称",      unit: "", type: "PLAIN", editable: true, multiline: true, },
                    //{ name: "year",          label: "年份",                     unit: "",        type: "PLAIN", editable: true, placeholder: "", },
                    { name: "aopDays",         label: "年运营天数",               unit: "",        type: "PLAIN", editable: true, placeholder: "", },
                    { name: "waterUsers",      label: "用水人数",                 unit: "",        type: "PLAIN", editable: true, placeholder: "", },
                    { name: "itBuildArea",     label: "建筑面积",                 unit: "㎡",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "itGreenArea",     label: "绿化面积",                 unit: "㎡",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "itTotalWater",    label: "总取水量(不含非生产用水)",   unit: "m³/d",    type: "PLAIN", editable: false, placeholder: "提交后自动计算", },
                    { name: "itLifeWater",     label: "生活用水量",               unit: "m³/d",    type: "PLAIN", editable: false, placeholder: "提交后自动计算", },
                    { name: "itGreenWater",    label: "绿化用水量",               unit: "m³/a",    type: "PLAIN", editable: false, placeholder: "提交后自动计算", },
                    { name: "itProdOutput",    label: "产品产量(每天)",           unit: "",        type: "PLAIN", editable: false, placeholder: "提交后自动计算", props: {multiline: true}, },
                    { name: "itAvgLifeWater",  label: "人均生活用水量",           unit: "m³/人·a", type: "PLAIN", editable: false, placeholder: "提交后自动计算", },
                    { name: "itAvgGreenWater", label: "单位绿化面积用水量",       unit: "m³/m²·a", type: "PLAIN", editable: false, placeholder: "提交后自动计算", },
                    { name: "itAvgProdWater",  label: "单位产品取水量",           unit: "m³",      type: "PLAIN", editable: false, placeholder: "提交后自动计算", props: {multiline: true}, },
                    { name: "itAvgBuildWater", label: "单位建筑面积取水量",       unit: "m³/m²·a", type: "PLAIN", editable: false, placeholder: "提交后自动计算", },
                ]
            },
        ],
        service: [
            {
                inputs: [
                    //{ name: "name",       label: "表单名称",      unit: "", type: "PLAIN", editable: true, multiline: true, },
                    //{ name: "year",          label: "年份",                     unit: "",        type: "PLAIN", editable: true, placeholder: "", },
                    { name: "aopDays",         label: "年运营天数",               unit: "",        type: "PLAIN", editable: true, placeholder: "", },
                    { name: "waterUsers",      label: "用水人数",                 unit: "",        type: "PLAIN", editable: true, placeholder: "", },
                    { name: "stBuildArea",     label: "建筑面积",                 unit: "㎡",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "stGreenArea",     label: "绿化面积",                 unit: "㎡",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "stLifeWater",     label: "生活取水量",               unit: "m³/d",    type: "PLAIN", editable: false, placeholder: "提交后自动计算", },
                    { name: "stGreenWater",    label: "绿化用水量",               unit: "m³/d",    type: "PLAIN", editable: false, placeholder: "提交后自动计算", },
                    { name: "stAvgLifeWater",  label: "人均生活用水量",           unit: "m³/人·a", type: "PLAIN", editable: false, placeholder: "提交后自动计算", },
                    { name: "stAvgGreenWater", label: "单位绿化面积用水量",       unit: "m³/m²·a", type: "PLAIN", editable: false, placeholder: "提交后自动计算", },
                    { name: "stAvgBuildWater", label: "单位建筑面积取水量",       unit: "m³/m²·a", type: "PLAIN", editable: false, placeholder: "提交后自动计算", },
                ]
            },
        ],
    };
    const FieldsConfig_1_table = {
        industry: [
            {
                inputs: [
                    //{ name: "name",       label: "表单名称",      unit: "", type: "PLAIN", editable: true, multiline: true, },
                    //{ name: "year",          label: "年份",                     unit: "",        type: "PLAIN", editable: true, placeholder: "", },
                    { name: "aopDays",         label: "年运营天数",               unit: "",        type: "PLAIN", editable: true, placeholder: "", },
                    { name: "waterUsers",      label: "用水人数",                 unit: "",        type: "PLAIN", editable: true, placeholder: "", },
                    { name: "itBuildArea",     label: "建筑面积",                 unit: "㎡",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "itGreenArea",     label: "绿化面积",                 unit: "㎡",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "itTotalWater",    label: "总取水量(不含非生产用水)",   unit: "m³/d",    type: "PLAIN", editable: false, placeholder: "提交后自动计算", },
                    { name: "itLifeWater",     label: "生活用水量",               unit: "m³/d",    type: "PLAIN", editable: false, placeholder: "提交后自动计算", },
                    { name: "itGreenWater",    label: "绿化用水量",               unit: "m³/a",    type: "PLAIN", editable: false, placeholder: "提交后自动计算", },
                    { name: "itProdOutput",    label: "产品产量(每天)",           unit: "",        type: "PLAIN", editable: false, placeholder: "提交后自动计算", props: {multiline: true}, },
                    { name: "itAvgLifeWater",  label: "人均生活用水量",           unit: "m³/人·a", type: "PLAIN", editable: false, placeholder: "提交后自动计算", },
                    { name: "itAvgGreenWater", label: "单位绿化面积用水量",       unit: "m³/m²·a", type: "PLAIN", editable: false, placeholder: "提交后自动计算", },
                    { name: "itAvgProdWater",  label: "单位产品取水量",           unit: "m³",      type: "PLAIN", editable: false, placeholder: "提交后自动计算", props: {multiline: true}, },
                    { name: "itAvgBuildWater", label: "单位建筑面积取水量",       unit: "m³/m²·a", type: "PLAIN", editable: false, placeholder: "提交后自动计算", },
                ]
            },
        ],
        service: [
            {
                inputs: [
                    //{ name: "name",       label: "表单名称",      unit: "", type: "PLAIN", editable: true, multiline: true, },
                    //{ name: "year",          label: "年份",                     unit: "",        type: "PLAIN", editable: true, placeholder: "", },
                    { name: "aopDays",         label: "年运营天数",               unit: "",        type: "PLAIN", editable: true, placeholder: "", },
                    { name: "waterUsers",      label: "用水人数",                 unit: "",        type: "PLAIN", editable: true, placeholder: "", },
                    { name: "stBuildArea",     label: "建筑面积",                 unit: "㎡",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "stGreenArea",     label: "绿化面积",                 unit: "㎡",      type: "PLAIN", editable: true, placeholder: "", },
                    { name: "stLifeWater",     label: "生活取水量",               unit: "m³/d",    type: "PLAIN", editable: false, placeholder: "提交后自动计算", },
                    { name: "stGreenWater",    label: "绿化用水量",               unit: "m³/d",    type: "PLAIN", editable: false, placeholder: "提交后自动计算", },
                    { name: "stAvgLifeWater",  label: "人均生活用水量",           unit: "m³/人·a", type: "PLAIN", editable: false, placeholder: "提交后自动计算", },
                    { name: "stAvgGreenWater", label: "单位绿化面积用水量",       unit: "m³/m²·a", type: "PLAIN", editable: false, placeholder: "提交后自动计算", },
                    { name: "stAvgBuildWater", label: "单位建筑面积取水量",       unit: "m³/m²·a", type: "PLAIN", editable: false, placeholder: "提交后自动计算", },
                ]
            },
        ],
    };

    const FieldsConfig_2_book  = {industry: [], service: []};
    const FieldsConfig_2_table = {industry: [], service: []};

    const FieldsConfig_3_book = {
        industry: [
        // 其它部分
            {
                inputs: [
                //{ name: "others", label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "",  props: {multiline: true}, },
                ]
            },
        ],
        service: [
            // 其它部分
            {
                inputs: [
                    //{ name: "others", label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "",  props: {multiline: true}, },
                ]
            },
        ],
    };
    const FieldsConfig_3_table = {
        industry:[
        // 其它部分
            {
                inputs: [
                //{ name: "others", label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "",  props: {multiline: true}, },
                ]
            },
        ],
        service:[
            // 其它部分
            {
                inputs: [
                    //{ name: "others", label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "",  props: {multiline: true}, },
                ]
            },
        ],
    };

    let FieldsConfig = [];
    switch(projSubclass) {
        case 1: // 表
            FieldsConfig = [...FieldsConfig_1_table[projIndustry], ...FieldsConfig_2_table[projIndustry], ...FieldsConfig_3_table[projIndustry]];
            break;
        case 2: // 书
            FieldsConfig = [...FieldsConfig_1_book[projIndustry], ...FieldsConfig_2_book[projIndustry], ...FieldsConfig_3_book[projIndustry]];
            break;
        default:
            console.warn("Unknown subclass:", projSubclass);
            FieldsConfig = [];
            break;
    }

    //const FieldsConfig = [...FieldsConfig_1_book, ...FieldsConfig_2_book, ...FieldsConfig_3_book];

    return (
        <View style={{ flex: 1 }}>

            <HeaderBar
                //title={screenTitle}
                navigation={navigation}
                goBackCallback={() => {
                    callOneByOne(
                        //() => { _mainCversionGetter=undefined; _subCversionGetter=undefined;},
                        //resetSelectorStates,
                    );
                    /*callOneByOne(clearStore, resetSelectorStates)*/  // 返回前清空mmkv存储因为临时数据不再需要, 重置状态使得页面切换后不会使用前面的数据
                }}
                menuItemArray={formType === 0 ? [{ title: "删除表单", action: deleteAlert }, { title: "清空缓存", action: clearCache }] : []}
            />

            <ScreenWrapper contentContainerStyle={styles.container}>

                <View style={styles.formEntry}>
                    <FormListNodesMapper
                        fieldsConfig={FieldsConfig}
                        subCversionRef={subCversionRef}
                        control={control}
                        setStore={setStore}
                        resetField={resetField}
                        errors={errors}
                        setSnackBarMessage={setSnackBarMessage}
                        setShowSnackbar={setShowSnackbar}
                        //formDisabledGlobal={formDisabledGlobal}
                        editable={formEditableGlobal}
                        rowStyle={styles.rowContainer}
                    />

                    {/*formType === 0 && <ControlledTextInput
                        name="remarks"
                        rowLabel="备注"
                        control={control}
                        placeholder=""
                        onChangeText={(text) => {
                            subCversionRef.current++;
                            setStore("subCversion", subCversionRef.current);
                            setStore("remarks", text);
                        }}
                        onClearText={() => {
                            subCversionRef.current++;
                            setStore("subCversion", subCversionRef.current);
                            setStore("remarks", "");
                            resetField("remarks", { defaultValue: "" });
                        }}
                        editable={formEditableGlobal}
                        mode="flat"
                        multiline={true}
                        formError={errors}
                        style={styles.rowContainerLongText}
                        //disabled={formDisabledGlobal}
                        required={false}
                    />*/}

                    <View style={{marginTop: 15}}>
                        <Text>注: 自动计算字段会在本表单和相关数据表单提交后自动计算, 如果相关数据尚未填写, 则仍然显示为空!</Text>
                    </View>

                </View>

                {formType === 0 && <View style={styles.button}>
                    <Button mode="contained"
                        disabled={okButtonDisabled}
                        loading={okButtonLoading}
                        icon={saveButtonIcon}
                        onPress={handleSubmit(onHandleSubmit, (formErr) => onPreSubmitError(formErr, getStore, "WaterUsageCalcRecordsUpdating"))}
                    >{bottomBarLabels.ok}</Button>
                </View>}
            </ScreenWrapper>

            <LoadingIndicator
                title={""}
                message={"加载中....."}
                visible={loadingIndicatorVisible}
                onClose={()=>setLoadingIndicatorVisible(false)}
            />

            <Snackbar
                style={styles.snackBar}
                visible={showSnackbar}
                onDismiss={() => setShowSnackbar(false)}
                onIconPress={() => setShowSnackbar(false)}
                duration={Snackbar.DURATION_LONG}
            >
                {snackBarMessage}
            </Snackbar>

            <DialogToConfirm
                visible={showConfirm}
                title={confimDialogConfig.title ? confimDialogConfig.title : ""}
                text={confimDialogConfig.text ? confimDialogConfig.text : ""}
                onOK={confimDialogConfig.onOK ? confimDialogConfig.onOK : () => setShowConfirm(false)}
                okBtnLabel={confimDialogConfig.okLabel ? confimDialogConfig.okLabel : "确认"}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    rowContainer:{
        firstColumn: {
            flex: 1,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 1,
        // /borderWidth: 1,
        // height: 40,
        },
    },
    rowContainerLongText: {
        firstColumn: {
            flex: 1,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 4,
            // /borderWidth: 1,
            // height: 40,
        },
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        width: "80%",
        alignSelf: "center",
    },
    formEntry: {
        margin: 8,
    },
    snackBar: {
        flexDirection: "column",
    },
});

export default RecordsUpdating;
