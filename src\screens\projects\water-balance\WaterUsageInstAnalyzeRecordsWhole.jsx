import React, { useEffect, useRef, useState } from "react";
import { Snackbar, DataTable, Text, useTheme } from "react-native-paper";
import { StyleSheet, View, ScrollView } from "react-native";

import cloneDeep from "lodash/cloneDeep";
import ScreenWrapper from "../../ScreenWrapper";
import HeaderBar from "../../../components/HeaderBar";
import { DialogToConfirm } from "../../../components/DialogToConfirm";
import { LoadingIndicator } from "../../../components/LoadingIndicator";


// 新组件需要重新!!
import {
    makeReqGetWaterBalanceRecords as recordSelectQueryMaker,
} from "../../../api/projectRecordsQueries";
import { roundNearest, roundNearestTo, roundPercent } from "../../../utils/numeric";


/**
 * 用水单位用水分析表
 * 注意, 项目数据表单与工作台管理表单的数据有一个显著的区别:
 * 项目数据不需要像用户编号那样脱敏, 两边传输的数据是直接的, 不需要做{id, name}这种映射, 部分需要映射的可以在客户端本地完成,
 * DataTable styles: https://github.com/callstack/react-native-paper/issues/1113
 * @param {object} args
 * @param {object} args.navigation
 * @param {object} args.route
 * @returns
 */
const RecordsWhole = ({ navigation, route }) => {
    console.log("RecordsWhole pageMeta from nav:", route.params.pageMeta);
    console.log("RecordsWhole projMeta from nav:", route.params.projMeta);

    // 注意, id原本是消息的pubid, 但在其它屏幕, 中间经过RecordListingTemplate组件后, pageMeta会被替换, id会变成对应表格记录的pubid
    // 在这里, pageMeta仍然是消息屏幕传来的pageMeta, id是消息的pubid, 而contentId是projId
    const { queryKwd, contentId: projId, name: recordName, formType } = route.params.pageMeta; // projId is the alias of id
    //const saveButtonIcon = "content-save-all-outline";

    const theme = useTheme();

    const tableContents = useRef({});
    const tableHeaderConfig = useRef([
        { name: "unitName",     type: "",                    label: "用水单元" },
        { name: "inputTotal",   type: "numeric", scale: 1,   label: "用水量(m³)" },
        { name: "inputPercent", type: "numeric", scale: 100, label: "占总用水量比例(%)" },
        { name: "intakeTotal",  type: "numeric", scale: 1,   label: "取水量(m³)" },
        { name: "intakePercent",type: "numeric", scale: 100, label: "占总取水量比例(%)" },
        { name: "recycleTotal", type: "numeric", scale: 1,   label: "重复利用水量(m³)" },
        { name: "dumpSewage",   type: "numeric", scale: 1,   label: "排至污水处理站水量(m³)" },
        { name: "dumpOutside",  type: "numeric", scale: 1,   label: "排水量(排至厂外)(m³)" },
        { name: "waterConsume", type: "numeric", scale: 1,   label: "耗水量(m³)" },
        { name: "waterLeakage", type: "numeric", scale: 1,   label: "漏损水量(m³)" },
    ]);
    const ratesHeaderConfig = useRef([
        { name: "dccwCircleRate",       type: "numeric", scale: 100, label: "直接冷却水循环率(%)" },
        { name: "scrCircleRate",        type: "numeric", scale: 100, label: "冷凝水回用率(%)" },
        { name: "leakageRate",          type: "numeric", scale: 100, label: "漏损率(%)" },
        { name: "complianceOutputRate", type: "numeric", scale: 100, label: "达标排放率(%)" },
        { name: "reuseRate",            type: "numeric", scale: 100, label: "重复利用率(%)" },
        { name: "iccwCircleRate",       type: "numeric", scale: 100, label: "间接冷却水循环率(%)" },
        { name: "dumpSweageRate",       type: "numeric", scale: 100, label: "排水率(%)" },
        { name: "sweageReuseRate",      type: "numeric", scale: 100, label: "废水回用率(%)" },
        { name: "irregularWaterRate",   type: "numeric", scale: 100, label: "非常规水资源替代率(%)" },
    ]);
    const waterIntakePerProdHeaderConfig = useRef([
        { name: "prodName", type: "",                  label: "产品名称" },
        { name: "wipup",    type: "numeric", scale: 1, label: "单位产品取水量(m³/t)" },
    ]);


    // 新组件需要修改!!
    //const bottomBarLabels = { ok: "提交" };                    // 底部按钮显示文字
    const [snackBarMessage, setSnackBarMessage] = useState("更新表单遇到错误"); // 下方错误提示文字内容


    const [confimDialogConfig, setConfirmDialogConfig] = useState({});       // 对话框信息, {title, text, okLabel, onOK}, 但显示对话框的变量在showConfirm

    // 新组件不需改动
    const [screenTitle]      = useState("用水单位用水分析"); // 屏幕上方导航栏标题
    //const [okButtonLoading,  setOkButtonLoading]  = useState(false);            // 右下方按钮图标是否旋转
    //const [okButtonDisabled, setOkButtonDisabled] = useState(!checkPermits());  // 右下方按钮是否禁用
    const [showSnackbar,     setShowSnackbar]     = useState(false);            // 下方是否显示错误通知
    const [showConfirm,      setShowConfirm]      = useState(false);            // 是否显示确认对话框
    const [loadingIndicatorVisible, setLoadingIndicatorVisible] = useState(false); // 是否显示loading

    const onRecordSelectSuccess = (data) => { // data: {"id": 3, "leader": 0, "name": "DEPT-03", "superior": 0}
        // 注意, 进入onSuccess之前就意味着服务端版本比客户端版本新, 因此要更新客户端主版本号
        console.log("onRecordSelectSuccess:", data);
        if (data.STATUS === 0) {
            console.log("onRecordSelectSuccess status OK", data);
            tableContents.current = cloneDeep(data.DATA);
        } else  {
            console.log("STATUS !== 0", data.STATUS);
            setSnackBarMessage(`表单数据拉取发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        }
    };
    const onRecordSelectError = (err) => {
        console.log("Record selecting error:", err);
    };
    const onRecordSelectSettled = (err) => {
        loadingIndicatorVisible && setLoadingIndicatorVisible(false);
    };
    //const recordSelectQuery = recordSelectQueryMaker([queryKwd, "get", projId], onRecordSelectSuccess, onRecordSelectError, onRecordSelectSettled);
    // formType为1或2, 这与water units recordings表的formType枚举不同, 1表示触发器插入或更新的数据, 2表示统计求和数据
    let apiPath = "getall";
    const recordSelectQuery = recordSelectQueryMaker([queryKwd, apiPath, projId], onRecordSelectSuccess, onRecordSelectError, onRecordSelectSettled);

    useEffect(() => {
        recordSelectQuery.mutate();
    }, []);

    /*
    const deleteAlert = () =>{
        return (
            Alert.alert("删除表单", `删除后不可撤销, 请确认是否删除: ${delQueryInfo.current?.name}?`, [
                { text: "取消" },
                { text: "确定", onPress: () => recordDeleteQuery.mutate() },
            ])
        );
    };*/

    console.log("tableContents.................: ", tableContents);
    return (
        <View style={{ flex: 1 }}>

            <HeaderBar
                //title={screenTitle}
                navigation={navigation}
                goBackCallback={() => {
                    /*callOneByOne(clearStore, resetSelectorStates)*/  // 返回前清空mmkv存储因为临时数据不再需要, 重置状态使得页面切换后不会使用前面的数据
                }}
                //menuItemArray={[{ title: "删除表单", action: deleteAlert }]}
            />

            <ScreenWrapper >

                {tableContents.current.classStats && tableContents.current.classStats.map((eachCollect, idx) => {
                    return (
                        <View key={`class-${idx}`} style={styles.tableWrapper}>
                            <View style={styles.tableTitleWrapper}>
                                <Text variant="titleLarge">
                                    {(eachCollect.waterClass === 1 && "主要生产用水") || (eachCollect.waterClass === 2 && "辅助生产用水") || (eachCollect.waterClass === 3 && "附属生产用水") }
                                </Text>
                            </View>
                            <DataTable style={{ ...styles.dataTable, borderColor: theme.colors.elevation.level5, }}>
                                <ScrollView horizontal contentContainerStyle={styles.scrollView}>
                                    <DataTable.Header>
                                        {tableHeaderConfig.current.map((titleItem, idx) => {
                                            return titleItem.type === "numeric" ?
                                                <DataTable.Title numeric key={`title-${idx}`} numberOfLines={2} style={styles.dataTableTitle}>{titleItem.label}</DataTable.Title>
                                                :
                                                <DataTable.Title key={`title-${idx}`} numberOfLines={2} style={styles.dataTableTitle}>{titleItem.label}</DataTable.Title>;
                                        })}
                                    </DataTable.Header>
                                    {eachCollect.rows.map((eachRow, idx) => {
                                        return (
                                            <DataTable.Row key={`row-${idx}`}>
                                                {tableHeaderConfig.current.map((eachColumn, idx) => {
                                                    //console.log("eachColumn", eachColumn);
                                                    return eachColumn.type === "numeric" ?
                                                        <DataTable.Cell numeric key={`cell-${idx}`} style={styles.dataTableCell}>
                                                            {roundNearestTo(eachRow[eachColumn.name] * eachColumn.scale,
                                                                (eachColumn.scale === 100 && 2) || (eachColumn.scale === 10000 && 4) || 3)}
                                                        </DataTable.Cell>
                                                        :
                                                        <DataTable.Cell key={`cell-${idx}`} style={styles.dataTableCell}>
                                                            {eachRow[eachColumn.name]}
                                                        </DataTable.Cell>;
                                                })}
                                            </DataTable.Row>
                                        );
                                    })}
                                </ScrollView>

                            </DataTable>

                        </View>
                    );
                })}

                {tableContents.current.rateStates && <View style={styles.tableWrapper}>
                    <View style={{ marginLeft: 20, marginBottom: 5 }}>
                        <Text variant="titleLarge">
                            {"统计结果"}
                        </Text>
                    </View>
                    <DataTable style={{ ...styles.dataTable, borderColor: theme.colors.elevation.level5, }}>
                        <ScrollView horizontal contentContainerStyle={styles.scrollView}>
                            <DataTable.Header>
                                {ratesHeaderConfig.current.map((headerItem, idx) => {
                                    return (
                                        <DataTable.Title numeric key={`title-${idx}`} numberOfLines={2} style={styles.dataTableTitle}>{headerItem.label}</DataTable.Title>
                                    );
                                })}
                            </DataTable.Header>
                            <DataTable.Row>
                                {ratesHeaderConfig.current.map((headerItem, idx) => {
                                    return (
                                        <DataTable.Cell numeric key={`cell-${idx}`} style={styles.dataTableCell}>
                                            {roundPercent(tableContents.current.rateStates[headerItem.name])}
                                        </DataTable.Cell>
                                    );
                                })}
                            </DataTable.Row>
                        </ScrollView>
                    </DataTable>
                </View>}

                {tableContents.current.wipup && <View style={styles.tableWrapper}>
                    <View style={{ marginLeft: 20, marginBottom: 5 }}>
                        <Text variant="titleLarge">
                            {"单位产品取水量"}
                        </Text>
                    </View>
                    <DataTable style={{ ...styles.dataTable, borderColor: theme.colors.elevation.level5, }}>
                        <ScrollView horizontal contentContainerStyle={styles.scrollView}>
                            <DataTable.Header>
                                {waterIntakePerProdHeaderConfig.current.map((titleItem, idx) => {
                                    return titleItem.type === "numeric" ?
                                        <DataTable.Title numeric key={`title-${idx}`} numberOfLines={2} style={{}}>{titleItem.label}</DataTable.Title> // the shared style below will have the header wrapped
                                        :
                                        <DataTable.Title key={`title-${idx}`} numberOfLines={2} style={{}}>{titleItem.label}</DataTable.Title>;
                                })}
                            </DataTable.Header>

                            {tableContents.current.wipup.map((eachRow, idx) => {
                                //console.log("wipup each row:", eachRow);
                                return (
                                    <DataTable.Row key={`wipup-row-${idx}`}>
                                        {waterIntakePerProdHeaderConfig.current.map((eachColumn, idx) => {
                                            //console.log("wipup eachColumn:", eachColumn);
                                            return eachColumn.type === "numeric" ?
                                                <DataTable.Cell numeric key={`wipup-cell-${idx}`} style={styles.dataTableCell}>
                                                    {roundNearestTo(eachRow[eachColumn.name] * eachColumn.scale,
                                                        (eachColumn.scale === 100 && 2) || (eachColumn.scale === 10000 && 4) || 3)}
                                                </DataTable.Cell>
                                                :
                                                <DataTable.Cell key={`cell-${idx}`} style={styles.dataTableCell}>
                                                    {eachRow[eachColumn.name]}
                                                </DataTable.Cell>;
                                        })}
                                    </DataTable.Row>
                                );
                            })}



                        </ScrollView>
                    </DataTable>
                </View>}

            </ScreenWrapper>

            <LoadingIndicator
                title={""}
                message={"加载中....."}
                visible={loadingIndicatorVisible}
                onClose={()=>setLoadingIndicatorVisible(false)}
            />

            <Snackbar
                style={styles.snackBar}
                visible={showSnackbar}
                onDismiss={() => setShowSnackbar(false)}
                onIconPress={() => setShowSnackbar(false)}
                duration={Snackbar.DURATION_LONG}
            >
                {snackBarMessage}
            </Snackbar>

        </View>
    );
};

const styles = StyleSheet.create({
    tableWrapper: {
        marginTop: 20,
        marginBottom: 5,
    },
    tableTitleWrapper: {
        marginLeft: 20,
        marginBottom: 5,
    },
    dataTable: {
        borderTopWidth: 2,
        borderBottomWidth: 2,
        paddingBottom: 0,
    },
    scrollView: {
        flexDirection: "column",
        borderWidth: 2 ,
    },
    dataTableTitle: {
        width: 100,
    },
    dataTableCell: {
        flex: 1,
        width: 100,
    },

    snackBar: {
        flexDirection: "column",
    },
});

export default RecordsWhole;
