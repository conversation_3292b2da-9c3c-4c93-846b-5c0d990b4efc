import React, { useEffect, useRef, useState } from "react";
import { StyleSheet, View } from "react-native";
import { Appbar, DataTable, Divider, List, Snackbar, Text, useTheme } from "react-native-paper";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import {
    makeReqGenWaterBalanceDoc as genDocQueryMaker,
    makeReqLstWaterBalanceRecords as listRecordsQueryMaker,
    makeReqSumWaterBalanceRecords as sumupRecordsQueryMaker
} from "../../api/projectRecordsQueries";
import BottomBarButton from "../../components/BottomBarButton";
import { DialogToConfirm } from "../../components/DialogToConfirm";
import HeaderBar from "../../components/HeaderBar";
import { PromptDialog } from "../../components/PromptDialog";
import { useRefreshOnFocus } from "../../hooks/useRefreshOnFocus";
import log from "../../services/logging";
import { parseServerState } from "../../utils/messages";
import EmptyScreen from "../EmptyScreen";
import ScreenWrapper from "../ScreenWrapper";


/**
 *
 * @param {object} args
 * @param {string} args.pubid 本项目的pubid
 * @param {string} args.wholeNaviTo 汇总表的导航名称, 如果为假就表示没有汇总表
 * @param {bool} args.displaySumup 用户控制统计表单的栏目是否显示
 * @returns
 */
const RecordsListingTemplate = ({ title, queryKwd, pubid, listItemNavigateTo, addingScreenNaviTo, bottomBarLabel, emptyScreenText, snackBarDefaultText, listLeftIcon, addButtonIcon, addButtonLabel, navigation, includeSumup, wholeNaviTo, projMeta, displayAppbar = true, ...props }) => {

    const { bottom, left, right } = useSafeAreaInsets();
    const theme = useTheme();
    const height = theme.isV3 ? 80 : 56;

    const [snackBarMessage, setSnackBarMessage] = useState(snackBarDefaultText);

    const [showSnackbar, setShowSnackbar] = useState(false);
    const [dataArray, setDataArray] = useState([]);
    const dataArrayBuf = useRef([]);

    const [promptsVisible, setPromptsVisible] = useState(false);
    const [promptsText, setPromptsText] = useState("根据水平衡资讯行业的知识总结这个表格");
    const [dialogConfirmVisible, setDialogConfirmVisible] = useState(false);

    // query data record to fill the list content of current page
    const onRecordListSuccess = (data) => {
        console.log("onRecordListSuccess data:", data);
        if (data.STATUS === 0) {
            dataArrayBuf.current = [];
            wholeNaviTo && (dataArrayBuf.current = dataArrayBuf.current.concat([
                {
                    cversion: 0,
                    formType: 3,
                    id: pubid,
                    name: "数据汇总"
                }]));
            dataArrayBuf.current = dataArrayBuf.current.concat(data.DATA.filter(item => item?.id !== 0));
            setDataArray(dataArrayBuf.current);
            if(includeSumup){
                //dataArrayBuf.current = [];
                //dataArrayBuf.current = data.DATA.filter(item => item?.id !== 0);
                //setDataArray(dataArrayBuf.current);
                (dataArrayBuf.current.length > 0) && pageDataQueryAppend.mutate();
            } else {
                //setDataArray(data.DATA.filter(item => item?.id !== 0));
            }
        } else {
            setSnackBarMessage(parseServerState(data.STATUS || data.status, data.DATA.info));
            setShowSnackbar(true);
        }
    };
    const onRecordListError = (err) => { console.log(title, "onRecordListError", err); };
    const onRecordListSettled = () => {};
    const pageDataQuery = listRecordsQueryMaker([queryKwd, "lst", pubid], onRecordListSuccess, onRecordListError, onRecordListSettled);

    // query data record to fill the list content of current page
    const onRecordSumupSuccess = (data) => {
        console.log("onRecordSumupSuccess data:", data);
        if (data.STATUS === 0) {
            // id = 0 has the special meaning, eg. as the root, but it cannot be modified by users
            //setDataArray(data.DATA.filter(item => item?.id !== 0));
            setDataArray(dataArrayBuf.current.concat(data.DATA));
        } else {
            setSnackBarMessage(parseServerState(data.STATUS || data.status, data.DATA.info));
            setShowSnackbar(true);
        }
    };
    const onRecordSumupError = (err) => { console.log(title, "onRecordSumupError", err); };
    const onRecordSumupSettled = () => { };
    const pageDataQueryAppend = sumupRecordsQueryMaker([queryKwd, "lstsum", pubid], onRecordSumupSuccess, onRecordSumupError, onRecordSumupSettled);

    // Query: generate all docs of this table of the project
    const { contentId: projPubid  } = projMeta;
    const projGenDocOnSuccess = () => {
        console.log("genDoc success!");
    };
    const projGenDocOnError = (error) => {
        console.log("genDoc error:", error);
    };
    const payload = {prompts: promptsText};
    const projGenDocQuery = genDocQueryMaker(payload, ["tbl", queryKwd, projPubid], projGenDocOnSuccess, projGenDocOnError);

    useEffect(() => {
        pageDataQuery.mutate();
    }, []);

    useRefreshOnFocus(pageDataQuery.mutate); // 通过返回按钮返回时刷新数据

    return (
        <>
            <HeaderBar
                title={title}
                navigation={navigation}
                //goBackCallback={() => {}}
                // 获得外链需要根据权限开启
                menuItemArray={[
                    //{ title: "获得外链", action: ()=>{} },
                    { title: "生成文档(AI)", action: ()=>{setPromptsVisible(true);} }
                ]}
            />
            {dataArray.length === 0 ? <EmptyScreen text={emptyScreenText} /> :
                <ScreenWrapper contentContainerStyle={styles.container}>
                    <DataTable style={styles.dataTable}>
                        <Divider />

                        {dataArray.map((group, i) => {
                            return (
                                <View key={`${i-0}`}>
                                    <List.Accordion
                                        key={`${i}`}
                                        title={group.name}>
                                        {group.records.map((item, j) => {
                                            return (
                                                <View key={`${i-j}`}>
                                                    <List.Item
                                                        title={<Text style={{ fontSize: 18 }}>{item.name}</Text>}
                                                        onPress={() => {
                                                            if (item.formType === 0 || item.formType === 1 || item.formType === 2) {
                                                                navigation.navigate(listItemNavigateTo, { pageMeta: { queryKwd: queryKwd, ...item }, projMeta: projMeta });
                                                            } else if (item.formType === 3) {
                                                                wholeNaviTo && navigation.navigate(wholeNaviTo, { pageMeta: { queryKwd: queryKwd, ...item }, projMeta: projMeta });
                                                            } else {
                                                                log.warn("formType enum warn: %s, navi to: %s", item.formType, listItemNavigateTo);
                                                                navigation.navigate(listItemNavigateTo, { pageMeta: { queryKwd: queryKwd, ...item }, projMeta: projMeta });
                                                            }
                                                        }}
                                                        left={(props) => <List.Icon {...props} icon={listLeftIcon} />}
                                                        right={props => <List.Icon {...props} icon="arrow-right" />}
                                                        style={{ borderWidth: 0, marginHorizontal: 10, marginVertical: 2 }}
                                                        titleStyle={{ fontSize: 18 }}
                                                        titleNumberOfLines={5}
                                                        descriptionStyle={{}}
                                                    />
                                                    <Divider />
                                                </View>
                                            );
                                        })}
                                    </List.Accordion>
                                    <Divider bold={true}/>
                                </View>
                            );})}
                    </DataTable>
                    {/* Give a margin, or the Appbar will be on top of the last ListingItem. */}
                    <Divider style={{ marginBottom: height + bottom }} />
                </ScreenWrapper>}

            <PromptDialog
                visible={promptsVisible}
                setVisible={setPromptsVisible}
                promptsText={promptsText}
                onOKCallback={(prompts) => {
                    setPromptsText(prompts);
                    setTimeout(() => {
                        projGenDocQuery.mutate();
                    }, 100);
                    setDialogConfirmVisible(true);
                }}
                onCancelCallback={() => {}}
                dialogTitle={"请填写AI提示词"}
                okBtnLabel={"确定"}
                cancelBtnLabel={"取消"}
            />

            <DialogToConfirm
                visible={dialogConfirmVisible}
                title={"生成文档"}
                text={"已将请求发送给AI, 稍后请在Web端查看结果!"}
                onOK={() => {setDialogConfirmVisible(false);}}
                okBtnLabel={"确认"}
            />

            {displayAppbar && <Appbar
                style={[
                    styles.bottom,
                    {
                        height: height + bottom,
                    },
                    theme.isV3 && {
                        backgroundColor: theme.colors.elevation.level2,
                    },
                ]}
                safeAreaInsets={{ bottom, left, right }}
                theme={{ mode: "adaptive" }}
            //mode='center-aligned'
            >

                <View style={styles.bottomBarContainer}>
                    <Divider />
                    <View style={styles.bottomBarComponents}>
                        <BottomBarButton
                            label={bottomBarLabel}
                            icon={addButtonIcon}
                            onPress={() => {
                                navigation.navigate(addingScreenNaviTo, { pageMeta: { queryKwd: queryKwd, formType: 0 }, projMeta: projMeta });
                            }}
                        />
                    </View>
                </View>
            </Appbar>}

            <Snackbar
                style={styles.snackBar}
                visible={showSnackbar}
                onDismiss={() => {
                    setShowSnackbar(false);
                }}
                onIconPress={() => {
                    setShowSnackbar(false);
                }}
                duration={Snackbar.DURATION_LONG}
            >
                {snackBarMessage}
            </Snackbar>

        </>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    dataTable: {
        fontSize: 24,
        //marginTop: 18,
        //borderColor: "black"
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        flex: 1,
        marginHorizontal: 6
    },
    snackBar: {
        flexDirection: "column",
    },
});

export default RecordsListingTemplate;
