import React, { useEffect, useRef, useState } from "react";
import { Divider, Appbar, useTheme, Snackbar, Text } from "react-native-paper";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { StyleSheet, View, Alert } from "react-native";
import { useForm } from "react-hook-form";
import { joiResolver } from "@hookform/resolvers/joi";
import "fast-text-encoding";
import <PERSON><PERSON> from "joi";
import ScreenWrapper from "../../ScreenWrapper";
import HeaderBar from "../../../components/HeaderBar";
import BottomBarButton from "../../../components/BottomBarButton";
import { DialogToConfirm } from "../../../components/DialogToConfirm";
import ControlledTextInput from "../../../components/ControlledTextInput";
import ControlledRadioInputWithQuery from "../../../components/ControlledRadioInputWithQuery";
import ControlledCheckboxInputWithQuery from "../../../components/ControlledCheckboxInputWithQuery";
import ControlledDateTimePicker from "../../../components/ControlledDateTimePicker";
import cloneDeep from "lodash/cloneDeep";
import { creatMMKVStore } from "../../../services/local-storage";
import { callOneByOne, makeDataFeeder } from "../../../utils";
import { validatorBase } from "../../../utils/validatorBase";
import { snackbarState, loadingIndicatorState } from "../../../hooks/globalStates";

// 新组件需要重新!!
import { WATER_BALANCE_UPDATE as pageMainKey } from "../../../config/keysConfig";
import { queryGetProjectWBPage as pageFetchQuery } from "../../../api/selectingQueries";
import { makeRequestUpdateProjectWB as updateQueryClient } from "../../../api/updatingQueries";
import { makeRequestDeleteProjectWB as deleteQueryClient } from "../../../api/deletingQueries";
import { makeRequestListingUsers as allUsersQuery } from "../../../api/listingQueries";
import { makeRequestListingClients as allClientsQueryClient } from "../../../api/listingQueries";
import { makeRequestListingProjBase as projbaseQuery } from "../../../api/listingQueries";

import { waterBalanceUpdateStates as selectorStates } from "../../../hooks/selectorStates";
import { useShallow } from "zustand/shallow";


// 新组件需要重新设置. 注意这里虽然设置了存储 但实际上没使用存储内容,
const { setStore, getStore, clearStore, setStoreObject } = creatMMKVStore(pageMainKey.store);
//const dataFeeder = makeDataFeeder();

//const saveButtonIcon = "content-save-all-outline";

const WaterBalanceOverview = ({ navigation, route }) => {
    console.log("WaterBalanceOverview pageMeta from nav:", route.params.pageMeta);
    console.log("WaterBalanceOverview projMeta from nav:", route.params.projMeta);

    // render++;
    const { bottom, left, right } = useSafeAreaInsets();
    const theme = useTheme();
    const height = theme.isV3 ? 80 : 56;

    const [setSnackBarMessage] = snackbarState(useShallow(state => [state.onOpen]));
    const [loadingIndicatorOnOpen, loadingIndicatorOnClose] = loadingIndicatorState(useShallow(state => [state.onOpen, state.onClose]));

    // 新组件需要修改!!
    //const bottomBarLabels = { ok: "更新", cancel: "取消" };                    // 底部按钮显示文字
    //const [snackBarMessage, setSnackBarMessage] = useState("更新部门遇到错误"); // 下方错误提示文字内容
    const [
        clientRadioState,
        projbaseRadioState,
        difficultyRadioState,
        expectedTimePickerState,
        salesmanRadioState,
        managerRadioState,
        managerLevelRadioState,
        areaPrincipalRadioState,
        courtesyCopiesCheckState,
        setClientRadioState,
        setProjbaseRadioState,
        setDifficultyRadioState,
        setExpectedTimePickerState,
        setSalesmanRadioState,
        setManagerRadioState,
        setManagerLevelRadioState,
        setAreaPrincipalRadioState,
        setCourtesyCopiesCheckState,
        resetSelectorStates
    ] = selectorStates(useShallow(state => [
        state.clientRadio,
        state.projbaseRadio,
        state.difficultyRadio,
        state.expectedTimePicker,
        state.salesmanRadio,
        state.managerRadio,
        state.managerLevelRadio,
        state.areaPrincipalRadio,
        state.courtesyCopiesCheck,
        state.setClientRadio,
        state.setProjbaseRadio,
        state.setDifficultyRadio,
        state.setExpectedTimePicker,
        state.setSalesmanRadio,
        state.setManagerRadio,
        state.setManagerLevelRadio,
        state.setAreaPrincipalRadio,
        state.setCourtesyCopiesCheck,
        state.resetStates,
    ])); // radio组件状态
    //const [confimDialogConfig, setConfirmDialogConfig] = useState({});       // 对话框信息, {title, text, okLabel, onOK}, 但显示对话框的变量在showConfirm

    // 新组件不需改动
    //const [okButtonLoading, setOkButtonLoading] = useState(false);           // 右下方按钮图标是否旋转
    //const [okButtonDisabled, setOkButtonDisabled] = useState(false);         // 右下方按钮是否禁用
    //const [cancelButtonDisabled, setCancelButtonDisabled] = useState(false); // 左下方按钮是否禁用
    //const [showSnackbar, setShowSnackbar] = useState(false);                 // 下方是否显示错误通知
    //const [showConfirm, setShowConfirm] = useState(false);                   // 是否显示确认对话框
    //const delQueryInfo = useRef({});                                         // 用于删除本记录的查询变量

    const difficultyStateDataProviderRef = useRef([{ id: 1, name: "简单" }, { id: 2, name: "中等" }, { id: 3, name: "困难" }]);  // 用作难度状态选项的数据源
    const managerLevelDataProviderRef    = useRef([{ id: 1, name: "低" },   { id: 2, name: "中" }, { id: 3, name: "高" }]);     // 用作类型状态选项的数据源

    const formDefaults  = useRef({});
    const storeDefaults = useRef({});
    const selectorDefaults = useRef({});
    const onPageInfoQuerySuccess = (data) => { // data: {"id": 3, "leader": 0, "name": "DEPT-03", "superior": 0}
        console.log("onPageInfoQuerySuccess................", data);
        if (data.STATUS === 0) {
            // delQueryInfo用于记录删除, id是查询api参数, name用于界面显示删除谁
            //delQueryInfo.current = { id: route.params.pageMeta.id, name: route.params.pageMeta.name };
            formDefaults.current  = {
                projectName:    data.DATA.projectName,
                productName:    data.DATA.productName,
                client:         data.DATA.client.id,
                projbase:       data.DATA.projbase.id,
                difficulty:     data.DATA.difficulty,
                expectedTime:   data.DATA.expectedTime || 0,
                salesman:       data.DATA.salesman.id,
                manager:        data.DATA.manager.id,
                managerLevel:   data.DATA.managerLevel,
                areaPrincipal:  data.DATA.areaPrincipal.id,
                courtesyCopies: data.DATA.courtesyCopies ? data.DATA.courtesyCopies.filter(item => item).map(item => item.id) : [],
            };
            storeDefaults.current = {
                projectName:    data.DATA.projectName,
                productName:    data.DATA.productName,
                client:         data.DATA.client,
                projbase:       data.DATA.projbase,
                difficulty:     {id: data.DATA.difficulty, name: difficultyStateDataProviderRef.current.find(item => item.id === data.DATA.difficulty)?.name || ""},
                expectedTime:   data.DATA.expectedTime || 0,
                salesman:       data.DATA.salesman,
                manager:        data.DATA.manager,
                managerLevel:   {id: data.DATA.managerLevel, name: managerLevelDataProviderRef.current.find(item => item.id === data.DATA.managerLevel)?.name || ""},
                areaPrincipal:  data.DATA.areaPrincipal,
                courtesyCopies: data.DATA.courtesyCopies ? data.DATA.courtesyCopies.filter(item => item).map(item => item.id) : [],
            };
            selectorDefaults.current = {
                client:         data.DATA.client,
                projbase:       data.DATA.projbase,
                difficulty:     {id: data.DATA.difficulty, name: difficultyStateDataProviderRef.current.find(item => item.id === data.DATA.difficulty)?.name || ""},
                expectedTime:   data.DATA.expectedTime || 0,
                salesman:       data.DATA.salesman,
                manager:        data.DATA.manager,
                managerLevel:   {id: data.DATA.managerLevel, name: managerLevelDataProviderRef.current.find(item => item.id === data.DATA.managerLevel)?.name || ""},
                areaPrincipal:  data.DATA.areaPrincipal,
                courtesyCopies: data.DATA.courtesyCopies ? data.DATA.courtesyCopies.filter(item => item) : [],
            };
            reset(formDefaults.current);                                     // 重置react-form
            setStoreObject(storeDefaults.current);                           // 设置mmkv存储
            // 设置selector菜单状态
            setProjbaseRadioState(selectorDefaults.current.projbase);
            setClientRadioState(selectorDefaults.current.client);
            setDifficultyRadioState(selectorDefaults.current.difficulty);
            setExpectedTimePickerState(selectorDefaults.current.expectedTime);
            setSalesmanRadioState(selectorDefaults.current.salesman);
            setManagerRadioState(selectorDefaults.current.manager);
            setManagerLevelRadioState(selectorDefaults.current.managerLevel);
            setAreaPrincipalRadioState(selectorDefaults.current.areaPrincipal);
            setCourtesyCopiesCheckState(selectorDefaults.current.courtesyCopies);
            // 设置下拉选项的默认数据, 静态数据不需更新
            // positionStateDataProviderRef.current = cloneDeep(selectorDefaults.current.state);
        }
    };
    const onPageInfoQueryOnError = ()=>{};
    const onPageInfoQueryOnSettled = () => { loadingIndicatorOnClose(); };
    const queryId = route.params.pageMeta.contentId;  // contentId是项目pubid, 见服务端defroute list-project-messages
    const pageInfoQuery = pageFetchQuery(queryId, onPageInfoQuerySuccess, onPageInfoQueryOnError, onPageInfoQueryOnSettled); // pageMeta was set in ListingTemplate.jsx

    // 新组件需要重新定义!!
    const schema = Joi.object({
        projectName:    validatorBase.wbProjectName.required,
        productName:    validatorBase.wbProductName.required,
        client:         validatorBase.wbClient.required,
        projbase:       validatorBase.wbProjbase.required,
        difficulty:     validatorBase.wbDifficulty.required,
        expectedTime:   validatorBase.universalTime.required,
        salesman:       validatorBase.wbSalesman.required,
        manager:        validatorBase.wbManager.required,
        managerLevel:   validatorBase.wbManagerLevel.required,
        areaPrincipal:  validatorBase.wbAreaPrincipal.unrequired,
        courtesyCopies: validatorBase.wbCourtesyCopies.required,
    });

    // 新组件需要重新定义defaultValues!!
    const {
        handleSubmit,
        control,
        resetField,
        reset,
        formState: { errors },
        getValues,
    } = useForm({
        resolver: joiResolver(schema),
        //reValidateMode: "onBlur", // https://github.com/orgs/react-hook-form/discussions/10327
        defaultValues: {
            projectName:    "",
            productName:    "",
            client:         "",
            projbase:       "",
            difficulty:     "",
            expectedTime:   0,
            salesman:       "",
            manager:        "",
            managerLevel:   0,
            areaPrincipal:  "",
            courtesyCopies: [],
        },
    });

    // 新组件需要重新定义setDiagText!!
    /*
    const onHandleSubmit = (data) => {
        //setCancelButtonDisabled(true);
        //setOkButtonDisabled(true);
        //setOkButtonLoading(true);
        dataFeeder(data);
        submitQuery.mutate();
    };
    */
    // 新组件不需改动
    /*
    const onCancel = () => {
        clearStore();
        // 每增加一个下拉框都要设置各自的默认值
        reset(formDefaults.current);                       // 重置react-form
        setStoreObject(storeDefaults.current);             // 重置mmkv存储
        // 重置下拉组件状态
        setProjbaseRadioState(selectorDefaults.current.projbase);
        setClientRadioState(selectorDefaults.current.client);
        setDifficultyRadioState(selectorDefaults.current.difficulty);
        setExpectedTimePickerState(selectorDefaults.current.expectedTime);
        setSalesmanRadioState(selectorDefaults.current.salesman);
        setManagerRadioState(selectorDefaults.current.manager);
        setManagerLevelRadioState(selectorDefaults.current.managerLevel);
        setAreaPrincipalRadioState(selectorDefaults.current.areaPrincipal);
        setCourtesyCopiesCheckState(selectorDefaults.current.courtesyCopies);
    };
    */

    // 新组件不需改动
    /*
    const commitOnSuccess = (data) => {
        console.log("response success, data: ", data);
        if(data.STATUS !== 0) {
            setSnackBarMessage(`项目更新发生错误, 错误码: ${data.STATUS}`);
            //setShowSnackbar(true);
        } else {
            // 设置确认框数据
            //setConfirmDialogConfig({
            //    title: "更新项目",
            //    text: "项目信息更新成功!",
            //    okLabel: "确定",
            //    onOK: () => { pageInfoQuery.mutate(); setShowConfirm(false); }});
            //setShowConfirm(true);
        }
    };
    const commitOnError = (error) => {
        setSnackBarMessage(`项目更新发生错误, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const commitOnSettled = (data, error) => {
        setCancelButtonDisabled(false);
        setOkButtonDisabled(false);
        setOkButtonLoading(false);

    };
    */
    //const submitQuery = updateQueryClient(dataFeeder, route.params.pageMeta.id, commitOnSuccess, commitOnError, commitOnSettled);

    useEffect(() => {
        loadingIndicatorOnOpen("", "数据传输中.....");
        pageInfoQuery.mutate();
    }, []);

    /*
    const deleteOnSuccess = () => {
        navigation.goBack();
    };
    const deleteOnError = (error) => {
        setSnackBarMessage(`项目删除失败, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const deleteQuery = deleteQueryClient(delQueryInfo.current?.id, deleteOnSuccess, deleteOnError);

    const deleteAlert = () =>{
        return (
            Alert.alert("删除项目", `删除后不可撤销, 请确认是否删除: ${delQueryInfo.current?.name}?`, [
                { text: "取消" },
                { text: "确定", onPress: () => deleteQuery.mutate() },
            ])
        );
    };
    */

    //console.log("formdata.........", getValues());
    //console.log("formDefaults.....", formDefaults);
    //console.log("selectorDefaults....", selectorDefaults);

    return (
        <View style={{ flex: 1 }}>

            <HeaderBar
                title={route.params?.pageMeta?.title || ""}
                navigation={navigation}
                goBackCallback={() => callOneByOne(clearStore, resetSelectorStates)} // 返回前清空mmkv存储因为临时数据不再需要, 重置状态使得页面切换后不会使用前面的数据
                //menuItemArray={[{ title: "删除项目", action: deleteAlert }]}
            />

            <ScreenWrapper contentContainerStyle={styles.container}>
                {/*<Text>Render: {render}</Text>*/}

                <View style={styles.formEntry}>

                    <ControlledTextInput
                        rowLabel="项目名称"
                        control={control}
                        name="projectName"
                        //placeholder="XXXX项目"
                        onChangeText={(text) => setStore("projectName", text)}
                        onClearText={() => {
                            setStore("projectName", "");
                            resetField("projectName", { defaultValue: "" });
                        }}
                        editable={false}
                        mode="outlined"
                        formError={errors}
                    />

                    {/*<ControlledTextInput
                        rowLabel="产品名称"
                        control={control}
                        name="productName"
                        //placeholder="XXXX产品"
                        onChangeText={(text) => setStore("productName", text)}
                        onClearText={() => {
                            setStore("productName", "");
                            resetField("productName", { defaultValue: "" });
                        }}
                        editable={false}
                        mode="outlined"
                        formError={errors}
                    />*/}

                    <ControlledRadioInputWithQuery
                        name="client"
                        rowLabel="客户"
                        control={control}
                        placeholder="请拉选客户"
                        onDialogConfirm={(obj) => {
                            console.log("confirm client: ", obj);
                            setStore("client", obj);
                            setClientRadioState(obj);
                        }}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        dataProvider={allClientsQueryClient}
                        setFetchFailMessage={setSnackBarMessage}
                        //setDisplayFailBar={setShowSnackbar}
                        dialogState={clientRadioState}
                        setDialogState={setClientRadioState}
                        defaultNullOptionsTip={"未找到客户数据, 请联系管理员!"}
                        editable={false}
                    />

                    <ControlledRadioInputWithQuery
                        name="projbase"
                        rowLabel="项目库类型"
                        control={control}
                        placeholder="请拉选项目库类型"
                        onDialogConfirm={(obj) => {
                            console.log("confirm projbase: ", obj);
                            setStore("projbase", obj);
                            setProjbaseRadioState(obj);
                        }}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        dataProvider={projbaseQuery}
                        setFetchFailMessage={setSnackBarMessage}
                        //setDisplayFailBar={setShowSnackbar}
                        dialogState={projbaseRadioState}
                        setDialogState={setProjbaseRadioState}
                        defaultNullOptionsTip={"未找到项目类型数据, 请联系管理员!"}
                        editable={false}
                    />

                    <ControlledRadioInputWithQuery
                        name="difficulty"
                        rowLabel="难度"
                        control={control}
                        placeholder="请拉选项目难度"
                        onDialogConfirm={(obj) => {
                            console.log("confirm difficulty: ", obj);
                            setStore("difficulty", obj);
                            setDifficultyRadioState(obj);
                        }}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        //dataProvider={allUsersQuery}
                        dataProvider={difficultyStateDataProviderRef}
                        setFetchFailMessage={setSnackBarMessage}
                        //setDisplayFailBar={setShowSnackbar}
                        dialogState={difficultyRadioState}
                        setDialogState={setDifficultyRadioState}
                        defaultNullOptionsTip={"未找到难度数据, 请联系管理员!"}
                        editable={false}
                    />

                    <ControlledDateTimePicker
                        name="expectedTime"
                        mode={"date"}
                        rowLabel="截止日期"
                        placeholder="请拉选截止日期"
                        dialogState={expectedTimePickerState}
                        onDialogConfirm={(obj) => {
                            console.log("confirm expectedTime: ", obj);
                            setStore("expectedTime", obj);
                            setExpectedTimePickerState(obj);
                        }}
                        formError={errors}
                        control={control}
                        editable={false}
                    />

                    <ControlledRadioInputWithQuery
                        name="salesman"
                        rowLabel="业务员"
                        control={control}
                        placeholder="请拉选项目难度"
                        onDialogConfirm={(obj) => {
                            console.log("confirm salesman: ", obj);
                            setStore("salesman", obj);
                            setDifficultyRadioState(obj);
                        }}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        dataProvider={allUsersQuery}
                        setFetchFailMessage={setSnackBarMessage}
                        //setDisplayFailBar={setShowSnackbar}
                        dialogState={salesmanRadioState}
                        setDialogState={setSalesmanRadioState}
                        defaultNullOptionsTip={"未找到难度数据, 请联系管理员!"}
                        editable={false}
                    />

                    <ControlledRadioInputWithQuery
                        name="manager"
                        rowLabel="项目经理"
                        control={control}
                        placeholder="请拉选项目经理"
                        onDialogConfirm={(obj) => {
                            console.log("confirm manager: ", obj);
                            setStore("manager", obj);
                            setManagerRadioState(obj);
                        }}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        dataProvider={allUsersQuery}
                        setFetchFailMessage={setSnackBarMessage}
                        //setDisplayFailBar={setShowSnackbar}
                        dialogState={managerRadioState}
                        setDialogState={setManagerRadioState}
                        defaultNullOptionsTip={"未找到项目经理数据, 请联系管理员!"}
                        editable={false}
                    />

                    {/*<ControlledRadioInputWithQuery
                        name="managerLevel"
                        rowLabel="项目经理水平"
                        control={control}
                        placeholder="请拉选项目经理水平"
                        onDialogConfirm={(obj) => {
                            console.log("confirm managerLevel: ", obj);
                            setStore("managerLevel", obj);
                            setManagerLevelRadioState(obj);
                        }}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        dataProvider={managerLevelDataProviderRef}
                        setFetchFailMessage={setSnackBarMessage}
                        //setDisplayFailBar={setShowSnackbar}
                        dialogState={managerLevelRadioState}
                        setDialogState={setManagerLevelRadioState}
                        defaultNullOptionsTip={"未找到项目经理水平数据, 请联系管理员!"}
                        editable={false}
                    />*/}

                    <ControlledRadioInputWithQuery
                        name="areaPrincipal"
                        rowLabel="区域负责人"
                        control={control}
                        placeholder="请拉选项区域负责人"
                        onDialogConfirm={(obj) => {
                            console.log("confirm areaPrincipal: ", obj);
                            setStore("areaPrincipal", obj);
                            setAreaPrincipalRadioState(obj);
                        }}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        dataProvider={allUsersQuery}
                        setFetchFailMessage={setSnackBarMessage}
                        //setDisplayFailBar={setShowSnackbar}
                        dialogState={areaPrincipalRadioState}
                        setDialogState={setAreaPrincipalRadioState}
                        defaultNullOptionsTip={"未找到区域负责人数据, 请联系管理员!"}
                        editable={false}
                    />

                    <ControlledCheckboxInputWithQuery
                        name="courtesyCopies"
                        rowLabel="抄送"
                        control={control}
                        placeholder="请拉选抄送成员"
                        onDialogConfirm={(obj) => {
                            console.log("confirm courtesyCopies: ", obj);
                            setStore("courtesyCopies", obj);
                            setCourtesyCopiesCheckState(obj);
                        }}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        //queryClient={allUsersQuery}
                        dataProvider={allUsersQuery}
                        setFetchFailMessage={setSnackBarMessage}
                        //setDisplayFailBar={setShowSnackbar}
                        dialogState={courtesyCopiesCheckState}
                        setDialogState={setCourtesyCopiesCheckState}
                        defaultNullOptionsTip={"未找到成员数据, 请检查您的网络或联系管理员!"}
                        editable={false}
                    />
                    <Divider/>

                </View>

            </ScreenWrapper>

            {/*<Appbar
                style={[
                    styles.bottom,
                    {
                        height: height + bottom,
                    },
                    theme.isV3 && {
                        backgroundColor: theme.colors.elevation.level2,
                    },
                ]}
                safeAreaInsets={{ bottom, left, right }}
                theme={{ mode: "adaptive" }}
            //mode='center-aligned'
            >

                <View style={styles.bottomBarContainer}>
                    <Divider />
                    <View style={styles.bottomBarComponents}>
                        <BottomBarButton
                            label={bottomBarLabels.cancel}
                            disabled={cancelButtonDisabled}
                            onPress={onCancel}
                        />
                        <BottomBarButton
                            label={bottomBarLabels.ok}
                            loading={okButtonLoading}
                            disabled={okButtonDisabled}
                            icon={saveButtonIcon}
                            onPress={handleSubmit(onHandleSubmit, (formErr) => onPreSubmitError(formErr, getStore, "WaterBalanceOverview"))}
                        />
                    </View>
                </View>
            </Appbar>*/}

            {/*<Snackbar
                style={styles.snackBar}
                visible={showSnackbar}
                onDismiss={() => setShowSnackbar(false)}
                onIconPress={() => setShowSnackbar(false)}
                duration={Snackbar.DURATION_LONG}
            >
                {snackBarMessage}
        </Snackbar>*/}

            {/*<DialogToConfirm
                visible={showConfirm}
                title={confimDialogConfig.title ? confimDialogConfig.title : ""}
                text={confimDialogConfig.text ? confimDialogConfig.text : ""}
                onOK={confimDialogConfig.onOK ? confimDialogConfig.onOK : () => setShowConfirm(false)}
                okBtnLabel={confimDialogConfig.okLabel ? confimDialogConfig.okLabel : "确认"}
    />*/}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    firstColumn: {
        flex: 2,
        //borderWidth: 1,
        //height: 40,
        content: {
            fontSize: 18,
        }
    },
    secondColumn: {
        flex: 3,
        // /borderWidth: 1,
        // height: 40,
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        flex: 1,
        marginHorizontal: 6
    },
    formEntry: {
        margin: 8,
    },
    snackBar: {
        flexDirection: "column",
    },
});

export default WaterBalanceOverview;
