import * as keys from "../config/keysConfig";
import { makeInsertingClient as makeQuery } from "./makeInsertingClient";


export const makeRequestInsertOrg        = makeQuery(keys.ORG_ADD);
export const makeRequestInsertDepartment = makeQuery(keys.ORG_DEPARTMENT_ADD);
export const makeRequestInsertUser       = makeQuery(keys.ORG_USER_ADD);
export const makeRequestInsertPosition   = makeQuery(keys.ORG_POSITION_ADD);
export const makeRequestInsertClient     = makeQuery(keys.ORG_CLIENT_ADD);
export const makeRequestInsertRole       = makeQuery(keys.ORG_ROLE_ADD);
export const makeRequestInsertProjbase   = makeQuery(keys.ORG_PROJ_BASE_ADD);

// 新建项目: 水平衡, 低碳诊断
export const makeRequestInsertProjWB     = makeQuery(keys.WATER_BALANCE_ADD);
export const makeRequestInsertProjZC     = makeQuery(keys.ZERO_CARBON_ADD);

// 项目拷贝
export const makeRequestCopyMyProj       = makeQuery(keys.COPY_MY_PROJ);
