import { joi<PERSON>es<PERSON>ver } from "@hookform/resolvers/joi";
import "fast-text-encoding";
import <PERSON><PERSON> from "joi";
import debounce from "lodash/debounce";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { useForm, useWatch } from "react-hook-form";
import { Alert, StyleSheet, View } from "react-native";
import { Snackbar } from "react-native-paper";
import Button from "../../../components/Button";
import ControlledTextInput from "../../../components/ControlledTextInput";
import { DialogToConfirm } from "../../../components/DialogToConfirm";
import { FormListNodesMapper } from "../../../components/FormListNodesMapper";
import HeaderBar from "../../../components/HeaderBar";
import { LoadingIndicator } from "../../../components/LoadingIndicator";
import { creatMMKVStore, makeStoredValueToFormValue } from "../../../services/local-storage";
import { callOneByOne, ifTruthLet, isNullness, makeDataFeeder, parseIndustryCode } from "../../../utils";
import { checkPermits } from "../../../utils/permits";
import { checkIdsToObject, isServerDataNewer, makeGetClientCversion, radioIdToObject } from "../../../utils/records";
import { validatorBase } from "../../../utils/validatorBase";
import ScreenWrapper from "../../ScreenWrapper";


// 新组件需要重新!!
import { useShallow } from "zustand/shallow";
import {
    makeReqDelWaterBalanceRecords as recordDeleteQueryMaker,
    makeReqGetWaterBalanceRecords as recordSelectQueryMaker,
    makeReqUpdWaterBalanceRecords as recordUpdateQueryMaker,
} from "../../../api/projectRecordsQueries";
import { UPD_WATER_BALANCE_RECORDS as pageMainKey } from "../../../config/keysConfig";
import { equipRumtimeEnum, intakePurposeEnum, testMethodEnum, waterUnitScaleEnum } from "../../../config/waterBalance";
import { wbWaterUsageUnitsRecordingsStates as selectorStates } from "../../../hooks/selectorStates";
import log from "../../../services/logging";
import { roundNearest } from "../../../utils/numeric";
import { onPreSubmitError } from "../../../utils/screens";


const dataFeeder = makeDataFeeder();

/**
 * 主要生产用水
 * 主要生产用水与辅助生产用水的区别在于: waterClass前者为1后者为2, 工序或设备名称字段的placeholder也有区别.
 * 而附属生产用水的waterClass为3, 并且没有outSteam和dumpOutside两个字段, 配置中注释掉这两个字段即可.
 * 注意, 项目数据表单与工作台管理表单的数据有一个显著的区别:
 * 项目数据不需要像用户编号那样脱敏, 两边传输的数据是直接的, 不需要做{id, name}这种映射, 部分需要映射的可以在客户端本地完成.
 * @param {object} args
 * @param {object} args.navigation
 * @param {object} args.route
 * @returns
 */
const RecordsUpdating = ({ navigation, route }) => {
    console.log("RecordsUpdating pageMeta from nav:", route.params.pageMeta);
    console.log("RecordsUpdating projMeta from nav:", route.params.projMeta);
    const projSubclass = route.params.projMeta.subclass;  // 1表, 2书
    const industryType = route.params.projMeta.industry;  // 1工业, 2服务业
    //const reportType   = route.params.projMeta.subclass;  // 1表, 2书
    const projIndustry = parseIndustryCode(industryType); // 1 -> "industry", 2 -> "service"

    const waterClass = route.params.pageMeta?.waterClass || 0; // 配置于ProjectIndex.jsx

    // formType对应着服务端的form-type, 为0时表示表单是用户填写, 为1表示服务端生成, 为1时只读
    const { queryKwd, id: recordPubid, name: recordName, formType } = route.params.pageMeta; // recordPubid is the alias of id
    const saveButtonIcon = "content-save-all-outline";
    const formEditableGlobal = checkPermits() ? (formType === 0 ? true : false) : false;

    // 使用memoize来优化
    const { setStore, getStore, clearStore, setStoreObject } = useMemo(() => {
        console.log("creatMMKVStore memo:", pageMainKey.store, queryKwd, recordPubid);
        return creatMMKVStore(`${pageMainKey.store}/${queryKwd}/${recordPubid}`);
    }, [queryKwd, recordPubid]);

    const storedValueToFormValue = useRef(makeStoredValueToFormValue(getStore));
    const getClientCversion = useRef(makeGetClientCversion(() => getStore("mainCversion"), () => getStore("subCversion")));

    // 客户端cversion = `${mainCversion}.${subCversion}`
    (getStore("mainCversion") === undefined) ? setStore("mainCversion", 0) : undefined; // 客户端主版本号
    (getStore("subCversion")  === undefined) ? setStore("subCversion",  0) : undefined; // 客户端次版本号, 修改一次自增一次
    //let mainCversion = getStore("mainCversion"); // 主版本号, 与服务端cversion对应
    // 次版本号, 表示本地更改次数, 用于程序上对子版本号的增加, 用于判断与服务端版本的差异, 同时可用于判断避免重复从MMKV获取数据
    const subCversionRef = useRef(getStore("subCversion"));

    // 新组件需要修改!!
    const bottomBarLabels = { ok: "提交" };                    // 底部按钮显示文字
    const [snackBarMessage, setSnackBarMessage] = useState("更新表单遇到错误"); // 下方错误提示文字内容

    const [
        waterUnitScaleState,
        testDatePickerState,
        //endDatePickerState,
        equipRumtimeRadioState,
        testMethodCheckState,
        intakePurposeRadioState,
        setWaterUnitScaleState,
        setTestDatePickerState,
        //setEndDatePickerState,
        setEquipRumtimeRadioState,
        setTestMethodCheckState,
        setIntakePurposeRadioState,
        resetSelectorStates,
    ] = selectorStates(useShallow(state => [
        state.waterUnitScale,
        state.testDatePicker,
        //state.endDatePicker,
        state.equipRumtimeRadio,
        state.testMethodCheck,
        state.intakePurposeRadio,
        state.setWaterUnitScale,
        state.setTestDatePicker,
        //state.setEndDatePicker,
        state.setEquipRumtimeRadio,
        state.setTestMethodCheck,
        state.setIntakePurposeRadio,
        state.resetStates,
    ])); // radio组件状态

    const [confimDialogConfig, setConfirmDialogConfig] = useState({});       // 对话框信息, {title, text, okLabel, onOK}, 但显示对话框的变量在showConfirm

    // 新组件不需改动
    const [screenTitle,      setScreenTitle]      = useState(route.params.pageMeta.name); // 屏幕上方导航栏标题
    const [okButtonLoading,  setOkButtonLoading]  = useState(false);            // 右下方按钮图标是否旋转
    const [okButtonDisabled, setOkButtonDisabled] = useState(!checkPermits());  // 右下方按钮是否禁用
    const [showSnackbar,     setShowSnackbar]     = useState(false);            // 下方是否显示错误通知
    const [showConfirm,      setShowConfirm]      = useState(false);            // 是否显示确认对话框
    const [loadingIndicatorVisible, setLoadingIndicatorVisible] = useState(false); // 是否显示loading
    const [unitToggleState, setUnitToggleState ]  = useState(false);            // 用于控制切换单位后刷新数据
    const delQueryInfo = useRef({ id: recordPubid, name: recordName });         // 用于删除本记录的查询变量

    //const waterTypeStateDataProviderRef     = useRef([{ id: 1, name: "自来水" }, { id: 2, name: "地表水" }, { id: 3, name: "地下水" }, { id: 4, name: "市政中水" }]);
    //const accuracyLevelStateDataProviderRef = useRef([{ id: 1, name: "一级" }, { id: 2, name: "二级" }, ]);
    //const equipStateStateDataProviderRef    = useRef([{ id: 1, name: "合格" }, { id: 2, name: "不合格" },]);
    const waterUnitScaleStateDataProviderRef = useRef(waterUnitScaleEnum);
    const equipRumtimeStateDataProviderRef   = useRef(equipRumtimeEnum);
    const testMethodStateDataProviderRef     = useRef(testMethodEnum);
    const intakePurposeStateDataProviderRef  = useRef(intakePurposeEnum);

    // 用于存储waterScale的值, 当unitToggleState发生改变时需要通过该值更新表单字段
    const waterUnitScaleRef = useRef(0);

    // Define form
    // 新组件需要重新定义!!
    const schema = Joi.object({
        name:           validatorBase.waterBalance.waterUsageUnits.name,
        //waterClass:   validatorBase.waterBalance.waterUsageUnits.,
        testDate:      validatorBase.waterBalance.waterUsageUnits.dateFieldUnrequired,
        //endDate:        validatorBase.waterBalance.waterUsageUnits.dateFieldUnrequired,
        //unitId:       validatorBase.waterBalance.waterUsageUnits.,
        unitName:       validatorBase.waterBalance.waterUsageUnits.textField,
        //subunitId:    validatorBase.waterBalance.waterUsageUnits.,
        subunitName:    validatorBase.waterBalance.waterUsageUnits.textField,
        intakePurpose:  validatorBase.waterBalance.waterUsageUnits.intakePurpose,
        modelSpec:      validatorBase.waterBalance.waterUsageUnits.textField,
        equipRumtime:   validatorBase.waterBalance.waterUsageUnits.equipRumtime,
        equipPlace:     validatorBase.waterBalance.waterUsageUnits.textField,
        testMethod:     validatorBase.waterBalance.waterUsageUnits.testMethod,
        tempIn:         validatorBase.waterBalance.waterUsageUnits.float.unrequired,
        tempOut:        validatorBase.waterBalance.waterUsageUnits.float.unrequired,
        inputTotal:     validatorBase.waterBalance.waterUsageUnits.floatField,
        intakeTapWater: validatorBase.waterBalance.waterUsageUnits.float.unrequired,
        intakeSurface:  validatorBase.waterBalance.waterUsageUnits.float.unrequired,
        intakeGround:   validatorBase.waterBalance.waterUsageUnits.float.unrequired,
        intakeSoft:     validatorBase.waterBalance.waterUsageUnits.float.unrequired,
        intakeSteam:    validatorBase.waterBalance.waterUsageUnits.float.unrequired,
        intakeBought:   validatorBase.waterBalance.waterUsageUnits.float.unrequired,
        //intakeExt1:   validatorBase.waterBalance.waterUsageUnits.float.unrequired,
        //intakeExt2:   validatorBase.waterBalance.waterUsageUnits.float.unrequired,
        //intakeExt3:   validatorBase.waterBalance.waterUsageUnits.float.unrequired,
        //intakeExt4:   validatorBase.waterBalance.waterUsageUnits.float.unrequired,
        intakeSea:      validatorBase.waterBalance.waterUsageUnits.float.unrequired,
        intakeBrackish: validatorBase.waterBalance.waterUsageUnits.float.unrequired,
        intakeRecycled: validatorBase.waterBalance.waterUsageUnits.float.unrequired,
        intakeMine:     validatorBase.waterBalance.waterUsageUnits.float.unrequired,
        intakeRain:     validatorBase.waterBalance.waterUsageUnits.float.unrequired,
        //intakeExt11:  validatorBase.waterBalance.waterUsageUnits.float.unrequired,
        //intakeExt12:  validatorBase.waterBalance.waterUsageUnits.float.unrequired,
        //intakeExt13:  validatorBase.waterBalance.waterUsageUnits.float.unrequired,
        //intakeExt14:  validatorBase.waterBalance.waterUsageUnits.float.unrequired,
        intakeTotal:    validatorBase.waterBalance.waterUsageUnits.floatField,
        inCircleDCCW:   validatorBase.waterBalance.waterUsageUnits.floatField,
        inCircleICCW:   validatorBase.waterBalance.waterUsageUnits.floatField,
        inCircleOCW:    validatorBase.waterBalance.waterUsageUnits.floatField,
        inSerialSCR:    validatorBase.waterBalance.waterUsageUnits.floatField,
        inSerialRW:     validatorBase.waterBalance.waterUsageUnits.floatField,
        inSerialSteam:  validatorBase.waterBalance.waterUsageUnits.floatField,
        inSerialOTW:    validatorBase.waterBalance.waterUsageUnits.floatField,
        outCircleDCCW:  validatorBase.waterBalance.waterUsageUnits.floatField,
        outCircleICCW:  validatorBase.waterBalance.waterUsageUnits.floatField,
        outCircleOCW:   validatorBase.waterBalance.waterUsageUnits.floatField,
        outSerialSCR:   validatorBase.waterBalance.waterUsageUnits.floatField,
        outSerialRW:    validatorBase.waterBalance.waterUsageUnits.floatField,
        outSteam:       validatorBase.waterBalance.waterUsageUnits.floatField,
        outSerialOTW:   validatorBase.waterBalance.waterUsageUnits.floatField,
        dumpSewage:     validatorBase.waterBalance.waterUsageUnits.floatField,
        dumpOutside:    validatorBase.waterBalance.waterUsageUnits.floatField,
        waterLeakage:   validatorBase.waterBalance.waterUsageUnits.floatField,
        waterConsume:   validatorBase.waterBalance.waterUsageUnits.floatField,
        //outputTotal:  validatorBase.waterBalance.waterUsageUnits.floatField,
        others:         validatorBase.waterBalance.waterUsageUnits.textField,
        remarks:        validatorBase.waterBalance.waterUsageUnits.longTextField,
        waterScale:     validatorBase.waterBalance.waterUsageUnits.waterScale,
    });

    // 新组件需要重新定义defaultValues!!
    const {
        handleSubmit,
        control,
        resetField,
        reset,
        formState: { errors },
        setValue,
        getValues,
    } = useForm({
        resolver: joiResolver(schema),
        //reValidateMode: "onBlur", // https://github.com/orgs/react-hook-form/discussions/10327
        defaultValues: { // 默认值在组件载入时会被更新, 这里的初始值正常情况下没有用处
            // 注意, 默认为0的是允许默认为0的值, 或者通过计算得到的值
            name:           "",
            //waterClass:   "",
            testDate:      "",
            //endDate:        "",
            //unitId:       "",
            unitName:       "",
            //subunitId:    "",
            subunitName:    "",
            intakePurpose:  "",
            modelSpec:      "",
            equipRumtime:   "",
            equipPlace:     "",
            testMethod:     "",
            tempIn:         "",
            tempOut:        "",
            inputTotal:     "0",
            intakeTapWater: "0",
            intakeSurface:  "0",
            intakeGround:   "0",
            intakeSoft:     "0",
            intakeSteam:    "0",
            intakeBought:   "0",
            //intakeExt1:   "0",
            //intakeExt2:   "0",
            //intakeExt3:   "0",
            //intakeExt4:   "0",
            intakeSea:      "0",
            intakeBrackish: "0",
            intakeRecycled: "0",
            intakeMine:     "0",
            intakeRain:     "0",
            //intakeExt11:  "0",
            //intakeExt12:  "0",
            //intakeExt13:  "0",
            //intakeExt14:  "0",
            intakeTotal:    "0",
            inCircleDCCW:   "",
            inCircleICCW:   "",
            inCircleOCW:    "",
            inSerialSCR:    "",
            inSerialRW:     "",
            inSerialSteam:  "",
            inSerialOTW:    "",
            outCircleDCCW:  "",
            outCircleICCW:  "",
            outCircleOCW:   "",
            outSerialSCR:   "",
            outSerialRW:    "",
            outSteam:       "",
            outSerialOTW:   "",
            dumpSewage:     "",
            dumpOutside:    "",
            waterLeakage:   "",
            waterConsume:   "",
            //outputTotal:  "",
            others:         "",
            remarks:        "",
            waterScale:     "0",
        },
    });

    // Query: select record
    const onRecordSelectSuccess = (data) => { // data: {"id": 3, "leader": 0, "name": "DEPT-03", "superior": 0}
        // 注意, 进入onSuccess之前就意味着服务端版本比客户端版本新, 因此要更新客户端主版本号
        console.log("onRecordSelectSuccess:", data);
        if (data.STATUS === 0) {
            setStore("mainCversion", data.DATA.cversion);
            setStore("subCversion", 0);
            const factor = 10 ** (data.DATA.waterScale || 0);

            const formObject = {
                name:           String(data.DATA.name),
                //waterClass:   String(data.DATA.waterClass),
                testDate:      String(data.DATA.testDate || ""),
                //endDate:        String(data.DATA.endDate || ""),
                //unitId:       String(data.DATA.unitId),
                unitName:       String(data.DATA.unitName),
                //subunitId:    String(data.DATA.unitId),
                subunitName:    String(data.DATA.subunitName),
                intakePurpose:  String(data.DATA.intakePurpose), // -1用于设置逻辑默认值为其它, 而0表示数据库默认值, 表示未填写
                modelSpec:      String(data.DATA.modelSpec),
                equipRumtime:   String(data.DATA.equipRumtime),
                equipPlace:     String(data.DATA.equipPlace),
                testMethod:     data.DATA.testMethod,           // 数组类型, 不能转为字符串
                tempIn:         String(data.DATA.tempIn),
                tempOut:        String(data.DATA.tempOut),
                inputTotal:     String(data.DATA.inputTotal     / factor),
                intakeTapWater: String(data.DATA.intakeTapWater / factor),
                intakeSurface:  String(data.DATA.intakeSurface  / factor),
                intakeGround:   String(data.DATA.intakeGround   / factor),
                intakeSoft:     String(data.DATA.intakeSoft     / factor),
                intakeSteam:    String(data.DATA.intakeSteam    / factor),
                intakeBought:   String(data.DATA.intakeBought   / factor),
                //intakeExt1:   String(data.DATA.intakeExt1 / factor),
                //intakeExt2:   String(data.DATA.intakeExt2 / factor),
                //intakeExt3:   String(data.DATA.intakeExt3 / factor),
                //intakeExt4:   String(data.DATA.intakeExt4 / factor),
                intakeSea:      String(data.DATA.intakeSea      / factor),
                intakeBrackish: String(data.DATA.intakeBrackish / factor),
                intakeRecycled: String(data.DATA.intakeRecycled / factor),
                intakeMine:     String(data.DATA.intakeMine / factor),
                intakeRain:     String(data.DATA.intakeRain / factor),
                //intakeExt11:  String(data.DATA.intakeExt11 / factor),
                //intakeExt12:  String(data.DATA.intakeExt12 / factor),
                //intakeExt13:  String(data.DATA.intakeExt13 / factor),
                //intakeExt14:  String(data.DATA.intakeExt14 / factor),
                intakeTotal:    String(data.DATA.intakeTotal / factor),
                inCircleDCCW:   String(data.DATA.inCircleDCCW / factor),
                inCircleICCW:   String(data.DATA.inCircleICCW / factor),
                inCircleOCW:    String(data.DATA.inCircleOCW / factor),
                inSerialSCR:    String(data.DATA.inSerialSCR / factor),
                inSerialRW:     String(data.DATA.inSerialRW / factor),
                inSerialSteam:  String(data.DATA.inSerialSteam / factor),
                inSerialOTW:    String(data.DATA.inSerialOTW / factor),
                outCircleDCCW:  String(data.DATA.outCircleDCCW / factor),
                outCircleICCW:  String(data.DATA.outCircleICCW / factor),
                outCircleOCW:   String(data.DATA.outCircleOCW / factor),
                outSerialSCR:   String(data.DATA.outSerialSCR / factor),
                outSerialRW:    String(data.DATA.outSerialRW / factor),
                outSteam:       String(data.DATA.outSteam / factor),
                outSerialOTW:   String(data.DATA.outSerialOTW / factor),
                dumpSewage:     String(data.DATA.dumpSewage / factor),
                dumpOutside:    String(data.DATA.dumpOutside / factor),
                waterLeakage:   String(data.DATA.waterLeakage / factor),
                waterConsume:   String(data.DATA.waterConsume / factor),
                //outputTotal:  String(data.DATA.outputTotal / factor),
                others:         String(data.DATA.others),
                remarks:        String(data.DATA.remarks),
                waterScale:     String(data.DATA.waterScale),
            };
            reset(formObject);           // 重置react-form

            const storeObjects = checkPermits() && (formType === 0) && {
                ...formObject,
                //waterType:     radioIdToObject(waterTypeStateDataProviderRef.current,     data.DATA.waterType),
                //accuracyLevel: radioIdToObject(accuracyLevelStateDataProviderRef.current, data.DATA.accuracyLevel),
                //equipState:    radioIdToObject(equipStateStateDataProviderRef.current,    data.DATA.equipState),
                waterScale:    radioIdToObject(waterUnitScaleStateDataProviderRef.current, data.DATA.waterScale),
                equipRumtime:  radioIdToObject(equipRumtimeStateDataProviderRef.current,   data.DATA.equipRumtime),
                testMethod:    checkIdsToObject(testMethodStateDataProviderRef.current,    data.DATA.testMethod),
                intakePurpose: radioIdToObject(intakePurposeStateDataProviderRef.current,  data.DATA.intakePurpose),
            };
            storeObjects && setStoreObject(storeObjects, false); // 判断用户权限, 把服务端数据写到mmkv存储, 只有可写用户才需要本地存储, 不会首先清空老数据

            // 设置selector菜单状态
            setWaterUnitScaleState(storeObjects.waterScale);
            setEquipRumtimeRadioState(storeObjects.equipRumtime);
            setTestMethodCheckState(storeObjects.testMethod);
            setIntakePurposeRadioState(storeObjects.intakePurpose);
            setTestDatePickerState(formObject.testDate);
            //setEndDatePickerState(formObject.endDate);

            waterUnitScaleRef.current = data.DATA.waterScale || 0; // 根据服务端数据更新
            // 设置屏幕标题
            (screenTitle !== formObject.name) && setScreenTitle(formObject.name);
        } else {
            console.log("STATUS !== 0", data.STATUS);
            setSnackBarMessage(`表单数据拉取发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        }
    };
    const onRecordSelectError = (err) => {
        console.log("Record selecting error:", err);
    };
    const onRecordSelectSettled = (err) => {
        loadingIndicatorVisible && setLoadingIndicatorVisible(false);
    };
    const getPath = formType === 0 ? "get" : "getsum";
    const recordSelectQuery = recordSelectQueryMaker([queryKwd, getPath, recordPubid], onRecordSelectSuccess, onRecordSelectError, onRecordSelectSettled);

    // Query: update record
    const onHandleSubmit = (data) => {
        const factor = 10 ** Number(waterUnitScaleRef.current);
        setOkButtonDisabled(true);
        setOkButtonLoading(true);
        dataFeeder({ ...data,
            cversion: getClientCversion.current(),
            waterClass: waterClass,
            //name:           normalizeWaterUnitRecordsFormName(industryType, reportType, data.unitName, data.subunitName, data.testDate),
            // unifify data units
            inputTotal:     data.inputTotal * factor,
            intakeTapWater: data.intakeTapWater * factor,
            intakeSurface:  data.intakeSurface * factor,
            intakeGround:   data.intakeGround * factor,
            intakeSoft:     data.intakeSoft * factor,
            intakeSteam:    data.intakeSteam * factor,
            intakeBought:   data.intakeBought * factor,
            intakeSea:      data.intakeSea * factor,
            intakeBrackish: data.intakeBrackish * factor,
            intakeRecycled: data.intakeRecycled * factor,
            intakeMine:     data.intakeMine * factor,
            intakeRain:     data.intakeRain * factor,
            intakeTotal:    data.intakeTotal * factor,
            inCircleDCCW:   data.inCircleDCCW * factor,
            inCircleICCW:   data.inCircleICCW * factor,
            inCircleOCW:    data.inCircleOCW * factor,
            inSerialSCR:    data.inSerialSCR * factor,
            inSerialRW:     data.inSerialRW * factor,
            inSerialSteam:  data.inSerialSteam * factor,
            inSerialOTW:    data.inSerialOTW * factor,
            outCircleDCCW:  data.outCircleDCCW * factor,
            outCircleICCW:  data.outCircleICCW * factor,
            outCircleOCW:   data.outCircleOCW * factor,
            outSerialSCR:   data.outSerialSCR * factor,
            outSerialRW:    data.outSerialRW * factor,
            outSteam:       data.outSteam * factor,
            outSerialOTW:   data.outSerialOTW * factor,
            dumpSewage:     data.dumpSewage * factor,
            dumpOutside:    data.dumpOutside * factor,
            waterLeakage:   data.waterLeakage * factor,
            waterConsume:   data.waterConsume * factor
        }); // append client cversion
        recordUpdateQuery.mutate();
    };
    // 新组件不需改动
    const onRecordUpdateSuccess = (data) => {
        console.log(" Record update success response data: ", data);
        if(data.STATUS !== 0) {
            setSnackBarMessage(`表单更新发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        } else {
            // 设置确认框数据
            setConfirmDialogConfig({
                title: "更新表单",
                text: "表单信息更新成功!",
                okLabel: "确定",
                onOK: () => {
                    setShowConfirm(false);
                    recordSelectQuery.mutate();
                }});
            setShowConfirm(true);
        }
    };
    const onRecordUpdateError = (error) => {
        setSnackBarMessage(`表单更新发生错误, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const onRecordUpdateSettled = (data, error) => {
        setOkButtonDisabled(false);
        setOkButtonLoading(false);
    };
    const recordUpdateQuery = recordUpdateQueryMaker(dataFeeder, [queryKwd, "upd", recordPubid], onRecordUpdateSuccess, onRecordUpdateError, onRecordUpdateSettled);

    // Query: delete record
    const recordDeleteOnSuccess = () => {
        clearStore();
        navigation.goBack();
    };
    const recordDeleteOnError = (error) => {
        setSnackBarMessage(`表单删除失败, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const recordDeleteQuery = recordDeleteQueryMaker([queryKwd, "del", recordPubid], recordDeleteOnSuccess, recordDeleteOnError);

    const deleteAlert = () => {
        return (
            Alert.alert("删除表单", `删除后不可撤销, 请确认是否删除: ${delQueryInfo.current?.name}?`, [
                { text: "取消" },
                { text: "确定", onPress: () => recordDeleteQuery.mutate() },
            ])
        );
    };

    const clearCache = () => {
        return (
            Alert.alert("清空本地缓存", "1. 用于未知原因导致数据无法提交的情况\r\n2. 未提交的内容将会丢失, 请做好数据截图备份\r\n3. 已提交到服务端的数据不受影响\r\n4. 确认清空请点击“确认”按钮", [
                { text: "取消" },
                { text: "确定", onPress: () => { clearStore(); navigation.goBack(); } },
            ])
        );
    };


    // Hook: Auto calc water volumn sumups
    // 输入水量 - 常规水量
    const intakeTapWater = Number(useWatch({ control, name: "intakeTapWater" }));
    const intakeSurface  = Number(useWatch({ control, name: "intakeSurface" }));
    const intakeGround   = Number(useWatch({ control, name: "intakeGround" }));
    const intakeSoft     = Number(useWatch({ control, name: "intakeSoft" }));
    const intakeSteam    = Number(useWatch({ control, name: "intakeSteam" }));
    const intakeBought   = Number(useWatch({ control, name: "intakeBought" }));
    // 输入水量 - 非常规水量
    const intakeSea      = Number(useWatch({ control, name: "intakeSea" }));
    const intakeBrackish = Number(useWatch({ control, name: "intakeBrackish" }));
    const intakeRecycled = Number(useWatch({ control, name: "intakeRecycled" }));
    const intakeMine     = Number(useWatch({ control, name: "intakeMine" }));
    const intakeRain     = Number(useWatch({ control, name: "intakeRain" }));
    //const intakeTotal  = Number(useWatch({ control, name: "intakeTotal" }));
    // 循环水量
    const inCircleDCCW   = Number(useWatch({ control, name: "inCircleDCCW" }));
    const inCircleICCW   = Number(useWatch({ control, name: "inCircleICCW" }));
    const inCircleOCW    = Number(useWatch({ control, name: "inCircleOCW" }));
    // 串联水量
    const inSerialSCR    = Number(useWatch({ control, name: "inSerialSCR" }));
    const inSerialRW     = Number(useWatch({ control, name: "inSerialRW" }));
    const inSerialSteam  = Number(useWatch({ control, name: "inSerialSteam" }));
    const inSerialOTW    = Number(useWatch({ control, name: "inSerialOTW" }));
    // 输出水量
    const outCircleDCCW = Number(useWatch({ control, name: "outCircleDCCW" }));
    const outCircleICCW = Number(useWatch({ control, name: "outCircleICCW" }));
    const outCircleOCW  = Number(useWatch({ control, name: "outCircleOCW" }));
    const outSerialSCR  = Number(useWatch({ control, name: "outSerialSCR" }));
    const outSerialRW   = Number(useWatch({ control, name: "outSerialRW" }));
    const outSteam      = Number(useWatch({ control, name: "outSteam" }));
    const outSerialOTW  = Number(useWatch({ control, name: "outSerialOTW" }));
    // 排到污水处理站水量, 外排水量, 漏水量, 用于计算耗水量
    const dumpSewage     = Number(useWatch({ control, name: "dumpSewage" }));
    const dumpOutside    = Number(useWatch({ control, name: "dumpOutside" }));
    const waterLeakage   = Number(useWatch({ control, name: "waterLeakage" }));

    const updateGlobalState = debounce((intakeTapWater, intakeSurface, intakeGround, intakeSoft, intakeSteam, intakeBought, intakeSea, intakeBrackish, intakeRecycled, intakeMine, intakeRain, inCircleDCCW, inCircleICCW, inCircleOCW, inSerialSCR, inSerialRW, inSerialSteam, inSerialOTW, outCircleDCCW, outCircleICCW, outCircleOCW, outSerialSCR, outSerialRW, outSteam, outSerialOTW, dumpSewage, dumpOutside, waterLeakage) => {
        const intakeTotal_ = intakeTapWater + intakeSurface + intakeGround + intakeSoft + intakeSteam + intakeBought + intakeSea + intakeBrackish + intakeRecycled + intakeMine + intakeRain;
        const inputTotal_ = intakeTotal_ + inCircleDCCW + inCircleICCW + inCircleOCW + inSerialSCR + inSerialRW + inSerialSteam + inSerialOTW;
        const outputTotalExceptConsume = outCircleDCCW + outCircleICCW + outCircleOCW + outSerialSCR + outSerialRW + outSteam + outSerialOTW + dumpSewage + dumpOutside + waterLeakage;
        // 输入水量=输出水量, 取水量+循环水量+串联水量+回用水量=循环水量+串联水量+回用水量+排水量+漏失水量+耗水量
        const waterConsume_ = inputTotal_ - outputTotalExceptConsume;

        setValue("intakeTotal",  `${roundNearest(intakeTotal_)}`);  // 取水量
        setValue("inputTotal",   `${roundNearest(inputTotal_)}`);   // 输入水量
        setValue("waterConsume", `${roundNearest(waterConsume_)}`); // 耗水量

        setStore("intakeTotal",  getValues("intakeTotal"));
        setStore("inputTotal",   getValues("inputTotal"));
        setStore("waterConsume", getValues("waterConsume"));
        subCversionRef.current++;
    }, 100);

    // 注意, 这个useEffect必需在恢复本地存储之前运行, 否则初始化时不会触发
    useEffect(() => {
        (formType === 0) && updateGlobalState(intakeTapWater, intakeSurface, intakeGround, intakeSoft, intakeSteam, intakeBought, intakeSea, intakeBrackish, intakeRecycled, intakeMine, intakeRain, inCircleDCCW, inCircleICCW, inCircleOCW, inSerialSCR, inSerialRW, inSerialSteam, inSerialOTW, outCircleDCCW, outCircleICCW, outCircleOCW, outSerialSCR, outSerialRW, outSteam, outSerialOTW, dumpSewage, dumpOutside, waterLeakage);
    }, [intakeTapWater, intakeSurface, intakeGround, intakeSoft, intakeSteam, intakeBought, intakeSea, intakeBrackish, intakeRecycled, intakeMine, intakeRain, inCircleDCCW, inCircleICCW, inCircleOCW, inSerialSCR, inSerialRW, inSerialSteam, inSerialOTW, outCircleDCCW, outCircleICCW, outCircleOCW, outSerialSCR, outSerialRW, outSteam, outSerialOTW, dumpSewage, dumpOutside, waterLeakage]);


    // Hook: onMount fetch data
    useEffect(() => {
        // 优化方案: 根据用户权限进行mutate, 只有可写用户才需要本地存储
        const localCversion = getClientCversion.current();
        const remoteCversion = route.params.pageMeta.cversion;
        console.log("localCversion:", localCversion, "remoteSversion:", remoteCversion, "subCversionRef:", subCversionRef.current);

        const restoreLocalData = () => {
            const formObject = { // 默认值用户表单的数据显示
                name:           storedValueToFormValue.current("name"),
                //waterClass: storedValueToFormValue.current("waterClass"),
                testDate:      storedValueToFormValue.current("testDate"),
                //endDate:        storedValueToFormValue.current("endDate"),
                //unitId:       storedValueToFormValue.current("unitId"),
                unitName:       storedValueToFormValue.current("unitName"),
                //subunitId:    storedValueToFormValue.current("subunitId"),
                subunitName:    storedValueToFormValue.current("subunitName"),
                intakePurpose:  storedValueToFormValue.current("intakePurpose", "[]"),
                modelSpec:      storedValueToFormValue.current("modelSpec"),
                equipRumtime:   storedValueToFormValue.current("equipRumtime", "0"),
                equipPlace:     storedValueToFormValue.current("equipPlace"),
                testMethod:     storedValueToFormValue.current("testMethod"),
                tempIn:         storedValueToFormValue.current("tempIn"),
                tempOut:        storedValueToFormValue.current("tempOut"),
                inputTotal:     storedValueToFormValue.current("inputTotal", "0"),
                intakeTapWater: storedValueToFormValue.current("intakeTapWater", "0"),
                intakeSurface:  storedValueToFormValue.current("intakeSurface", "0"),
                intakeGround:   storedValueToFormValue.current("intakeGround", "0"),
                intakeSoft:     storedValueToFormValue.current("intakeSoft", "0"),
                intakeSteam:    storedValueToFormValue.current("intakeSteam", "0"),
                intakeBought:   storedValueToFormValue.current("intakeBought", "0"),
                //intakeExt1:   storedValueToFormValue.current("intakeExt1"),
                //intakeExt2:   storedValueToFormValue.current("intakeExt2"),
                //intakeExt3:   storedValueToFormValue.current("intakeExt3"),
                //intakeExt4:   storedValueToFormValue.current("intakeExt4"),
                intakeSea:      storedValueToFormValue.current("intakeSea", "0"),
                intakeBrackish: storedValueToFormValue.current("intakeBrackish", "0"),
                intakeRecycled: storedValueToFormValue.current("intakeRecycled", "0"),
                intakeMine:     storedValueToFormValue.current("intakeMine", "0"),
                intakeRain:     storedValueToFormValue.current("intakeRain", "0"),
                //intakeExt11:  storedValueToFormValue.current("intakeExt11"),
                //intakeExt12:  storedValueToFormValue.current("intakeExt12"),
                //intakeExt13:  storedValueToFormValue.current("intakeExt13"),
                //intakeExt14:  storedValueToFormValue.current("intakeExt14"),
                intakeTotal:    storedValueToFormValue.current("intakeTotal"),
                inCircleDCCW:   storedValueToFormValue.current("inCircleDCCW"),
                inCircleICCW:   storedValueToFormValue.current("inCircleICCW"),
                inCircleOCW:    storedValueToFormValue.current("inCircleOCW"),
                inSerialSCR:    storedValueToFormValue.current("inSerialSCR"),
                inSerialRW:     storedValueToFormValue.current("inSerialRW"),
                inSerialSteam:  storedValueToFormValue.current("inSerialSteam"),
                inSerialOTW:    storedValueToFormValue.current("inSerialOTW"),
                outCircleDCCW:  storedValueToFormValue.current("outCircleDCCW"),
                outCircleICCW:  storedValueToFormValue.current("outCircleICCW"),
                outCircleOCW:   storedValueToFormValue.current("outCircleOCW"),
                outSerialSCR:   storedValueToFormValue.current("outSerialSCR"),
                outSerialRW:    storedValueToFormValue.current("outSerialRW"),
                outSteam:       storedValueToFormValue.current("outSteam"),
                outSerialOTW:   storedValueToFormValue.current("outSerialOTW"),
                dumpSewage:     storedValueToFormValue.current("dumpSewage"),
                dumpOutside:    storedValueToFormValue.current("dumpOutside"),
                waterLeakage:   storedValueToFormValue.current("waterLeakage"),
                waterConsume:   storedValueToFormValue.current("waterConsume"),
                //outputTotal:  storedValueToFormValue.current("outputTotal"),

                others:         storedValueToFormValue.current("others"),
                remarks:        storedValueToFormValue.current("remarks"),
                waterScale:     storedValueToFormValue.current("waterScale", "0"),
            };
            reset(formObject); // 重置react-form数据

            // 设置selector数据
            const defaultWaterUnitScale = 0;
            setWaterUnitScaleState(ifTruthLet(getStore("waterScale"),        isNullness, () => {return {id: 0, name: ""};}, value => value));
            setEquipRumtimeRadioState(ifTruthLet(getStore("equipRumtime"),   isNullness, () => {return {id: 0, name: ""};}, value => value));
            setTestMethodCheckState(ifTruthLet(getStore("testMethod"),       isNullness, () => {return [];},                value => value));
            setIntakePurposeRadioState(ifTruthLet(getStore("intakePurpose"), isNullness, () => {return {id: 0, name: ""};}, value => value));
            setTestDatePickerState(formObject.testDate);
            //setEndDatePickerState(formObject.endDate);

            waterUnitScaleRef.current = getStore("waterScale")?.id || defaultWaterUnitScale; // 根据本地存储更新
        };

        // 根据反馈, 要求默认显示0
        if (localCversion === 0) {
            recordSelectQuery.mutate();
            return;
        }

        if(formType === 1) {
            recordSelectQuery.mutate();
        } else if (localCversion === 0 && remoteCversion > 0) {
            console.log("Local cversion === 0, serve may have something nontrivial initialized, query mutate!");
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        } else if (isServerDataNewer(localCversion, remoteCversion) && subCversionRef.current > 0) { // 服务端有更新数据, 但本地有修改
            console.log("Client cversion fell behind the server cversion, someone might modified the database, choose to use the data version!");
            Alert.alert("注意", "由于其他人向服务器提交了数据, 服务端数据比本地数据新, 请选择使用哪份数据?", [
                { text: "使用本地数据", onPress: () => { setStore("mainCversion", remoteCversion); restoreLocalData(); } },
                { text: "使用服务端数据", onPress: () => { setLoadingIndicatorVisible(true); recordSelectQuery.mutate(); } },
            ]);
        } else if (isServerDataNewer(localCversion, remoteCversion)) { // localCversion==0时要mutate是因为服务端可能在创建记录时生成了必要数据
            console.log("Server cversion may be newer, query mutate:", localCversion, remoteCversion);
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        }
        else {
            console.log("Local cversion >= server cversion, query will not mutate!");
            restoreLocalData();
        }
    }, []);

    // Hook: refresh data by unit changing
    useEffect(() => {
        //if (waterUnitScaleState.id !== undefined && waterUnitScaleRef.current !== waterUnitScaleState.id) { // waterUnitScaleState may not be updated in realtime
        if (unitToggleState === true ) {
            const oldFactor = 10 ** Number(waterUnitScaleRef.current);
            const newFactor = 10 ** Number(waterUnitScaleState.id);
            const changedFields = { // 默认值用户表单的数据显示
                intakeTapWater: String(roundNearest(getValues("intakeTapWater") * oldFactor / newFactor)),
                intakeSurface:  String(roundNearest(getValues("intakeSurface")  * oldFactor / newFactor)),
                intakeGround:   String(roundNearest(getValues("intakeGround")   * oldFactor / newFactor)),
                intakeSoft:     String(roundNearest(getValues("intakeSoft")     * oldFactor / newFactor)),
                intakeSteam:    String(roundNearest(getValues("intakeSteam")    * oldFactor / newFactor)),
                intakeBought:   String(roundNearest(getValues("intakeBought")   * oldFactor / newFactor)),
                intakeSea:      String(roundNearest(getValues("intakeSea")      * oldFactor / newFactor)),
                intakeBrackish: String(roundNearest(getValues("intakeBrackish") * oldFactor / newFactor)),
                intakeRecycled: String(roundNearest(getValues("intakeRecycled") * oldFactor / newFactor)),
                intakeMine:     String(roundNearest(getValues("intakeMine")     * oldFactor / newFactor)),
                intakeRain:     String(roundNearest(getValues("intakeRain")     * oldFactor / newFactor)),
                inCircleDCCW:   String(roundNearest(getValues("inCircleDCCW")   * oldFactor / newFactor)),
                inCircleICCW:   String(roundNearest(getValues("inCircleICCW")   * oldFactor / newFactor)),
                inCircleOCW:    String(roundNearest(getValues("inCircleOCW")    * oldFactor / newFactor)),
                inSerialSCR:    String(roundNearest(getValues("inSerialSCR")    * oldFactor / newFactor)),
                inSerialRW:     String(roundNearest(getValues("inSerialRW")     * oldFactor / newFactor)),
                inSerialSteam:  String(roundNearest(getValues("inSerialSteam")  * oldFactor / newFactor)),
                inSerialOTW:    String(roundNearest(getValues("inSerialOTW")    * oldFactor / newFactor)),
                outCircleDCCW:  String(roundNearest(getValues("outCircleDCCW")  * oldFactor / newFactor)),
                outCircleICCW:  String(roundNearest(getValues("outCircleICCW")  * oldFactor / newFactor)),
                outCircleOCW:   String(roundNearest(getValues("outCircleOCW")   * oldFactor / newFactor)),
                outSerialSCR:   String(roundNearest(getValues("outSerialSCR")   * oldFactor / newFactor)),
                outSerialRW:    String(roundNearest(getValues("outSerialRW")    * oldFactor / newFactor)),
                outSteam:       String(roundNearest(getValues("outSteam")       * oldFactor / newFactor)),
                outSerialOTW:   String(roundNearest(getValues("outSerialOTW")   * oldFactor / newFactor)),
                dumpSewage:     String(roundNearest(getValues("dumpSewage")     * oldFactor / newFactor)),
                dumpOutside:    String(roundNearest(getValues("dumpOutside")    * oldFactor / newFactor)),
                waterLeakage:   String(roundNearest(getValues("waterLeakage")   * oldFactor / newFactor)),
                waterConsume:   String(roundNearest(getValues("waterConsume")   * oldFactor / newFactor)),
            };
            for (const [key, val] of Object.entries(changedFields)) {
                setValue(key, val); // set react hook form
                setStore(key, val); // set mmkv
            }

            waterUnitScaleRef.current = waterUnitScaleState.id || 0;
            setUnitToggleState(false);
        }
    }, [unitToggleState]);


    const refreshUnits = () => { setUnitToggleState(true); };
    const calcUnit = (unit, unitStatObj = waterUnitScaleState) => {
        if (unitStatObj.id === 0) { return unit; }
        else if (unitStatObj.id === 4) { return "万" + unit; }
        else if (unitStatObj.id === 3) { return "千" + unit; }
        else if (unitStatObj.id === 2) { return "百" + unit; }
        else if (unitStatObj.id === 1) { return "十" + unit; }
        else { log.warn("Invalid unit scale: %s, unitStatObj: %s", unit, JSON.stringify(unitStatObj)); return unit; }
    };
        // 统一配置提示文字和
    const fieldTips = {
        subunitName: { toolTip: "主要生产系统指从原料到产品的物料加工过程。", placeholder: "从原料到产品的物料加工过程" },
    };
    // remarks不需配置
    const FieldsConfig_1_book = {
        industry: [
            {
                inputs: [
                    //{ name: "name",       label: "表单名称",      unit: "", type: "PLAIN", editable: true, multiline: true, },
                    //{ name: "unitName",   label: "用水单元名称",   unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "subunitName",label: "工序或设备名称", unit: "", type: "PLAIN", editable: true, placeholder: fieldTips.subunitName.placeholder, toolTip: fieldTips.subunitName.toolTip, props: {multiline: true}, },
                    { name: "testDate",   label: "日期",         unit: "", type: "DATE",  editable: true, placeholder: "", selectorState: testDatePickerState, setSelectorState: setTestDatePickerState, },
                    //{ name: "endDate",    label: "结束日期",      unit: "", type: "DATE",  editable: true, placeholder: "", selectorState: endDatePickerState,   setSelectorState: setEndDatePickerState, },
                    { name: "waterScale", label: "水量单位",      unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: waterUnitScaleState,  setSelectorState: setWaterUnitScaleState, dataProvider: waterUnitScaleEnum, cb: refreshUnits },
                    { name: "modelSpec",    label: "型号规格",    unit: "",  type: "PLAIN", editable: true, placeholder: "", },
                    { name: "equipRumtime", label: "设备用水时间", unit: "",  type: "RADIO", editable: true, placeholder: "", selectorState: equipRumtimeRadioState,  setSelectorState: setEquipRumtimeRadioState, dataProvider: equipRumtimeEnum, },
                    { name: "equipPlace",   label: "安装地点",    unit: "",  type: "PLAIN", editable: true, placeholder: "", },
                    { name: "testMethod",   label: "测试方法",    unit: "",  type: "CHECK", editable: true, placeholder: "", selectorState: testMethodCheckState,  setSelectorState: setTestMethodCheckState, dataProvider: testMethodStateDataProviderRef, },
                    { name: "tempIn",       label: "入口水温",    unit: "℃", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "tempOut",      label: "出口水温",    unit: "℃", type: "PLAIN", editable: true, placeholder: "", },

                ]
            },
        ],
        service: [
            {
                inputs: [
                    //{ name: "name",       label: "表单名称",      unit: "", type: "PLAIN", editable: true, multiline: true, },
                    { name: "unitName",   label: "用水单元名称",   unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "subunitName",label: "工序或设备名称", unit: "", type: "PLAIN", editable: true, placeholder: fieldTips.subunitName.placeholder, toolTip: fieldTips.subunitName.toolTip, props: {multiline: true}, },
                    { name: "testDate",   label: "日期",         unit: "", type: "DATE",  editable: true, placeholder: "", selectorState: testDatePickerState, setSelectorState: setTestDatePickerState, },
                    //{ name: "endDate",    label: "结束日期",      unit: "", type: "DATE",  editable: true, placeholder: "", selectorState: endDatePickerState,   setSelectorState: setEndDatePickerState, },
                    { name: "waterScale", label: "水量单位",      unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: waterUnitScaleState,  setSelectorState: setWaterUnitScaleState, dataProvider: waterUnitScaleEnum, cb: refreshUnits },
                    { name: "modelSpec",    label: "型号规格",    unit: "",  type: "PLAIN", editable: true, placeholder: "", },
                    { name: "equipRumtime", label: "设备用水时间", unit: "",  type: "RADIO", editable: true, placeholder: "", selectorState: equipRumtimeRadioState,  setSelectorState: setEquipRumtimeRadioState, dataProvider: equipRumtimeEnum, },
                    { name: "equipPlace",   label: "安装地点",    unit: "",  type: "PLAIN", editable: true, placeholder: "", },
                    { name: "testMethod",   label: "测试方法",    unit: "",  type: "CHECK", editable: true, placeholder: "", selectorState: testMethodCheckState,  setSelectorState: setTestMethodCheckState, dataProvider: testMethodStateDataProviderRef, },
                    { name: "tempIn",       label: "入口水温",    unit: "℃", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "tempOut",      label: "出口水温",    unit: "℃", type: "PLAIN", editable: true, placeholder: "", },

                ]
            },
        ],
    };
    const FieldsConfig_1_table = {
        industry: [
            {
                inputs: [
                    //{ name: "name",       label: "表单名称",      unit: "", type: "PLAIN", editable: true, multiline: true, },
                    { name: "testDate",   label: "日期",         unit: "", type: "DATE",  editable: true, placeholder: "", selectorState: testDatePickerState, setSelectorState: setTestDatePickerState, },
                    //{ name: "endDate",    label: "结束日期",      unit: "", type: "DATE",  editable: true, placeholder: "", selectorState: endDatePickerState,   setSelectorState: setEndDatePickerState, },
                    { name: "unitName",   label: "用水单元名称",   unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "subunitName",label: "工序或设备名称", unit: "", type: "PLAIN", editable: true, placeholder: fieldTips.subunitName.placeholder, toolTip: fieldTips.subunitName.toolTip, props: {multiline: true}, },
                    { name: "intakePurpose", label: "用途",      unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: intakePurposeRadioState,  setSelectorState: setIntakePurposeRadioState, dataProvider: intakePurposeEnum, },
                    { name: "waterScale",    label: "水量单位",   unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: waterUnitScaleState,      setSelectorState: setWaterUnitScaleState,     dataProvider: waterUnitScaleEnum, cb: refreshUnits },
                ]
            },
        ],
        service: [
            {
                inputs: [
                    //{ name: "name",       label: "表单名称",      unit: "", type: "PLAIN", editable: true, multiline: true, },
                    { name: "testDate",   label: "日期",         unit: "", type: "DATE",  editable: true, placeholder: "", selectorState: testDatePickerState, setSelectorState: setTestDatePickerState, },
                    //{ name: "endDate",    label: "结束日期",      unit: "", type: "DATE",  editable: true, placeholder: "", selectorState: endDatePickerState,   setSelectorState: setEndDatePickerState, },
                    { name: "unitName",   label: "用水单元名称",   unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    //{ name: "subunitName",label: "工序或设备名称", unit: "", type: "PLAIN", editable: true, placeholder: fieldTips.subunitName.placeholder, toolTip: fieldTips.subunitName.toolTip, props: {multiline: true}, },
                    { name: "intakePurpose", label: "用途",      unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: intakePurposeRadioState,  setSelectorState: setIntakePurposeRadioState, dataProvider: intakePurposeEnum, },
                    { name: "waterScale",    label: "水量单位",   unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: waterUnitScaleState,      setSelectorState: setWaterUnitScaleState,     dataProvider: waterUnitScaleEnum, cb: refreshUnits },
                ]
            },
        ],
    };

    const FieldsConfig_2_book = {
        industry: [
        // 输入水量
        // 输入水量-取水量
            {
                nodeType: "Section", title: "输入水量", nodes: [
                    {
                        inputs: [
                            { name: "intakeTotal", label: "合计取水量", unit: calcUnit("m³/d"), type: "PLAIN", editable: false, placeholder: "", },
                        ]
                    },
                    {
                        nodeType: "Accordion", title: "常规水源取水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "intakeTapWater", label: "自来水",    unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeSurface",  label: "地表水",    unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeGround",   label: "地下水",    unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeSoft",     label: "外购软化水", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeSteam",    label: "外购蒸汽",   unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeBought",   label: "外购水",     unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },
                    {
                        nodeType: "Accordion", title: "非常规水源取水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "intakeSea",      label: "海水",         unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeBrackish", label: "苦咸水",        unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeRecycled", label: "城镇污水再生水", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeMine",     label: "矿井水",        unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeRain",     label: "雨水",         unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },
                    // 输入水量-循环水量
                    {
                        nodeType: "Accordion", title: "循环水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "inCircleDCCW", label: "直接冷却循环水量", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "inCircleICCW", label: "间接冷却循环水量", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "inCircleOCW",  label: "其他循环水量",    unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },
                    // 输入水量-串联水量
                    {
                        nodeType: "Accordion", title: "串联水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "inSerialSCR", label: "蒸汽冷凝水回用量", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "inSerialRW",  label: "回用水量",        unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "inSerialSteam", label: "蒸汽量",        unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "inSerialOTW", label: "其他串联水",      unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },],
            },
            // 输出水量
            // 输出水量-循环水量
            {
                nodeType: "Section", title: "输出水量", nodes: [
                    {
                        nodeType: "Accordion", title: "循环水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "outCircleDCCW", label: "直接冷却循环水量", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "outCircleICCW", label: "间接冷却循环水量", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "outCircleOCW",  label: "其他循环水量",     unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },
                    // 输出水量-串联水量
                    {
                        nodeType: "Accordion", title: "串联水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "outSerialSCR", label: "蒸汽冷凝水回用量", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "outSerialRW",  label: "回用水量",        unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "outSteam",     label: "蒸汽量",          unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "outSerialOTW", label: "其他串联水",      unit: calcUnit("m³/d"),  type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },
                    {
                        inputs: [
                            { name: "dumpSewage",   label: "排至污水处理站水量", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            { name: "dumpOutside",  label: "排水量(排至厂外)",  unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            { name: "waterLeakage", label: "漏损水量",         unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            { name: "waterConsume", label: "耗水量",           unit: calcUnit("m³/d"), type: "PLAIN", editable: false, placeholder: "", },
                        ]
                    },
                ]
            },
        ],
        service: [
            // 输入水量
            // 输入水量-取水量
            {
                nodeType: "Section", title: "输入水量", nodes: [
                    {
                        inputs: [
                            { name: "intakeTotal", label: "合计取水量", unit: calcUnit("m³/d"), type: "PLAIN", editable: false, placeholder: "", },
                        ]
                    },
                    {
                        nodeType: "Accordion", title: "常规水源取水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "intakeTapWater", label: "自来水",    unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeSurface",  label: "地表水",    unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeGround",   label: "地下水",    unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeSoft",     label: "外购软化水", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeSteam",    label: "外购蒸汽",   unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeBought",   label: "外购水",     unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },
                    {
                        nodeType: "Accordion", title: "非常规水源取水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "intakeSea",      label: "海水",         unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeBrackish", label: "苦咸水",        unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeRecycled", label: "城镇污水再生水", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeMine",     label: "矿井水",        unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeRain",     label: "雨水",         unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },
                    // 输入水量-循环水量
                    {
                        nodeType: "Accordion", title: "循环水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "inCircleDCCW", label: "直接冷却循环水量", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "inCircleICCW", label: "间接冷却循环水量", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "inCircleOCW",  label: "其他循环水量",    unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },
                    // 输入水量-串联水量
                    {
                        nodeType: "Accordion", title: "串联水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "inSerialSCR", label: "蒸汽冷凝水回用量", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "inSerialRW",  label: "回用水量",        unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "inSerialSteam", label: "蒸汽量",        unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "inSerialOTW", label: "其他串联水",      unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },],
            },
            // 输出水量
            // 输出水量-循环水量
            {
                nodeType: "Section", title: "输出水量", nodes: [
                    {
                        nodeType: "Accordion", title: "循环水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "outCircleDCCW", label: "直接冷却循环水量", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "outCircleICCW", label: "间接冷却循环水量", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "outCircleOCW",  label: "其他循环水量",     unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },
                    // 输出水量-串联水量
                    {
                        nodeType: "Accordion", title: "串联水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "outSerialSCR", label: "蒸汽冷凝水回用量", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "outSerialRW",  label: "回用水量",        unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "outSteam",     label: "蒸汽量",          unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "outSerialOTW", label: "其他串联水",      unit: calcUnit("m³/d"),  type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },
                    {
                        inputs: [
                            { name: "dumpSewage",   label: "排至污水处理站水量", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            { name: "dumpOutside",  label: "排水量(排至厂外)",  unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            { name: "waterLeakage", label: "漏损水量",         unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            { name: "waterConsume", label: "耗水量",           unit: calcUnit("m³/d"), type: "PLAIN", editable: false, placeholder: "", },
                        ]
                    },
                ]
            },
        ],
    };
    const FieldsConfig_2_table = {
        industry: [
        // 输入水量
        // 输入水量-取水量
            {
                nodeType: "Section", title: "输入水量", nodes: [
                    {
                        inputs: [
                            { name: "intakeTotal", label: "合计取水量", unit: calcUnit("m³/d"), type: "PLAIN", editable: false, placeholder: "", },
                        ]
                    },
                    {
                        nodeType: "Accordion", title: "常规水源取水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "intakeTapWater", label: "自来水",    unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeSurface",  label: "地表水",    unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeGround",   label: "地下水",    unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeSoft",     label: "外购软化水", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeSteam",    label: "外购蒸汽",   unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeBought",   label: "外购水",     unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },
                    {
                        nodeType: "Accordion", title: "非常规水源取水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "intakeSea",      label: "海水",         unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeBrackish", label: "苦咸水",        unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeRecycled", label: "城镇污水再生水", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeMine",     label: "矿井水",        unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeRain",     label: "雨水",         unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },
                    // 输入水量-循环水量
                    {
                        nodeType: "Accordion", title: "循环水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "inCircleDCCW", label: "直接冷却循环水量", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "inCircleICCW", label: "间接冷却循环水量", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "inCircleOCW",  label: "其他循环水量",    unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },
                    // 输入水量-串联水量
                    {
                        nodeType: "Accordion", title: "串联水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "inSerialSCR", label: "蒸汽冷凝水回用量", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "inSerialRW",  label: "回用水量",        unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "inSerialSteam", label: "蒸汽量",        unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "inSerialOTW", label: "其他串联水",      unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },],
            },
            // 输出水量
            // 输出水量-循环水量
            {
                nodeType: "Section", title: "输出水量", nodes: [
                    {
                        nodeType: "Accordion", title: "循环水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "outCircleDCCW", label: "直接冷却循环水量", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "outCircleICCW", label: "间接冷却循环水量", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "outCircleOCW",  label: "其他循环水量",     unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },
                    // 输出水量-串联水量
                    {
                        nodeType: "Accordion", title: "串联水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "outSerialSCR", label: "蒸汽冷凝水回用量", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "outSerialRW",  label: "回用水量",        unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "outSteam",     label: "蒸汽量",          unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "outSerialOTW", label: "其他串联水",      unit: calcUnit("m³/d"),  type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },
                    {
                        inputs: [
                            { name: "dumpSewage",   label: "排至污水处理站水量", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            { name: "dumpOutside",  label: "排水量(排至厂外)",  unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            { name: "waterLeakage", label: "漏损水量",         unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            { name: "waterConsume", label: "耗水量",           unit: calcUnit("m³/d"), type: "PLAIN", editable: false, placeholder: "", },
                        ]
                    },
                ]
            },
        ],
        service: [
            // 输入水量
            // 输入水量-取水量
            {
                nodeType: "Section", title: "输入水量", nodes: [
                    {
                        inputs: [
                            { name: "intakeTotal", label: "合计取水量", unit: calcUnit("m³/d"), type: "PLAIN", editable: false, placeholder: "", },
                        ]
                    },
                    {
                        nodeType: "Accordion", title: "常规水源取水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "intakeTapWater", label: "自来水",    unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeSurface",  label: "地表水",    unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeGround",   label: "地下水",    unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeSoft",     label: "外购软化水", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeSteam",    label: "外购蒸汽",   unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeBought",   label: "外购水",     unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },
                    {
                        nodeType: "Accordion", title: "非常规水源取水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "intakeSea",      label: "海水",         unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeBrackish", label: "苦咸水",        unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeRecycled", label: "城镇污水再生水", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeMine",     label: "矿井水",        unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "intakeRain",     label: "雨水",         unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },
                    // 输入水量-循环水量
                    {
                        nodeType: "Accordion", title: "循环水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "inCircleDCCW", label: "直接冷却循环水量", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "inCircleICCW", label: "间接冷却循环水量", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "inCircleOCW",  label: "其他循环水量",    unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },
                    // 输入水量-串联水量
                    {
                        nodeType: "Accordion", title: "串联水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "inSerialSCR", label: "蒸汽冷凝水回用量", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "inSerialRW",  label: "回用水量",        unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "inSerialSteam", label: "蒸汽量",        unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "inSerialOTW", label: "其他串联水",      unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },],
            },
            // 输出水量
            // 输出水量-循环水量
            {
                nodeType: "Section", title: "输出水量", nodes: [
                    {
                        nodeType: "Accordion", title: "循环水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "outCircleDCCW", label: "直接冷却循环水量", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "outCircleICCW", label: "间接冷却循环水量", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "outCircleOCW",  label: "其他循环水量",     unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },
                    // 输出水量-串联水量
                    {
                        nodeType: "Accordion", title: "串联水量", icon: "folder-outline", nodes: [{
                            inputs: [
                                { name: "outSerialSCR", label: "蒸汽冷凝水回用量", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "outSerialRW",  label: "回用水量",        unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "outSteam",     label: "蒸汽量",          unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                                { name: "outSerialOTW", label: "其他串联水",      unit: calcUnit("m³/d"),  type: "PLAIN", editable: true, placeholder: "", },
                            ]
                        }]
                    },
                    {
                        inputs: [
                            { name: "dumpSewage",   label: "排至污水处理站水量", unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            { name: "dumpOutside",  label: "排水量(排至厂外)",  unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            { name: "waterLeakage", label: "漏损水量",         unit: calcUnit("m³/d"), type: "PLAIN", editable: true, placeholder: "", },
                            { name: "waterConsume", label: "耗水量",           unit: calcUnit("m³/d"), type: "PLAIN", editable: false, placeholder: "", },
                        ]
                    },
                ]
            },
        ],
    };

    const FieldsConfig_3_book = {
        industry: [
        // 其它部分
            {
                inputs: [
                    { name: "others", label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "", },
                ]
            },
        ],
        service: [
            // 其它部分
            {
                inputs: [
                    { name: "others", label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "", },
                ]
            },
        ],
    };
    const FieldsConfig_3_table = {
        industry: [
        // 其它部分
            {
                inputs: [
                    { name: "others", label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "", },
                ]
            },
        ],
        service: [
            // 其它部分
            {
                inputs: [
                    { name: "others", label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "", },
                ]
            },
        ],
    };

    let FieldsConfig = [];
    switch(projSubclass) {
        case 1: // 表
            FieldsConfig = [...FieldsConfig_1_table[projIndustry], ...FieldsConfig_2_table[projIndustry], ...FieldsConfig_3_table[projIndustry]];
            break;
        case 2: // 书
            FieldsConfig = [...FieldsConfig_1_book[projIndustry], ...FieldsConfig_2_book[projIndustry], ...FieldsConfig_3_book[projIndustry]];
            break;
        default:
            console.warn("Unknown subclass:", projSubclass);
            FieldsConfig = [];
            break;
    }

    return (
        <View style={{ flex: 1 }}>

            <HeaderBar
                //title={screenTitle}
                navigation={navigation}
                goBackCallback={() => {
                    callOneByOne(
                        //() => { _mainCversionGetter=undefined; _subCversionGetter=undefined;},
                        resetSelectorStates,
                    );
                    /*callOneByOne(clearStore, resetSelectorStates)*/  // 返回前清空mmkv存储因为临时数据不再需要, 重置状态使得页面切换后不会使用前面的数据
                }}
                menuItemArray={formType === 0 ? [{ title: "删除表单", action: deleteAlert }, { title: "清空缓存", action: clearCache }] : []}
            />

            <ScreenWrapper contentContainerStyle={styles.container}>

                <View style={styles.formEntry}>
                    <FormListNodesMapper
                        fieldsConfig={FieldsConfig}
                        subCversionRef={subCversionRef}
                        control={control}
                        setStore={setStore}
                        resetField={resetField}
                        errors={errors}
                        setSnackBarMessage={setSnackBarMessage}
                        setShowSnackbar={setShowSnackbar}
                        //formDisabledGlobal={formDisabledGlobal}
                        editable={formEditableGlobal}
                        rowStyle={styles.rowContainer}
                    />

                    {formType === 0 && <ControlledTextInput
                        name="remarks"
                        rowLabel="备注"
                        control={control}
                        placeholder=""
                        onChangeText={(text) => {
                            subCversionRef.current++;
                            setStore("subCversion", subCversionRef.current);
                            setStore("remarks", text);
                        }}
                        onClearText={() => {
                            subCversionRef.current++;
                            setStore("subCversion", subCversionRef.current);
                            setStore("remarks", "");
                            resetField("remarks", { defaultValue: "" });
                        }}
                        editable={formEditableGlobal}
                        mode="flat"
                        multiline={true}
                        formError={errors}
                        style={styles.rowContainerLongText}
                        //disabled={formDisabledGlobal}
                        required={false}
                    />}
                </View>

                {formType === 0 && <View style={styles.button}>
                    <Button mode="contained"
                        disabled={okButtonDisabled}
                        loading={okButtonLoading}
                        icon={saveButtonIcon}
                        onPress={handleSubmit(onHandleSubmit, (formErr) => onPreSubmitError(formErr, getStore, "WaterUsageUnitsFirstRecordsUpdating"))}
                    >{bottomBarLabels.ok}</Button>
                </View>}
            </ScreenWrapper>

            <LoadingIndicator
                title={""}
                message={"加载中....."}
                visible={loadingIndicatorVisible}
                onClose={()=>setLoadingIndicatorVisible(false)}
            />

            <Snackbar
                style={styles.snackBar}
                visible={showSnackbar}
                onDismiss={() => setShowSnackbar(false)}
                onIconPress={() => setShowSnackbar(false)}
                duration={Snackbar.DURATION_LONG}
            >
                {snackBarMessage}
            </Snackbar>

            <DialogToConfirm
                visible={showConfirm}
                title={confimDialogConfig.title ? confimDialogConfig.title : ""}
                text={confimDialogConfig.text ? confimDialogConfig.text : ""}
                onOK={confimDialogConfig.onOK ? confimDialogConfig.onOK : () => setShowConfirm(false)}
                okBtnLabel={confimDialogConfig.okLabel ? confimDialogConfig.okLabel : "确认"}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    rowContainer:{
        firstColumn: {
            flex: 2,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 3,
        // /borderWidth: 1,
        // height: 40,
        },
    },
    rowContainerLongText: {
        firstColumn: {
            flex: 1,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 4,
            // /borderWidth: 1,
            // height: 40,
        },
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        width: "80%",
        alignSelf: "center",
    },
    formEntry: {
        margin: 8,
    },
    snackBar: {
        flexDirection: "column",
    },
});

export default RecordsUpdating;
