import React, { useEffect, useRef } from "react";
import { Controller, useForm } from "react-hook-form";
import { View } from "react-native";
import { Button, Dialog, TextInput as PaperTextInput, Portal } from "react-native-paper";


/**
 * 用户输入Prompts的输入框
 * @param {*} props
 * @param {boolean} props.visible
 * @param {function} props.setVisible
 * @param {string} props.promptsText
 * @param {function} props.onOKCallback
 * @param {function} props.onCancelCallback
 * @param {string} props.dialogTitle
 * @param {string} props.okBtnLabel
 * @param {string} props.cancelBtnLabel
 * @returns
 */
export const PromptDialog = ({ visible, setVisible, promptsText, onOKCallback, onCancelCallback, dialogTitle = "", okBtnLabel = "确定", cancelBtnLabel = "取消"}) => {
    const {
        control,
        reset,
        handleSubmit,
    } = useForm({
        defaultValues: {
            prompts: promptsText,
        },
    });

    useEffect(()=>{
        reset({
            prompts: promptsText,
        });
    }, [promptsText]);

    // 提交按钮, data由handleSubmit传入
    const onOK = (data) => {
        if(data) {
            setVisible(false);
            onOKCallback(data.prompts);
            reset(); // 恢复默认值
        }
    };
        // 取消按钮
    const onCancel = () => {
        setVisible(false); // 先隐藏后清空的主观感受好些
        onCancelCallback();
        reset(); // 恢复默认值
    };
    const placeholder = useRef("该表关于xxxxx, \n............\n请按如下要求分析: \n1. ... \n2. ... \n3. ...");

    return (
        <Portal>
            <Dialog onDismiss={() => { setVisible(false); }} visible={visible} dismissable={false}>
                {dialogTitle && <Dialog.Title>{dialogTitle}</Dialog.Title>}
                <Dialog.Content>
                    <View flexDirection="column" style={{marginTop: 16}}>
                        <View>
                            {/*<PaperText variant="bodyLarge" style={{margin: 6}}>AI提示词</PaperText>*/}
                            <Controller
                                control={control}
                                name="prompts"
                                //rules={{ required: true, }}
                                render={({ field: { onChange, onBlur, value } }) => (
                                    <PaperTextInput
                                        mode="outlined"
                                        multiline={true}
                                        onBlur={onBlur}
                                        onChangeText={onChange}
                                        value={value}
                                        placeholder={placeholder.current}
                                        style={{minHeight: 220,}}
                                    />
                                )}
                            />
                        </View>
                    </View>
                </Dialog.Content>
                <Dialog.Actions>
                    <Button onPress={onCancel}>{cancelBtnLabel}</Button>
                    <Button onPress={handleSubmit(onOK)}>{okBtnLabel}</Button>
                </Dialog.Actions>
            </Dialog>
        </Portal>
    );
};
