import { standarHttpClient as httpClient } from "../http/standardClient";

/**
 * 服务端给每个用户一个消息队列, 用于当某用户的操作对其他人有影响时
 */
const sendBack = async (id, data) => {
    try {
        await httpClient.post(`rpc/${id}`, { json: { data: data } });
    } catch (err) {
        console.log(`Failed to send back rpc result ${data} for id ${id}, error "${err.message}"!`);
    }
};

/**
 * RPC名称映射到函数
 */
const RpcNameMap = {
    refresh: (args) => {console.log(args);}
};

/**
 * 服务端发起一个RPC请求, 客户端执行对应函数, 根据RPC类型的不同可以将运行结果返回服务端.
 * 这种RPC的典型用例是, 服务端将RPC数据附带在其它HTTP请求的响应中, 客户端收到RPC后执行对应逻辑.
 * 例如某用户的状态发生改变, 服务端在其它消息中附带RPC告知客户端刷新数据.
 * @param {object} args
 * @param {integer} args.id  服务端生成的惟一标识码, 在服务端用于该rpc的数据记录的索引.
 * @param {string} args.name rpc名称, 用于客户端查找执行函数.
 * @param {string} args.type rpc类型, 如果不需要返回运行结果给服务端, 就是"tell", 需要返回结果到服务, 就使用"ask".
 * @param {any} args.args    客户端函数运行所需的参数, 若不需要参数则为undefined.
 */
export const callRpc = ({id, name, type, args}) => {
    if(type === "ask") {
        RpcNameMap?.[name] ? sendBack(id, RpcNameMap?.[name](args)) : undefined;
    } else if(type==="tell") {
        RpcNameMap?.[name](args);
    } else {
        console.error("Invalid type of rpc: ", id, name, type, args);
    }
};
