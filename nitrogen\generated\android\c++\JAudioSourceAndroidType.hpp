///
/// JAudioSourceAndroidType.hpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc R<PERSON>avy @ Margelo
///

#pragma once

#include <fbjni/fbjni.h>
#include "AudioSourceAndroidType.hpp"

namespace margelo::nitro::react_native_audio_recorder_player {

  using namespace facebook;

  /**
   * The C++ JNI bridge between the C++ enum "AudioSourceAndroidType" and the the Kotlin enum "AudioSourceAndroidType".
   */
  struct JAudioSourceAndroidType final: public jni::JavaClass<JAudioSourceAndroidType> {
  public:
    static auto constexpr kJavaDescriptor = "Lcom/margelo/nitro/reactnativeaudiorecorderplayer/AudioSourceAndroidType;";

  public:
    /**
     * Convert this Java/Kotlin-based enum to the C++ enum AudioSourceAndroidType.
     */
    [[maybe_unused]]
    [[nodiscard]]
    AudioSourceAndroidType toCpp() const {
      static const auto clazz = javaClassStatic();
      static const auto fieldOrdinal = clazz->getField<int>("_ordinal");
      int ordinal = this->getFieldValue(fieldOrdinal);
      return static_cast<AudioSourceAndroidType>(ordinal);
    }

  public:
    /**
     * Create a Java/Kotlin-based enum with the given C++ enum's value.
     */
    [[maybe_unused]]
    static jni::alias_ref<JAudioSourceAndroidType> fromCpp(AudioSourceAndroidType value) {
      static const auto clazz = javaClassStatic();
      static const auto fieldDEFAULT = clazz->getStaticField<JAudioSourceAndroidType>("DEFAULT");
      static const auto fieldMIC = clazz->getStaticField<JAudioSourceAndroidType>("MIC");
      static const auto fieldVOICE_UPLINK = clazz->getStaticField<JAudioSourceAndroidType>("VOICE_UPLINK");
      static const auto fieldVOICE_DOWNLINK = clazz->getStaticField<JAudioSourceAndroidType>("VOICE_DOWNLINK");
      static const auto fieldVOICE_CALL = clazz->getStaticField<JAudioSourceAndroidType>("VOICE_CALL");
      static const auto fieldCAMCORDER = clazz->getStaticField<JAudioSourceAndroidType>("CAMCORDER");
      static const auto fieldVOICE_RECOGNITION = clazz->getStaticField<JAudioSourceAndroidType>("VOICE_RECOGNITION");
      static const auto fieldVOICE_COMMUNICATION = clazz->getStaticField<JAudioSourceAndroidType>("VOICE_COMMUNICATION");
      static const auto fieldREMOTE_SUBMIX = clazz->getStaticField<JAudioSourceAndroidType>("REMOTE_SUBMIX");
      static const auto fieldUNPROCESSED = clazz->getStaticField<JAudioSourceAndroidType>("UNPROCESSED");
      static const auto fieldRADIO_TUNER = clazz->getStaticField<JAudioSourceAndroidType>("RADIO_TUNER");
      static const auto fieldHOTWORD = clazz->getStaticField<JAudioSourceAndroidType>("HOTWORD");
      
      switch (value) {
        case AudioSourceAndroidType::DEFAULT:
          return clazz->getStaticFieldValue(fieldDEFAULT);
        case AudioSourceAndroidType::MIC:
          return clazz->getStaticFieldValue(fieldMIC);
        case AudioSourceAndroidType::VOICE_UPLINK:
          return clazz->getStaticFieldValue(fieldVOICE_UPLINK);
        case AudioSourceAndroidType::VOICE_DOWNLINK:
          return clazz->getStaticFieldValue(fieldVOICE_DOWNLINK);
        case AudioSourceAndroidType::VOICE_CALL:
          return clazz->getStaticFieldValue(fieldVOICE_CALL);
        case AudioSourceAndroidType::CAMCORDER:
          return clazz->getStaticFieldValue(fieldCAMCORDER);
        case AudioSourceAndroidType::VOICE_RECOGNITION:
          return clazz->getStaticFieldValue(fieldVOICE_RECOGNITION);
        case AudioSourceAndroidType::VOICE_COMMUNICATION:
          return clazz->getStaticFieldValue(fieldVOICE_COMMUNICATION);
        case AudioSourceAndroidType::REMOTE_SUBMIX:
          return clazz->getStaticFieldValue(fieldREMOTE_SUBMIX);
        case AudioSourceAndroidType::UNPROCESSED:
          return clazz->getStaticFieldValue(fieldUNPROCESSED);
        case AudioSourceAndroidType::RADIO_TUNER:
          return clazz->getStaticFieldValue(fieldRADIO_TUNER);
        case AudioSourceAndroidType::HOTWORD:
          return clazz->getStaticFieldValue(fieldHOTWORD);
        default:
          std::string stringValue = std::to_string(static_cast<int>(value));
          throw std::invalid_argument("Invalid enum value (" + stringValue + "!");
      }
    }
  };

} // namespace margelo::nitro::react_native_audio_recorder_player
