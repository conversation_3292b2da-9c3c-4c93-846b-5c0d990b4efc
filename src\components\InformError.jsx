import React from "react";
import { StyleSheet } from "react-native";
import { Snackbar } from "react-native-paper";

// https://callstack.github.io/react-native-paper/docs/components/Snackbar

/*
const action = {
    label: "Action",
    onPress: () => console.log("Press callback"),
};
*/

export const ErrorInform = ({ message, visible, onDismiss, onIconPress }) => {
    return (
        <Snackbar
            style={styles}
            visible={visible}
            onDismiss={onDismiss}             // duration超时后触发
            onIconPress={onIconPress}         // 点击X按钮触发
            duration={Snackbar.DURATION_LONG}
        // action={action} //文字按钮回调
        >
            {message}
        </Snackbar>);
};

const styles = StyleSheet.create({
    flexDirection: "column",
});
