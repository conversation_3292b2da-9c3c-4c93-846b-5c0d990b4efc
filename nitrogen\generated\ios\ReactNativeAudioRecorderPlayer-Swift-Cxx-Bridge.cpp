///
/// ReactNativeAudioRecorderPlayer-Swift-Cxx-Bridge.cpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON> @ Margelo
///

#include "ReactNativeAudioRecorderPlayer-Swift-Cxx-Bridge.hpp"

// Include C++ implementation defined types
#include "HybridAudioRecorderPlayerSpecSwift.hpp"
#include "ReactNativeAudioRecorderPlayer-Swift-Cxx-Umbrella.hpp"

namespace margelo::nitro::react_native_audio_recorder_player::bridge::swift {

  // pragma MARK: std::function<void(const std::string& /* result */)>
  Func_void_std__string create_Func_void_std__string(void* _Nonnull swiftClosureWrapper) {
    auto swiftClosure = ReactNativeAudioRecorderPlayer::Func_void_std__string::fromUnsafe(swiftClosureWrapper);
    return [swiftClosure = std::move(swiftClosure)](const std::string& result) mutable -> void {
      swiftClosure.call(result);
    };
  }
  
  // pragma MARK: std::function<void(const std::exception_ptr& /* error */)>
  Func_void_std__exception_ptr create_Func_void_std__exception_ptr(void* _Nonnull swiftClosureWrapper) {
    auto swiftClosure = ReactNativeAudioRecorderPlayer::Func_void_std__exception_ptr::fromUnsafe(swiftClosureWrapper);
    return [swiftClosure = std::move(swiftClosure)](const std::exception_ptr& error) mutable -> void {
      swiftClosure.call(error);
    };
  }
  
  // pragma MARK: std::function<void(const RecordBackType& /* recordingMeta */)>
  Func_void_RecordBackType create_Func_void_RecordBackType(void* _Nonnull swiftClosureWrapper) {
    auto swiftClosure = ReactNativeAudioRecorderPlayer::Func_void_RecordBackType::fromUnsafe(swiftClosureWrapper);
    return [swiftClosure = std::move(swiftClosure)](const RecordBackType& recordingMeta) mutable -> void {
      swiftClosure.call(recordingMeta);
    };
  }
  
  // pragma MARK: std::function<void(const PlayBackType& /* playbackMeta */)>
  Func_void_PlayBackType create_Func_void_PlayBackType(void* _Nonnull swiftClosureWrapper) {
    auto swiftClosure = ReactNativeAudioRecorderPlayer::Func_void_PlayBackType::fromUnsafe(swiftClosureWrapper);
    return [swiftClosure = std::move(swiftClosure)](const PlayBackType& playbackMeta) mutable -> void {
      swiftClosure.call(playbackMeta);
    };
  }
  
  // pragma MARK: std::shared_ptr<margelo::nitro::react_native_audio_recorder_player::HybridAudioRecorderPlayerSpec>
  std::shared_ptr<margelo::nitro::react_native_audio_recorder_player::HybridAudioRecorderPlayerSpec> create_std__shared_ptr_margelo__nitro__react_native_audio_recorder_player__HybridAudioRecorderPlayerSpec_(void* _Nonnull swiftUnsafePointer) {
    ReactNativeAudioRecorderPlayer::HybridAudioRecorderPlayerSpec_cxx swiftPart = ReactNativeAudioRecorderPlayer::HybridAudioRecorderPlayerSpec_cxx::fromUnsafe(swiftUnsafePointer);
    return std::make_shared<margelo::nitro::react_native_audio_recorder_player::HybridAudioRecorderPlayerSpecSwift>(swiftPart);
  }
  void* _Nonnull get_std__shared_ptr_margelo__nitro__react_native_audio_recorder_player__HybridAudioRecorderPlayerSpec_(std__shared_ptr_margelo__nitro__react_native_audio_recorder_player__HybridAudioRecorderPlayerSpec_ cppType) {
    std::shared_ptr<margelo::nitro::react_native_audio_recorder_player::HybridAudioRecorderPlayerSpecSwift> swiftWrapper = std::dynamic_pointer_cast<margelo::nitro::react_native_audio_recorder_player::HybridAudioRecorderPlayerSpecSwift>(cppType);
  #ifdef NITRO_DEBUG
    if (swiftWrapper == nullptr) [[unlikely]] {
      throw std::runtime_error("Class \"HybridAudioRecorderPlayerSpec\" is not implemented in Swift!");
    }
  #endif
    ReactNativeAudioRecorderPlayer::HybridAudioRecorderPlayerSpec_cxx& swiftPart = swiftWrapper->getSwiftPart();
    return swiftPart.toUnsafe();
  }

} // namespace margelo::nitro::react_native_audio_recorder_player::bridge::swift
