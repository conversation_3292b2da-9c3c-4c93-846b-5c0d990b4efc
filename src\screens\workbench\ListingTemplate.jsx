import React, { useEffect, useState } from "react";
import { StyleSheet, View } from "react-native";
import { Appbar, DataTable, Divider, Snackbar, useTheme } from "react-native-paper";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import BottomBarButton from "../../components/BottomBarButton";
import { ListingItem } from "../../components/ListingItem";
import EmptyScreen from "../EmptyScreen";
import ScreenWrapper from "../ScreenWrapper";
//import { makeRequestListingPositions as queryClient } from "../../api/listingQueries";
import { parseServerState } from "../../utils/messages";
//import { allPositionsState as pageDataState } from "../../hooks/globalStates";
import { useShallow } from "zustand/shallow";
import { useRefreshOnFocus } from "../../hooks/useRefreshOnFocus";

const ListingTemplate = ({ bottomBarLabel, saveButtonIcon, emptyScreenText, snackBarDefaultText, queryClient, pageDataState, listItemNavigateTo, addingButtonNavigateTo, navigation, ...props }) => {
    const { bottom, left, right } = useSafeAreaInsets();
    const theme = useTheme();
    const height = theme.isV3 ? 80 : 56;

    const [snackBarMessage, setSnackBarMessage] = useState(snackBarDefaultText);

    const [showSnackbar, setShowSnackbar] = useState(false);
    const [dataArray, setDataArray] = pageDataState(useShallow((state) => [state.data, state.setData]));

    const onSuccess = (data) => {
        if (data.STATUS === 0) {
            // id = 0 has the special meaning, eg. as the root, but it cannot be modified by users
            setDataArray(data.DATA.filter(item => item?.id !== 0));
        } else {
            setSnackBarMessage(parseServerState(data.STATUS || data.status, data.DATA.info));
            setShowSnackbar(true);
        }
    };
    const onError = () => { };
    const onSettled = () => { };

    const pageDataQuery = queryClient(onSuccess, onError, onSettled);

    useEffect(() => {
        pageDataQuery.mutate();
    }, []);

    useRefreshOnFocus(pageDataQuery.mutate); // 通过返回按钮返回时刷新数据

    return (
        <>
            {dataArray.length === 0 ? <EmptyScreen text={emptyScreenText} /> :
                <ScreenWrapper contentContainerStyle={styles.container}>
                    <DataTable style={styles.dataTable}>
                        <Divider />

                        {dataArray.map((item, i) => {
                            return <ListingItem
                                key={i}
                                pubid={item.pubid}
                                name={item.name}
                                department={item.department}
                                position={item.position}
                                onPress={() => {
                                    navigation.navigate(listItemNavigateTo, {pageMeta: item});
                                }}
                            />;
                        })}


                    </DataTable>
                    {/* Give a margin, or the Appbar will be on top of the last ListingItem. */}
                    <Divider style={{ marginBottom: height + bottom }} />
                </ScreenWrapper>}

            <Appbar
                style={[
                    styles.bottom,
                    {
                        height: height + bottom,
                    },
                    theme.isV3 && {
                        backgroundColor: theme.colors.elevation.level2,
                    },
                ]}
                safeAreaInsets={{ bottom, left, right }}
                theme={{ mode: "adaptive" }}
            //mode='center-aligned'
            >

                <View style={styles.bottomBarContainer}>
                    <Divider />
                    <View style={styles.bottomBarComponents}>
                        <BottomBarButton
                            label={bottomBarLabel}
                            //loading={saveButtonLoading}
                            //disabled={saveButtonDisabled}
                            icon={saveButtonIcon}
                            onPress={() => {
                                navigation.navigate(addingButtonNavigateTo);
                            }}
                        />
                    </View>
                </View>
            </Appbar>

            <Snackbar
                style={styles.snackBar}
                visible={showSnackbar}
                onDismiss={() => {
                    setShowSnackbar(false);
                }}
                onIconPress={() => {
                    setShowSnackbar(false);
                }}
                duration={Snackbar.DURATION_LONG}
            >
                {snackBarMessage}
            </Snackbar>

        </>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    dataTable: {
        fontSize: 24,
        //marginTop: 18,
        //borderColor: "black"
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        flex: 1,
        marginHorizontal: 6
    },
    snackBar: {
        flexDirection: "column",
    },
});

export default ListingTemplate;
