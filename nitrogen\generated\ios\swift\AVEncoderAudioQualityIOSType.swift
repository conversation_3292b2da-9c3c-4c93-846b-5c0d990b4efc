///
/// AVEncoderAudioQualityIOSType.swift
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON> @ Margelo
///

/**
 * Represents the JS enum `AVEncoderAudioQualityIOSType`, backed by a C++ enum.
 */
public typealias AVEncoderAudioQualityIOSType = margelo.nitro.react_native_audio_recorder_player.AVEncoderAudioQualityIOSType

public extension AVEncoderAudioQualityIOSType {
  /**
   * Get a AVEncoderAudioQualityIOSType for the given String value, or
   * return `nil` if the given value was invalid/unknown.
   */
  init?(fromString string: String) {
    switch string {
      case "min":
        self = .min
      case "low":
        self = .low
      case "medium":
        self = .medium
      case "high":
        self = .high
      case "max":
        self = .max
      default:
        return nil
    }
  }

  /**
   * Get the String value this AVEncoderAudioQualityIOSType represents.
   */
  var stringValue: String {
    switch self {
      case .min:
        return "min"
      case .low:
        return "low"
      case .medium:
        return "medium"
      case .high:
        return "high"
      case .max:
        return "max"
    }
  }
}
