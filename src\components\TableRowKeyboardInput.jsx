import React from "react";
import { StyleSheet } from "react-native";
import { DataTable, Text } from "react-native-paper";
import CellTextInput from "./CellTextInput";
import PropTypes from "prop-types";

/**
 * DataTable中一个2列的记录行
 * @param {Object} arg
 * @param {string} arg.value
 * @param {function} arg.onClearText
 * @param {boolean} arg.disabled
 * @param {"flat" | "outlined"} arg.mode
 */
const TableRowKeyboardInput = ({ rowLabel, inputValue, placeholder, onChangeText, onClearText, editable, mode, ...props }) => {
    return (
        <DataTable.Row style={styles.container}>
            <DataTable.Cell style={styles.firstColumn}>
                <Text style={styles.firstColumn.content}>
                    {rowLabel}
                </Text>
            </DataTable.Cell>
            <DataTable.Cell style={styles.secondColumn}>
                <CellTextInput
                    //label={inputLabel} // 不需要label, 因为左边已经有了标签
                    returnKeyType="next"
                    value={inputValue}
                    placeholder={placeholder}
                    mode={mode}
                    editable={editable}
                    onChangeText={onChangeText}
                    autoCapitalize="none"
                    autoComplete="off"
                    textContentType="none"
                    onClearText={onClearText}
                    {...props}
                />
            </DataTable.Cell>
        </DataTable.Row>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        height: 60,
        //borderColor: "black",

    },
    firstColumn: {
        flex: 2,
        //borderWidth: 1,
        //height: 40,
        content: {
            fontSize: 18,
        }
    },
    secondColumn: {
        flex: 3,
        //borderWidth: 1,
        // height: 40,
    },
});

export default TableRowKeyboardInput;
