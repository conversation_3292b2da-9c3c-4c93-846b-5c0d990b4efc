import React, { useState } from "react";
import { TextInput as PaperTextInput, useTheme } from "react-native-paper";
import TextInput from "./TextInput";
import PropTypes from "prop-types";

// TextInput.Icon
// https://callstack.github.io/react-native-paper/docs/components/TextInput/TextInputIcon/

/**
 * @param {Object} arg
 * @param {string} arg.errorText
 * @param {string} arg.description
 */
const PasswordTextInput = ({ errorText, description, ...props }) => {
    const [textSecureEntry, setTextSecureEntry] = useState(true);
    const theme = useTheme();
    return (
        <TextInput
            errorText={errorText}
            description={description}
            secureTextEntry={textSecureEntry}
            right={<PaperTextInput.Icon
                icon={textSecureEntry ? "eye" : "eye-off"}
                color={isTextInputFocused => isTextInputFocused ? (errorText ? theme.colors.error : theme.colors.primary) : "transparent"}
                onPress={() => setTextSecureEntry(!textSecureEntry)}
            />}
            {...props}>
        </TextInput>
    );
};

PasswordTextInput.propTypes = {
    errorText: PropTypes.string,
    description: PropTypes.string,
};

export default PasswordTextInput;
