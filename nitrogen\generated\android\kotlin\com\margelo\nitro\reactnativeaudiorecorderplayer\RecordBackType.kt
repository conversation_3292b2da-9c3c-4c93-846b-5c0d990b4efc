///
/// RecordBackType.kt
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON> @ Margelo
///

package com.margelo.nitro.reactnativeaudiorecorderplayer

import androidx.annotation.Keep
import com.facebook.proguard.annotations.DoNotStrip
import com.margelo.nitro.core.*

/**
 * Represents the JavaScript object/struct "RecordBackType".
 */
@DoNotStrip
@Keep
data class RecordBackType
  @DoNotStrip
  @Keep
  constructor(
    val isRecording: Boolean?,
    val currentPosition: Double,
    val currentMetering: Double?,
    val recordSecs: Double?
  ) {
  /* main constructor */
}
