import * as React from "react";
import { StyleSheet, View } from "react-native";
import { Text, TouchableRipple } from "react-native-paper";
import ScreenWrapper from "./ScreenWrapper";

/**
 * 显示一个带文本内容的空屏幕, 用于屏幕内容为空或暂时未拉取到时给用户的展示.
 * @param {object} args
 * @param {string} args.text
 * @returns
 */
const EmptyScreen = ({text, route, ...props}) => {
    //console.log("route.params.pageMeta: ", route?.params?.pageMeta);
    return (
        <ScreenWrapper contentContainerStyle={styles.container}>
            <TouchableRipple
                style={styles.ripple}
                onPress={() => {}}
                rippleColor="rgba(0, 0, 0, .32)"
            >
                <View pointerEvents="none" style={{marginHorizontal: 20, marginBottom: 180}}>
                    <Text variant="headlineMedium" style={styles.text}>{text || route?.params?.pageMeta?.text || ""}</Text>
                </View>
            </TouchableRipple>
        </ScreenWrapper>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    ripple: {
        flex: 1,
        alignItems: "center",
        justifyContent: "center",
    },
    text:{
    }
});

export default EmptyScreen;
