///
/// AVModeIOSOption.swift
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 <PERSON> @ Margelo
///

/**
 * Represents the JS union `AVModeIOSOption`, backed by a C++ enum.
 */
public typealias AVModeIOSOption = margelo.nitro.react_native_audio_recorder_player.AVModeIOSOption

public extension AVModeIOSOption {
  /**
   * Get a AVModeIOSOption for the given String value, or
   * return `nil` if the given value was invalid/unknown.
   */
  init?(fromString string: String) {
    switch string {
      case "gameChatAudio":
        self = .gamechataudio
      case "videoRecording":
        self = .videorecording
      case "voiceChat":
        self = .voicechat
      case "videoChat":
        self = .videochat
      default:
        return nil
    }
  }

  /**
   * Get the String value this AVModeIOSOption represents.
   */
  var stringValue: String {
    switch self {
      case .gamechataudio:
        return "gameChatAudio"
      case .videorecording:
        return "videoRecording"
      case .voicechat:
        return "voiceChat"
      case .videochat:
        return "videoChat"
    }
  }
}
