import { isInteger, isNumber } from ".";
import { getUserPermits, getUserType } from "./user";


/*
;; lcmp.server/src/model/roles.lisp
(defparameter *role-bit-mask*
  '(;; 0-19用于管理相关的角色, 管理员包含对应功能的CRUD操作
    ;; 这些权限用于客户端工作台上静态功能列表的展示
    ;; (:admin-management  0  "组织管理")  ;; 添加某个组织, 这个权限太大了, 不能放在这里, 而是在allusert表中通过USER_TYPE=1指定
    (:user-management   0  "用户管理")
    (:client-management 1  "客户管理")
    (:org-config        2  "组织架构配置")
    (:project-config    3  "项目类型配置")
    ;; 20-29用于项目的全局权限, 借助linux用户权限方式
    ;; 这些list权限的是在单独的一个tab上(目前暂未实现), 例如点击水平衡卡片列出所有水平衡项目
    (:list-all   20 "能够查看所有项目")       ; 例如董事长有权限查看所有项目
    (:list-group 21 "能够查看组内项目")       ; 例如区域经理查看区域项目
    (:list-user  22 "只能查看本人相关项目")   ; 查看本人创建的或其它与本人有关的项目
    ;; 30-60用于项目流程相关的角色, 只允许访问与自己相关的功能
    ;; 这些project权限, 首先start权限是创建岗位/创建用户必需的, 用户有这个权限才能新建项目
    ;; 这些project权限在定义岗位时需要使用
    ;; 而在定义流程时, 这些权限的名称可以用作流程的步骤名, 即该步骤关联到某岗位
    ;; 对于具体的项目, 由于各步都指定了人员, 指定人员的操作是明确的, 但某些情况下, 拥有下列权限的其他用户也可以考虑进去
    (:project-abort     30 "项目废除")
    (:project-start     31 "项目创建")
    (:project-assign    32 "项目分配") ; 各步骤指定执行人员, 但目前水平衡是创建时就指定了
    (:project-execute   33 "项目实施") ; 项目执行
    (:project-review    34 "项目反馈") ; 客户反馈专用
    (:project-supervise 35 "项目监督") ; 只读, 用于抄送
    (:project-approval  36 "项目验收") ; 验证确认
    (:project-finish    37 "项目完结") ; 完成并结束项目
    ;;(:project-audit     40 "项目审计") ; 项目审计, 预留
    )
  "0~19用于管理员权限, 20~29用于项目的list权限, 30~60用于针对具体项目的权限")
*/

/**
 * code -> name of `*role-bit-mask*`
 */
const PermissionTable = {
    0: "userMgt",
    1: "clientMgt",
    2: "orgCfg",
    3: "projCfg",
    20: "projListAll",
    21: "projListGroup",
    22: "projListSelf",
    30: "projAbort",
    31: "projStart",
    32: "projAssign",
    33: "projExecute",
    34: "projReview",
    35: "projSupervise",
    36: "projApproval",
    37: "projFinish",
};

/**
 * name -> code of `*role-bit-mask*`
 */
export const PermissionCode = {
    userMgt      : 0 ,
    clientMgt    : 1 ,
    orgCfg       : 2 ,
    projCfg      : 3 ,
    projListAll  : 20,
    projListGroup: 21,
    projListSelf : 22,
    projAbort    : 30,
    projStart    : 31,
    projAssign   : 32,
    projExecute  : 33,
    projReview   : 34,
    projSupervise: 35,
    projApproval : 36,
    projFinish   : 37,
};

/**
 * Accept an array of integers and write the corresponding permissions to the object of `myPermits`.
 * @param {int[]} permitArray
 */
export const assignPermits = (permitsArray) => {
    permitsArray.map(permitId => PermissionTable[permitId] ? myPermits[PermissionTable[permitId]] = true : undefined);
    return myPermits;
};

/**
 * An object whose keys are permission names and values are boolean denoting if the user has that permission.
 * The contents should be assigned by the function of `assignPermits` which is called if the user logins succefully.
 */
export const myPermits = {};


/**
 * 检查用户是否拥有某项权限.
 * 暂时全部返回true.
 * 注意: pageMeta的principalPubid是结点的处理人的pubid, 可据此判断是否可写.
 * @returns
 */
export const checkPermits = () => {
    return true;
};

/**
 *
 * @param {int} code
 * @returns
 */
export const roleVerify = (code) => {
    const userPermits = getUserPermits();
    return isInteger(code) ? (userPermits.find(item => item === code) === code ? true : false) : false;
};


/**
(defparameter *user-type-mask*
  '((:sys-admin 0  "系统管理员") ; 维护系统, 由软件开发维护者持有, 可以涉及程序方便的内容, 类似于游戏GM, 账户不必属于某组织
    (:sub-admin 1  "订阅管理员") ; 添加/修改订阅组织的管理员, 由软件销售方持有, 账户不必属于某组织
    (:org-admin 2  "组织管理员") ; 某订阅组织内部的管理员, 允许操作本组织中*role-bit-mask*涉及的内容, 拥有workbench页面所有非测试功能
    (:qa-0      10 "QA-0")     ; QA权限
    )
 */

const UserTypeTable = {
    "sysAdmin": 0,
    "subAdmin": 1,
    "orgAdmin": 2,
    "qa_0":     10,
};

export const UserTypeList = {
    sysAdmin: "sysAdmin",
    subAdmin: "subAdmin",
    orgAdmin: "orgAdmin",
    qa_0:     "qa_0",
};

/**
 * 根据用户类型掩码检查该用户是否拥有对应名称的权限, 拥有则返回true, 否则返回false.
 * @param {int} userType 用户类型, allusers的user-type槽位值
 * @param {string} typeName 类型名称, 对应UserTypeTable的键
 * @example checkUserType(7, "sysAdmin") // -> true
 */
export const checkUserType = (userType, typeName) => {
    const typeCode = UserTypeTable[typeName];
    if(isNumber(typeCode)) {
        return (userType & (1 << typeCode)) === 0 ? false : true;
    } else {
        console.warn(`IN "checkUserType", typeName is not valid: ${typeName}.`);
        return false;
    }
};

/**
 * 验证当前用户是否拥有对应名称的权限, 拥有则返回true, 否则返回false.
 * @param {string} typeName 类型名称, 对应UserTypeTable的键
 * @example verifyUserType("sysAdmin") // -> true
 */
export const verifyUserType = (typeName) => {
    const userType = getUserType() || 0;
    return checkUserType(userType, typeName);
};

/**
 * 验证当前用户是否拥有名称列表中至少某一项权限, 只要拥有至少一项则返回true, 否则返回false.
 * @param {string[]} typeNames 类型名称列表, 对应UserTypeTable的键
 * @example verifySomeUserTypes("sysAdmin") // -> true
 */
export const verifySomeUserTypes = (...typeNames) => {
    const userType = getUserType() || 0;
    return typeNames.some(typeName => checkUserType(userType, typeName));
};

/**
 * 验证当前用户是否拥有名称列表中至少某一项权限, 只要拥有至少一项则返回true, 否则返回false.
 * @param {string[]} typeNames 类型名称列表, 对应UserTypeTable的键
 * @example verifyEveryUserTypes("sysAdmin") // -> true
 */
export const verifyEveryUserTypes = (...typeNames) => {
    const userType = getUserType() || 0;
    return typeNames.every(typeName => checkUserType(userType, typeName));
};
