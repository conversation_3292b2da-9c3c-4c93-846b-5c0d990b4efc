import Geolocation from "@react-native-community/geolocation";
import PropTypes from "prop-types";
import React, { useEffect, useState } from "react";
import { StyleSheet } from "react-native";
import { TextInput, useTheme } from "react-native-paper";
import { REQUEST_LOCATION_MAXIMUM_AGE, REQUEST_LOCATION_TIMEOUT } from "../config";
import { requestLocationWithPermission } from "../services/system/permissions";

/**
 * 由于CellTextInput的左边有固定的文字标签,
 * 因此在这里, 原本用作TextInput的label的内容, 在此被placeholder取代
 * @param {Object} arg
 * @param {string} arg.value
 * @param {function} arg.onClearText
 * @param {boolean} arg.disabled
 * @param {"flat" | "outlined"} arg.mode
 */
const ControlledCellLocationInput = ({ value, placeholder, onClearText, onChangeText, onBlur, editable = false, multiline=false, mode = "outlined", ...props }) => {
    const [iconColor, setIconColor] = useState("transparent");
    const [labelText, setLabelText] = useState(props.errorText || placeholder);
    const [placeholderText, setPlaceholderText] = useState(placeholder);



    const theme = useTheme();
    const styles = StyleSheet.create({
        input: {
            width: "100%",
            backgroundColor: theme.colors.surface,
            fontSize: 18,
            //borderWidth: 1,
            //borderColor: "red",
        },
        error: {
            fontSize: 13,
            color: theme.colors.error,
            paddingTop: 8,
        },
    });

    useEffect(()=>{
        setLabelText(""); // set label to empty string, so that the input area will display the placeholder initially
        setIconColor(theme.colors.primary);
    }, []);

    return (
        <TextInput
            value={value}
            label={labelText}
            placeholder={placeholderText}
            editable={false}
            disabled={!editable}
            style={styles.input}
            selectionColor={theme.colors.primary}
            underlineColor={theme.colors.elevation.level0}
            outlineColor={theme.colors.elevation.level0} // 透明, 文本外框颜色
            placeholderTextColor={theme.colors.elevation.level5} // 占位符文字颜色
            mode={mode}
            multiline={multiline}
            right={(onClearText && editable) ? <TextInput.Icon icon="map-marker-outline" color={iconColor} onPress={ () => {
                setLabelText("正在定位, 请稍等...");
                // https://github.com/michalchudziak/react-native-geolocation?tab=readme-ov-file#details
                Geolocation.setRNConfiguration({
                    skipPermissionRequests: false,         // (boolean) - Defaults to false. If true, you must request permissions before using Geolocation APIs.
                    authorizationLevel: "auto",            // (string, iOS-only) - Either "whenInUse", "always", or "auto". Changes whether the user will be asked to give "always" or "when in use" location services permission. Any other value or auto will use the default behaviour, where the permission level is based on the contents of your Info.plist.
                    enableBackgroundLocationUpdates: true, // (boolean, iOS-only) - When using skipPermissionRequests, toggle wether to automatically enableBackgroundLocationUpdates. Defaults to true.
                    locationProvider: "android",              // (string, Android-only) - Either "playServices", "android", or "auto". Determines wether to use Google’s Location Services API or Android’s Location API. The "auto" mode defaults to android, and falls back to Android's Location API if play services aren't available.
                });
                requestLocationWithPermission(
                    pos => {
                        onChangeText([pos.coords.longitude, pos.coords.latitude]); // [经度, 纬度]
                        setLabelText("");
                    },
                    error => {
                        setLabelText("定位失败, 请点击右侧图标重试");
                        console.log(labelText, "定位失败", error);
                    },
                    { enableHighAccuracy: true, timeout: REQUEST_LOCATION_TIMEOUT, maximumAge: REQUEST_LOCATION_MAXIMUM_AGE},
                );
            }} /> : undefined}
            onFocus={() => {
                setLabelText(placeholder);
                setIconColor(theme.colors.primary); // display the icon with color
                setPlaceholderText(" "); // display nothing, for prettier display, should be a true value, or it will display the placeholder text
            }}
            onBlur={() => {
                onBlur(); // react-hook-form的onBlur
                setIconColor("transparent");     // hide the icon
                setPlaceholderText(placeholder); // restore placeholder
                setLabelText("");                // for prettier display
            }}
            onChangeText={(text) => onChangeText(text)}
            {...props}
        />
    );
};

ControlledCellLocationInput.propTypes = {
    value: PropTypes.string,
    //label: PropTypes.string,
    placeholder: PropTypes.string,
    onClearText: PropTypes.func,
    onChangeText: PropTypes.func,
    editable: PropTypes.bool,
    mode: PropTypes.string,
};

export default ControlledCellLocationInput;
