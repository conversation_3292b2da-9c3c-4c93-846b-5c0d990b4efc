import { createZustandStoredState } from "../services/state-management";
import { STORE_PREFERENCES_KEY } from "../config/keysConfig";


const stateDefinition = (set, get) => ({
    theme: "light",
    setTheme: (newTheme) => set({ theme: newTheme }),
});


const usePreferenceZustandStoredState = createZustandStoredState(STORE_PREFERENCES_KEY, stateDefinition);

export { usePreferenceZustandStoredState };
