///
/// AudioQualityType.hpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON> @ Margelo
///

#pragma once

#if __has_include(<NitroModules/NitroHash.hpp>)
#include <NitroModules/NitroHash.hpp>
#else
#error NitroModules cannot be found! Are you sure you installed NitroModules properly?
#endif
#if __has_include(<NitroModules/JSIConverter.hpp>)
#include <NitroModules/JSIConverter.hpp>
#else
#error NitroModules cannot be found! Are you sure you installed NitroModules properly?
#endif
#if __has_include(<NitroModules/NitroDefines.hpp>)
#include <NitroModules/NitroDefines.hpp>
#else
#error NitroModules cannot be found! Are you sure you installed NitroModules properly?
#endif

namespace margelo::nitro::react_native_audio_recorder_player {

  /**
   * An enum which can be represented as a JavaScript union (AudioQualityType).
   */
  enum class AudioQualityType {
    LOW      SWIFT_NAME(low) = 0,
    MEDIUM      SWIFT_NAME(medium) = 1,
    HIGH      SWIFT_NAME(high) = 2,
  } CLOSED_ENUM;

} // namespace margelo::nitro::react_native_audio_recorder_player

namespace margelo::nitro {

  using namespace margelo::nitro::react_native_audio_recorder_player;

  // C++ AudioQualityType <> JS AudioQualityType (union)
  template <>
  struct JSIConverter<AudioQualityType> final {
    static inline AudioQualityType fromJSI(jsi::Runtime& runtime, const jsi::Value& arg) {
      std::string unionValue = JSIConverter<std::string>::fromJSI(runtime, arg);
      switch (hashString(unionValue.c_str(), unionValue.size())) {
        case hashString("low"): return AudioQualityType::LOW;
        case hashString("medium"): return AudioQualityType::MEDIUM;
        case hashString("high"): return AudioQualityType::HIGH;
        default: [[unlikely]]
          throw std::invalid_argument("Cannot convert \"" + unionValue + "\" to enum AudioQualityType - invalid value!");
      }
    }
    static inline jsi::Value toJSI(jsi::Runtime& runtime, AudioQualityType arg) {
      switch (arg) {
        case AudioQualityType::LOW: return JSIConverter<std::string>::toJSI(runtime, "low");
        case AudioQualityType::MEDIUM: return JSIConverter<std::string>::toJSI(runtime, "medium");
        case AudioQualityType::HIGH: return JSIConverter<std::string>::toJSI(runtime, "high");
        default: [[unlikely]]
          throw std::invalid_argument("Cannot convert AudioQualityType to JS - invalid value: "
                                    + std::to_string(static_cast<int>(arg)) + "!");
      }
    }
    static inline bool canConvert(jsi::Runtime& runtime, const jsi::Value& value) {
      if (!value.isString()) {
        return false;
      }
      std::string unionValue = JSIConverter<std::string>::fromJSI(runtime, value);
      switch (hashString(unionValue.c_str(), unionValue.size())) {
        case hashString("low"):
        case hashString("medium"):
        case hashString("high"):
          return true;
        default:
          return false;
      }
    }
  };

} // namespace margelo::nitro
