import React from "react";
import ListingTemplate from "./ListingTemplate";
import { makeRequestListingOrgs as queryClient } from "../../api/listingQueries";
import { allRolesState as pageDataState } from "../../hooks/globalStates";


const OrgListing = ({ navigation }) => {
    return (
        <ListingTemplate
            bottomBarLabel={"添加App客户"}
            emptyScreenText={"请点击下方按钮添加App客户"}
            snackBarDefaultText={"添加App客户遇到错误"}
            saveButtonIcon={"account-plus-outline"}
            queryClient={queryClient}
            pageDataState={pageDataState}
            listItemNavigateTo={"WorkbenchOrgUpdating"}
            addingButtonNavigateTo={"WorkbenchOrgInserting"}
            navigation={navigation}
        />
    );
};

export default OrgListing;
