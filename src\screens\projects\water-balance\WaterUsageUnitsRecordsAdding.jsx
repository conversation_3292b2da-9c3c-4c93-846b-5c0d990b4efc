import { joiResolver } from "@hookform/resolvers/joi";
import "fast-text-encoding";
import <PERSON><PERSON> from "joi";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { StyleSheet, View } from "react-native";
import { Snackbar } from "react-native-paper";
import { useShallow } from "zustand/shallow";
import {
    //makeReqGetWaterBalanceRecords as prerequireQueryMaker,
    makeReqUpdWaterBalanceRecords as recordAddQueryMaker,
    waterBalanceUseQueryClient
} from "../../../api/projectRecordsQueries";
import Button from "../../../components/Button";
import { DialogToConfirm } from "../../../components/DialogToConfirm";
import { FormListNodesMapper } from "../../../components/FormListNodesMapper";
import HeaderBar from "../../../components/HeaderBar";
import { LoadingIndicator } from "../../../components/LoadingIndicator";
import { equipRumtimeEnum, intakePurposeEnum, testMethodEnum, waterUnitScaleEnum } from "../../../config/waterBalance";
import { wbWaterUsageUnitsRecordsAddingStates as selectorStates, } from "../../../hooks/selectorStates";
import { queryClient } from "../../../services/query";
import { callOneByOne, makeDataFeeder, parseIndustryCode } from "../../../utils";
import { checkPermits } from "../../../utils/permits";
import { radioIdToObject } from "../../../utils/records";
import { onPreSubmitError } from "../../../utils/screens";
import { getUniversalTime } from "../../../utils/time";
import { validatorBase } from "../../../utils/validatorBase";
import ScreenWrapper from "../../ScreenWrapper";


const dataFeeder = makeDataFeeder();

/**
 * 批量添加用水记录
 * @param {object} args
 * @param {object} args.navigation
 * @param {object} args.route
 * @returns
 */
const RecordsUpdating = ({ navigation, route }) => {
    console.log("RecordsUpdating pageMeta from nav:", route.params.pageMeta);
    console.log("RecordsUpdating projMeta from nav:", route.params.projMeta);
    const {
        queryKwd,
        formType,
    } = route.params.pageMeta;

    const {
        waterClass,
        subclass: projSubclass,
        industry: industryType,
        contentId: projPubId,
    } = route.params.projMeta;
    //const projSubclass = route.params.projMeta.subclass;  // 1表, 2书
    //const industryType = route.params.projMeta.industry;  // 1工业, 2服务业
    //const reportType   = route.params.projMeta.subclass;  // 1表, 2书
    const projIndustry = parseIndustryCode(industryType); // 1 -> "industry", 2 -> "service"

    // formType对应着服务端的form-type, 为0时表示表单是用户填写, 为1表示服务端生成, 为1时只读

    const saveButtonIcon = "content-save-all-outline";
    const formEditableGlobal = checkPermits() ? (formType === 0 ? true : false) : false;

    // 次版本号, 表示本地更改次数, 用于程序上对子版本号的增加, 用于判断与服务端版本的差异, 同时可用于判断避免重复从MMKV获取数据
    const subCversionRef = useRef(0); // 实际上没用, 但为了保持一致性, 保留这个变量

    // 新组件需要修改!!
    const bottomBarLabels = { ok: "提交" };                    // 底部按钮显示文字
    const [snackBarMessage, setSnackBarMessage] = useState("更新表单遇到错误"); // 下方错误提示文字内容

    const [
        waterUnitScaleState,
        startDatePickerState,
        equipRumtimeRadioState,
        testMethodCheckState,
        intakePurposeRadioState,
        setWaterUnitScaleState,
        setStartDatePickerState,
        setEquipRumtimeRadioState,
        setTestMethodCheckState,
        setIntakePurposeRadioState,
        resetSelectorStates,
    ] = selectorStates(useShallow(state => [
        state.waterUnitScale,
        state.startDatePicker,
        state.equipRumtimeRadio,
        state.testMethodCheck,
        state.intakePurposeRadio,
        state.setWaterUnitScale,
        state.setStartDatePicker,
        state.setEquipRumtimeRadio,
        state.setTestMethodCheck,
        state.setIntakePurposeRadio,
        state.resetStates,
    ])); // radio组件状态

    const [confimDialogConfig, setConfirmDialogConfig] = useState({});       // 对话框信息, {title, text, okLabel, onOK}, 但显示对话框的变量在showConfirm

    // 新组件不需改动
    const [okButtonLoading,  setOkButtonLoading]  = useState(false);            // 右下方按钮图标是否旋转
    const [okButtonDisabled, setOkButtonDisabled] = useState(!checkPermits());  // 右下方按钮是否禁用
    const [showSnackbar,     setShowSnackbar]     = useState(false);            // 下方是否显示错误通知
    const [showConfirm,      setShowConfirm]      = useState(false);            // 是否显示确认对话框
    const [loadingIndicatorVisible, setLoadingIndicatorVisible] = useState(false); // 是否显示loading

    // Define form
    // 新组件需要重新定义!!
    const schema = Joi.object({
        startDate:      validatorBase.waterBalance.waterUsageUnits.startDate,
        testDays:       validatorBase.waterBalance.waterUsageUnits.testDays,
        unitName:       validatorBase.waterBalance.waterUsageUnits.textField,
        subunitName:    validatorBase.waterBalance.waterUsageUnits.textField,
        intakePurpose:  validatorBase.waterBalance.waterUsageUnits.intakePurpose,
        modelSpec:      validatorBase.waterBalance.waterUsageUnits.textField,
        equipRumtime:   validatorBase.waterBalance.waterUsageUnits.equipRumtime,
        equipPlace:     validatorBase.waterBalance.waterUsageUnits.textField,
        testMethod:     validatorBase.waterBalance.waterUsageUnits.testMethod,
        tempIn:         validatorBase.waterBalance.waterUsageUnits.float.unrequired,
        tempOut:        validatorBase.waterBalance.waterUsageUnits.float.unrequired,
        waterScale:     validatorBase.waterBalance.waterUsageUnits.waterScale,
    });

    // 新组件需要重新定义defaultValues!!
    const {
        handleSubmit,
        control,
        resetField,
        reset,
        formState: { errors },
        //setValue,
        //getValues,
    } = useForm({
        resolver: joiResolver(schema),
        //reValidateMode: "onBlur", // https://github.com/orgs/react-hook-form/discussions/10327
        defaultValues: { // 默认值在组件载入时会被更新, 这里的初始值正常情况下没有用处
            // 注意, 默认为0的是允许默认为0的值, 或者通过计算得到的值
            startDate:      String(getUniversalTime()),
            testDays:       "7",
            unitName:       "",
            subunitName:    "",
            intakePurpose:  "",
            modelSpec:      "",
            equipRumtime:   "",
            equipPlace:     "",
            testMethod:     "",
            tempIn:         "",
            tempOut:        "",
            waterScale:     "0",
        },
    });

    const onPrerequireQuerySuccess = (data) => {
        console.log("Record prerequire query success response data: ", data);
        if(data.STATUS === 0) {
            const waterScale = data.DATA.waterScale;
            resetField("waterScale", String(waterScale));
            setWaterUnitScaleState(radioIdToObject(waterUnitScaleEnum, waterScale));
        } else {
            setSnackBarMessage(`表单更新发生错误, 错误码: ${data.STATUS}, 错误消息: ${data.DATA.info}!`);
            setShowSnackbar(true);
        }
    };
    const onPrerequireQueryError = (error) => {
        console.log(`获取数据发生错误, 错误消息: ${error.message}`);
    };

    /*
    const onPrerequireQuerySettled = (data, error) => {
        //loadingIndicatorVisible && setLoadingIndicatorVisible(false);
    };
    const prerequireQuery = prerequireQueryMaker([queryKwd, "pre", projPubId], onPrerequireQuerySuccess, onPrerequireQueryError, onPrerequireQuerySettled);
    */

    // 使用useQuery获取数据
    const { isSuccess, isError,
        data: prerequireData,
        error: prerequireError } = waterBalanceUseQueryClient([queryKwd, "pre", projPubId], queryClient, { refetchOnMount: "always" });


    // Query: add record batch
    const onHandleSubmit = (data) => {
        setOkButtonDisabled(true);
        setOkButtonLoading(true);
        dataFeeder({ ...data,
            waterClass: waterClass,
        });
        recordUpdateQuery.mutate();
    };
    // 新组件不需改动
    const onRecordAddingSuccess = (data) => {
        console.log("Record adding success response data: ", data);
        if(data.STATUS !== 0) {
            setSnackBarMessage(`添加表单发生错误, 错误码: ${data.STATUS}, 错误消息: ${data.DATA.info}!`);
            setShowSnackbar(true);
        } else {
            // 设置确认框数据
            setConfirmDialogConfig({
                title: "创建表单",
                text: "新表单创建成功!",
                okLabel: "确定",
                onOK: () => {
                    reset();
                    resetSelectorStates();
                    setShowConfirm(false);
                    navigation.goBack();
                }});
            setShowConfirm(true);
        }
    };
    const onRecordAddingError = (error) => {
        setSnackBarMessage(`表单创建发生错误, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const onRecordAddingSettled = (data, error) => {
        setOkButtonDisabled(false);
        setOkButtonLoading(false);
    };
    const recordUpdateQuery = recordAddQueryMaker(dataFeeder, [queryKwd, "addbat", projPubId], onRecordAddingSuccess, onRecordAddingError, onRecordAddingSettled);

    // Hook: onMount init data
    useEffect(() => {
        setStartDatePickerState(getUniversalTime());
        //prerequireQuery.mutate(); // 本行是引起屏幕闪烁的元凶, 但即使回调中没有任何功能，仍然会闪烁
    }, []);

    useEffect(() => {
        if(isSuccess) {
            onPrerequireQuerySuccess(prerequireData);
        } else if (isError) {
            onPrerequireQueryError(prerequireError);
        }
    }, [prerequireData, prerequireError]);


    // 统一配置提示文字和
    const fieldTips = {
        subunitName: { toolTip: "主要生产系统指从原料到产品的物料加工过程。", placeholder: "从原料到产品的物料加工过程" },
    };
    // remarks不需配置
    const FieldsConfig_1_book = {
        industry: [
            {
                inputs: [
                    //{ name: "unitName",   label: "用水单元名称",   unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "subunitName",label: "工序或设备名称", unit: "", type: "PLAIN", editable: true, placeholder: fieldTips.subunitName.placeholder, toolTip: fieldTips.subunitName.toolTip, props: {multiline: true}, },
                    { name: "testDays",   label: "测试天数",      unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "startDate",  label: "开始日期",      unit: "", type: "DATE",  editable: true, placeholder: "", selectorState: startDatePickerState, setSelectorState: setStartDatePickerState, },
                    { name: "waterScale", label: "水量单位",      unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: waterUnitScaleState,  setSelectorState: setWaterUnitScaleState, dataProvider: waterUnitScaleEnum, },
                    { name: "modelSpec",    label: "型号规格",    unit: "",  type: "PLAIN", editable: true, placeholder: "", },
                    { name: "equipRumtime", label: "设备用水时间", unit: "",  type: "RADIO", editable: true, placeholder: "", selectorState: equipRumtimeRadioState,  setSelectorState: setEquipRumtimeRadioState, dataProvider: equipRumtimeEnum, },
                    { name: "equipPlace",   label: "安装地点",    unit: "",  type: "PLAIN", editable: true, placeholder: "", },
                    { name: "testMethod",   label: "测试方法",    unit: "",  type: "CHECK", editable: true, placeholder: "", selectorState: testMethodCheckState,  setSelectorState: setTestMethodCheckState, dataProvider: testMethodEnum, },
                    { name: "tempIn",       label: "入口水温",    unit: "℃", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "tempOut",      label: "出口水温",    unit: "℃", type: "PLAIN", editable: true, placeholder: "", },

                ]
            },
        ],
        service: [
            {
                inputs: [
                    { name: "unitName",   label: "用水单元名称",   unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "subunitName",label: "工序或设备名称", unit: "", type: "PLAIN", editable: true, placeholder: fieldTips.subunitName.placeholder, toolTip: fieldTips.subunitName.toolTip, props: {multiline: true}, },
                    { name: "testDays",   label: "测试天数",      unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "startDate",  label: "开始日期",      unit: "", type: "DATE",  editable: true, placeholder: "", selectorState: startDatePickerState, setSelectorState: setStartDatePickerState, },
                    { name: "waterScale", label: "水量单位",      unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: waterUnitScaleState,  setSelectorState: setWaterUnitScaleState, dataProvider: waterUnitScaleEnum, },
                    { name: "modelSpec",    label: "型号规格",    unit: "",  type: "PLAIN", editable: true, placeholder: "", },
                    { name: "equipRumtime", label: "设备用水时间", unit: "",  type: "RADIO", editable: true, placeholder: "", selectorState: equipRumtimeRadioState,  setSelectorState: setEquipRumtimeRadioState, dataProvider: equipRumtimeEnum, },
                    { name: "equipPlace",   label: "安装地点",    unit: "",  type: "PLAIN", editable: true, placeholder: "", },
                    { name: "testMethod",   label: "测试方法",    unit: "",  type: "CHECK", editable: true, placeholder: "", selectorState: testMethodCheckState,  setSelectorState: setTestMethodCheckState, dataProvider: testMethodEnum, },
                    { name: "tempIn",       label: "入口水温",    unit: "℃", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "tempOut",      label: "出口水温",    unit: "℃", type: "PLAIN", editable: true, placeholder: "", },

                ]
            },
        ],
    };
    const FieldsConfig_1_table = {
        industry: [
            {
                inputs: [
                    { name: "unitName",   label: "用水单元名称",   unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "subunitName",label: "工序或设备名称", unit: "", type: "PLAIN", editable: true, placeholder: fieldTips.subunitName.placeholder, toolTip: fieldTips.subunitName.toolTip, props: {multiline: true}, },
                    { name: "testDays",   label: "测试天数",      unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "startDate",  label: "开始日期",         unit: "", type: "DATE",  editable: true, placeholder: "", selectorState: startDatePickerState, setSelectorState: setStartDatePickerState, },
                    { name: "intakePurpose", label: "用途",      unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: intakePurposeRadioState,  setSelectorState: setIntakePurposeRadioState, dataProvider: intakePurposeEnum, },
                    { name: "waterScale",    label: "水量单位",   unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: waterUnitScaleState,      setSelectorState: setWaterUnitScaleState,     dataProvider: waterUnitScaleEnum, },
                ]
            },
        ],
        service: [
            {
                inputs: [
                    { name: "unitName",   label: "用水单元名称",   unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "startDate",  label: "开始日期",      unit: "", type: "DATE",  editable: true, placeholder: "", selectorState: startDatePickerState, setSelectorState: setStartDatePickerState, },
                    { name: "testDays",   label: "测试天数",      unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "intakePurpose", label: "用途",      unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: intakePurposeRadioState,  setSelectorState: setIntakePurposeRadioState, dataProvider: intakePurposeEnum, },
                    { name: "waterScale",    label: "水量单位",   unit: "", type: "RADIO", editable: true, placeholder: "", selectorState: waterUnitScaleState,      setSelectorState: setWaterUnitScaleState,     dataProvider: waterUnitScaleEnum, },
                ]
            },
        ],
    };

    const FieldsConfig_2_book = { industry: [], service: [], };
    const FieldsConfig_2_table = { industry: [], service: [], };

    const FieldsConfig_3_book = { industry: [], service: [], };
    const FieldsConfig_3_table = { industry: [], service: [], };
    const FieldsConfig = useMemo(() => {
        switch(projSubclass) {
            case 1: // 表
                return [...FieldsConfig_1_table[projIndustry], ...FieldsConfig_2_table[projIndustry], ...FieldsConfig_3_table[projIndustry]];
            case 2: // 书
                return [...FieldsConfig_1_book[projIndustry], ...FieldsConfig_2_book[projIndustry], ...FieldsConfig_3_book[projIndustry]];
            default:
                console.warn("Unknown subclass:", projSubclass);
                return [];
        }
    }, [projSubclass, projIndustry,
        waterUnitScaleState,
        startDatePickerState,
        equipRumtimeRadioState,
        testMethodCheckState,
        intakePurposeRadioState,]);

    return (
        <View style={{ flex: 1 }}>

            <HeaderBar
                //title={screenTitle}
                navigation={navigation}
                goBackCallback={() => {
                    callOneByOne(
                        //() => { _mainCversionGetter=undefined; _subCversionGetter=undefined;},
                        resetSelectorStates,
                        //clearStore,
                    );
                    /*callOneByOne(clearStore, resetSelectorStates)*/  // 返回前清空mmkv存储因为临时数据不再需要, 重置状态使得页面切换后不会使用前面的数据
                }}
                //menuItemArray={[]}
            />

            <ScreenWrapper contentContainerStyle={styles.container}>

                <View style={styles.formEntry}>
                    <FormListNodesMapper
                        fieldsConfig={FieldsConfig}
                        subCversionRef={subCversionRef}
                        control={control}
                        setStore={null}
                        resetField={resetField}
                        errors={errors}
                        setSnackBarMessage={setSnackBarMessage}
                        setShowSnackbar={setShowSnackbar}
                        //formDisabledGlobal={formDisabledGlobal}
                        editable={formEditableGlobal}
                        rowStyle={styles.rowContainer}
                    />

                </View>

                {formType === 0 && <View style={styles.button}>
                    <Button mode="contained"
                        disabled={okButtonDisabled}
                        loading={okButtonLoading}
                        icon={saveButtonIcon}
                        onPress={handleSubmit(onHandleSubmit, (formErr) => onPreSubmitError(formErr, null, "WaterUsageRecordsAdding"))}
                    >{bottomBarLabels.ok}</Button>
                </View>}
            </ScreenWrapper>

            <LoadingIndicator
                title={""}
                message={"加载中....."}
                visible={loadingIndicatorVisible}
                onClose={()=>setLoadingIndicatorVisible(false)}
            />

            <Snackbar
                style={styles.snackBar}
                visible={showSnackbar}
                onDismiss={() => setShowSnackbar(false)}
                onIconPress={() => setShowSnackbar(false)}
                duration={Snackbar.DURATION_LONG}
            >
                {snackBarMessage}
            </Snackbar>

            <DialogToConfirm
                visible={showConfirm}
                title={confimDialogConfig.title ? confimDialogConfig.title : ""}
                text={confimDialogConfig.text ? confimDialogConfig.text : ""}
                onOK={confimDialogConfig.onOK ? confimDialogConfig.onOK : () => setShowConfirm(false)}
                okBtnLabel={confimDialogConfig.okLabel ? confimDialogConfig.okLabel : "确认"}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    rowContainer:{
        firstColumn: {
            flex: 2,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 3,
        // /borderWidth: 1,
        // height: 40,
        },
    },
    rowContainerLongText: {
        firstColumn: {
            flex: 1,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 4,
            // /borderWidth: 1,
            // height: 40,
        },
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        width: "80%",
        alignSelf: "center",
    },
    formEntry: {
        margin: 8,
    },
    snackBar: {
        flexDirection: "column",
    },
});

export default RecordsUpdating;
