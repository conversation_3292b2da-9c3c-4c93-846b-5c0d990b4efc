import React, { useState } from "react";
import { Image, ImageBackground, Pressable, StyleSheet, useWindowDimensions, View } from "react-native";
import { Modal, Portal, useTheme } from "react-native-paper";
//import PropTypes from "prop-types";

// Dimensions文档建议使用useWindowDimensions: https://reactnative.dev/docs/dimensions


/**
<View style={{alignItems: "center"}}>
    {quotaRefImage && <Image
        source={quotaRefImage}
        resizeMode="center"
        style={{
            //aspectRatio: Image.resolveAssetSource(quotaRefImage).width / Image.resolveAssetSource(quotaRefImage).height,
            height: Image.resolveAssetSource(quotaRefImage).height * (windowWidth - formMargin * 2) / Image.resolveAssetSource(quotaRefImage).width,
            width: windowWidth - formMargin * 2,
            //transform: [{rotate: "45deg"}]
        }}
    />}

    <StaticImageView
        source={quotaRefImage}
        reservedMargin={formMargin}
    />
</View>
 */


/**
 * 在界面上显示一张图片, 点击后全屏显示
 * 布局文档, 居中关键是alignItems: https://reactnative.cn/docs/flexbox#align-content
 * @param {Object} args
 * @param {any} args.source 目前仅能接受通过`import img from '../path/img.png'`形式导入并传递的img参数
 * @param {number | null} args.width
 * @param {number | null} args.height
 * @param {Object} imageStyles 传递本参数的意图主要是处理默认图片的背景色问题
 * @returns
 */
export const StaticImageView = ({ source, width, height, reservedMargin = 0, resizeMode = "center", imageStyles = {}}) => {
    const {height: windowHeight, width: windowWidth} = useWindowDimensions();
    const imageWidth  = source && Image.resolveAssetSource(source).width;
    const imageHeight = source && Image.resolveAssetSource(source).height;
    const theme = useTheme();
    const [showfullScreen, setShowfullScreen] = useState(false);

    const styles = StyleSheet.create({
        container: {
            alignItems: "center",
            ...imageStyles?.container,
        },
        image: {
            width:  width  || (windowWidth - reservedMargin * 2),
            height: height || (imageHeight * (windowWidth - reservedMargin * 2) / imageWidth),
            resizeMode: resizeMode,
            backgroundColor: theme.colors.surface,
            //transform: [{rotate: "45deg"}]
            ...imageStyles?.image,
        },
        fullScreen:
        {
            justifyContent: "center",
            //width: Dimensions.get("window").width,
            //height: Dimensions.get("window").height,
            width: windowWidth,
            height: windowHeight,
            resizeMode:  "center", // "contain "
            ...styles?.fullScreen,
        },
    });

    return source ? (
        <View style={styles.container}>
            <Pressable onPress={()=>{ // 如果不能点击, 就要添加zIndex: style={{zIndex: 0.5, }}, https://reactnative.dev/docs/layout-props#zindex
                setShowfullScreen(true);
            }}>
                <Image
                    source={source}
                    style={styles.image}>
                </Image>
            </Pressable>

            <Portal>
                <Modal visible={showfullScreen} onDismiss={() => setShowfullScreen(false)}>
                    <Pressable
                        onPress={()=>{
                            setShowfullScreen(false);
                        }}>
                        <ImageBackground
                            source={source}
                            resizeMode="center"
                            backgroundColor="black"
                            style={styles.fullScreen}>
                        </ImageBackground>
                    </Pressable>
                </Modal>
            </Portal>
        </View>
    ) : <></>;
};

