import React from "react";
import { View } from "react-native";
import { Divider, List } from "react-native-paper";
import { FormInputNodesMapper } from "./FormInputNodesMapper";
import log from "../services/logging";


export const FormListNodesMapper = ({ fieldsConfig, subCversionRef, control, getStore, setStore, resetField, errors, setSnackBarMessage, setShowSnackbar, editable, getValues, rowStyle={}, parentKey="" }) => {
    return (
        <>
            {fieldsConfig.map((item, i) => {
                if (item.inputs !== undefined) {
                    return (
                        <FormInputNodesMapper
                            key={parentKey ? `${parentKey}-${i}` : `${i}`}
                            parentKey={parentKey ? `${parentKey}-${i}` : `${i}`}
                            fieldsConfig={item.inputs}
                            subCversionRef={subCversionRef}
                            control={control}
                            setStore={setStore}
                            getStore={getStore}
                            resetField={reset<PERSON>ield}
                            errors={errors}
                            setSnackBarMessage={setSnackBarMessage}
                            setShowSnackbar={setShowSnackbar}
                            editable={editable}
                            getValues={getValues}
                            rowStyle={rowStyle}
                        />
                    );
                } else if (item.nodeType && (item.nodeType.toUpperCase() === "SECTION")) {
                    return (
                        <View key={parentKey ? `${parentKey}-${i}` : `${i}`}>
                            <List.Section
                                title={item.title || ""}
                                style={{ borderWidth:0, marginBottom:0}}>
                                <FormListNodesMapper
                                    parentKey={parentKey ? `${parentKey}-${i}` : `${i}`}
                                    fieldsConfig={item.nodes}
                                    subCversionRef={subCversionRef}
                                    control={control}
                                    setStore={setStore}
                                    resetField={resetField}
                                    errors={errors}
                                    setSnackBarMessage={setSnackBarMessage}
                                    setShowSnackbar={setShowSnackbar}
                                    editable={editable}
                                    getValues={getValues}
                                    rowStyle={rowStyle}
                                >
                                </FormListNodesMapper>
                            </List.Section>
                            <Divider bold={true}/>
                        </View>
                    );
                } else if (item.nodeType && (item.nodeType.toUpperCase() === "ACCORDION")) {                    //console.log("nodeType:", item.nodeType.toUpperCase(), item.title);
                    return (
                        <List.Accordion
                            key={parentKey ? `${parentKey}-${i}` : `${i}`}
                            left={item.icon ? (props) => <List.Icon {...props} icon={item.icon} /> : undefined}
                            title={item.title || ""}
                            {...item.props}>
                            <FormListNodesMapper
                                parentKey={parentKey ? `${parentKey}-${i}` : `${i}`}
                                fieldsConfig={item.nodes}
                                subCversionRef={subCversionRef}
                                control={control}
                                setStore={setStore}
                                resetField={resetField}
                                errors={errors}
                                setSnackBarMessage={setSnackBarMessage}
                                setShowSnackbar={setShowSnackbar}
                                editable={editable}
                                getValues={getValues}
                                rowStyle={rowStyle}
                            >
                            </FormListNodesMapper>
                        </List.Accordion>
                    );
                } else {
                    log.error("Invalid field type %s in fieldsConfig ", item.type);
                    return <></>;
                }

            })}
        </>
    );
};
