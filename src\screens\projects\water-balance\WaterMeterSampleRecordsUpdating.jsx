import { joi<PERSON>esolver } from "@hookform/resolvers/joi";
import "fast-text-encoding";
import <PERSON><PERSON> from "joi";
import debounce from "lodash/debounce";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { useForm, useWatch } from "react-hook-form";
import { Alert, StyleSheet, View } from "react-native";
import { Divider, Snackbar } from "react-native-paper";
import Button from "../../../components/Button";
import ControlledTextInput from "../../../components/ControlledTextInput";
import { DialogToConfirm } from "../../../components/DialogToConfirm";
import HeaderBar from "../../../components/HeaderBar";
import { LoadingIndicator } from "../../../components/LoadingIndicator";
import { creatMMKVStore, makeStoredValueToFormValue } from "../../../services/local-storage";
import { callOneByOne, makeDataFeeder } from "../../../utils";
import { checkPermits } from "../../../utils/permits";
import { isServerDataNewer, makeGetClientCversion, radioIdToObject } from "../../../utils/records";
import { validatorBase } from "../../../utils/validatorBase";
import ScreenWrapper from "../../ScreenWrapper";

// 新组件需要重新!!
import { useShallow } from "zustand/shallow";
import {
    makeReqDelWaterBalanceRecords as recordDeleteQueryMaker,
    makeReqGetWaterBalanceRecords as recordSelectQueryMaker,
    makeReqUpdWaterBalanceRecords as recordUpdateQueryMaker,
} from "../../../api/projectRecordsQueries";
import { FormListNodesMapper } from "../../../components/FormListNodesMapper";
import { UPD_WATER_BALANCE_RECORDS as pageMainKey } from "../../../config/keysConfig";
import { meterPipeLevelEnum } from "../../../config/waterBalance";
import { wbWaterMeterSampleStates as selectorStates } from "../../../hooks/selectorStates";
import { onPreSubmitError } from "../../../utils/screens";
import { MS_A_DAY } from "../../../utils/time";


const dataFeeder = makeDataFeeder();

/**
 * 水表原始数据记录
 * 注意, 项目数据表单与工作台管理表单的数据有一个显著的区别:
 * 项目数据不需要像用户编号那样脱敏, 两边传输的数据是直接的, 不需要做{id, name}这种映射, 部分需要映射的可以在客户端本地完成,
 * @param {object} args
 * @param {object} args.navigation
 * @param {object} args.route
 * @returns
 */
const RecordsUpdating = ({ navigation, route }) => {
    console.log("WaterMeterSampleRecordsUpdating pageMeta from nav:", route.params.pageMeta);
    console.log("WaterMeterSampleRecordsUpdating projMeta from nav:", route.params.projMeta);

    const { queryKwd, id: recordPubid, name: recordName, formType } = route.params.pageMeta; // recordPubid is the alias of id
    const saveButtonIcon = "content-save-all-outline";
    //const formDisabledGlobal = checkPermits() ? false : true;
    const formEditableGlobal = checkPermits() ? (formType === 0 ? true : false) : false;

    // 使用memoize来优化
    const { setStore, getStore, clearStore, setStoreObject } = useMemo(() => {
        console.log("creatMMKVStore memo:", pageMainKey.store, queryKwd, recordPubid);
        return creatMMKVStore(`${pageMainKey.store}/${queryKwd}/${recordPubid}`);
    }, [queryKwd, recordPubid]);

    const storedValueToFormValue = useRef(makeStoredValueToFormValue(getStore));
    const getClientCversion = useRef(makeGetClientCversion(() => getStore("mainCversion"), () => getStore("subCversion")));

    // 客户端cversion = `${mainCversion}.${subCversion}`
    (getStore("mainCversion") === undefined) ? setStore("mainCversion", 0) : undefined; // 客户端主版本号
    (getStore("subCversion")  === undefined) ? setStore("subCversion",  0) : undefined; // 客户端次版本号, 修改一次自增一次
    //let mainCversion = getStore("mainCversion"); // 主版本号, 与服务端cversion对应
    //let subCversion  = getStore("subCversion");  // 次版本号, 表示本地更改次数, 用于程序上对子版本号的增加, 用于判断与服务端版本的差异, 同时可用于判断避免重复从MMKV获取数据
    const subCversionRef = useRef(getStore("subCversion"));

    // 新组件需要修改!!
    const bottomBarLabels = { ok: "提交" };                    // 底部按钮显示文字
    const [snackBarMessage, setSnackBarMessage] = useState("更新表单遇到错误"); // 下方错误提示文字内容
    const [
        pipeLevelState,
        startDatePickerState,
        datePicker1State,
        datePicker2State,
        datePicker3State,
        datePicker4State,
        datePicker5State,
        datePicker6State,
        datePicker7State,
        setPipeLevelState,
        setStartDatePickerState,
        setDatePicker1State,
        setDatePicker2State,
        setDatePicker3State,
        setDatePicker4State,
        setDatePicker5State,
        setDatePicker6State,
        setDatePicker7State,
        resetSelectorStates
    ] = selectorStates(useShallow(state => [
        state.pipeLevel,
        state.startDatePicker,
        state.datePicker1,
        state.datePicker2,
        state.datePicker3,
        state.datePicker4,
        state.datePicker5,
        state.datePicker6,
        state.datePicker7,
        state.setPipeLevel,
        state.setStartDatePicker,
        state.setDatePicker1,
        state.setDatePicker2,
        state.setDatePicker3,
        state.setDatePicker4,
        state.setDatePicker5,
        state.setDatePicker6,
        state.setDatePicker7,
        state.resetStates,
    ])); // radio组件状态

    const [confimDialogConfig, setConfirmDialogConfig] = useState({});       // 对话框信息, {title, text, okLabel, onOK}, 但显示对话框的变量在showConfirm

    // 新组件不需改动
    const [screenTitle,      setScreenTitle]      = useState(route.params.pageMeta.name); // 屏幕上方导航栏标题
    const [okButtonLoading,  setOkButtonLoading]  = useState(false);            // 右下方按钮图标是否旋转
    const [okButtonDisabled, setOkButtonDisabled] = useState(!checkPermits());  // 右下方按钮是否禁用
    const [showSnackbar,     setShowSnackbar]     = useState(false);            // 下方是否显示错误通知
    const [showConfirm,      setShowConfirm]      = useState(false);            // 是否显示确认对话框
    const [loadingIndicatorVisible, setLoadingIndicatorVisible] = useState(false); // 是否显示loading
    const delQueryInfo = useRef({ id: recordPubid, name: recordName });         // 用于删除本记录的查询变量

    //const waterTypeStateDataProviderRef     = useRef([{ id: 1, name: "自来水" }, { id: 2, name: "地表水" }, { id: 3, name: "地下水" }, { id: 4, name: "市政中水" }]);
    //const accuracyLevelStateDataProviderRef = useRef([{ id: 1, name: "一级" }, { id: 2, name: "二级" }, ]);
    //const equipStateStateDataProviderRef    = useRef([{ id: 1, name: "合格" }, { id: 2, name: "不合格" },]);
    const pipeLevelDataProviderRef = useRef(meterPipeLevelEnum);

    const onRecordSelectSuccess = (data) => { // data: {"id": 3, "leader": 0, "name": "DEPT-03", "superior": 0}
        // 注意, 进入onSuccess之前就意味着服务端版本比客户端版本新, 因此要更新客户端主版本号
        console.log("onRecordSelectSuccess:", data);
        if (data.STATUS === 0) {
            setStore("mainCversion", data.DATA.cversion);
            setStore("subCversion", 0);
            const formObject  = {
                name:          String(data.DATA.name),
                waterMeterId:  String(data.DATA.waterMeterId),
                pipeLevel:     String(data.DATA.pipeLevel),
                place:         String(data.DATA.place),
                diameter:      String(data.DATA.diameter),
                startDate:     String(data.DATA.startDate),
                sampleValInit: String(data.DATA.sampleValInit),
                sampleDate1:   String(data.DATA.sampleDate1),
                sampleVal1:    String(data.DATA.sampleVal1),
                consume1:      String(data.DATA.consume1),
                sampleDate2:   String(data.DATA.sampleDate2),
                sampleVal2:    String(data.DATA.sampleVal2),
                consume2:      String(data.DATA.consume2),
                sampleDate3:   String(data.DATA.sampleDate3),
                sampleVal3:    String(data.DATA.sampleVal3),
                consume3:      String(data.DATA.consume3),
                sampleDate4:   String(data.DATA.sampleDate4),
                sampleVal4:    String(data.DATA.sampleVal4),
                consume4:      String(data.DATA.consume4),
                sampleDate5:   String(data.DATA.sampleDate5),
                sampleVal5:    String(data.DATA.sampleVal5),
                consume5:      String(data.DATA.consume5),
                sampleDate6:   String(data.DATA.sampleDate6),
                sampleVal6:    String(data.DATA.sampleVal6),
                consume6:      String(data.DATA.consume6),
                sampleDate7:   String(data.DATA.sampleDate7),
                sampleVal7:    String(data.DATA.sampleVal7),
                consume7:      String(data.DATA.consume7),
                totalConsume:  String(data.DATA.totalConsume),
                dailyConsume:  String(data.DATA.dailyConsume),
                others:        String(data.DATA.others),
                remarks:       String(data.DATA.remarks),
            };
            reset(formObject);           // 重置react-form

            const storeObjects = checkPermits() && {
                ...formObject,
                pipeLevel:       radioIdToObject(pipeLevelDataProviderRef.current,     data.DATA.pipeLevel),
                //waterType:     radioIdToObject(waterTypeStateDataProviderRef.current,     data.DATA.waterType),
                //accuracyLevel: radioIdToObject(accuracyLevelStateDataProviderRef.current, data.DATA.accuracyLevel),
                //equipState:    radioIdToObject(equipStateStateDataProviderRef.current,    data.DATA.equipState),
            };
            storeObjects && setStoreObject(storeObjects, false); // 判断用户权限, 把服务端数据写到mmkv存储, 只有可写用户才需要本地存储, 不会首先清空老数据

            // 设置selector菜单状态
            setPipeLevelState(radioIdToObject(pipeLevelDataProviderRef.current, data.DATA.pipeLevel));
            setStartDatePickerState(formObject.startDate);
            setDatePicker1State(formObject.sampleDate1);
            setDatePicker2State(formObject.sampleDate2);
            setDatePicker3State(formObject.sampleDate3);
            setDatePicker4State(formObject.sampleDate4);
            setDatePicker5State(formObject.sampleDate5);
            setDatePicker6State(formObject.sampleDate6);
            setDatePicker7State(formObject.sampleDate7);

            // 设置屏幕标题
            (screenTitle !== formObject.name) && setScreenTitle(formObject.name);
        } else  {
            console.log("STATUS !== 0", data.STATUS);
            setSnackBarMessage(`表单数据拉取发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        }
    };
    const onRecordSelectError = (err) => {
        console.log("Record selecting error:", err);
    };
    const onRecordSelectSettled = (err) => {
        loadingIndicatorVisible && setLoadingIndicatorVisible(false);
    };
    const recordSelectQuery = recordSelectQueryMaker([queryKwd, "get", recordPubid], onRecordSelectSuccess, onRecordSelectError, onRecordSelectSettled);

    // 新组件需要重新定义!!
    const schema = Joi.object({
        name:          validatorBase.waterBalance.waterMeterSample.name,
        pipeLevel:     validatorBase.waterBalance.waterMeterSample.pipeLevel,
        waterMeterId:  validatorBase.waterBalance.waterMeterSample.textField,
        place:         validatorBase.waterBalance.waterMeterSample.textField,
        diameter:      validatorBase.waterBalance.waterMeterSample.intField,
        startDate:     validatorBase.waterBalance.waterMeterSample.dateField,
        sampleValInit: validatorBase.waterBalance.waterMeterSample.floatFieldUnrequired,
        sampleDate1:   validatorBase.waterBalance.waterMeterSample.dateField,
        sampleVal1:    validatorBase.waterBalance.waterMeterSample.floatFieldUnrequired,
        consume1:      validatorBase.waterBalance.waterMeterSample.floatFieldUnrequired,
        sampleDate2:   validatorBase.waterBalance.waterMeterSample.dateField,
        sampleVal2:    validatorBase.waterBalance.waterMeterSample.floatFieldUnrequired,
        consume2:      validatorBase.waterBalance.waterMeterSample.floatFieldUnrequired,
        sampleDate3:   validatorBase.waterBalance.waterMeterSample.dateField,
        sampleVal3:    validatorBase.waterBalance.waterMeterSample.floatFieldUnrequired,
        consume3:      validatorBase.waterBalance.waterMeterSample.floatFieldUnrequired,
        sampleDate4:   validatorBase.waterBalance.waterMeterSample.dateField,
        sampleVal4:    validatorBase.waterBalance.waterMeterSample.floatFieldUnrequired,
        consume4:      validatorBase.waterBalance.waterMeterSample.floatFieldUnrequired,
        sampleDate5:   validatorBase.waterBalance.waterMeterSample.dateField,
        sampleVal5:    validatorBase.waterBalance.waterMeterSample.floatFieldUnrequired,
        consume5:      validatorBase.waterBalance.waterMeterSample.floatFieldUnrequired,
        sampleDate6:   validatorBase.waterBalance.waterMeterSample.dateField,
        sampleVal6:    validatorBase.waterBalance.waterMeterSample.floatFieldUnrequired,
        consume6:      validatorBase.waterBalance.waterMeterSample.floatFieldUnrequired,
        sampleDate7:   validatorBase.waterBalance.waterMeterSample.dateField,
        sampleVal7:    validatorBase.waterBalance.waterMeterSample.floatFieldUnrequired,
        consume7:      validatorBase.waterBalance.waterMeterSample.floatFieldUnrequired,
        totalConsume:  validatorBase.waterBalance.waterMeterSample.floatFieldUnrequired,
        dailyConsume:  validatorBase.waterBalance.waterMeterSample.floatFieldUnrequired,
        others:        validatorBase.waterBalance.comWaterMeterEquip.textField,
        remarks:       validatorBase.waterBalance.comWaterMeterEquip.longTextField,
    });

    // 新组件需要重新定义defaultValues!!
    const {
        handleSubmit,
        control,
        resetField,
        reset,
        formState: { errors },
        setValue,
        //getValues,
    } = useForm({
        resolver: joiResolver(schema),
        //reValidateMode: "onBlur", // https://github.com/orgs/react-hook-form/discussions/10327
        defaultValues: { // 默认值在组件载入时会被更新, 这里的初始值正常情况下没有用处
            name:          "",
            pipeLevel:     "",
            waterMeterId:  "",
            place:         "",
            diameter:      "",
            startDate:     "",
            sampleValInit: "",
            sampleDate1:   "",
            sampleVal1:    "",
            consume1:      "",
            sampleDate2:   "",
            sampleVal2:    "",
            consume2:      "",
            sampleDate3:   "",
            sampleVal3:    "",
            consume3:      "",
            sampleDate4:   "",
            sampleVal4:    "",
            consume4:      "",
            sampleDate5:   "",
            sampleVal5:    "",
            consume5:      "",
            sampleDate6:   "",
            sampleVal6:    "",
            consume6:      "",
            sampleDate7:   "",
            sampleVal7:    "",
            consume7:      "",
            totalConsume:  "",
            dailyConsume:  "",
            others:        "",
            remarks:       "",
        },
    });

    const sampleVal0 = Number(useWatch({ control, name: "sampleValInit" }));
    const sampleVal1 = Number(useWatch({ control, name: "sampleVal1" }));
    const sampleVal2 = Number(useWatch({ control, name: "sampleVal2" }));
    const sampleVal3 = Number(useWatch({ control, name: "sampleVal3" }));
    const sampleVal4 = Number(useWatch({ control, name: "sampleVal4" }));
    const sampleVal5 = Number(useWatch({ control, name: "sampleVal5" }));
    const sampleVal6 = Number(useWatch({ control, name: "sampleVal6" }));
    const sampleVal7 = Number(useWatch({ control, name: "sampleVal7" }));
    // https://stackoverflow.com/questions/76501023/alternate-to-react-hook-form-usewatch
    const updateGlobalState = debounce((val0, val1, val2, val3, val4, val5, val6, val7) => {
        const res1 = `${((val1 - val0) * 1.0).toFixed(3)}`;
        const res2 = `${((val2 - val1) * 1.0).toFixed(3)}`;
        const res3 = `${((val3 - val2) * 1.0).toFixed(3)}`;
        const res4 = `${((val4 - val3) * 1.0).toFixed(3)}`;
        const res5 = `${((val5 - val4) * 1.0).toFixed(3)}`;
        const res6 = `${((val6 - val5) * 1.0).toFixed(3)}`;
        const res7 = `${((val7 - val6) * 1.0).toFixed(3)}`;
        const summary = `${((val7 - val0) * 1.0).toFixed(3)}`;
        const average = `${((val7 - val0) / 7.0).toFixed(3)}`;
        setValue("consume1", res1);
        setValue("consume2", res2);
        setValue("consume3", res3);
        setValue("consume4", res4);
        setValue("consume5", res5);
        setValue("consume6", res6);
        setValue("consume7", res7);
        setValue("totalConsume", summary);
        setValue("dailyConsume", average);
        setStore("consume1", res1);
        setStore("consume2", res2);
        setStore("consume3", res3);
        setStore("consume4", res4);
        setStore("consume5", res5);
        setStore("consume6", res6);
        setStore("consume7", res7);
        setStore("totalConsume", summary);
        setStore("dailyConsume", average);
        //subCversion++;
        subCversionRef.current++;
        setStore("subCversion", subCversionRef.current);
    }, 100);
    useEffect(() => {
        updateGlobalState(sampleVal0, sampleVal1, sampleVal2, sampleVal3, sampleVal4, sampleVal5, sampleVal6, sampleVal7);
    }, [sampleVal0, sampleVal1, sampleVal2, sampleVal3, sampleVal4, sampleVal5, sampleVal6, sampleVal7]);

    const startDate = Number(useWatch({ control, name: "startDate" }));
    const fillSevenDays = debounce((msDay0) => {
        const msDay1 = msDay0 + MS_A_DAY * 1; setDatePicker1State(msDay1); setValue("sampleDate1", msDay1); setStore("sampleDate1", msDay1);
        const msDay2 = msDay0 + MS_A_DAY * 2; setDatePicker2State(msDay2); setValue("sampleDate2", msDay2); setStore("sampleDate2", msDay2);
        const msDay3 = msDay0 + MS_A_DAY * 3; setDatePicker3State(msDay3); setValue("sampleDate3", msDay3); setStore("sampleDate3", msDay3);
        const msDay4 = msDay0 + MS_A_DAY * 4; setDatePicker4State(msDay4); setValue("sampleDate4", msDay4); setStore("sampleDate4", msDay4);
        const msDay5 = msDay0 + MS_A_DAY * 5; setDatePicker5State(msDay5); setValue("sampleDate5", msDay5); setStore("sampleDate5", msDay5);
        const msDay6 = msDay0 + MS_A_DAY * 6; setDatePicker6State(msDay6); setValue("sampleDate6", msDay6); setStore("sampleDate6", msDay6);
        const msDay7 = msDay0 + MS_A_DAY * 7; setDatePicker7State(msDay7); setValue("sampleDate7", msDay7); setStore("sampleDate7", msDay7);
        subCversionRef.current++;
        setStore("subCversion", subCversionRef.current);
    }, 10);
    useEffect(() => {
        fillSevenDays(startDate);
    }, [startDate]);

    // 新组件需要重新定义setDiagText!!
    const onHandleSubmit = (data) => {
        setOkButtonDisabled(true);
        setOkButtonLoading(true);
        dataFeeder({
            ...data,
            cversion: getClientCversion.current(),
            //name: `${data.place}/${data.waterMeterId}`, // gen in the server
        }); // append client cversion
        recordUpdateQuery.mutate();
    };

    // 新组件不需改动
    const onRecordUpdateSuccess = (data) => {
        console.log("response success, data: ", data);
        if(data.STATUS !== 0) {
            setSnackBarMessage(`表单更新发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        } else {
            // 设置确认框数据
            setConfirmDialogConfig({
                title: "更新表单",
                text: "表单信息更新成功!",
                okLabel: "确定",
                onOK: () => {
                    setShowConfirm(false);
                    recordSelectQuery.mutate();
                }});
            setShowConfirm(true);
        }
    };
    const onRecordUpdateError = (error) => {
        setSnackBarMessage(`表单更新发生错误, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const onRecordUpdateSettled = (data, error) => {
        setOkButtonDisabled(false);
        setOkButtonLoading(false);
    };
    const recordUpdateQuery = recordUpdateQueryMaker(dataFeeder, [queryKwd, "upd", recordPubid], onRecordUpdateSuccess, onRecordUpdateError, onRecordUpdateSettled);

    const recordDeleteOnSuccess = () => {
        clearStore();
        navigation.goBack();
    };
    const recordDeleteOnError = (error) => {
        setSnackBarMessage(`表单删除失败, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const recordDeleteQuery = recordDeleteQueryMaker([queryKwd, "del", recordPubid], recordDeleteOnSuccess, recordDeleteOnError);

    const deleteAlert = () =>{
        return (
            Alert.alert("删除表单", `删除后不可撤销, 请确认是否删除: ${delQueryInfo.current?.name}?`, [
                { text: "取消" },
                { text: "确定", onPress: () => recordDeleteQuery.mutate() },
            ])
        );
    };

    const clearCache = () => {
        return (
            Alert.alert("清空本地缓存", "1. 用于未知原因导致数据无法提交的情况\r\n2. 未提交的内容将会丢失, 请做好数据截图备份\r\n3. 已提交到服务端的数据不受影响\r\n4. 确认清空请点击“确认”按钮", [
                { text: "取消" },
                { text: "确定", onPress: () => { clearStore(); navigation.goBack(); } },
            ])
        );
    };

    useEffect(() => {
        // 优化方案: 根据用户权限进行mutate, 只有可写用户才需要本地存储
        const localCversion = getClientCversion.current();
        const remoteCversion = route.params.pageMeta.cversion;
        console.log("localCversion:", localCversion, "remoteSversion:", remoteCversion);

        const restoreLocalData = () => {
            const formObject = { // 默认值用户表单的数据显示
                name:           storedValueToFormValue.current("name"),

                waterMeterId:  storedValueToFormValue.current("waterMeterId"),
                pipeLevel:     storedValueToFormValue.current("pipeLevel"),
                place:         storedValueToFormValue.current("place"),
                diameter:      storedValueToFormValue.current("diameter"),
                startDate:     storedValueToFormValue.current("startDate"),
                sampleValInit: storedValueToFormValue.current("sampleValInit"),
                sampleDate1:   storedValueToFormValue.current("sampleDate1"),
                sampleVal1:    storedValueToFormValue.current("sampleVal1"),
                consume1:      storedValueToFormValue.current("consume1"),
                sampleDate2:   storedValueToFormValue.current("sampleDate2"),
                sampleVal2:    storedValueToFormValue.current("sampleVal2"),
                consume2:      storedValueToFormValue.current("consume2"),
                sampleDate3:   storedValueToFormValue.current("sampleDate3"),
                sampleVal3:    storedValueToFormValue.current("sampleVal3"),
                consume3:      storedValueToFormValue.current("consume3"),
                sampleDate4:   storedValueToFormValue.current("sampleDate4"),
                sampleVal4:    storedValueToFormValue.current("sampleVal4"),
                consume4:      storedValueToFormValue.current("consume4"),
                sampleDate5:   storedValueToFormValue.current("sampleDate5"),
                sampleVal5:    storedValueToFormValue.current("sampleVal5"),
                consume5:      storedValueToFormValue.current("consume5"),
                sampleDate6:   storedValueToFormValue.current("sampleDate6"),
                sampleVal6:    storedValueToFormValue.current("sampleVal6"),
                consume6:      storedValueToFormValue.current("consume6"),
                sampleDate7:   storedValueToFormValue.current("sampleDate7"),
                sampleVal7:    storedValueToFormValue.current("sampleVal7"),
                consume7:      storedValueToFormValue.current("consume7"),
                totalConsume:  storedValueToFormValue.current("totalConsume"),
                dailyConsume:  storedValueToFormValue.current("dailyConsume"),
                others:        storedValueToFormValue.current("others"),
                remarks:       storedValueToFormValue.current("remarks"),
            };
            reset(formObject); // 重置react-form数据

            // 设置selector数据
            const defaultPipeLevel = 0;
            setPipeLevelState(getStore("pipeLevel") || radioIdToObject(pipeLevelDataProviderRef.current, defaultPipeLevel));
            setStartDatePickerState(formObject.startDate);
            setDatePicker1State(formObject.sampleDate1);
            setDatePicker2State(formObject.sampleDate2);
            setDatePicker3State(formObject.sampleDate3);
            setDatePicker4State(formObject.sampleDate4);
            setDatePicker5State(formObject.sampleDate5);
            setDatePicker6State(formObject.sampleDate6);
            setDatePicker7State(formObject.sampleDate7);
        };

        if (localCversion === 0 && remoteCversion > 0) {
            console.log("Local cversion === 0, serve may have something nontrivial initialized, query mutate!");
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        } else if (isServerDataNewer(localCversion, remoteCversion) && subCversionRef.current > 0) { // 服务端有更新数据, 但本地有修改
            console.log("Client cversion fell behind the server cversion, someone might modified the database, choose to use the data version!");
            Alert.alert("注意", "由于其他人向服务器提交了数据, 服务端数据比本地数据新, 请选择使用哪份数据?", [
                { text: "使用本地数据", onPress: () => { setStore("mainCversion", remoteCversion); restoreLocalData(); } },
                { text: "使用服务端数据", onPress: () => { setLoadingIndicatorVisible(true); recordSelectQuery.mutate(); } },
            ]);
        } else if (isServerDataNewer(localCversion, remoteCversion)) { // localCversion==0时要mutate是因为服务端可能在创建记录时生成了必要数据
            console.log("Server cversion may be newer, query mutate:", localCversion, remoteCversion);
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        }
        else {
            console.log("Local cversion >= server cversion, query will not mutate!");
            restoreLocalData();
        }
    }, []);


    // congif for Inputs mapping, type: "PLAIN", "RADIO", "CHECK", "TIME", "DATE", "RANGE"
    // remarks不需配置
    const FieldsConfig_1 = [
        {
            inputs: [
                //{ name: "name",           label: "表单名称",      unit: "",   type: "PLAIN", editable: true, multiline: true, },
                { name: "waterMeterId",   label: "水表编号",      unit: "",   type: "PLAIN", editable: true, },
                { name: "pipeLevel",      label: "水表级别",      unit: "",   type: "RADIO", editable: true, selectorState: pipeLevelState,      setSelectorState: setPipeLevelState,       dataProvider: meterPipeLevelEnum },
                { name: "place",          label: "所在位置",      unit: "",   type: "PLAIN", editable: true, },
                { name: "diameter",       label: "口径",         unit: "mm", type: "PLAIN", editable: true, },
                { name: "startDate",      label: "起始日期",      unit: "",   type: "DATE",  editable: true, selectorState: startDatePickerState, setSelectorState: setStartDatePickerState, },
                { name: "sampleValInit",  label: "水表读数(起始)", unit: "",   type: "PLAIN", editable: true, },
            ],
        }
    ];
    const FieldsConfig_2 = [
        { nodeType: "Section", title: "测试第1天", nodes: [
            {
                inputs: [
                    { name: "sampleDate1", label: "日期",      unit: "",   type: "DATE",  editable: false, selectorState: datePicker1State,     setSelectorState: setDatePicker1State, },
                    { name: "sampleVal1",  label: "水表读数",   unit: "",   type: "PLAIN", editable: true, },
                    { name: "consume1",    label: "本日用水量", unit: "m³",  type: "PLAIN", editable: false,},
                ]
            },
        ]},
        { nodeType: "Section", title: "测试第2天", nodes: [
            {
                inputs: [
                    { name: "sampleDate2", label: "日期",      unit: "",   type: "DATE",  editable: false, selectorState: datePicker2State,     setSelectorState: setDatePicker2State, },
                    { name: "sampleVal2",  label: "水表读数",   unit: "",   type: "PLAIN", editable: true, },
                    { name: "consume2",    label: "本日用水量", unit: "m³",  type: "PLAIN", editable: false,},
                ]
            },
        ]},
        { nodeType: "Section", title: "测试第3天", nodes: [
            {
                inputs: [
                    { name: "sampleDate3", label: "日期",      unit: "",   type: "DATE",  editable: false, selectorState: datePicker3State,     setSelectorState: setDatePicker3State, },
                    { name: "sampleVal3",  label: "水表读数",   unit: "",   type: "PLAIN", editable: true, },
                    { name: "consume3",    label: "本日用水量", unit: "m³",  type: "PLAIN", editable: false,},
                ]
            },
        ]},
        { nodeType: "Section", title: "测试第4天", nodes: [
            {
                inputs: [
                    { name: "sampleDate4", label: "日期",      unit: "",   type: "DATE",  editable: false, selectorState: datePicker4State,     setSelectorState: setDatePicker4State, },
                    { name: "sampleVal4",  label: "水表读数",   unit: "",   type: "PLAIN", editable: true, },
                    { name: "consume4",    label: "本日用水量", unit: "m³",  type: "PLAIN", editable: false,},
                ]
            },
        ]},
        { nodeType: "Section", title: "测试第5天", nodes: [
            {
                inputs: [
                    { name: "sampleDate5", label: "日期",      unit: "",   type: "DATE",  editable: false, selectorState: datePicker5State,     setSelectorState: setDatePicker5State, },
                    { name: "sampleVal5",  label: "水表读数",   unit: "",   type: "PLAIN", editable: true, },
                    { name: "consume5",    label: "本日用水量", unit: "m³",  type: "PLAIN", editable: false,},
                ]
            },
        ]},
        { nodeType: "Section", title: "测试第6天", nodes: [
            {
                inputs: [
                    { name: "sampleDate6", label: "日期",      unit: "",   type: "DATE",  editable: false, selectorState: datePicker6State,     setSelectorState: setDatePicker6State, },
                    { name: "sampleVal6",  label: "水表读数",   unit: "",   type: "PLAIN", editable: true, },
                    { name: "consume6",    label: "本日用水量", unit: "m³",  type: "PLAIN", editable: false,},
                ]
            },
        ]},
        { nodeType: "Section", title: "测试第7天", nodes: [
            {
                inputs: [
                    { name: "sampleDate7", label: "日期",      unit: "",   type: "DATE",  editable: false, selectorState: datePicker7State,     setSelectorState: setDatePicker7State, },
                    { name: "sampleVal7",  label: "水表读数",   unit: "",   type: "PLAIN", editable: true, },
                    { name: "consume7",    label: "本日用水量", unit: "m³",  type: "PLAIN", editable: false,},
                ]
            },
        ]},

    ];
    const FieldsConfig_3 = [
        {
            inputs: [
                { name: "totalConsume",   label: "合计水量",      unit: "m³",  type: "PLAIN", editable: false,},
                { name: "dailyConsume",   label: "日均水量",      unit: "m³/d", type: "PLAIN", editable: false,},
                { name: "others",         label: "其它",         unit: "",      type: "PLAIN", editable: true, },
            ]
        },
    ];
    const FieldsConfig = [...FieldsConfig_1, ...FieldsConfig_2, ...FieldsConfig_3];


    return (
        <View style={{ flex: 1 }}>

            <HeaderBar
                //title={screenTitle}
                navigation={navigation}
                goBackCallback={() => {
                    callOneByOne(
                        //() => { _mainCversionGetter=undefined; _subCversionGetter=undefined;},
                        resetSelectorStates);
                    /*callOneByOne(clearStore, resetSelectorStates)*/  // 返回前清空mmkv存储因为临时数据不再需要, 重置状态使得页面切换后不会使用前面的数据
                }}
                menuItemArray={[{ title: "删除表单", action: deleteAlert }, { title: "清空缓存", action: clearCache }]}
            />

            <ScreenWrapper contentContainerStyle={styles.container}>

                <View style={styles.formEntry}>

                    <FormListNodesMapper
                        fieldsConfig={FieldsConfig}
                        subCversionRef={subCversionRef}
                        control={control}
                        setStore={setStore}
                        resetField={resetField}
                        errors={errors}
                        setSnackBarMessage={setSnackBarMessage}
                        setShowSnackbar={setShowSnackbar}
                        editable={formEditableGlobal}
                        rowStyle={styles.rowContainer}
                    />

                    {formType === 0 && <ControlledTextInput
                        name="remarks"
                        rowLabel="备注"
                        control={control}
                        placeholder=""
                        onChangeText={(text) => {
                            subCversionRef.current++;
                            setStore("subCversion", subCversionRef.current);
                            setStore("remarks", text);
                        }}
                        onClearText={() => {
                            subCversionRef.current++;
                            setStore("subCversion", subCversionRef.current);
                            setStore("remarks", "");
                            resetField("remarks", { defaultValue: "" });
                        }}
                        editable={formEditableGlobal}
                        mode="flat"
                        multiline={true}
                        formError={errors}
                        style={styles.rowContainerLongText}
                        //disabled={formDisabledGlobal}
                        required={false}
                    />}

                    <Divider bold={true}/>

                </View>

                <View style={styles.button}>
                    <Button mode="contained"
                        disabled={okButtonDisabled}
                        loading={okButtonLoading}
                        icon={saveButtonIcon}
                        onPress={handleSubmit(onHandleSubmit, (formErr) => onPreSubmitError(formErr, getStore, "WaterMeterSampleRecordsUpdating"))}
                    >{bottomBarLabels.ok}</Button>
                </View>
            </ScreenWrapper>

            <LoadingIndicator
                title={""}
                message={"加载中....."}
                visible={loadingIndicatorVisible}
                onClose={()=>setLoadingIndicatorVisible(false)}
            />

            <Snackbar
                style={styles.snackBar}
                visible={showSnackbar}
                onDismiss={() => setShowSnackbar(false)}
                onIconPress={() => setShowSnackbar(false)}
                duration={Snackbar.DURATION_LONG}
            >
                {snackBarMessage}
            </Snackbar>

            <DialogToConfirm
                visible={showConfirm}
                title={confimDialogConfig.title ? confimDialogConfig.title : ""}
                text={confimDialogConfig.text ? confimDialogConfig.text : ""}
                onOK={confimDialogConfig.onOK ? confimDialogConfig.onOK : () => setShowConfirm(false)}
                okBtnLabel={confimDialogConfig.okLabel ? confimDialogConfig.okLabel : "确认"}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    rowContainer:{
        firstColumn: {
            flex: 1,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 2,
        // /borderWidth: 1,
        // height: 40,
        },
    },
    rowContainerLongText: {
        firstColumn: {
            flex: 1,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 4,
            // /borderWidth: 1,
            // height: 40,
        },
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        width: "80%",
        alignSelf: "center",
    },
    formEntry: {
        margin: 8,
    },
    snackBar: {
        flexDirection: "column",
    },
});

export default RecordsUpdating;
