import { isEmptyObject } from ".";
import { utc1900Format } from "./time";

/**
 * 在mmkv上, radio数据存储为一个对象{id, name}, 比如水平衡的对象: {"id": 3, "name": "地下水"}
 * 本函数将id转换成这个对象, 用于将服务器传过来的数据转换成radio组件能够显示的数据.
 * 注意!!! find方法的比较函数中使用==而不是===, 因为id可能是普通字符串, 可能是整数, 也可能是整数字符串, 而===不能比较整数与整数字符串的情况.
 * 此错误可能由服务端逻辑导致, 其产生原因为: 客户端由react form提交的数据全部都是字符串, 如果在服务端忘记转型为数值, 则表单序列化后会返回字符串结果, 导致本函数判断失败.
 * @param {Array} idNameArray An arrwy of {id, name}.
 * @param {*} id The id to find, should not have to be an integer.
 * @param {object} fallback Default object to return when id is not found.
 * Typically it should be {id: 0, name: ""} as in WaterQuotaInfoRecordsUpdating, .
 * however the default value is set to {} to be compatible with other invokings in the app.
 * @returns
 */
export const radioIdToObject = (idNameArray, id, fallback = {}) => idNameArray.find(item => item.id == id) || fallback;

/**
 * 在mmkv上, check数据存储为一个对象数组[{id, name}], 比如水平衡的对象: [{"id": 3, "name": "地下水"}, ...]
 * 本函数将ids数组转换成这个对象, 用于将服务器传过来的数据转换成check组件能够显示的数据.
 * @param {Object[]} idNameArray
 * @param {int[]} ids
 * @returns
 */
export const checkIdsToObject = (idNameArray, ids) => ids.map(id => idNameArray.find(item => item.id === id) || {id: id, name: "未定义项"}).filter(item => !isEmptyObject(item));

/**
 * 客户端版本号 <= 服务端版本号时都判断为服务端数据是最新的.
 * @param {number} clientVersion
 * @param {int} serverVersion
 * @returns
 */
export const isServerDataNewer = (clientVersion, serverVersion) => (serverVersion > 0) && (clientVersion <= serverVersion);

/**
 * 客户端cversion是一个number, 格式为"主版本.次版本"", 服务端cversion是一个整数, update数据库是自增.
 * @param {int} mainVersion 主版本号, 与服务端cversion对应
 * @param {int} subVersion  次版本号, 表示本地更改次数
 * @returns {number}
 */
export const craftClientCversion = (mainVersion, subVersion) => Number(`${mainVersion}.${subVersion}`);

/**
 * Craft the client cversion from the mmkc store!
 * @returns
 */
export const makeGetClientCversion = (_mainCversionGetter, _subCversionGetter) => {
    return () => {
        if (_mainCversionGetter && _subCversionGetter) {
            return craftClientCversion(_mainCversionGetter(), _subCversionGetter());
        } else {
            throw new Error("CVersion getter not initialized", {
                cause: (_mainCversionGetter ? "" : "mainCversionGetter") + " " + (_subCversionGetter ? "" : "subCversionGetter")
            });
        }
    };
};

/**
 * 根据行业类型、表/书报告类型、用水单元名称、工序或设备名称、开始日期，生成水平衡测试表单名称
 * 目前有两种情况：
 * 1. 服务业表， 命名为：用水单元名称/日期
 * 2. 其它， 命名为：用水单元名称/工序或设备名称
 *
 * @param {int} industryType - 行业类型，1工业，2服务业
 * @param {int} reportType - 报告类型，1表，书
 * @param {string} unitName - 用水单元名称
 * @param {string} subUnitName - 工序或设备名称
 * @param {int} startDate - 开始日期，服务业表的命名规范是用水单元名称/日期
 * @returns {string} 水平衡测试表单名称
 */
export const normalizeWaterUnitRecordsFormName = (industryType, reportType, unitName, subUnitName, startDate) => {
    if(industryType === 2 && reportType === 1) { // 服务业表
        return `${unitName || "?"}/${startDate ? utc1900Format(startDate, "YY.MM.DD") : "?"}`;
    } if(industryType === 2 && reportType === 2) { // 服务业书
        return `${unitName || "?"}/${subUnitName || "?"}/${startDate ? utc1900Format(startDate, "M.D") : "?"}`;
    } else {
        return ((unitName && subUnitName) ? `${unitName}/${subUnitName}` : (unitName || subUnitName || "?")) + (startDate ? `/${utc1900Format(startDate, "YY.MM.DD")}` : "");
    }
};
