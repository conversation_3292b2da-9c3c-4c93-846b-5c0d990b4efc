///
/// HybridAudioRecorderPlayerSpec.cpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON>avy @ Margelo
///

#include "HybridAudioRecorderPlayerSpec.hpp"

namespace margelo::nitro::react_native_audio_recorder_player {

  void HybridAudioRecorderPlayerSpec::loadHybridMethods() {
    // load base methods/properties
    HybridObject::loadHybridMethods();
    // load custom methods/properties
    registerHybrids(this, [](Prototype& prototype) {
      prototype.registerHybridMethod("startRecorder", &HybridAudioRecorderPlayerSpec::startRecorder);
      prototype.registerHybridMethod("pauseRecorder", &HybridAudioRecorderPlayerSpec::pauseRecorder);
      prototype.registerHybridMethod("resumeRecorder", &HybridAudioRecorderPlayerSpec::resumeRecorder);
      prototype.registerHybridMethod("stopRecorder", &HybridAudioRecorderPlayerSpec::stopRecorder);
      prototype.registerHybridMethod("startPlayer", &HybridAudioRecorderPlayerSpec::startPlayer);
      prototype.registerHybridMethod("stopPlayer", &HybridAudioRecorderPlayerSpec::stopPlayer);
      prototype.registerHybridMethod("pausePlayer", &HybridAudioRecorderPlayerSpec::pausePlayer);
      prototype.registerHybridMethod("resumePlayer", &HybridAudioRecorderPlayerSpec::resumePlayer);
      prototype.registerHybridMethod("seekToPlayer", &HybridAudioRecorderPlayerSpec::seekToPlayer);
      prototype.registerHybridMethod("setVolume", &HybridAudioRecorderPlayerSpec::setVolume);
      prototype.registerHybridMethod("setPlaybackSpeed", &HybridAudioRecorderPlayerSpec::setPlaybackSpeed);
      prototype.registerHybridMethod("setSubscriptionDuration", &HybridAudioRecorderPlayerSpec::setSubscriptionDuration);
      prototype.registerHybridMethod("addRecordBackListener", &HybridAudioRecorderPlayerSpec::addRecordBackListener);
      prototype.registerHybridMethod("removeRecordBackListener", &HybridAudioRecorderPlayerSpec::removeRecordBackListener);
      prototype.registerHybridMethod("addPlayBackListener", &HybridAudioRecorderPlayerSpec::addPlayBackListener);
      prototype.registerHybridMethod("removePlayBackListener", &HybridAudioRecorderPlayerSpec::removePlayBackListener);
      prototype.registerHybridMethod("mmss", &HybridAudioRecorderPlayerSpec::mmss);
      prototype.registerHybridMethod("mmssss", &HybridAudioRecorderPlayerSpec::mmssss);
    });
  }

} // namespace margelo::nitro::react_native_audio_recorder_player
