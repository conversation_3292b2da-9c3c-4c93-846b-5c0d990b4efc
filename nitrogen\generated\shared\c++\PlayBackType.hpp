///
/// PlayBackType.hpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc R<PERSON>avy @ Margelo
///

#pragma once

#if __has_include(<NitroModules/JSIConverter.hpp>)
#include <NitroModules/JSIConverter.hpp>
#else
#error NitroModules cannot be found! Are you sure you installed NitroModules properly?
#endif
#if __has_include(<NitroModules/NitroDefines.hpp>)
#include <NitroModules/NitroDefines.hpp>
#else
#error NitroModules cannot be found! Are you sure you installed NitroModules properly?
#endif



#include <optional>

namespace margelo::nitro::react_native_audio_recorder_player {

  /**
   * A struct which can be represented as a JavaScript object (PlayBackType).
   */
  struct PlayBackType {
  public:
    std::optional<bool> isMuted     SWIFT_PRIVATE;
    double duration     SWIFT_PRIVATE;
    double currentPosition     SWIFT_PRIVATE;

  public:
    PlayBackType() = default;
    explicit PlayBackType(std::optional<bool> isMuted, double duration, double currentPosition): isMuted(isMuted), duration(duration), currentPosition(currentPosition) {}
  };

} // namespace margelo::nitro::react_native_audio_recorder_player

namespace margelo::nitro {

  using namespace margelo::nitro::react_native_audio_recorder_player;

  // C++ PlayBackType <> JS PlayBackType (object)
  template <>
  struct JSIConverter<PlayBackType> final {
    static inline PlayBackType fromJSI(jsi::Runtime& runtime, const jsi::Value& arg) {
      jsi::Object obj = arg.asObject(runtime);
      return PlayBackType(
        JSIConverter<std::optional<bool>>::fromJSI(runtime, obj.getProperty(runtime, "isMuted")),
        JSIConverter<double>::fromJSI(runtime, obj.getProperty(runtime, "duration")),
        JSIConverter<double>::fromJSI(runtime, obj.getProperty(runtime, "currentPosition"))
      );
    }
    static inline jsi::Value toJSI(jsi::Runtime& runtime, const PlayBackType& arg) {
      jsi::Object obj(runtime);
      obj.setProperty(runtime, "isMuted", JSIConverter<std::optional<bool>>::toJSI(runtime, arg.isMuted));
      obj.setProperty(runtime, "duration", JSIConverter<double>::toJSI(runtime, arg.duration));
      obj.setProperty(runtime, "currentPosition", JSIConverter<double>::toJSI(runtime, arg.currentPosition));
      return obj;
    }
    static inline bool canConvert(jsi::Runtime& runtime, const jsi::Value& value) {
      if (!value.isObject()) {
        return false;
      }
      jsi::Object obj = value.getObject(runtime);
      if (!JSIConverter<std::optional<bool>>::canConvert(runtime, obj.getProperty(runtime, "isMuted"))) return false;
      if (!JSIConverter<double>::canConvert(runtime, obj.getProperty(runtime, "duration"))) return false;
      if (!JSIConverter<double>::canConvert(runtime, obj.getProperty(runtime, "currentPosition"))) return false;
      return true;
    }
  };

} // namespace margelo::nitro
