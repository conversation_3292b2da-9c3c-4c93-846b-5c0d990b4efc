import { ErrorMessage } from "@hookform/error-message";
import React from "react";
import { Controller } from "react-hook-form";
import { Keyboard, Pressable, StyleSheet, View } from "react-native";
import { DataTable, Text, useTheme } from "react-native-paper";
import { isFunction, isObject } from "../utils";
import ControlledCellTextInput from "./ControlledCellTextInput";
//import PropTypes from "prop-types";

/**
 * DataTable中一个2列的记录行
 * 注意: props中同时包含style和rowStyle的原因是:
 * 在FormListNodesMapper中会有别的样式加入并组合成一个数组后通过style传到本组件, 使得在这里展开后不能覆盖
 * style array: [{"paddingLeft": 40}, {"firstColumn": {"content": [Object], "flex": 1}, "secondColumn": {"flex": 1}}]
 * This array has a padding for List.Accordion with an indent efx and which has been ignored here.
 * @param {Object} arg
 * @param {string} arg.rowLabel
 * @param {function} arg.onClearText
 * @param {boolean} arg.disabled
 * @param {"flat" | "outlined"} arg.mode
 */
const ControlledTextInput = ({ name, rowLabel, control, placeholder, onChangeText, onClearText, editable, multiline, mode, toolTip, style={}, rowStyle={}, ...props }) => {
    const theme = useTheme();
    const styles = StyleSheet.create({
        container: {
            paddingLeft: 4,
            paddingRight: 0,
            marginLeft: 0,
            marginRight: -8,
            //borderWidth: 1,
            //height: 60,
            //borderColor: "black",
        },
        firstColumn: {
            flex: 1,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 2,
            //borderWidth: 1,
            // height: 40,
        },
        inputBox: {
            width: "100%"
        },
        errMsg: {
            fontSize: 16,
            color: theme.colors.error,
        },
        ...(isObject(style) ? style : {}),
        ...rowStyle,
    });

    return (
        <DataTable.Row style={styles.container}>
            <DataTable.Cell style={styles.firstColumn}>
                {toolTip ?
                    <Pressable onPress={()=>{
                        // 隐藏键盘
                        if (typeof Keyboard !== "undefined") {
                            Keyboard.dismiss();
                        }
                        isFunction(props.setSnackbarMessage) && props.setSnackbarMessage(toolTip);
                        isFunction(props.setDisplaySnackbar) && props.setDisplaySnackbar(true);
                    }}>
                        <Text style={styles.firstColumn.content}>
                            {(props.required ? rowLabel + " *" : rowLabel) + " ℹ️"}
                        </Text>
                    </Pressable> :
                    <Pressable onPress={() => {
                        // 隐藏键盘
                        if (typeof Keyboard !== "undefined") {
                            Keyboard.dismiss();
                        }
                    }}>
                        <Text style={styles.firstColumn.content}>
                            {props.required ? rowLabel + " *" : rowLabel}
                        </Text>
                    </Pressable>}
            </DataTable.Cell>
            <DataTable.Cell style={styles.secondColumn}>
                <Controller
                    control={control}
                    render={({ field: { onChange, onBlur, value } }) => (
                        <View style={styles.inputBox}>
                            <ControlledCellTextInput
                                onBlur={onBlur}
                                onChangeText={value => {
                                    onChange(value);     // react-hook-form修改内部状态
                                    onChangeText(value); // 调用mmkv保存当前输入
                                }}
                                value={value}
                                returnKeyType="next"
                                placeholder={placeholder}
                                mode={mode}
                                editable={editable}
                                multiline={multiline}
                                autoCapitalize="none"
                                autoComplete="off"
                                textContentType="none"
                                onClearText={onClearText}
                                {...props}
                            />
                            {props?.formError &&
                                <ErrorMessage
                                    errors={props.formError}
                                    name={name}
                                    render={({ message }) => {
                                        return (
                                            <Text style={styles.errMsg}>
                                                {"⚠ " + message}
                                            </Text>);
                                    }}
                                />
                            }
                        </View>
                    )}
                    name={name}
                    rules={{ required: true }}
                />



            </DataTable.Cell>
        </DataTable.Row>
    );
};


export default ControlledTextInput;
