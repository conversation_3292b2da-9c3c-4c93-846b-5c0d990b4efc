/**
 * 位于./android/app/src/main/java/com/lowcarbonmanagement
 * Orientation.kt, OrientationModule.kt, OrientationPackage.kt
 * 目前只有Android端实现了设备方向监听, iOS端未实现
 * 监听设备方向变化, 通过原生模块实现
 * @description: 设备方向监听
 */

import { NativeEventEmitter, NativeModules } from "react-native";
const { OrientationModule } = NativeModules;

export const orientationEmitter = new NativeEventEmitter(OrientationModule);


/* 用法示例
    // 设备朝向, 包含4种类型: "PORTRAIT", "PORTRAIT_UPSIDEDOWN", "LANDSCAPE_LEFT", "LANDSCAPE_RIGHT"
    const [orientation, setOrientation] = useState("PORTRAIT");

    useEffect(() => {
        const subscription = orientationEmitter.addListener(
            "OrientationChange",
            (newOrientation) => {
                // 规范化朝向值, 设置UNKNOWN为上一次的有效朝向
                setOrientation(newOrientation == "UNKNOWN" ? (orientation === "UNKNOWN" ? "PORTRAIT" : orientation) : newOrientation);
            }
        );

        return () => {
            subscription.remove();
        };
    }, []);
    */
