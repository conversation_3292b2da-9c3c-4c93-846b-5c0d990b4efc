import React from "react";
import ListingTemplate from "./ListingTemplate";
import { makeRequestListingPositions as queryClient } from "../../api/listingQueries";
import { allPositionsState as pageDataState } from "../../hooks/globalStates";


const PositionsListing = ({ navigation }) => {
    return (
        <ListingTemplate
            bottomBarLabel={"添加岗位"}
            emptyScreenText={"请点击下方按钮添加岗位"}
            snackBarDefaultText={"添加岗位遇到错误"}
            saveButtonIcon={"account-plus-outline"}
            queryClient={queryClient}
            pageDataState={pageDataState}
            listItemNavigateTo={"WorkbenchPositionsUpdating"}
            addingButtonNavigateTo={"WorkbenchPositionsInserting"}
            navigation={navigation}
        />
    );
};

export default PositionsListing;
