import { joi<PERSON>esolver } from "@hookform/resolvers/joi";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { Alert, StyleSheet, View } from "react-native";
import { Divider, Snackbar } from "react-native-paper";
//import debounce from "lodash/debounce";
import "fast-text-encoding";
import Jo<PERSON> from "joi";
import Button from "../../../components/Button";
import { DialogToConfirm } from "../../../components/DialogToConfirm";
import HeaderBar from "../../../components/HeaderBar";
import { LoadingIndicator } from "../../../components/LoadingIndicator";
import ScreenWrapper from "../../ScreenWrapper";
//import ControlledTextInput from "../../../components/ControlledTextInput";
import { FormListNodesMapper } from "../../../components/FormListNodesMapper";
import { wbBasicInfoStatesDefinitionStates as fieldsStates } from "../../../hooks/selectorStates";
import { creatMMKVStore, makeStoredValueToFormValue } from "../../../services/local-storage";
import { callOneByOne, makeDataFeeder, parseIndustryCode } from "../../../utils";
import { checkPermits } from "../../../utils/permits";
import { isServerDataNewer, makeGetClientCversion } from "../../../utils/records";
import { validatorBase } from "../../../utils/validatorBase";

// 新组件需要重新!!
import { useShallow } from "zustand/shallow";
import {
    makeReqDelWaterBalanceRecords as recordDeleteQueryMaker,
    makeReqGetWaterBalanceRecords as recordSelectQueryMaker,
    makeReqUpdWaterBalanceRecords as recordUpdateQueryMaker,
} from "../../../api/projectRecordsQueries";
import { DEFAULT_COORDS_PICK_LIMIT } from "../../../config";
import { UPD_WATER_BALANCE_RECORDS as pageMainKey } from "../../../config/keysConfig";
import { onPreSubmitError } from "../../../utils/screens";
//import { waterUnitScaleEnum } from "../../../config/waterBalance";
//import { roundNearest } from "../../../utils/numeric";
//import log from "../../../services/logging";


const dataFeeder = makeDataFeeder();

/**
 * 基本情况表
 * 目前只有表有此需求, 因此在表格类型列表中只有表项目会显示出来.
 * @param {object} args
 * @param {object} args.navigation
 * @param {object} args.route
 * @returns
 */
const RecordsUpdating = ({ navigation, route }) => {
    console.log("RecordsUpdating pageMeta from nav:", route.params.pageMeta);
    console.log("RecordsUpdating projMeta from nav:", route.params.projMeta);
    const projSubclass = route.params.projMeta.subclass; // 1表, 2书
    const projIndustry = parseIndustryCode(route.params.projMeta.industry); // 1 -> industry, 2 -> service

    const PROJ_DESC      = useRef("主要内容包括：占地面积、主要产品、设计产能、产品产量、职工人数、生产制度、节水管理机构、用水节水相关规章制度建设情况等。\n");
    const INTAKE_DESC    = useRef("主要介绍企业取水水源情况，包括常规水资源和非常规水资源。常规水资源为地表水、地下水和集中供水工程供水以及外购的水或水的产品(如蒸汽、软化水等),非常规水资源为淡化海水、苦咸水、再生水、矿井水、集蓄雨水等。 介绍取水口位置、取用水手续(取水许可证、用水计划、供水协议等)、取水口在线计量安装情况。\n");
    const WATER_USE_DESC = useRef("介绍主要生产工艺、技术及用水设备设施，生活用水(厂办公楼、科研楼、厂内食堂、厂内浴室、保健站、绿化、汽车队等)情况，明确是否有国家明令淘汰的工艺、设备、器具。\n1)主要产品及产量：测试期间取水量及主要产品产量。\n2)具体用水情况：测试期间主要生产单元和生活的取、用、耗、排水量情况，如有基建、居民生活及外供用水情况单独说明。\n3)企业内部用水管理平台建设情况。\n");
    const DRAIN_DESC     = useRef("介绍企业排水情况(包括排水口位置、排水量、水质、排水手续等)。\n");
    const IMPROVE_DESC   = useRef("介绍近3年采取的节水措施、节水投资及节水效益等。\n");
    const IMPROVE_PLAN   = useRef("近三年的节水方面的措施、时间、费用、取得效果；根据企业规划未来三年计划节水措施、时间费用、预计效果。\n");

    // formType对应着服务端的form-type, 为0时表示表单是用户填写, 为1表示服务端生成, 为1时只读
    const { queryKwd, id: recordPubid, name: recordName, formType } = route.params.pageMeta; // recordPubid is the alias of id
    const saveButtonIcon = "content-save-all-outline";
    const formEditableGlobal = checkPermits() ? (formType === 0 ? true : false) : false;

    // 使用memoize来优化
    const { setStore, getStore, clearStore, setStoreObject } = useMemo(() => {
        console.log("creatMMKVStore memo:", pageMainKey.store, queryKwd, recordPubid);
        return creatMMKVStore(`${pageMainKey.store}/${queryKwd}/${recordPubid}`);
    }, [queryKwd, recordPubid]);

    const storedValueToFormValue = useRef(makeStoredValueToFormValue(getStore));
    const getClientCversion = useRef(makeGetClientCversion(() => getStore("mainCversion"), () => getStore("subCversion")));

    // 客户端cversion = `${mainCversion}.${subCversion}`
    (getStore("mainCversion") === undefined) ? setStore("mainCversion", 0) : undefined; // 客户端主版本号
    (getStore("subCversion")  === undefined) ? setStore("subCversion",  0) : undefined; // 客户端次版本号, 修改一次自增一次
    //let mainCversion = getStore("mainCversion"); // 主版本号, 与服务端cversion对应
    // 次版本号, 表示本地更改次数, 用于程序上对子版本号的增加, 用于判断与服务端版本的差异, 同时可用于判断避免重复从MMKV获取数据
    const subCversionRef = useRef(getStore("subCversion"));

    const intakePickLimit = DEFAULT_COORDS_PICK_LIMIT; // processChart image pick num

    // 新组件需要修改!!
    const bottomBarLabels = { ok: "提交" };                    // 底部按钮显示文字
    const [snackBarMessage, setSnackBarMessage] = useState("更新表单遇到错误"); // 下方错误提示文字内容

    const [
        intakeCoordsLocationStates,
        setIntakeCoordsLocationStates,
        resetFieldsStates,
    ] = fieldsStates(useShallow(state => [
        state.intakeCoordsLocations,
        state.setIntakeCoordsLocations,
        state.resetStates,
    ])); // radio组件状态

    const [confimDialogConfig, setConfirmDialogConfig] = useState({});       // 对话框信息, {title, text, okLabel, onOK}, 但显示对话框的变量在showConfirm

    // 新组件不需改动
    const [screenTitle,      setScreenTitle]      = useState(route.params.pageMeta.name); // 屏幕上方导航栏标题
    const [okButtonLoading,  setOkButtonLoading]  = useState(false);            // 右下方按钮图标是否旋转
    const [okButtonDisabled, setOkButtonDisabled] = useState(!checkPermits());  // 右下方按钮是否禁用
    const [showSnackbar,     setShowSnackbar]     = useState(false);            // 下方是否显示错误通知
    const [showConfirm,      setShowConfirm]      = useState(false);            // 是否显示确认对话框
    const [loadingIndicatorVisible, setLoadingIndicatorVisible] = useState(false); // 是否显示loading
    const delQueryInfo = useRef({ id: recordPubid, name: recordName });         // 用于删除本记录的查询变量

    //const waterTypeStateDataProviderRef     = useRef([{ id: 1, name: "自来水" }, { id: 2, name: "地表水" }, { id: 3, name: "地下水" }, { id: 4, name: "市政中水" }]);
    //const accuracyLevelStateDataProviderRef = useRef([{ id: 1, name: "一级" }, { id: 2, name: "二级" }, ]);
    //const equipStateStateDataProviderRef    = useRef([{ id: 1, name: "合格" }, { id: 2, name: "不合格" },]);
    //const waterUnitScaleStateDataProviderRef = useRef(waterUnitScaleEnum);

    // 用于存储waterScale的值, 当unitToggleState发生改变时需要通过该值更新表单字段
    //const waterUnitScaleRef = useRef(0);

    // Define form
    // 新组件需要重新定义!!
    const schema = Joi.object({
        name:         validatorBase.waterBalance.commons.name,
        clientName:   validatorBase.waterBalance.commons.name,
        address:      validatorBase.waterBalance.commons.longTextField,
        industryCode: validatorBase.waterBalance.commons.textField,
        industryProp: validatorBase.waterBalance.commons.textField,
        zipCode:      validatorBase.waterBalance.commons.textField,
        estbDate:     validatorBase.waterBalance.commons.textField,
        phone:        validatorBase.waterBalance.commons.textField,
        fax:          validatorBase.waterBalance.commons.textField,
        website:      validatorBase.waterBalance.commons.textField,
        email:        validatorBase.waterBalance.commons.textField,
        lr:           validatorBase.waterBalance.commons.textField,
        jobTitle:     validatorBase.waterBalance.commons.textField,
        bureau:       validatorBase.waterBalance.commons.textField,
        contact:      validatorBase.waterBalance.commons.textField,
        testInst:     validatorBase.waterBalance.commons.textField,
        testStuff:    validatorBase.waterBalance.commons.textField,
        testPeriod:   validatorBase.waterBalance.commons.textField,
        projDesc:     validatorBase.waterBalance.commons.largeTextField,
        intakeDesc:   validatorBase.waterBalance.commons.largeTextField,
        waterUseDesc: validatorBase.waterBalance.commons.largeTextField,
        drainDesc:    validatorBase.waterBalance.commons.largeTextField,
        improveDesc:  validatorBase.waterBalance.commons.largeTextField,
        improvePlan:  validatorBase.waterBalance.commons.largeTextField,
        // 服务业书
        residentNum: validatorBase.waterBalance.commons.intFieldUnrequired,
        flowNum: validatorBase.waterBalance.commons.intFieldUnrequired,
        wsDept: validatorBase.waterBalance.commons.textField,
        wsContact: validatorBase.waterBalance.commons.textField,
        wsLeader: validatorBase.waterBalance.commons.textField,
        wsLeaderTitle: validatorBase.waterBalance.commons.textField,
        wsManager: validatorBase.waterBalance.commons.textField,
        wsManagerTitle: validatorBase.waterBalance.commons.textField,
        // 工业书
        regCapital:   validatorBase.waterBalance.commons.intFieldUnrequired,
        prodTime:     validatorBase.waterBalance.commons.textField,
        industryType: validatorBase.waterBalance.commons.textField,
        prodName:     validatorBase.waterBalance.commons.textField,
        prodOutput:   validatorBase.waterBalance.commons.textField,
        prodCapacity: validatorBase.waterBalance.commons.textField,
        prodRevenue:  validatorBase.waterBalance.commons.textField,
        //revenue:      validatorBase.waterBalance.commons.textField,
        //products:     validatorBase.waterBalance.commons.simpleObject,
        stuffNum:     validatorBase.waterBalance.commons.intFieldUnrequired,
        fluidNum:     validatorBase.waterBalance.commons.intFieldUnrequired,
        landArea:     validatorBase.waterBalance.commons.intFieldUnrequired,
        buildArea:    validatorBase.waterBalance.commons.intFieldUnrequired,
        greenArea:    validatorBase.waterBalance.commons.intFieldUnrequired,
        aopDays:      validatorBase.waterBalance.commons.intFieldUnrequired,
        prodShift:    validatorBase.waterBalance.commons.textField,
        shiftDuraion: validatorBase.waterBalance.commons.textField,
        supervisor:   validatorBase.waterBalance.commons.textField,
        department:   validatorBase.waterBalance.commons.textField,
        // GPS
        comCoords:    validatorBase.waterBalance.commons.locationUnrequired,
        drainCoords:  validatorBase.waterBalance.commons.locationUnrequired,
        intakeCoords: validatorBase.waterBalance.commons.simpleArray,
        // ...
        others:       validatorBase.waterBalance.commons.textField,
        remarks:      validatorBase.waterBalance.commons.longTextField,
    });

    // 新组件需要重新定义defaultValues!!
    const {
        handleSubmit,
        control,
        resetField,
        reset,
        formState: { errors },
        //setValue,
        getValues,
    } = useForm({
        resolver: joiResolver(schema),
        //reValidateMode: "onBlur", // https://github.com/orgs/react-hook-form/discussions/10327
        defaultValues: { // 默认值在组件载入时会被更新, 这里的初始值正常情况下没有用处
            // 注意, 默认为0的是允许默认为0的值, 或者通过计算得到的值
            name:         "",
            clientName:   "",
            address:      "",
            industryCode: "",
            industryProp: "",
            zipCode:      "",
            estbDate:     "",
            phone:        "",
            fax:          "",
            website:      "",
            email:        "",
            lr:           "",
            jobTitle:     "",
            bureau:       "",
            contact:      "",
            testInst:     "",
            testStuff:    "",
            testPeriod:   "",
            projDesc:     "",
            intakeDesc:   "",
            waterUseDesc: "",
            drainDesc:    "",
            improveDesc:  "",
            improvePlan:  "",
            // service book
            residentNum:  "",
            flowNum:  "",
            wsDept:  "",
            wsContact:  "",
            wsLeader:  "",
            wsLeaderTitle:  "",
            wsManager:  "",
            wsManagerTitle:  "",
            // industry book
            regCapital:   "",
            prodTime:     "",
            industryType: "",
            prodName:     "",
            prodOutput:   "",
            prodCapacity: "",
            prodRevenue:  "",
            //revenue:      "",
            //products:     "",
            stuffNum:     "",
            fluidNum:     "",
            landArea:     "",
            buildArea:    "",
            greenArea:    "",
            aopDays:      "",
            prodShift:    "",
            shiftDuraion: "",
            supervisor:   "",
            department:   "",
            // GPS
            comCoords:    "",
            drainCoords:  "",
            intakeCoords: [],
            //...
            others:       "",
            remarks:      "",
        },
    });

    // Query: select record
    const onRecordSelectSuccess = (data) => { // data: {"id": 3, "leader": 0, "name": "DEPT-03", "superior": 0}
        // 注意, 进入onSuccess之前就意味着服务端版本比客户端版本新, 因此要更新客户端主版本号
        if (data.STATUS === 0) {
            setStore("mainCversion", data.DATA.cversion);
            setStore("subCversion", 0);
            console.log("onRecordSelectSuccess:", data);

            const formObject = {
                name:         String(data.DATA.name),
                clientName:   String(data.DATA.clientName),
                address:      String(data.DATA.address),
                industryCode: String(data.DATA.industryCode),
                industryProp: String(data.DATA.industryProp),
                zipCode:      String(data.DATA.zipCode),
                estbDate:     String(data.DATA.estbDate),
                phone:        String(data.DATA.phone),
                fax:          String(data.DATA.fax),
                website:      String(data.DATA.website),
                email:        String(data.DATA.email),
                lr:           String(data.DATA.lr),
                jobTitle:     String(data.DATA.jobTitle),
                bureau:       String(data.DATA.bureau),
                contact:      String(data.DATA.contact),
                testInst:     String(data.DATA.testInst),
                testStuff:    String(data.DATA.testStuff),
                testPeriod:   String(data.DATA.testPeriod),
                projDesc:     String(data.DATA.projDesc),
                intakeDesc:   String(data.DATA.intakeDesc),
                waterUseDesc: String(data.DATA.waterUseDesc),
                drainDesc:    String(data.DATA.drainDesc),
                improveDesc:  String(data.DATA.improveDesc),
                improvePlan:  String(data.DATA.improvePlan),
                // service book
                residentNum:   String(data.DATA.residentNum),
                flowNum:       String(data.DATA.flowNum),
                wsDept:        String(data.DATA.wsDept),
                wsContact:     String(data.DATA.wsContact),
                wsLeader:      String(data.DATA.wsLeader),
                wsLeaderTitle: String(data.DATA.wsLeaderTitle),
                wsManager:     String(data.DATA.wsManager),
                wsManagerTitle:String(data.DATA.wsManagerTitle),
                // industry book
                regCapital:   String(data.DATA.regCapital),
                prodTime:     String(data.DATA.prodTime),
                industryType: String(data.DATA.industryType),
                prodName:     String(data.DATA.prodName),
                prodOutput:   String(data.DATA.prodOutput),
                prodCapacity: String(data.DATA.prodCapacity),
                prodRevenue:  String(data.DATA.prodRevenue),
                //revenue:      String(data.DATA.revenue),
                //products:     data.DATA.products ? JSON.parse(data.DATA.products) : {}, // 对象类型
                stuffNum:     String(data.DATA.stuffNum),
                fluidNum:     String(data.DATA.fluidNum),
                landArea:     String(data.DATA.landArea),
                buildArea:    String(data.DATA.buildArea),
                greenArea:    String(data.DATA.greenArea),
                aopDays:      String(data.DATA.aopDays),
                prodShift:    String(data.DATA.prodShift),
                shiftDuraion: String(data.DATA.shiftDuraion),
                supervisor:   String(data.DATA.supervisor),
                department:   String(data.DATA.department),
                // GPS
                comCoords:    data.DATA.comCoords    || [0, 0],
                drainCoords:  data.DATA.drainCoords  || [0, 0],
                intakeCoords: data.DATA.intakeCoords ? JSON.parse(data.DATA.intakeCoords) : [], // 对象数组
                //...
                others:       String(data.DATA.others),
                remarks:      String(data.DATA.remarks),
            };
            reset(formObject);           // 重置react-form
            console.log("Restore location form server: ", getValues("comCoords"), typeof(getValues("comCoords")));

            const storeObjects = checkPermits() && (formType === 0) && {
                ...formObject,
                //waterType:     radioIdToObject(waterTypeStateDataProviderRef.current,     data.DATA.waterType),
                //accuracyLevel: radioIdToObject(accuracyLevelStateDataProviderRef.current, data.DATA.accuracyLevel),
                //equipState:    radioIdToObject(equipStateStateDataProviderRef.current,    data.DATA.equipState),
            };
            storeObjects && setStoreObject(storeObjects, false); // 判断用户权限, 把服务端数据写到mmkv存储, 只有可写用户才需要本地存储, 不会首先清空老数据

            // 设置selector菜单状态
            setIntakeCoordsLocationStates(formObject.intakeCoords);
            //setWaterUnitScaleState(radioIdToObject(waterUnitScaleStateDataProviderRef.current, data.DATA.waterScale));
            //setStartDatePickerState(formObject.startDate);
            //setEndDatePickerState(formObject.endDate);

            //waterUnitScaleRef.current = data.DATA.waterScale || 0; // 根据服务端数据更新
            // 设置屏幕标题
            (screenTitle !== formObject.name) && setScreenTitle(formObject.name);
        } else {
            console.log("STATUS !== 0", data.STATUS);
            setSnackBarMessage(`表单数据拉取发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        }
    };
    const onRecordSelectError = (err) => {
        console.log("Record selecting error:", err);
    };
    const onRecordSelectSettled = (err) => {
        loadingIndicatorVisible && setLoadingIndicatorVisible(false);
    };
    const getPath = formType === 0 ? "get" : "getsum";
    const recordSelectQuery = recordSelectQueryMaker([queryKwd, getPath, recordPubid], onRecordSelectSuccess, onRecordSelectError, onRecordSelectSettled);

    // Query: update record
    const onHandleSubmit = (data) => {
        setOkButtonDisabled(true);
        setOkButtonLoading(true);
        dataFeeder({ ...data,
            cversion: getClientCversion.current(),
            name:       data.clientName,
            // 对象类型和复合数组必需转换为字符串, 否则发送消息时客户端会报错: Network request failed!
            // 但comCoords这种只含有数值或字符串的数组, 在makeFormData会自动连接, 不需要转换.
            //products:     JSON.stringify(data.products || {}),
            intakeCoords: JSON.stringify(data.intakeCoords),
            // unifify data units
            // ...
        }); // append client cversion
        recordUpdateQuery.mutate();
    };
    // 新组件不需改动
    const onRecordUpdateSuccess = (data) => {
        console.log("Record update success with response data: ", data);
        if(data.STATUS !== 0) {
            setSnackBarMessage(`表单更新发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        } else {
            // 设置确认框数据
            setConfirmDialogConfig({
                title: "更新表单",
                text: "表单信息更新成功!",
                okLabel: "确定",
                onOK: () => {
                    setShowConfirm(false);
                    recordSelectQuery.mutate();
                } });
            setShowConfirm(true);
        }
    };
    const onRecordUpdateError = (error) => {
        setSnackBarMessage(`表单更新发生错误, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const onRecordUpdateSettled = (data, error) => {
        setOkButtonDisabled(false);
        setOkButtonLoading(false);
    };
    const recordUpdateQuery = recordUpdateQueryMaker(dataFeeder, [queryKwd, "upd", recordPubid], onRecordUpdateSuccess, onRecordUpdateError, onRecordUpdateSettled);

    // Query: delete record
    const recordDeleteOnSuccess = () => {
        clearStore();
        navigation.goBack();
    };
    const recordDeleteOnError = (error) => {
        setSnackBarMessage(`表单删除失败, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const recordDeleteQuery = recordDeleteQueryMaker([queryKwd, "del", recordPubid], recordDeleteOnSuccess, recordDeleteOnError);

    const deleteAlert = () => {
        return (
            Alert.alert("删除表单", `删除后不可撤销, 请确认是否删除: ${delQueryInfo.current?.name}?`, [
                { text: "取消" },
                { text: "确定", onPress: () => recordDeleteQuery.mutate() },
            ])
        );
    };

    const clearCache = () => {
        return (
            Alert.alert("清空本地缓存", "1. 用于未知原因导致数据无法提交的情况\r\n2. 未提交的内容将会丢失, 请做好数据截图备份\r\n3. 已提交到服务端的数据不受影响\r\n4. 确认清空请点击“确认”按钮", [
                { text: "取消" },
                { text: "确定", onPress: () => { clearStore(); navigation.goBack(); } },
            ])
        );
    };

    // Hook: onMount fetch data
    useEffect(() => {
        // 优化方案: 根据用户权限进行mutate, 只有可写用户才需要本地存储
        const localCversion = getClientCversion.current();
        const remoteCversion = route.params.pageMeta.cversion;
        console.log("localCversion:", localCversion, "remoteSversion:", remoteCversion, "subCversionRef:", subCversionRef.current);

        const restoreLocalData = () => {
            const formObject = { // 默认值用户表单的数据显示
                name:         storedValueToFormValue.current("name"),
                clientName:   storedValueToFormValue.current("clientName"),
                address:      storedValueToFormValue.current("address"),
                industryCode: storedValueToFormValue.current("industryCode"),
                industryProp: storedValueToFormValue.current("industryProp"),
                zipCode:      storedValueToFormValue.current("zipCode"),
                estbDate:     storedValueToFormValue.current("estbDate"),
                phone:        storedValueToFormValue.current("phone"),
                fax:          storedValueToFormValue.current("fax"),
                website:      storedValueToFormValue.current("website"),
                email:        storedValueToFormValue.current("email"),
                lr:           storedValueToFormValue.current("lr"),
                jobTitle:     storedValueToFormValue.current("jobTitle"),
                bureau:       storedValueToFormValue.current("bureau"),
                contact:      storedValueToFormValue.current("contact"),
                testInst:     storedValueToFormValue.current("testInst"),
                testStuff:    storedValueToFormValue.current("testStuff"),
                testPeriod:   storedValueToFormValue.current("testPeriod"),
                projDesc:     storedValueToFormValue.current("projDesc"),
                intakeDesc:   storedValueToFormValue.current("intakeDesc"),
                waterUseDesc: storedValueToFormValue.current("waterUseDesc"),
                drainDesc:    storedValueToFormValue.current("drainDesc"),
                improveDesc:  storedValueToFormValue.current("improveDesc"),
                improvePlan:  storedValueToFormValue.current("improvePlan"),
                // service book
                residentNum:    storedValueToFormValue.current("residentNum"),
                flowNum:        storedValueToFormValue.current("flowNum"),
                wsDept:         storedValueToFormValue.current("wsDept"),
                wsContact:      storedValueToFormValue.current("wsContact"),
                wsLeader:       storedValueToFormValue.current("wsLeader"),
                wsLeaderTitle:  storedValueToFormValue.current("wsLeaderTitle"),
                wsManager:      storedValueToFormValue.current("wsManager"),
                wsManagerTitle: storedValueToFormValue.current("wsManagerTitle"),
                // industry book
                regCapital:   storedValueToFormValue.current("regCapital"),
                prodTime:     storedValueToFormValue.current("prodTime"),
                industryType: storedValueToFormValue.current("industryType"),
                prodName:     storedValueToFormValue.current("prodName"),
                prodOutput:   storedValueToFormValue.current("prodOutput"),
                prodCapacity: storedValueToFormValue.current("prodCapacity"),
                prodRevenue:  storedValueToFormValue.current("prodRevenue"),
                //revenue:      storedValueToFormValue.current("revenue"),
                //products:     storedValueToFormValue.current("products"),
                stuffNum:     storedValueToFormValue.current("stuffNum"),
                fluidNum:     storedValueToFormValue.current("fluidNum"),
                landArea:     storedValueToFormValue.current("landArea"),
                buildArea:    storedValueToFormValue.current("buildArea"),
                greenArea:    storedValueToFormValue.current("greenArea"),
                aopDays:      storedValueToFormValue.current("aopDays"),
                prodShift:    storedValueToFormValue.current("prodShift"),
                shiftDuraion: storedValueToFormValue.current("shiftDuraion"),
                supervisor:   storedValueToFormValue.current("supervisor"),
                department:   storedValueToFormValue.current("department"),
                // GPS
                comCoords:    storedValueToFormValue.current("comCoords"),
                drainCoords:  storedValueToFormValue.current("drainCoords"),
                intakeCoords: storedValueToFormValue.current("intakeCoords"),
                // ...
                others:       storedValueToFormValue.current("others"),
                remarks:      storedValueToFormValue.current("remarks"),
            };
            reset(formObject); // 重置react-form数据

            // 设置selector数据
            //const defaultWaterUnitScale = 0;
            setIntakeCoordsLocationStates(formObject.intakeCoords);
            //setWaterUnitScaleState(getStore("waterScale") || radioIdToObject(waterUnitScaleStateDataProviderRef.current, defaultWaterUnitScale));
            //setStartDatePickerState(formObject.startDate);
            //setEndDatePickerState(formObject.endDate);

            //waterUnitScaleRef.current = getStore("waterScale")?.id || defaultWaterUnitScale; // 根据本地存储更新
        };

        /*
         * 尽管根据历史注释要求显示0, 但本表数值项不多, 全部显示为空更合理, 而空值实际上也表示0, 因此注释掉, 20250324
        // 根据反馈, 要求默认显示0
        if (localCversion === 0) {
            recordSelectQuery.mutate();
            return;
        }
        */

        if(formType === 1) {
            recordSelectQuery.mutate();
        } else if (localCversion === 0 && remoteCversion > 0) {
            console.log("Local cversion === 0, serve may have something nontrivial initialized, query mutate!");
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        } else if (isServerDataNewer(localCversion, remoteCversion) && subCversionRef.current > 0) { // 服务端有更新数据, 但本地有修改
            console.log("Client cversion fell behind the server cversion, someone might modified the database, choose to use the data version!");
            Alert.alert("注意", "由于其他人向服务器提交了数据, 服务端数据比本地数据新, 请选择使用哪份数据?", [
                { text: "使用本地数据", onPress: () => { setStore("mainCversion", remoteCversion); restoreLocalData(); } },
                { text: "使用服务端数据", onPress: () => { setLoadingIndicatorVisible(true); recordSelectQuery.mutate(); } },
            ]);
        } else if (isServerDataNewer(localCversion, remoteCversion)) { // localCversion==0时要mutate是因为服务端可能在创建记录时生成了必要数据
            console.log("Server cversion may be newer, query mutate:", localCversion, remoteCversion);
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        }
        else {
            console.log("Local cversion >= server cversion, query will not mutate!");
            restoreLocalData();
        }
    }, []);

    // 统一配置提示文字和
    const fieldTips = {
        projDesc:     { toolTip: PROJ_DESC.current,      placeholder: "请点击左边查看填写帮助" },
        intakeDesc:   { toolTip: INTAKE_DESC.current,    placeholder: "请点击左边查看填写帮助" },
        waterUseDesc: { toolTip: WATER_USE_DESC.current, placeholder: "请点击左边查看填写帮助" },
        drainDesc:    { toolTip: DRAIN_DESC.current,     placeholder: "请点击左边查看填写帮助" },
        improveDesc:  { toolTip: IMPROVE_DESC.current,   placeholder: "请点击左边查看填写帮助" },
        improvePlan:  { toolTip: IMPROVE_PLAN.current,   placeholder: "请点击左边查看填写帮助" },
    };
    // remarks不需配置
    const FieldsConfig_1_book = {
        industry: [
            {
                inputs: [
                    { name: "clientName",   label: "客户名称",     unit: "",     type: "PLAIN",   editable: true, placeholder: "", props: { multiline: true }, },
                    { name: "address",      label: "地址",        unit: "",     type: "PLAIN",   editable: true, placeholder: "", props: { multiline: true }, },
                    { name: "industryProp", label: "性质",        unit: "",     type: "PLAIN",   editable: true, placeholder: "", },
                    { name: "regCapital",   label: "注册资金",     unit: "万元", type: "PLAIN",    editable: true, placeholder: "", },
                    { name: "estbDate",     label: "成立时间",     unit: "",     type: "PLAIN",   editable: true, placeholder: "", },
                    { name: "prodTime",     label: "投产时间",     unit: "",     type: "PLAIN",   editable: true, placeholder: "", },
                    { name: "industryType", label: "行业类别",     unit: "",     type: "PLAIN",   editable: true, placeholder: "", },
                    { name: "zipCode",      label: "邮政编码",     unit: "",     type: "PLAIN",   editable: true, placeholder: "", },
                    { name: "prodName",     label: "产品名称",     unit: "",     type: "PLAIN",   editable: true, placeholder: "", },
                    { name: "prodOutput",   label: "产量",        unit: "",     type: "PLAIN",   editable: true, placeholder: "", },
                    { name: "prodCapacity", label: "产品设计产能",  unit: "",     type: "PLAIN",   editable: true, placeholder: "", },
                    { name: "prodRevenue",  label: "产品产值/营收", unit: "万元",  type: "PLAIN",   editable: true, placeholder: "", },
                    { name: "stuffNum",     label: "职工人数",     unit: "",     type: "PLAIN",   editable: true, placeholder: "", },
                    { name: "fluidNum",     label: "流动人数",     unit: "",     type: "PLAIN",   editable: true, placeholder: "", },
                    { name: "landArea",     label: "占地面积",     unit: "m²",   type: "PLAIN",   editable: true, placeholder: "", },
                    { name: "buildArea",    label: "建筑面积",     unit: "m²",   type: "PLAIN",   editable: true, placeholder: "", },
                    { name: "greenArea",    label: "绿化面积",     unit: "m²",   type: "PLAIN",   editable: true, placeholder: "", },
                    { name: "aopDays",      label: "年生产天数",   unit: "",     type: "PLAIN",   editable: true, placeholder: "", },
                    { name: "prodShift",    label: "生产班制",     unit: "",     type: "PLAIN",   editable: true, placeholder: "", },
                    { name: "shiftDuraion", label: "每班时长",     unit: "h",    type: "PLAIN",   editable: true, placeholder: "", },
                    { name: "supervisor",   label: "主管领导姓名",  unit: "",     type: "PLAIN",   editable: true, placeholder: "", },
                    { name: "department",   label: "所属部门",     unit: "",     type: "PLAIN",    editable: true, placeholder: "", props: { multiline: true },  },
                    { name: "contact",      label: "联系方式",     unit: "", type: "PLAIN", editable: true, placeholder: "",  props: { multiline: true }, },

                    { name: "comCoords",    label: "厂区坐标",     unit: "",     type: "LOCATION", editable: true, placeholder: "请点击右侧图标获取经纬度", props: { multiline: true }, },
                    { name: "drainCoords",  label: "排水口坐标",   unit: "",     type: "LOCATION", editable: true, placeholder: "请点击右侧图标获取经纬度", props: { multiline: true }, },
                    { name: "intakeCoords", label: "取水口/自备井坐标", unit: "",  type: "LOCATIONS", editable: true, placeholder: "请点击右下方图标获取经纬度", pickName: "名称", pickDesc: "位置", pickGps: "坐标", pickLimit: intakePickLimit, locationStates: intakeCoordsLocationStates, setLocationStates: setIntakeCoordsLocationStates, },

                ]
            },
        ],
        service: [
            {
                inputs: [
                    //{ name: "name",       label: "表单名称",      unit: "", type: "PLAIN", editable: true, multiline: true, },
                    { name: "clientName",     label: "名称",            unit: "", type: "PLAIN", editable: true, placeholder: "", props: { multiline: true }, },
                    { name: "industryProp",   label: "性质",            unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "address",        label: "地址",            unit: "", type: "PLAIN", editable: true, placeholder: "", props: { multiline: true }, },
                    { name: "zipCode",        label: "邮编",            unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "residentNum",    label: "常驻实有人数",     unit: "人", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "flowNum",        label: "日流动人数",       unit: "人/日", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "wsLeader",       label: "主管节水领导姓名",  unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "wsLeaderTitle",  label: "职务",            unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "wsDept",         label: "节水管理机构名称",  unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "wsManager",      label: "节水管理负责人姓名", unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "wsManagerTitle", label: "职务",            unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "wsContact",      label: "联系电话",         unit: "", type: "PLAIN", editable: true, placeholder: "", },

                    { name: "comCoords",    label: "单位坐标",     unit: "",     type: "LOCATION", editable: true, placeholder: "请点击右侧图标获取经纬度", props: { multiline: true }, },
                    { name: "drainCoords",  label: "排水口坐标",   unit: "",     type: "LOCATION", editable: true, placeholder: "请点击右侧图标获取经纬度", props: { multiline: true }, },
                    { name: "intakeCoords", label: "取水口/自备井坐标", unit: "",  type: "LOCATIONS", editable: true, placeholder: "请点击右下方图标获取经纬度", pickName: "名称", pickDesc: "位置", pickGps: "经纬度", pickLimit: intakePickLimit, locationStates: intakeCoordsLocationStates, setLocationStates: setIntakeCoordsLocationStates, },

                ]
            },
        ],
    };

    const FieldsConfig_1_table = {
        industry: [
            {
                inputs: [
                //{ name: "name",       label: "表单名称",      unit: "", type: "PLAIN", editable: true, multiline: true, },
                    { name: "clientName",   label: "企业名称",      unit: "", type: "PLAIN", editable: true, placeholder: "", props: { multiline: true }, },
                    { name: "address",      label: "企业详细地址",   unit: "", type: "PLAIN", editable: true, placeholder: "", props: { multiline: true }, },
                    { name: "industryCode", label: "行业代码",      unit: "", type: "PLAIN", editable: true, placeholder: "", props: { multiline: true }, },
                    { name: "zipCode",      label: "邮编",          unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "estbDate",     label: "建立时间",       unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "phone",        label: "联系电话",       unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "fax",          label: "传真",          unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "website",      label: "企业网站",       unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "email",        label: "邮箱",          unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "lr",           label: "法定代表人",     unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "jobTitle",     label: "职务/职称",      unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "bureau",       label: "用水管理部门",    unit: "", type: "PLAIN", editable: true, placeholder: "",  props: { multiline: true }, },
                    { name: "contact",      label: "负责人及联系方式", unit: "", type: "PLAIN", editable: true, placeholder: "",  props: { multiline: true }, },
                    { name: "testInst",     label: "测试单位",       unit: "", type: "PLAIN", editable: true, placeholder: "",  props: { multiline: true }, },
                    { name: "testStuff",    label: "测试人员",       unit: "", type: "PLAIN", editable: true, placeholder: "",  props: { multiline: true }, },
                    { name: "testPeriod",   label: "测试时间",       unit: "", type: "PLAIN", editable: true, placeholder: "",  props: { multiline: true }, },

                    { name: "comCoords",    label: "厂区坐标",     unit: "",     type: "LOCATION", editable: true, placeholder: "请点击右侧图标获取经纬度", props: { multiline: true }, },
                    { name: "drainCoords",  label: "排水口坐标",   unit: "",     type: "LOCATION", editable: true, placeholder: "请点击右侧图标获取经纬度", props: { multiline: true }, },
                    { name: "intakeCoords", label: "取水口/自备井坐标", unit: "",  type: "LOCATIONS", editable: true, placeholder: "请点击右下方图标获取经纬度", pickName: "名称", pickDesc: "位置", pickGps: "经纬度", pickLimit: intakePickLimit, locationStates: intakeCoordsLocationStates, setLocationStates: setIntakeCoordsLocationStates, },

                    { name: "projDesc",     label: "项目概况",              unit: "", type: "PLAIN", editable: true, props: { multiline: true }, placeholder: fieldTips.projDesc.placeholder,     toolTip: fieldTips.projDesc.toolTip, },
                    { name: "intakeDesc",   label: "取水情况",              unit: "", type: "PLAIN", editable: true, props: { multiline: true }, placeholder: fieldTips.intakeDesc.placeholder,   toolTip: fieldTips.intakeDesc.toolTip, },
                    { name: "waterUseDesc", label: "用水情况",              unit: "", type: "PLAIN", editable: true, props: { multiline: true }, placeholder: fieldTips.waterUseDesc.placeholder, toolTip: fieldTips.waterUseDesc.toolTip, },
                    { name: "drainDesc",    label: "排水情况",              unit: "", type: "PLAIN", editable: true, props: { multiline: true }, placeholder: fieldTips.drainDesc.placeholder,    toolTip: fieldTips.drainDesc.toolTip, },
                    { name: "improveDesc",  label: "近3年节水改造情况",      unit: "", type: "PLAIN", editable: true, props: { multiline: true }, placeholder: fieldTips.improveDesc.placeholder,   toolTip: fieldTips.improveDesc.toolTip, },
                    { name: "improvePlan",  label: "拟采取节水措施及实施计划", unit: "", type: "PLAIN", editable: true, props: { multiline: true }, placeholder: fieldTips.improvePlan.placeholder,  toolTip: fieldTips.improvePlan.toolTip, },

                ]
            },
        ],
        service: [
            {
                inputs: [
                //{ name: "name",       label: "表单名称",      unit: "", type: "PLAIN", editable: true, multiline: true, },
                    { name: "clientName",   label: "用水单位名称",   unit: "", type: "PLAIN", editable: true, placeholder: "", props: { multiline: true }, },
                    { name: "address",      label: "详细地址",      unit: "", type: "PLAIN", editable: true, placeholder: "", props: { multiline: true }, },
                    { name: "industryCode", label: "行业代码",      unit: "", type: "PLAIN", editable: true, placeholder: "", props: { multiline: true }, },
                    { name: "zipCode",      label: "邮编",          unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "estbDate",     label: "建立时间",       unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "phone",        label: "联系电话",       unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "fax",          label: "传真",          unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    //{ name: "website",      label: "企业网站",       unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    //{ name: "email",        label: "邮箱",          unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "lr",           label: "法定代表人",     unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "jobTitle",     label: "职务/职称",      unit: "", type: "PLAIN", editable: true, placeholder: "", },
                    { name: "bureau",       label: "用水管理部门",    unit: "", type: "PLAIN", editable: true, placeholder: "",  props: { multiline: true }, },
                    { name: "contact",      label: "负责人及联系方式", unit: "", type: "PLAIN", editable: true, placeholder: "",  props: { multiline: true }, },
                    { name: "testInst",     label: "测试单位",       unit: "", type: "PLAIN", editable: true, placeholder: "",  props: { multiline: true }, },
                    { name: "testStuff",    label: "测试人员",       unit: "", type: "PLAIN", editable: true, placeholder: "",  props: { multiline: true }, },
                    { name: "testPeriod",   label: "测试时间",       unit: "", type: "PLAIN", editable: true, placeholder: "",  props: { multiline: true }, },

                    { name: "comCoords",    label: "单位坐标",     unit: "",     type: "LOCATION", editable: true, placeholder: "请点击右侧图标获取经纬度", props: { multiline: true }, },
                    { name: "drainCoords",  label: "排水口坐标",   unit: "",     type: "LOCATION", editable: true, placeholder: "请点击右侧图标获取经纬度", props: { multiline: true }, },
                    { name: "intakeCoords", label: "取水口/自备井坐标", unit: "",  type: "LOCATIONS", editable: true, placeholder: "请点击右下方图标获取经纬度", pickName: "名称", pickDesc: "位置", pickGps: "经纬度", pickLimit: intakePickLimit, locationStates: intakeCoordsLocationStates, setLocationStates: setIntakeCoordsLocationStates, },

                    { name: "projDesc",     label: "项目概况",              unit: "", type: "PLAIN", editable: true, props: { multiline: true }, placeholder: fieldTips.projDesc.placeholder,     toolTip: fieldTips.projDesc.toolTip, },
                    { name: "intakeDesc",   label: "取水情况",              unit: "", type: "PLAIN", editable: true, props: { multiline: true }, placeholder: fieldTips.intakeDesc.placeholder,   toolTip: fieldTips.intakeDesc.toolTip, },
                    { name: "waterUseDesc", label: "用水情况",              unit: "", type: "PLAIN", editable: true, props: { multiline: true }, placeholder: fieldTips.waterUseDesc.placeholder, toolTip: fieldTips.waterUseDesc.toolTip, },
                    { name: "drainDesc",    label: "排水情况",              unit: "", type: "PLAIN", editable: true, props: { multiline: true }, placeholder: fieldTips.drainDesc.placeholder,    toolTip: fieldTips.drainDesc.toolTip, },
                    { name: "improveDesc",  label: "近3年节水改造情况",      unit: "", type: "PLAIN", editable: true, props: { multiline: true }, placeholder: fieldTips.improveDesc.placeholder,   toolTip: fieldTips.improveDesc.toolTip, },
                    { name: "improvePlan",  label: "拟采取节水措施及实施计划", unit: "", type: "PLAIN", editable: true, props: { multiline: true }, placeholder: fieldTips.improvePlan.placeholder,  toolTip: fieldTips.improvePlan.toolTip, },

                ]
            },
        ],
    };

    const FieldsConfig_2_book  = { industry: [], service: [] };
    const FieldsConfig_2_table = { industry: [], service: [] };

    const FieldsConfig_3_book = {
        industry: [
        // 其它部分
            {
                inputs: [
                //{ name: "others", label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "",  props: {multiline: true}, },
                ]
            },
        ],
        service: [
            // 其它部分
            {
                inputs: [
                    //{ name: "others", label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "",  props: {multiline: true}, },
                ]
            },
        ],
    };
    const FieldsConfig_3_table ={
        industry: [
        // 其它部分
            {
                inputs: [
                //{ name: "others", label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "",  props: {multiline: true}, },
                ]
            },
        ],
        service: [
            // 其它部分
            {
                inputs: [
                    //{ name: "others", label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "",  props: {multiline: true}, },
                ]
            },
        ],
    };

    let FieldsConfig = [];
    switch(projSubclass) {
        case 1: // 表
            FieldsConfig = [...FieldsConfig_1_table[projIndustry], ...FieldsConfig_2_table[projIndustry], ...FieldsConfig_3_table[projIndustry]];
            break;
        case 2: // 书
            FieldsConfig = [...FieldsConfig_1_book[projIndustry], ...FieldsConfig_2_book[projIndustry], ...FieldsConfig_3_book[projIndustry]];
            break;
        default:
            console.warn("Unknown subclass:", projSubclass);
            FieldsConfig = [];
            break;
    }

    //const FieldsConfig = [...FieldsConfig_1_book, ...FieldsConfig_2_book, ...FieldsConfig_3_book];

    return (
        <View style={{ flex: 1 }}>

            <HeaderBar
                //title={screenTitle}
                navigation={navigation}
                goBackCallback={() => {
                    callOneByOne(
                        //() => { _mainCversionGetter=undefined; _subCversionGetter=undefined;},
                        resetFieldsStates,
                    );
                    /*callOneByOne(clearStore, resetFieldsStates)*/  // 返回前清空mmkv存储因为临时数据不再需要, 重置状态使得页面切换后不会使用前面的数据
                }}
                menuItemArray={formType === 0 ? [{ title: "删除表单", action: deleteAlert }, { title: "清空缓存", action: clearCache }] : []}
            />

            <ScreenWrapper contentContainerStyle={styles.container}>

                <View style={styles.formEntry}>
                    <FormListNodesMapper
                        fieldsConfig={FieldsConfig}
                        subCversionRef={subCversionRef}
                        control={control}
                        getStore={getStore}
                        setStore={setStore}
                        resetField={resetField}
                        errors={errors}
                        setSnackBarMessage={setSnackBarMessage}
                        setShowSnackbar={setShowSnackbar}
                        //formDisabledGlobal={formDisabledGlobal}
                        editable={formEditableGlobal}
                        getValues={getValues}
                        rowStyle={styles.rowContainer}
                    />
                    <Divider bold={true}/>
                    {/*<View style={{marginTop: 6}}>
                        <Text variant="titleMedium">拟采取节水措施及实施计划: </Text>
                        <Text variant="bodyMedium">近三年的节水方面的措施、时间、费用、取得效果；根据企业规划未来三年计划节水措施、时间费用、预计效果；</Text>
                    </View>*/}
                    {/*formType === 0 && <ControlledTextInput
                        name="remarks"
                        rowLabel="备注"
                        control={control}
                        placeholder=""
                        onChangeText={(text) => {
                            subCversionRef.current++;
                            setStore("subCversion", subCversionRef.current);
                            setStore("remarks", text);
                        }}
                        onClearText={() => {
                            subCversionRef.current++;
                            setStore("subCversion", subCversionRef.current);
                            setStore("remarks", "");
                            resetField("remarks", { defaultValue: "" });
                        }}
                        editable={formEditableGlobal}
                        mode="flat"
                        multiline={true}
                        formError={errors}
                        style={styles.rowContainerLongText}
                        //disabled={formDisabledGlobal}
                        required={false}
                    />*/}
                </View>

                <Button mode="contained"
                        disabled={false}
                        loading={okButtonLoading}
                        icon={saveButtonIcon}
                        onPress={()=>{}}
                    >录音</Button>

                {formType === 0 && <View style={styles.button}>
                    <Button mode="contained"
                        disabled={okButtonDisabled}
                        loading={okButtonLoading}
                        icon={saveButtonIcon}
                        onPress={handleSubmit(onHandleSubmit, (formErr) => onPreSubmitError(formErr, getStore, "BasicInfoRecordsUpdating"))}
                    >{bottomBarLabels.ok}</Button>
                </View>}
            </ScreenWrapper>

            <LoadingIndicator
                title={""}
                message={"加载中....."}
                visible={loadingIndicatorVisible}
                onClose={()=>setLoadingIndicatorVisible(false)}
            />

            <Snackbar
                style={styles.snackBar}
                visible={showSnackbar}
                onDismiss={() => setShowSnackbar(false)}
                onIconPress={() => setShowSnackbar(false)}
                duration={Snackbar.DURATION_LONG}
            >
                {snackBarMessage}
            </Snackbar>

            <DialogToConfirm
                visible={showConfirm}
                title={confimDialogConfig.title ? confimDialogConfig.title : ""}
                text={confimDialogConfig.text ? confimDialogConfig.text : ""}
                onOK={confimDialogConfig.onOK ? confimDialogConfig.onOK : () => setShowConfirm(false)}
                okBtnLabel={confimDialogConfig.okLabel ? confimDialogConfig.okLabel : "确认"}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    rowContainer:{
        firstColumn: {
            flex: 1,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 3,
        // /borderWidth: 1,
        // height: 40,
        },
    },
    rowContainerLongText: {
        firstColumn: {
            flex: 1,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 4,
            // /borderWidth: 1,
            // height: 40,
        },
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        width: "80%",
        alignSelf: "center",
    },
    formEntry: {
        margin: 8,
    },
    snackBar: {
        flexDirection: "column",
    },
});

export default RecordsUpdating;
