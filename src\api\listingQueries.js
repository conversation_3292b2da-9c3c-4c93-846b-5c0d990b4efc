import * as keys from "../config/keysConfig";
import { makeListingClient as httpClient } from "./makeListingClient";

export const makeRequestListingOrgs        = httpClient(keys.LIST_ALL_ORGS);
export const makeRequestListingUsers       = httpClient(keys.LIST_ALL_USERS);
export const makeRequestListingDepartments = httpClient(keys.LIST_ALL_DEPARTMENTS);
export const makeRequestListingPositions   = httpClient(keys.LIST_ALL_POSITIONS);
export const makeRequestListingRoles       = httpClient(keys.LIST_ALL_ROLES);
export const makeRequestListingClients     = httpClient(keys.LIST_ALL_CLIENTS);

export const makeRequestListingProjBase    = httpClient(keys.LIST_ORG_PROJ_BASE);    // 列出所有项目
export const makeRequestListingWBProjBase  = httpClient(keys.LIST_ORG_WB_PROJ_BASE); // 列出水平衡项目
export const makeRequestListingZCProjBase  = httpClient(keys.LIST_ORG_ZC_PROJ_BASE); // 列出零碳诊断项目

export const makeRequestListingStdRoles     = httpClient(keys.STD_ROLES_GET);
export const makeRequestListAvaibleServices = httpClient(keys.GET_AVAIBLE_SERVICES);

export const makeReqListWBByCreater        = httpClient(keys.LIST_WB_BY_CREATER);
export const makeReqListZCByCreater        = httpClient(keys.LIST_ZC_BY_CREATER);

// MessageScreen page data query
export const makeReqListProjectMessages    = httpClient(keys.LIST_PROJ_MESSAGES);

// 列出与本人相关的项目, 即, 消息列表中出现的项目
export const makeReqListMyProjALL = httpClient(keys.LIST_MY_PROJ_ALL);
export const makeReqListMyProjWB = httpClient(keys.LIST_MY_PROJ_WB);
export const makeReqListMyProjZC = httpClient(keys.LIST_MY_PROJ_ZC);
