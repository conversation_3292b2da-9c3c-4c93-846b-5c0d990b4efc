///
/// PlayBackType.kt
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON> @ Margelo
///

package com.margelo.nitro.reactnativeaudiorecorderplayer

import androidx.annotation.Keep
import com.facebook.proguard.annotations.DoNotStrip
import com.margelo.nitro.core.*

/**
 * Represents the JavaScript object/struct "PlayBackType".
 */
@DoNotStrip
@Keep
data class PlayBackType
  @DoNotStrip
  @Keep
  constructor(
    val isMuted: Boolean?,
    val duration: Double,
    val currentPosition: Double
  ) {
  /* main constructor */
}
