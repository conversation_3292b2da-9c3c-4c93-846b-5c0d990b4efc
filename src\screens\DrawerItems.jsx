import { DrawerContentScrollView } from "@react-navigation/drawer";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Linking, View } from "react-native";
import {
    Divider,
    Drawer,
    MD3Colors,
    Switch,
    Text,
    useTheme,
} from "react-native-paper";
import { usePreferenceZustandStoredState } from "../hooks/preferenceStoredState";
//import { userLoginState } from "../hooks/loginStoredState";
import { useShallow } from "zustand/shallow";
import { FeedbackDialog } from "../components/FeedbackDialog";
import { CUSTOMER_SERVICE_HOTLINE_1, DOWNLOAD_URL_ANDROID, DOWNLOAD_URL_ANDROID_TEST, TEST_SERIAL } from "../config";
import { STORE_LOGIN_INFO } from "../config/keysConfig";
import { userLoginState } from "../hooks/loginStoredState";
import { getMMKV } from "../services/local-storage";
import { APP_VERSION } from "../services/system";
import { callPhone } from "../services/system/phone";
import { UserTypeList, verifySomeUserTypes } from "../utils/permits";
import { clearPermits } from "../utils/user";


// https://reactnavigation.org/docs/drawer-navigator/

export const DrawerItems = ({ navigation }) => {
    const [themeMode, setThemeMode] = usePreferenceZustandStoredState(useShallow((state) => [state.theme, state.setTheme]));
    //const [setUserToken] = userLoginState(useShallow((state) => [state.setToken]));
    const name = getMMKV(STORE_LOGIN_INFO, "name");
    const theme = useTheme();

    const { t } = useTranslation(["drawerItems"]);

    const [resetLoginStates] = userLoginState(useShallow((state) => [state.resetStates]));
    const [showFeedback, setShowFeedback] = useState(false);

    //const [setUpProgressVisible, setUpProgressValue] = updateProgressState(useShallow(state => [state.setVisible, state.setProgress]));
    const toggleTheme = () => {
        if (themeMode === "dark") {
            setThemeMode("light");
        } else {
            setThemeMode("dark");
        }
    };

    const darkModeSwitch = () => {
        return (
            <View pointerEvents="none">
                <Switch value={themeMode === "dark"} />
            </View>
        );
    };

    const coloredLabelTheme = {
        colors: {
            secondaryContainer: MD3Colors.tertiary80,
            onSecondaryContainer: MD3Colors.tertiary20,
        }
    };

    return (
        <DrawerContentScrollView
            alwaysBounceVertical={false}
            backgroundColor={theme.colors.surface}
        >
            <Text variant="headlineMedium" style={{textAlign: "center", marginVertical: 10}}>{name}</Text>
            {/*<TouchableRipple onPress={toggleTheme}>
                <View style={[styles.preference, styles.v3Preference]}>
                    <Text variant="labelLarge">{t("drawerItems:theme")}</Text>
                    <View pointerEvents="none">
                        <Switch value={themeMode==="dark"} />
                    </View>
                </View>
    </TouchableRipple>*/}


            <Divider bold={true}/>
            <Drawer.Item
                label={t("drawerItems:theme")}
                icon="weather-night"
                right={darkModeSwitch}
                active={false}
                theme={coloredLabelTheme}
                onPress={toggleTheme}
            />
            <Divider/>

            <Drawer.Item
                label="修改密码"
                icon="lock-reset"
                active={false}
                theme={coloredLabelTheme}
                onPress={() => {
                    //("");
                    //navigation.navigate("LoginScreen");
                    navigation.reset({
                        index: 0,
                        routes: [{ name: "ResetPasswordScreen" }],
                    });
                }}
            />
            <Divider />

            <Drawer.Item
                label={`下载客户端(v${APP_VERSION})`}
                icon="cellphone-arrow-down"
                active={false}
                theme={coloredLabelTheme}
                onPress={() => {
                    Linking.openURL(DOWNLOAD_URL_ANDROID);
                    //setUpProgressVisible(true);
                    //downloadAPK(setUpProgressValue, ()=>setUpProgressVisible(false), ()=>setUpProgressVisible(false), ()=>setUpProgressVisible(false));
                }}
            />
            <Divider />

            {verifySomeUserTypes(UserTypeList.sysAdmin, UserTypeList.qa_0)  && <>
                <Drawer.Item
                    label={`测试客户端(v${APP_VERSION}.${TEST_SERIAL})`}
                    icon="ab-testing"
                    active={false}
                    theme={coloredLabelTheme}
                    onPress={() => {
                        Linking.openURL(DOWNLOAD_URL_ANDROID_TEST);
                    }}
                />
                <Divider />
            </>}

            <Drawer.Item
                label="切换账号"
                icon="logout"
                active={false}
                theme={coloredLabelTheme}
                onPress={() => {
                    //("");
                    //navigation.navigate("LoginScreen");
                    resetLoginStates();
                    clearPermits();
                    navigation.reset({
                        index: 0,
                        routes: [{ name: "LoginScreen" }],
                    });
                }}
            />
            <Divider />

            <Drawer.Item
                label={`客服电话: ${CUSTOMER_SERVICE_HOTLINE_1}`}
                icon="face-agent"
                active={false}
                theme={coloredLabelTheme}
                onPress={() => {
                    callPhone(CUSTOMER_SERVICE_HOTLINE_1);
                }}
            />
            <Divider />

            <Drawer.Item
                label="意见反馈"
                icon="message-cog-outline"
                active={false}
                theme={coloredLabelTheme}
                onPress={() => {
                    setShowFeedback(true);
                }}
            />
            <Divider bold={true} />

            <FeedbackDialog
                title={null}
                visible={showFeedback}
                setVisible={setShowFeedback}
                okBtnLabel="提交反馈"
                cancelBtnLabel="取消"
            />

        </DrawerContentScrollView>
    );
};

/*
const styles = StyleSheet.create({
    drawerContent: {
        flex: 1,
    },
    preference: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        paddingVertical: 12,
        paddingHorizontal: 16,
    },
    v3Preference: {
        height: 56,
        paddingHorizontal: 28,
    },
    badge: {
        alignSelf: "center",
    },
    collapsedSection: {
        marginTop: 16,
    },
    annotation: {
        marginHorizontal: 24,
        marginVertical: 6,
    },
});
*/
