import BottomSheet, { BottomSheetView } from "@gorhom/bottom-sheet";
import React, { useEffect, useRef, useState } from "react";
import { Alert, PermissionsAndroid, Platform, StyleSheet, View } from "react-native";
import AudioRecorderPlayer from "react-native-audio-recorder-player";
import { Button, Dialog, IconButton, Portal, Text } from "react-native-paper";
import { uploadFile } from "../api/uploadFile";

const AudioRecorder = ({ visible, onClose, uploadUri }) => {
  const [recordingState, setRecordingState] = useState("Record"); // "Record", "OnRecording", "Recorded"
  const [recordTime, setRecordTime] = useState("00:00");
  const [recordPath, setRecordPath] = useState("");
  const [isPlaying, setIsPlaying] = useState(false);
  const [showUploadDialog, setShowUploadDialog] = useState(false);

  const audioRecorderPlayer = useRef(new AudioRecorderPlayer()).current;
  const bottomSheetRef = useRef(null);
  const recordingTimer = useRef(null);
  const startTime = useRef(0);

  // BottomSheet snap points
  const snapPoints = ["50%", "70%"];

  useEffect(() => {
    if (visible) {
      bottomSheetRef.current?.expand();
    } else {
      bottomSheetRef.current?.close();
    }
  }, [visible]);

  useEffect(() => {
    return () => {
      // Cleanup on unmount
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current);
      }
      audioRecorderPlayer.stopRecorder();
      audioRecorderPlayer.stopPlayer();
      audioRecorderPlayer.removeRecordBackListener();
      audioRecorderPlayer.removePlayBackListener();
    };
  }, []);

  const formatTime = (milliseconds) => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  };

  const checkPermissions = async () => {
    if (Platform.OS === "android") {
      try {
        const grants = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
          PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
          PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
        ]);

        if (
          grants["android.permission.WRITE_EXTERNAL_STORAGE"] === PermissionsAndroid.RESULTS.GRANTED &&
          grants["android.permission.READ_EXTERNAL_STORAGE"] === PermissionsAndroid.RESULTS.GRANTED &&
          grants["android.permission.RECORD_AUDIO"] === PermissionsAndroid.RESULTS.GRANTED
        ) {
          return true;
        } else {
          Alert.alert("权限不足", "需要录音和存储权限才能使用录音功能");
          return false;
        }
      } catch (err) {
        console.warn(err);
        return false;
      }
    }
    return true; // iOS permissions are handled in Info.plist
  };

  const startRecording = async () => {
    try {
      // Check permissions first
      const hasPermissions = await checkPermissions();
      if (!hasPermissions) {
        return;
      }

      // Set up recording progress listener first
      audioRecorderPlayer.addRecordBackListener((e) => {
        setRecordTime(formatTime(e.currentPosition));
      });

      const path = await audioRecorderPlayer.startRecorder();
      setRecordPath(path);
      setRecordingState("OnRecording");
      startTime.current = Date.now();
    } catch (error) {
      console.error("Failed to start recording:", error);
      Alert.alert("错误", "录音启动失败");
    }
  };

  const stopRecording = async () => {
    try {
      const result = await audioRecorderPlayer.stopRecorder();
      audioRecorderPlayer.removeRecordBackListener();

      if (recordingTimer.current) {
        clearInterval(recordingTimer.current);
        recordingTimer.current = null;
      }

      setRecordingState("Recorded");
      console.log("Recording stopped:", result);
    } catch (error) {
      console.error("Failed to stop recording:", error);
      Alert.alert("错误", "录音停止失败");
    }
  };

  const playRecording = async () => {
    try {
      if (isPlaying) {
        await audioRecorderPlayer.stopPlayer();
        setIsPlaying(false);
      } else {
        await audioRecorderPlayer.startPlayer(recordPath);
        setIsPlaying(true);

        audioRecorderPlayer.addPlayBackListener((e) => {
          if (e.currentPosition === e.duration) {
            setIsPlaying(false);
            audioRecorderPlayer.removePlayBackListener();
          }
        });
      }
    } catch (error) {
      console.error("Failed to play recording:", error);
      Alert.alert("错误", "播放失败");
    }
  };

  const deleteRecording = () => {
    Alert.alert(
      "删除录音",
      "确定要删除这个录音吗？",
      [
        { text: "取消", style: "cancel" },
        {
          text: "删除",
          style: "destructive",
          onPress: () => {
            setRecordingState("Record");
            setRecordTime("00:00");
            setRecordPath("");
            setIsPlaying(false);
          }
        }
      ]
    );
  };

  const uploadRecording = async () => {
    if (!uploadUri || !recordPath) {
      Alert.alert("错误", "上传路径或录音文件不存在");
      return;
    }

    try {
      const fileName = `recording_${Date.now()}`;
      const response = await uploadFile(recordPath, fileName, uploadUri);

      if (response && response.STATUS === 0) {
        setShowUploadDialog(true);
      } else {
        Alert.alert("上传失败", "录音上传失败，请重试");
      }
    } catch (error) {
      console.error("Upload failed:", error);
      Alert.alert("上传失败", "录音上传失败，请重试");
    }
  };

  const handleUploadDialogConfirm = () => {
    setShowUploadDialog(false);
    setRecordingState("Record");
    setRecordTime("00:00");
    setRecordPath("");
    setIsPlaying(false);
  };

  const handleClose = () => {
    if (recordingState === "OnRecording") {
      stopRecording();
    }
    onClose();
  };

  const getStateColor = () => {
    switch (recordingState) {
      case "Record":
        return "#6366f1"; // Blue
      case "OnRecording":
        return "#ef4444"; // Red
      case "Recorded":
        return "#6b7280"; // Gray
      default:
        return "#6366f1";
    }
  };

  const renderRecordButton = () => {
    switch (recordingState) {
      case "Record":
        return (
          <IconButton
            icon="microphone"
            size={40}
            iconColor="#6366f1"
            containerColor="white"
            onPress={startRecording}
            style={styles.actionButton}
          />
        );
      case "OnRecording":
        return (
          <IconButton
            icon="stop"
            size={40}
            iconColor="#ef4444"
            containerColor="white"
            onPress={stopRecording}
            style={styles.actionButton}
          />
        );
      case "Recorded":
        return (
          <IconButton
            icon={isPlaying ? "pause" : "play"}
            size={40}
            iconColor="white"
            containerColor="white"
            onPress={playRecording}
            style={styles.actionButton}
          />
        );
      default:
        return null;
    }
  };

  const renderSideButtons = () => {
    if (recordingState === "Recorded") {
      return (
        <View style={styles.sideButtonsContainer}>
          <Button
            mode="text"
            icon="delete"
            textColor="white"
            onPress={deleteRecording}
          >
            Delete
          </Button>
          <Button
            mode="text"
            icon="upload"
            textColor="white"
            onPress={uploadRecording}
          >
            Upload
          </Button>
        </View>
      );
    }
    return null;
  };

  return (
    <>
      <BottomSheet
        ref={bottomSheetRef}
        index={visible ? 0 : -1}
        snapPoints={snapPoints}
        enablePanDownToClose
        onClose={handleClose}
      >
        <BottomSheetView style={[styles.container, { backgroundColor: getStateColor() }]}>
          <View style={styles.header}>
            <Text style={styles.stateText}>{recordingState}</Text>
            <IconButton
              icon="chevron-down"
              iconColor="white"
              onPress={handleClose}
            />
          </View>

          <View style={styles.timerContainer}>
            <Text style={styles.timerText}>{recordTime}</Text>
          </View>

          <View style={styles.controlsContainer}>
            {renderSideButtons()}
            {renderRecordButton()}
          </View>
        </BottomSheetView>
      </BottomSheet>

      <Portal>
        <Dialog visible={showUploadDialog} onDismiss={() => setShowUploadDialog(false)}>
          <Dialog.Title>上传成功</Dialog.Title>
          <Dialog.Content>
            <Text>录音已成功上传到服务器</Text>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={handleUploadDialogConfirm}>确定</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 20,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 10,
  },
  stateText: {
    color: "white",
    fontSize: 18,
    fontWeight: "bold",
  },
  timerContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  timerText: {
    color: "white",
    fontSize: 48,
    fontWeight: "bold",
    fontFamily: "monospace",
  },
  controlsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingBottom: 40,
  },
  sideButtonsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    flex: 1,
  },
  actionButton: {
    backgroundColor: "white",
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
});

export default AudioRecorder;
