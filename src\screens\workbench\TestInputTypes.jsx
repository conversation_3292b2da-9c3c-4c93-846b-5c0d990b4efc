import React, { useEffect, useState } from "react";
import { StyleSheet, View } from "react-native";
import { Appbar, DataTable, Divider, useTheme } from "react-native-paper";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useRoleAdding } from "../../api/addRole";
import BottomBarButton from "../../components/BottomBarButton";
import TableRowDropdownInput from "../../components/TableRowDropdownInput";
import TableRowKeyboardInput from "../../components/TableRowKeyboardInput";
import ResponsibilityConfig from "../../config/userResponsibility";
import ScreenWrapper from "../ScreenWrapper";

/**
 * 添加用户, 用于测试下拉菜单和多按钮, 已废弃!
 * @param {*} param0
 * @returns
 */
const TestInputTypes = ({ navigation }) => {
    const { bottom, left, right } = useSafeAreaInsets();
    const theme = useTheme();
    const height = theme.isV3 ? 80 : 56;

    const [saveButtonLoading, setSaveButtonLoading] = useState(false);
    const [saveButtonDisabled, setSaveButtonDisabled] = useState(false);
    const [saveButtonIcon, setSaveButtonIcon] = useState("content-save-all-outline");
    const [cancelButtonDisabled, setCancelButtonDisabled] = useState(true);

    const [editable, setEditable] = useState(true);

    const [nameObject, setNameObject] = useState({ value: "", error: "" });
    const [handyObject, setHandyObject] = useState({ value: "", error: "" });
    const [passwordObject, setPasswordObject] = useState({ value: "", error: "" });
    const [respObject, setRespObject] = useState({ label: "", value: "", error: "" });
    const [deptObject, setDeptObject] = useState({ label: "", value: "", error: "" });

    const formSettings = [setNameObject, setHandyObject, setPasswordObject, setRespObject, setDeptObject];
    const clearForm = () => { formSettings.map((setFun) => { setFun({ value: "", error: "" }); }); };

    useEffect(()=>{
        if (nameObject.value || handyObject.value || passwordObject.value || respObject.value || deptObject.value){
            setSaveButtonIcon("content-save-all-outline");
            cancelButtonDisabled ? setCancelButtonDisabled(false) : undefined;
        } else {
            cancelButtonDisabled ? undefined : setCancelButtonDisabled(true);
        }

    }, [nameObject, handyObject, passwordObject, respObject, deptObject ]);

    const respDropdownConfig = ResponsibilityConfig.map((item) => {
        return ({
            title: item.title,
            value: item.id,
            action: () => {
                setRespObject({ value: item.id, label: item.title, error: "" });
                if (item.dept.length === 1) {
                    setDeptObject({ label: item.dept[0], value: item.dept, error: "" });
                } else {
                    setDeptObject({ label: "", value: "", error: "" });
                }
            }
        });
    });

    const makeDeptDropdownConfig = respValue => {
        const resp = ResponsibilityConfig.find(item => item.id === respValue);
        return resp.dept.map((deptName) => {
            return ({
                title: deptName,
                value: deptName,
                action: () => setDeptObject({ label: deptName, value: deptName, error: "" }),
            });
        });
    };

    const makeRoleInfo = () => {
        return {
            name: nameObject.value,
            mobile: handyObject.value,
            password: passwordObject.value,
            responsibility: respObject.value,
            department: deptObject.value,
        };
    };

    const onCancelPress = () => {
        clearForm();
        setSaveButtonDisabled(false);
        setEditable(true);
        setSaveButtonIcon("content-save-all-outline");
    };

    const onSuccess = () => {
        setSaveButtonIcon("check");
        clearForm();
    };
    const onError = () => {
        setSaveButtonIcon("alert-circle-outline");
    };
    const onSettled = () => {
        setSaveButtonLoading(false);
        setEditable(true);
        setSaveButtonDisabled(false);
        //setCancelButtonDisabled(false);
        addRoleQuery.reset();
    };
    const addRoleQuery = useRoleAdding(makeRoleInfo(), onSuccess, onError, onSettled); // 此行不能放到前面, 否则作用域会出问题
    const onSavePress = () => {
        setEditable(false);
        setSaveButtonLoading(true);
        setSaveButtonDisabled(true);
        //setCancelButtonDisabled(true);
        //addRoleQuery.refetch(); // refetch for useQuery, mutate for useMutation
        addRoleQuery.mutate();
    };

    return (
        <>
            <ScreenWrapper contentContainerStyle={styles.container}>

                <DataTable style={{
                    fontSize: 24,
                    //marginTop: 18,
                    borderColor: "black"
                }}>

                    <Divider />

                    <TableRowKeyboardInput
                        rowLabel="姓名"
                        inputValue={nameObject.value}
                        placeholder="请输入用户姓名"
                        inputLabel={nameObject.error ? nameObject.error : ""}
                        onClearText={() => setNameObject({ value: "", error: "" })}
                        onChangeText={setNameObject}
                        error={!!nameObject.error}
                        errorText={nameObject.error}
                        editable={editable}
                        mode={editable ? "outlined" : "flat"}
                    />

                    <TableRowKeyboardInput
                        rowLabel="手机"
                        inputValue={handyObject.value}
                        placeholder="请输入11位手机号"
                        inputLabel={handyObject.error ? handyObject.error : ""}
                        onClearText={() => setHandyObject({ value: "", error: "" })}
                        onChangeText={setHandyObject}
                        error={!!handyObject.error}
                        errorText={handyObject.error}
                        editable={editable}
                        mode={editable ? "outlined" : "flat"}
                    />

                    <TableRowKeyboardInput
                        rowLabel="密码"
                        inputValue={passwordObject.value}
                        placeholder="请至少输入六位密码"
                        inputLabel={passwordObject.error ? passwordObject.error : ""}
                        onClearText={() => setPasswordObject({ value: "", error: "" })}
                        onChangeText={setPasswordObject}
                        error={!!passwordObject.error}
                        errorText={passwordObject.error}
                        editable={editable}
                        mode={editable ? "outlined" : "flat"}
                    />

                    <TableRowDropdownInput
                        rowLabel="角色"
                        inputValue={respObject.label}
                        //inputLabel={passwordObject.error ? passwordObject.error : ""}
                        //onClearText={() => setPasswordObject({ value: "", error: "" })}
                        //onChangeText={setPasswordObject}
                        error={!!passwordObject.error}
                        errorText={passwordObject.error}
                        editable={editable}
                        dropdownConfig={respDropdownConfig}
                    />

                    <TableRowDropdownInput
                        rowLabel="部门"
                        inputValue={deptObject.label}
                        //inputLabel={passwordObject.error ? passwordObject.error : ""}
                        //onClearText={() => setPasswordObject({ value: "", error: "" })}
                        //onChangeText={setPasswordObject}
                        error={!!passwordObject.error}
                        errorText={passwordObject.error}
                        editable={editable}
                        dropdownConfig={(respObject.value) ? makeDeptDropdownConfig(respObject.value) : undefined}
                    />

                </DataTable>

                <Divider />
            </ScreenWrapper>

            <Appbar
                style={[
                    styles.bottom,
                    {
                        height: height + bottom,
                    },
                    theme.isV3 && {
                        backgroundColor: theme.colors.elevation.level2,
                    },
                ]}
                safeAreaInsets={{ bottom, left, right }}
                theme={{ mode: "adaptive" }}
            //mode='center-aligned'
            >

                <View style={styles.bottomBarContainer}>
                    <Divider />
                    <View style={styles.bottomBarComponents}>
                        <BottomBarButton
                            label={"取消"}
                            disabled={cancelButtonDisabled}
                            onPress={onCancelPress}
                        />
                        <BottomBarButton
                            label={"创建"}
                            loading={saveButtonLoading}
                            disabled={saveButtonDisabled}
                            icon={saveButtonIcon}
                            onPress={onSavePress}
                        />
                    </View>
                </View>
            </Appbar>
        </>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    firstColumn: {
        flex: 2,
        //borderWidth: 1,
        //height: 40,
        content: {
            fontSize: 18,
        }
    },
    secondColumn: {
        flex: 3,
        // /borderWidth: 1,
        // height: 40,
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        flex: 1,
        marginHorizontal: 6
    }
});

export default TestInputTypes;
