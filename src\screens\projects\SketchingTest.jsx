import { Canvas, Group, Image, useImage } from "@shopify/react-native-skia";
import * as React from "react";
import { StyleSheet, View } from "react-native";
import ScreenWrapper from "../ScreenWrapper";

/**
 * 绘制图像的屏幕
 * 注意: colorPicker处, 使用rnpaper的IconButton效果不尽如意, 占位太大, containerColor区域也太大.
 */
const SketchingTestScreen = ({navigation, route, ...props}) => {
    const bgImage = useImage(require("../../assets/images/new-card.png"));
    //const bgImage = useImage(require("../../assets/images/bg-ver.png"));
    const canvasRef = React.useRef(null);
    const [canvasSize, setCanvasSize] = React.useState({ width: 0, height: 0 });

    // 监听Canvas尺寸变化
    const handleCanvasLayout = React.useCallback((event) => {
        const { width, height } = event.nativeEvent.layout;
        setCanvasSize({ width, height });
        console.log("Canvas尺寸更新:", width, height);
    }, []);

    // 渲染图片到Canvas
    const renderImage = React.useCallback(
        () => {
            if (!bgImage || canvasSize.width === 0 || canvasSize.height === 0) {
                console.log("图片或画布尺寸无效");
                return null;
            }

            const imageWidth = bgImage.width();
            const imageHeight = bgImage.height();
            console.log("图片尺寸:", imageWidth, imageHeight);

            // 判断图片方向
            const isImageHorizontal = imageWidth > imageHeight;
            // 判断画布方向
            const isCanvasHorizontal = canvasSize.width > canvasSize.height;

            console.log("图片是否横向:", isImageHorizontal, "画布是否横向:", isCanvasHorizontal);

            // 需要旋转的情况
            if (isImageHorizontal !== isCanvasHorizontal) {
                // 使用Group组件实现旋转
                let scale;
                if (isImageHorizontal) {
                    // 横图在竖屏上显示，旋转后以高度为基准
                    scale = Math.min(canvasSize.width / imageHeight, canvasSize.height / imageWidth);
                } else {
                    // 竖图在横屏上显示，旋转后以宽度为基准
                    scale = Math.min(canvasSize.width / imageHeight, canvasSize.height / imageWidth);
                }

                // 计算居中位置
                const centerX = canvasSize.width / 2;
                const centerY = canvasSize.height / 2;

                console.log("旋转90度，缩放比例:", scale, "中心点:", centerX, centerY);

                return (
                    <Group
                        transform={[
                            { translateX: centerX },
                            { translateY: centerY },
                            { rotate: Math.PI / 2 },
                            { scale: scale }
                        ]}
                    >
                        <Image
                            image={bgImage}
                            x={-imageWidth / 2}
                            y={-imageHeight / 2}
                            width={imageWidth}
                            height={imageHeight}
                        />
                    </Group>
                );
            } else {
                // 图片和画布方向一致，不需要旋转
                const canvasRatio = canvasSize.width / canvasSize.height;
                const imageRatio = imageWidth / imageHeight;

                let scale;
                if (canvasRatio > imageRatio) {
                    // 画布较宽，以高度为基准缩放
                    scale = canvasSize.height / imageHeight;
                } else {
                    // 画布较高，以宽度为基准缩放
                    scale = canvasSize.width / imageWidth;
                }

                // 计算居中位置
                const x = (canvasSize.width - imageWidth * scale) / 2;
                const y = (canvasSize.height - imageHeight * scale) / 2;

                console.log("不旋转，缩放比例:", scale, "位置:", x, y);

                return (
                    <Image
                        image={bgImage}
                        x={x}
                        y={y}
                        width={imageWidth * scale}
                        height={imageHeight * scale}
                    />
                );
            }
        },
        [bgImage, canvasSize]
    );

    return (
        <View style={{ flex: 1 }}>
            <ScreenWrapper contentContainerStyle={styles.container}>
                <Canvas
                    ref={canvasRef}
                    style={{ flex: 1 }}
                    onLayout={handleCanvasLayout}
                >
                    {bgImage && renderImage()}
                </Canvas>
            </ScreenWrapper>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
});

export default SketchingTestScreen;
