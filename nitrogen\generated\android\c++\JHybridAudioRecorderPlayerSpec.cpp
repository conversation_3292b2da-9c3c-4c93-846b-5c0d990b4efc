///
/// JHybridAudioRecorderPlayerSpec.cpp
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc Rousavy @ Margelo
///

#include "JHybridAudioRecorderPlayerSpec.hpp"

// Forward declaration of `AudioSet` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { struct AudioSet; }
// Forward declaration of `AudioSourceAndroidType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AudioSourceAndroidType; }
// Forward declaration of `OutputFormatAndroidType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class OutputFormatAndroidType; }
// Forward declaration of `AudioEncoderAndroidType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AudioEncoderAndroidType; }
// Forward declaration of `AVEncoderAudioQualityIOSType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AVEncoderAudioQualityIOSType; }
// Forward declaration of `AVModeIOSOption` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AVModeIOSOption; }
// Forward declaration of `AVEncodingOption` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AVEncodingOption; }
// Forward declaration of `AVLinearPCMBitDepthKeyIOSType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AVLinearPCMBitDepthKeyIOSType; }
// Forward declaration of `AudioQualityType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { enum class AudioQualityType; }
// Forward declaration of `RecordBackType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { struct RecordBackType; }
// Forward declaration of `PlayBackType` to properly resolve imports.
namespace margelo::nitro::react_native_audio_recorder_player { struct PlayBackType; }

#include <NitroModules/Promise.hpp>
#include <string>
#include <NitroModules/JPromise.hpp>
#include <optional>
#include "AudioSet.hpp"
#include "JAudioSet.hpp"
#include "AudioSourceAndroidType.hpp"
#include "JAudioSourceAndroidType.hpp"
#include "OutputFormatAndroidType.hpp"
#include "JOutputFormatAndroidType.hpp"
#include "AudioEncoderAndroidType.hpp"
#include "JAudioEncoderAndroidType.hpp"
#include "AVEncoderAudioQualityIOSType.hpp"
#include "JAVEncoderAudioQualityIOSType.hpp"
#include "AVModeIOSOption.hpp"
#include "JAVModeIOSOption.hpp"
#include "AVEncodingOption.hpp"
#include "JAVEncodingOption.hpp"
#include "AVLinearPCMBitDepthKeyIOSType.hpp"
#include "JAVLinearPCMBitDepthKeyIOSType.hpp"
#include "AudioQualityType.hpp"
#include "JAudioQualityType.hpp"
#include <unordered_map>
#include <functional>
#include "RecordBackType.hpp"
#include "JFunc_void_RecordBackType.hpp"
#include "JRecordBackType.hpp"
#include "PlayBackType.hpp"
#include "JFunc_void_PlayBackType.hpp"
#include "JPlayBackType.hpp"

namespace margelo::nitro::react_native_audio_recorder_player {

  jni::local_ref<JHybridAudioRecorderPlayerSpec::jhybriddata> JHybridAudioRecorderPlayerSpec::initHybrid(jni::alias_ref<jhybridobject> jThis) {
    return makeCxxInstance(jThis);
  }

  void JHybridAudioRecorderPlayerSpec::registerNatives() {
    registerHybrid({
      makeNativeMethod("initHybrid", JHybridAudioRecorderPlayerSpec::initHybrid),
    });
  }

  size_t JHybridAudioRecorderPlayerSpec::getExternalMemorySize() noexcept {
    static const auto method = javaClassStatic()->getMethod<jlong()>("getMemorySize");
    return method(_javaPart);
  }

  // Properties
  

  // Methods
  std::shared_ptr<Promise<std::string>> JHybridAudioRecorderPlayerSpec::startRecorder(const std::optional<std::string>& uri, const std::optional<AudioSet>& audioSets, std::optional<bool> meteringEnabled) {
    static const auto method = javaClassStatic()->getMethod<jni::local_ref<JPromise::javaobject>(jni::alias_ref<jni::JString> /* uri */, jni::alias_ref<JAudioSet> /* audioSets */, jni::alias_ref<jni::JBoolean> /* meteringEnabled */)>("startRecorder");
    auto __result = method(_javaPart, uri.has_value() ? jni::make_jstring(uri.value()) : nullptr, audioSets.has_value() ? JAudioSet::fromCpp(audioSets.value()) : nullptr, meteringEnabled.has_value() ? jni::JBoolean::valueOf(meteringEnabled.value()) : nullptr);
    return [&]() {
      auto __promise = Promise<std::string>::create();
      __result->cthis()->addOnResolvedListener([=](const jni::alias_ref<jni::JObject>& __boxedResult) {
        auto __result = jni::static_ref_cast<jni::JString>(__boxedResult);
        __promise->resolve(__result->toStdString());
      });
      __result->cthis()->addOnRejectedListener([=](const jni::alias_ref<jni::JThrowable>& __throwable) {
        jni::JniException __jniError(__throwable);
        __promise->reject(std::make_exception_ptr(__jniError));
      });
      return __promise;
    }();
  }
  std::shared_ptr<Promise<std::string>> JHybridAudioRecorderPlayerSpec::pauseRecorder() {
    static const auto method = javaClassStatic()->getMethod<jni::local_ref<JPromise::javaobject>()>("pauseRecorder");
    auto __result = method(_javaPart);
    return [&]() {
      auto __promise = Promise<std::string>::create();
      __result->cthis()->addOnResolvedListener([=](const jni::alias_ref<jni::JObject>& __boxedResult) {
        auto __result = jni::static_ref_cast<jni::JString>(__boxedResult);
        __promise->resolve(__result->toStdString());
      });
      __result->cthis()->addOnRejectedListener([=](const jni::alias_ref<jni::JThrowable>& __throwable) {
        jni::JniException __jniError(__throwable);
        __promise->reject(std::make_exception_ptr(__jniError));
      });
      return __promise;
    }();
  }
  std::shared_ptr<Promise<std::string>> JHybridAudioRecorderPlayerSpec::resumeRecorder() {
    static const auto method = javaClassStatic()->getMethod<jni::local_ref<JPromise::javaobject>()>("resumeRecorder");
    auto __result = method(_javaPart);
    return [&]() {
      auto __promise = Promise<std::string>::create();
      __result->cthis()->addOnResolvedListener([=](const jni::alias_ref<jni::JObject>& __boxedResult) {
        auto __result = jni::static_ref_cast<jni::JString>(__boxedResult);
        __promise->resolve(__result->toStdString());
      });
      __result->cthis()->addOnRejectedListener([=](const jni::alias_ref<jni::JThrowable>& __throwable) {
        jni::JniException __jniError(__throwable);
        __promise->reject(std::make_exception_ptr(__jniError));
      });
      return __promise;
    }();
  }
  std::shared_ptr<Promise<std::string>> JHybridAudioRecorderPlayerSpec::stopRecorder() {
    static const auto method = javaClassStatic()->getMethod<jni::local_ref<JPromise::javaobject>()>("stopRecorder");
    auto __result = method(_javaPart);
    return [&]() {
      auto __promise = Promise<std::string>::create();
      __result->cthis()->addOnResolvedListener([=](const jni::alias_ref<jni::JObject>& __boxedResult) {
        auto __result = jni::static_ref_cast<jni::JString>(__boxedResult);
        __promise->resolve(__result->toStdString());
      });
      __result->cthis()->addOnRejectedListener([=](const jni::alias_ref<jni::JThrowable>& __throwable) {
        jni::JniException __jniError(__throwable);
        __promise->reject(std::make_exception_ptr(__jniError));
      });
      return __promise;
    }();
  }
  std::shared_ptr<Promise<std::string>> JHybridAudioRecorderPlayerSpec::startPlayer(const std::optional<std::string>& uri, const std::optional<std::unordered_map<std::string, std::string>>& httpHeaders) {
    static const auto method = javaClassStatic()->getMethod<jni::local_ref<JPromise::javaobject>(jni::alias_ref<jni::JString> /* uri */, jni::alias_ref<jni::JMap<jni::JString, jni::JString>> /* httpHeaders */)>("startPlayer");
    auto __result = method(_javaPart, uri.has_value() ? jni::make_jstring(uri.value()) : nullptr, httpHeaders.has_value() ? [&]() -> jni::local_ref<jni::JMap<jni::JString, jni::JString>> {
      auto __map = jni::JHashMap<jni::JString, jni::JString>::create(httpHeaders.value().size());
      for (const auto& __entry : httpHeaders.value()) {
        __map->put(jni::make_jstring(__entry.first), jni::make_jstring(__entry.second));
      }
      return __map;
    }() : nullptr);
    return [&]() {
      auto __promise = Promise<std::string>::create();
      __result->cthis()->addOnResolvedListener([=](const jni::alias_ref<jni::JObject>& __boxedResult) {
        auto __result = jni::static_ref_cast<jni::JString>(__boxedResult);
        __promise->resolve(__result->toStdString());
      });
      __result->cthis()->addOnRejectedListener([=](const jni::alias_ref<jni::JThrowable>& __throwable) {
        jni::JniException __jniError(__throwable);
        __promise->reject(std::make_exception_ptr(__jniError));
      });
      return __promise;
    }();
  }
  std::shared_ptr<Promise<std::string>> JHybridAudioRecorderPlayerSpec::stopPlayer() {
    static const auto method = javaClassStatic()->getMethod<jni::local_ref<JPromise::javaobject>()>("stopPlayer");
    auto __result = method(_javaPart);
    return [&]() {
      auto __promise = Promise<std::string>::create();
      __result->cthis()->addOnResolvedListener([=](const jni::alias_ref<jni::JObject>& __boxedResult) {
        auto __result = jni::static_ref_cast<jni::JString>(__boxedResult);
        __promise->resolve(__result->toStdString());
      });
      __result->cthis()->addOnRejectedListener([=](const jni::alias_ref<jni::JThrowable>& __throwable) {
        jni::JniException __jniError(__throwable);
        __promise->reject(std::make_exception_ptr(__jniError));
      });
      return __promise;
    }();
  }
  std::shared_ptr<Promise<std::string>> JHybridAudioRecorderPlayerSpec::pausePlayer() {
    static const auto method = javaClassStatic()->getMethod<jni::local_ref<JPromise::javaobject>()>("pausePlayer");
    auto __result = method(_javaPart);
    return [&]() {
      auto __promise = Promise<std::string>::create();
      __result->cthis()->addOnResolvedListener([=](const jni::alias_ref<jni::JObject>& __boxedResult) {
        auto __result = jni::static_ref_cast<jni::JString>(__boxedResult);
        __promise->resolve(__result->toStdString());
      });
      __result->cthis()->addOnRejectedListener([=](const jni::alias_ref<jni::JThrowable>& __throwable) {
        jni::JniException __jniError(__throwable);
        __promise->reject(std::make_exception_ptr(__jniError));
      });
      return __promise;
    }();
  }
  std::shared_ptr<Promise<std::string>> JHybridAudioRecorderPlayerSpec::resumePlayer() {
    static const auto method = javaClassStatic()->getMethod<jni::local_ref<JPromise::javaobject>()>("resumePlayer");
    auto __result = method(_javaPart);
    return [&]() {
      auto __promise = Promise<std::string>::create();
      __result->cthis()->addOnResolvedListener([=](const jni::alias_ref<jni::JObject>& __boxedResult) {
        auto __result = jni::static_ref_cast<jni::JString>(__boxedResult);
        __promise->resolve(__result->toStdString());
      });
      __result->cthis()->addOnRejectedListener([=](const jni::alias_ref<jni::JThrowable>& __throwable) {
        jni::JniException __jniError(__throwable);
        __promise->reject(std::make_exception_ptr(__jniError));
      });
      return __promise;
    }();
  }
  std::shared_ptr<Promise<std::string>> JHybridAudioRecorderPlayerSpec::seekToPlayer(double time) {
    static const auto method = javaClassStatic()->getMethod<jni::local_ref<JPromise::javaobject>(double /* time */)>("seekToPlayer");
    auto __result = method(_javaPart, time);
    return [&]() {
      auto __promise = Promise<std::string>::create();
      __result->cthis()->addOnResolvedListener([=](const jni::alias_ref<jni::JObject>& __boxedResult) {
        auto __result = jni::static_ref_cast<jni::JString>(__boxedResult);
        __promise->resolve(__result->toStdString());
      });
      __result->cthis()->addOnRejectedListener([=](const jni::alias_ref<jni::JThrowable>& __throwable) {
        jni::JniException __jniError(__throwable);
        __promise->reject(std::make_exception_ptr(__jniError));
      });
      return __promise;
    }();
  }
  std::shared_ptr<Promise<std::string>> JHybridAudioRecorderPlayerSpec::setVolume(double volume) {
    static const auto method = javaClassStatic()->getMethod<jni::local_ref<JPromise::javaobject>(double /* volume */)>("setVolume");
    auto __result = method(_javaPart, volume);
    return [&]() {
      auto __promise = Promise<std::string>::create();
      __result->cthis()->addOnResolvedListener([=](const jni::alias_ref<jni::JObject>& __boxedResult) {
        auto __result = jni::static_ref_cast<jni::JString>(__boxedResult);
        __promise->resolve(__result->toStdString());
      });
      __result->cthis()->addOnRejectedListener([=](const jni::alias_ref<jni::JThrowable>& __throwable) {
        jni::JniException __jniError(__throwable);
        __promise->reject(std::make_exception_ptr(__jniError));
      });
      return __promise;
    }();
  }
  std::shared_ptr<Promise<std::string>> JHybridAudioRecorderPlayerSpec::setPlaybackSpeed(double playbackSpeed) {
    static const auto method = javaClassStatic()->getMethod<jni::local_ref<JPromise::javaobject>(double /* playbackSpeed */)>("setPlaybackSpeed");
    auto __result = method(_javaPart, playbackSpeed);
    return [&]() {
      auto __promise = Promise<std::string>::create();
      __result->cthis()->addOnResolvedListener([=](const jni::alias_ref<jni::JObject>& __boxedResult) {
        auto __result = jni::static_ref_cast<jni::JString>(__boxedResult);
        __promise->resolve(__result->toStdString());
      });
      __result->cthis()->addOnRejectedListener([=](const jni::alias_ref<jni::JThrowable>& __throwable) {
        jni::JniException __jniError(__throwable);
        __promise->reject(std::make_exception_ptr(__jniError));
      });
      return __promise;
    }();
  }
  void JHybridAudioRecorderPlayerSpec::setSubscriptionDuration(double sec) {
    static const auto method = javaClassStatic()->getMethod<void(double /* sec */)>("setSubscriptionDuration");
    method(_javaPart, sec);
  }
  void JHybridAudioRecorderPlayerSpec::addRecordBackListener(const std::function<void(const RecordBackType& /* recordingMeta */)>& callback) {
    static const auto method = javaClassStatic()->getMethod<void(jni::alias_ref<JFunc_void_RecordBackType::javaobject> /* callback */)>("addRecordBackListener_cxx");
    method(_javaPart, JFunc_void_RecordBackType_cxx::fromCpp(callback));
  }
  void JHybridAudioRecorderPlayerSpec::removeRecordBackListener() {
    static const auto method = javaClassStatic()->getMethod<void()>("removeRecordBackListener");
    method(_javaPart);
  }
  void JHybridAudioRecorderPlayerSpec::addPlayBackListener(const std::function<void(const PlayBackType& /* playbackMeta */)>& callback) {
    static const auto method = javaClassStatic()->getMethod<void(jni::alias_ref<JFunc_void_PlayBackType::javaobject> /* callback */)>("addPlayBackListener_cxx");
    method(_javaPart, JFunc_void_PlayBackType_cxx::fromCpp(callback));
  }
  void JHybridAudioRecorderPlayerSpec::removePlayBackListener() {
    static const auto method = javaClassStatic()->getMethod<void()>("removePlayBackListener");
    method(_javaPart);
  }
  std::string JHybridAudioRecorderPlayerSpec::mmss(double secs) {
    static const auto method = javaClassStatic()->getMethod<jni::local_ref<jni::JString>(double /* secs */)>("mmss");
    auto __result = method(_javaPart, secs);
    return __result->toStdString();
  }
  std::string JHybridAudioRecorderPlayerSpec::mmssss(double milisecs) {
    static const auto method = javaClassStatic()->getMethod<jni::local_ref<jni::JString>(double /* milisecs */)>("mmssss");
    auto __result = method(_javaPart, milisecs);
    return __result->toStdString();
  }

} // namespace margelo::nitro::react_native_audio_recorder_player
