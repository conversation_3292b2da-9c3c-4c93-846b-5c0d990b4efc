import React, { useEffect, useRef, useState } from "react";
import { ActivityIndicator, Image, StyleSheet, TouchableOpacity, View } from "react-native";
import { Text } from "react-native-paper";


/**
 * 图片卡片组件，用于显示带有标题、副标题、内容和图片的卡片
 * 支持横版和竖版图片的显示，竖版图片会自动旋转90度以横版方式展示
 * 
 * @component
 * @param {Object} props - 组件属性
 * @param {string} [props.imageURL] - 图片URL，如果未提供则使用默认图片
 * @param {string} [props.title] - 卡片标题
 * @param {string} [props.subtitle] - 卡片副标题
 * @param {string} [props.content] - 卡片内容文本
 * @param {Function} [props.onPress] - 点击卡片时的回调函数
 * @param {Function} [props.onLongPress] - 长按卡片时的回调函数
 * @param {Object} props.theme - React Native Paper主题对象
 * @returns {React.ReactElement} 渲染的卡片组件
 */
const ImageCard = ({ imageURL, title, subtitle, content, onPress, onLongPress, theme }) => {
    const containerRef = useRef(null);
    const [containerWidth, setContainerWidth] = useState(0);
    const [imageSize, setImageSize] = useState({ width: 0, height: 0 });
    const [imageLayout, setImageLayout] = useState({ width: 0, height: 0, transform: [] });
    const [isImageLoading, setIsImageLoading] = useState(true);
    const defaultImage = require("../assets/images/new-card.png");

    // 当容器宽度变化时记录
    const onContainerLayout = (event) => {
        const { width } = event.nativeEvent.layout;
        setContainerWidth(width);
    };

    // 获取图片尺寸
    useEffect(() => {
        if (imageURL) {
            setIsImageLoading(true);
            Image.getSize(
                imageURL,
                (width, height) => {
                    setImageSize({ width, height });
                    setIsImageLoading(false);
                },
                (error) => {
                    console.error("获取图片尺寸失败:", error);
                    setIsImageLoading(false);
                }
            );
        } else {
            // 使用默认图片
            const defaultImageSource = Image.resolveAssetSource(defaultImage);
            setImageSize({ width: defaultImageSource.width, height: defaultImageSource.height });
            setIsImageLoading(false);
        }
    }, [imageURL]);

    // 计算图片布局
    useEffect(() => {
        if (containerWidth > 0 && imageSize.width > 0 && imageSize.height > 0) {
            const imageWidth = imageSize.width;
            const imageHeight = imageSize.height;
            const imageAspectRatio = imageWidth / imageHeight;

            // 判断图片方向
            const isImageHorizontal = imageWidth >= imageHeight;

            if (isImageHorizontal) {
                // 横版图片，直接等比例缩放
                const scaledHeight = containerWidth / imageAspectRatio;
                setImageLayout({
                    width: containerWidth,
                    height: scaledHeight,
                    transform: []
                });
            } else {
                // 竖版图片，也按横版处理
                // 但需要调整容器高度，使其能完整显示旋转后的图片
                const scaledWidth = containerWidth;
                // 旋转后的高度应该是原始宽度与高度比例的反比
                const scaledHeight = scaledWidth * (imageWidth / imageHeight);

                setImageLayout({
                    width: scaledWidth,
                    height: scaledHeight,
                    transform: [
                        { rotate: "90deg" },
                        { scale: imageHeight / imageWidth }
                    ]
                });
            }
        }
    }, [containerWidth, imageSize]);

    return (
        <View
            ref={containerRef}
            onLayout={onContainerLayout}
            style={{
                ...styles.container,
                backgroundColor: theme.colors.elevation.level3,
                borderColor: theme.colors.onSurfaceVariant,
                paddingTop: title ? styles.container.paddingTop : 3,
                paddingBottom: content ? styles.container.paddingBottom : 3,
                shadowColor: theme.colors.onSurfaceVariant,
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.23,
                shadowRadius: 2.62,
                elevation: 4,
            }}
        >
            <TouchableOpacity onPress={() => { onPress && onPress(); }} onLongPress={() => { onLongPress && onLongPress(); }}>
                {title && <Text variant="titleLarge" style={styles.title}>{title}</Text>}
                {subtitle && <Text variant="titleSmall" style={styles.subtitle}>{subtitle}</Text>}
                <View
                    style={{
                        ...styles.imageContainer,
                        height: imageLayout.height || 200,
                        borderWidth: 1,
                        borderColor: theme.colors.secondaryContainer,
                    }}
                >
                    {isImageLoading && (
                        <View style={styles.loadingContainer}>
                            <ActivityIndicator size="large" color={theme.colors.primary} />
                        </View>
                    )}
                    {!isImageLoading && (
                        <Image
                            source={imageURL ? { uri: imageURL } : defaultImage}
                            style={{
                                width: imageLayout.width,
                                height: imageLayout.height,
                                transform: imageLayout.transform,
                            }}
                            resizeMode="contain"
                        />
                    )}
                </View>
                {content && <Text variant="bodyMedium" style={styles.content}>{content}</Text>}
            </TouchableOpacity>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        marginBottom: 10,
        borderWidth: 1,
        paddingTop: 10,
        paddingBottom: 10,
        borderRadius: 6,
        shadowColor: "#000",
    },
    title: {
        marginLeft: 20,
        paddingBottom: 5,
    },
    subtitle: {
        marginLeft: 20,
        paddingBottom: 5,
    },
    content: {
        marginTop: 5,
        marginLeft: 20,
    },
    imageContainer: {
        flex: 1,
        minHeight: 200,
        position: "relative",
        overflow: "hidden",
        backgroundColor: "#f5f5f5",
    },
    loadingContainer: {
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: "center",
        alignItems: "center",
        zIndex: 1,
    },
});

export default ImageCard;
