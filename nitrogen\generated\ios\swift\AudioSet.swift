///
/// AudioSet.swift
/// This file was generated by nitrogen. DO NOT MODIFY THIS FILE.
/// https://github.com/mrousavy/nitro
/// Copyright © 2025 Marc <PERSON>avy @ Margelo
///

import NitroModules

/**
 * Represents an instance of `AudioSet`, backed by a C++ struct.
 */
public typealias AudioSet = margelo.nitro.react_native_audio_recorder_player.AudioSet

public extension AudioSet {
  private typealias bridge = margelo.nitro.react_native_audio_recorder_player.bridge.swift

  /**
   * Create a new instance of `AudioSet`.
   */
  init(AudioSourceAndroid: AudioSourceAndroidType?, OutputFormatAndroid: OutputFormatAndroidType?, AudioEncoderAndroid: AudioEncoderAndroidType?, AVEncoderAudioQualityKeyIOS: AVEncoderAudioQualityIOSType?, AVModeIOS: AVModeIOSOption?, AVEncodingOptionIOS: AVEncodingOption?, AVFormatIDKeyIOS: AVEncodingOption?, AVNumberOfChannelsKeyIOS: Double?, AVLinearPCMBitDepthKeyIOS: AVLinearPCMBitDepthKeyIOSType?, AVLinearPCMIsBigEndianKeyIOS: Bool?, AVLinearPCMIsFloatKeyIOS: Bool?, AVLinearPCMIsNonInterleavedIOS: Bool?, AVSampleRateKeyIOS: Double?, AudioQuality: AudioQualityType?, AudioChannels: Double?, AudioSamplingRate: Double?, AudioEncodingBitRate: Double?, IncludeBase64: Bool?) {
    self.init({ () -> bridge.std__optional_AudioSourceAndroidType_ in
      if let __unwrappedValue = AudioSourceAndroid {
        return bridge.create_std__optional_AudioSourceAndroidType_(__unwrappedValue)
      } else {
        return .init()
      }
    }(), { () -> bridge.std__optional_OutputFormatAndroidType_ in
      if let __unwrappedValue = OutputFormatAndroid {
        return bridge.create_std__optional_OutputFormatAndroidType_(__unwrappedValue)
      } else {
        return .init()
      }
    }(), { () -> bridge.std__optional_AudioEncoderAndroidType_ in
      if let __unwrappedValue = AudioEncoderAndroid {
        return bridge.create_std__optional_AudioEncoderAndroidType_(__unwrappedValue)
      } else {
        return .init()
      }
    }(), { () -> bridge.std__optional_AVEncoderAudioQualityIOSType_ in
      if let __unwrappedValue = AVEncoderAudioQualityKeyIOS {
        return bridge.create_std__optional_AVEncoderAudioQualityIOSType_(__unwrappedValue)
      } else {
        return .init()
      }
    }(), { () -> bridge.std__optional_AVModeIOSOption_ in
      if let __unwrappedValue = AVModeIOS {
        return bridge.create_std__optional_AVModeIOSOption_(__unwrappedValue)
      } else {
        return .init()
      }
    }(), { () -> bridge.std__optional_AVEncodingOption_ in
      if let __unwrappedValue = AVEncodingOptionIOS {
        return bridge.create_std__optional_AVEncodingOption_(__unwrappedValue)
      } else {
        return .init()
      }
    }(), { () -> bridge.std__optional_AVEncodingOption_ in
      if let __unwrappedValue = AVFormatIDKeyIOS {
        return bridge.create_std__optional_AVEncodingOption_(__unwrappedValue)
      } else {
        return .init()
      }
    }(), { () -> bridge.std__optional_double_ in
      if let __unwrappedValue = AVNumberOfChannelsKeyIOS {
        return bridge.create_std__optional_double_(__unwrappedValue)
      } else {
        return .init()
      }
    }(), { () -> bridge.std__optional_AVLinearPCMBitDepthKeyIOSType_ in
      if let __unwrappedValue = AVLinearPCMBitDepthKeyIOS {
        return bridge.create_std__optional_AVLinearPCMBitDepthKeyIOSType_(__unwrappedValue)
      } else {
        return .init()
      }
    }(), { () -> bridge.std__optional_bool_ in
      if let __unwrappedValue = AVLinearPCMIsBigEndianKeyIOS {
        return bridge.create_std__optional_bool_(__unwrappedValue)
      } else {
        return .init()
      }
    }(), { () -> bridge.std__optional_bool_ in
      if let __unwrappedValue = AVLinearPCMIsFloatKeyIOS {
        return bridge.create_std__optional_bool_(__unwrappedValue)
      } else {
        return .init()
      }
    }(), { () -> bridge.std__optional_bool_ in
      if let __unwrappedValue = AVLinearPCMIsNonInterleavedIOS {
        return bridge.create_std__optional_bool_(__unwrappedValue)
      } else {
        return .init()
      }
    }(), { () -> bridge.std__optional_double_ in
      if let __unwrappedValue = AVSampleRateKeyIOS {
        return bridge.create_std__optional_double_(__unwrappedValue)
      } else {
        return .init()
      }
    }(), { () -> bridge.std__optional_AudioQualityType_ in
      if let __unwrappedValue = AudioQuality {
        return bridge.create_std__optional_AudioQualityType_(__unwrappedValue)
      } else {
        return .init()
      }
    }(), { () -> bridge.std__optional_double_ in
      if let __unwrappedValue = AudioChannels {
        return bridge.create_std__optional_double_(__unwrappedValue)
      } else {
        return .init()
      }
    }(), { () -> bridge.std__optional_double_ in
      if let __unwrappedValue = AudioSamplingRate {
        return bridge.create_std__optional_double_(__unwrappedValue)
      } else {
        return .init()
      }
    }(), { () -> bridge.std__optional_double_ in
      if let __unwrappedValue = AudioEncodingBitRate {
        return bridge.create_std__optional_double_(__unwrappedValue)
      } else {
        return .init()
      }
    }(), { () -> bridge.std__optional_bool_ in
      if let __unwrappedValue = IncludeBase64 {
        return bridge.create_std__optional_bool_(__unwrappedValue)
      } else {
        return .init()
      }
    }())
  }

  var AudioSourceAndroid: AudioSourceAndroidType? {
    @inline(__always)
    get {
      return self.__AudioSourceAndroid.has_value() ? self.__AudioSourceAndroid.pointee : nil
    }
    @inline(__always)
    set {
      self.__AudioSourceAndroid = { () -> bridge.std__optional_AudioSourceAndroidType_ in
        if let __unwrappedValue = newValue {
          return bridge.create_std__optional_AudioSourceAndroidType_(__unwrappedValue)
        } else {
          return .init()
        }
      }()
    }
  }
  
  var OutputFormatAndroid: OutputFormatAndroidType? {
    @inline(__always)
    get {
      return self.__OutputFormatAndroid.has_value() ? self.__OutputFormatAndroid.pointee : nil
    }
    @inline(__always)
    set {
      self.__OutputFormatAndroid = { () -> bridge.std__optional_OutputFormatAndroidType_ in
        if let __unwrappedValue = newValue {
          return bridge.create_std__optional_OutputFormatAndroidType_(__unwrappedValue)
        } else {
          return .init()
        }
      }()
    }
  }
  
  var AudioEncoderAndroid: AudioEncoderAndroidType? {
    @inline(__always)
    get {
      return self.__AudioEncoderAndroid.has_value() ? self.__AudioEncoderAndroid.pointee : nil
    }
    @inline(__always)
    set {
      self.__AudioEncoderAndroid = { () -> bridge.std__optional_AudioEncoderAndroidType_ in
        if let __unwrappedValue = newValue {
          return bridge.create_std__optional_AudioEncoderAndroidType_(__unwrappedValue)
        } else {
          return .init()
        }
      }()
    }
  }
  
  var AVEncoderAudioQualityKeyIOS: AVEncoderAudioQualityIOSType? {
    @inline(__always)
    get {
      return self.__AVEncoderAudioQualityKeyIOS.has_value() ? self.__AVEncoderAudioQualityKeyIOS.pointee : nil
    }
    @inline(__always)
    set {
      self.__AVEncoderAudioQualityKeyIOS = { () -> bridge.std__optional_AVEncoderAudioQualityIOSType_ in
        if let __unwrappedValue = newValue {
          return bridge.create_std__optional_AVEncoderAudioQualityIOSType_(__unwrappedValue)
        } else {
          return .init()
        }
      }()
    }
  }
  
  var AVModeIOS: AVModeIOSOption? {
    @inline(__always)
    get {
      return self.__AVModeIOS.value
    }
    @inline(__always)
    set {
      self.__AVModeIOS = { () -> bridge.std__optional_AVModeIOSOption_ in
        if let __unwrappedValue = newValue {
          return bridge.create_std__optional_AVModeIOSOption_(__unwrappedValue)
        } else {
          return .init()
        }
      }()
    }
  }
  
  var AVEncodingOptionIOS: AVEncodingOption? {
    @inline(__always)
    get {
      return self.__AVEncodingOptionIOS.value
    }
    @inline(__always)
    set {
      self.__AVEncodingOptionIOS = { () -> bridge.std__optional_AVEncodingOption_ in
        if let __unwrappedValue = newValue {
          return bridge.create_std__optional_AVEncodingOption_(__unwrappedValue)
        } else {
          return .init()
        }
      }()
    }
  }
  
  var AVFormatIDKeyIOS: AVEncodingOption? {
    @inline(__always)
    get {
      return self.__AVFormatIDKeyIOS.value
    }
    @inline(__always)
    set {
      self.__AVFormatIDKeyIOS = { () -> bridge.std__optional_AVEncodingOption_ in
        if let __unwrappedValue = newValue {
          return bridge.create_std__optional_AVEncodingOption_(__unwrappedValue)
        } else {
          return .init()
        }
      }()
    }
  }
  
  var AVNumberOfChannelsKeyIOS: Double? {
    @inline(__always)
    get {
      return self.__AVNumberOfChannelsKeyIOS.value
    }
    @inline(__always)
    set {
      self.__AVNumberOfChannelsKeyIOS = { () -> bridge.std__optional_double_ in
        if let __unwrappedValue = newValue {
          return bridge.create_std__optional_double_(__unwrappedValue)
        } else {
          return .init()
        }
      }()
    }
  }
  
  var AVLinearPCMBitDepthKeyIOS: AVLinearPCMBitDepthKeyIOSType? {
    @inline(__always)
    get {
      return self.__AVLinearPCMBitDepthKeyIOS.has_value() ? self.__AVLinearPCMBitDepthKeyIOS.pointee : nil
    }
    @inline(__always)
    set {
      self.__AVLinearPCMBitDepthKeyIOS = { () -> bridge.std__optional_AVLinearPCMBitDepthKeyIOSType_ in
        if let __unwrappedValue = newValue {
          return bridge.create_std__optional_AVLinearPCMBitDepthKeyIOSType_(__unwrappedValue)
        } else {
          return .init()
        }
      }()
    }
  }
  
  var AVLinearPCMIsBigEndianKeyIOS: Bool? {
    @inline(__always)
    get {
      return self.__AVLinearPCMIsBigEndianKeyIOS.value
    }
    @inline(__always)
    set {
      self.__AVLinearPCMIsBigEndianKeyIOS = { () -> bridge.std__optional_bool_ in
        if let __unwrappedValue = newValue {
          return bridge.create_std__optional_bool_(__unwrappedValue)
        } else {
          return .init()
        }
      }()
    }
  }
  
  var AVLinearPCMIsFloatKeyIOS: Bool? {
    @inline(__always)
    get {
      return self.__AVLinearPCMIsFloatKeyIOS.value
    }
    @inline(__always)
    set {
      self.__AVLinearPCMIsFloatKeyIOS = { () -> bridge.std__optional_bool_ in
        if let __unwrappedValue = newValue {
          return bridge.create_std__optional_bool_(__unwrappedValue)
        } else {
          return .init()
        }
      }()
    }
  }
  
  var AVLinearPCMIsNonInterleavedIOS: Bool? {
    @inline(__always)
    get {
      return self.__AVLinearPCMIsNonInterleavedIOS.value
    }
    @inline(__always)
    set {
      self.__AVLinearPCMIsNonInterleavedIOS = { () -> bridge.std__optional_bool_ in
        if let __unwrappedValue = newValue {
          return bridge.create_std__optional_bool_(__unwrappedValue)
        } else {
          return .init()
        }
      }()
    }
  }
  
  var AVSampleRateKeyIOS: Double? {
    @inline(__always)
    get {
      return self.__AVSampleRateKeyIOS.value
    }
    @inline(__always)
    set {
      self.__AVSampleRateKeyIOS = { () -> bridge.std__optional_double_ in
        if let __unwrappedValue = newValue {
          return bridge.create_std__optional_double_(__unwrappedValue)
        } else {
          return .init()
        }
      }()
    }
  }
  
  var AudioQuality: AudioQualityType? {
    @inline(__always)
    get {
      return self.__AudioQuality.value
    }
    @inline(__always)
    set {
      self.__AudioQuality = { () -> bridge.std__optional_AudioQualityType_ in
        if let __unwrappedValue = newValue {
          return bridge.create_std__optional_AudioQualityType_(__unwrappedValue)
        } else {
          return .init()
        }
      }()
    }
  }
  
  var AudioChannels: Double? {
    @inline(__always)
    get {
      return self.__AudioChannels.value
    }
    @inline(__always)
    set {
      self.__AudioChannels = { () -> bridge.std__optional_double_ in
        if let __unwrappedValue = newValue {
          return bridge.create_std__optional_double_(__unwrappedValue)
        } else {
          return .init()
        }
      }()
    }
  }
  
  var AudioSamplingRate: Double? {
    @inline(__always)
    get {
      return self.__AudioSamplingRate.value
    }
    @inline(__always)
    set {
      self.__AudioSamplingRate = { () -> bridge.std__optional_double_ in
        if let __unwrappedValue = newValue {
          return bridge.create_std__optional_double_(__unwrappedValue)
        } else {
          return .init()
        }
      }()
    }
  }
  
  var AudioEncodingBitRate: Double? {
    @inline(__always)
    get {
      return self.__AudioEncodingBitRate.value
    }
    @inline(__always)
    set {
      self.__AudioEncodingBitRate = { () -> bridge.std__optional_double_ in
        if let __unwrappedValue = newValue {
          return bridge.create_std__optional_double_(__unwrappedValue)
        } else {
          return .init()
        }
      }()
    }
  }
  
  var IncludeBase64: Bool? {
    @inline(__always)
    get {
      return self.__IncludeBase64.value
    }
    @inline(__always)
    set {
      self.__IncludeBase64 = { () -> bridge.std__optional_bool_ in
        if let __unwrappedValue = newValue {
          return bridge.create_std__optional_bool_(__unwrappedValue)
        } else {
          return .init()
        }
      }()
    }
  }
}
