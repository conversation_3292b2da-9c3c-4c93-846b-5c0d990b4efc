# LowCarbonManagement开发记录

## 命令行

```bat
# 清除Metro缓存, 重新编译JS代码
yarn start --reset-cache
# 清除gradle缓存
cd android && ./gradlew.bat clean
```



## 库的安装配置和使用

### 安装UI库[`react-native-paper`](https://callstack.github.io/react-native-paper/docs/guides/getting-started/)

```
npm install --save react-native-paper
npm install --save react-native-safe-area-context
npm install --save react-native-vector-icons
```

#### 配置`babel.config.js`

```js
module.exports = {
    presets: ["module:metro-react-native-babel-preset"],
    env: {
        production: {
            plugins: ["react-native-paper/babel"],
        },
    },
};
```

需要安装[`metro-react-native-babel-preset`](https://www.npmjs.com/package/metro-react-native-babel-preset):

```shell
npm i metro-react-native-babel-preset --save-dev
```

注意: RN0.76的`babel.config.js`应该配置为`presets: ["module:@react-native/babel-preset"]`



#### 配置[`react-native-vector-icons`](https://github.com/oblador/react-native-vector-icons#installation)

编辑android/app/build.gradle, 添加:

```gradle
apply from: file("../../node_modules/react-native-vector-icons/fonts.gradle")
```

编辑完成后重置缓存, 启动yarn:

```shell
yarn start --reset-cache
```

查看图标:

推荐: https://pictogrammers.com/library/mdi/

https://oblador.github.io/react-native-vector-icons/

#### **`useTheme`**

遇到一起奇怪的报错:

```jsx
const WorkbenchAddUser = ({ navigation }) => {
    const theme = useTheme();
    const { bottom, left, right } = useSafeAreaInsets();
    const height = theme.isV3 ? 80 : 56;
    ...

ERROR  TypeError: 0, _$$_REQUIRE(_dependencyMap[8], "react-native-paper").useTheme is not a function (it is undefined)
```

把`const theme = useTheme();`放到非首行则正常:

```jsx
const WorkbenchAddUser = ({ navigation }) => {
    const { bottom, left, right } = useSafeAreaInsets();
    const theme = useTheme();
    const height = theme.isV3 ? 80 : 56;
    ...
```

其原因应该是useSafeAreaInsets造成的.

注意对于RN0.75以上版本， 会弹出警告:

```
ERROR  Warning: A props object containing a "key" prop is being spread into JSX:
  let props = {key: someKey, route: ..., borderless: ..., centered: ..., rippleColor: ..., onPress: ..., onLongPress: ..., testID: ..., accessibilityLabel: ..., accessibilityRole: ..., accessibilityState: ..., style: ..., children: ...};
  <Touchable {...props} />
React keys must be passed directly to JSX without using spread:
  let props = {route: ..., borderless: ..., centered: ..., rippleColor: ..., onPress: ..., onLongPress: ..., testID: ..., accessibilityLabel: ..., accessibilityRole: ..., accessibilityState: ..., style: ..., children: ...};
  <Touchable key={someKey} {...props} />
    in BottomNavigation.Bar (created by SafeAreaInsetsContext)
    ...
```

修复方法:

打开`.\node_modules\react-native-paper\src\components\BottomNavigation\BottomNavigationBar.tsx`, 按如下方式修改:

```diff
diff --git a/src/components/BottomNavigation/BottomNavigationBar.tsx b/src/components/BottomNavigation/BottomNavigationBar.tsx
index 0bfe303bfb443396ede776726faa0f8ba32752cd..a59ae061dcaa51f026e78de0ccfd536318dd0aee 100644
--- a/src/components/BottomNavigation/BottomNavigationBar.tsx
+++ b/src/components/BottomNavigation/BottomNavigationBar.tsx
@@ -360,7 +360,7 @@ const BottomNavigationBar = <Route extends BaseRoute>({
   navigationState,
   renderIcon,
   renderLabel,
-  renderTouchable = (props: TouchableProps<Route>) => <Touchable {...props} />,
+  renderTouchable = ({ key, ...props }: TouchableProps<Route>) => (
+    <Touchable key={key} {...props} />
+  ),
   getLabelText = ({ route }: { route: Route }) => route.title,
   getBadge = ({ route }: { route: Route }) => route.badge,
   getColor = ({ route }: { route: Route }) => route.color,
```

参考: https://github.com/callstack/react-native-paper/issues/4401

Fixed: https://github.com/callstack/react-native-paper/pull/4494



### 安装[`react-navigation`](https://github.com/react-navigation/react-navigation)

https://reactnavigation.org/

```
npm install @react-navigation/native
npm install @react-navigation/stack
npm i @react-navigation/bottom-tabs
npm i @react-navigation/drawer
```

在`/android/app/build.gradle`添加

```gradle
apply from:("../../node_modules/react-native-vector-icons/fonts.gradle");
```



### 安装[`react-native-gesture-handler`](https://docs.swmansion.com/react-native-gesture-handler/docs/fundamentals/installation)

```
npm install --save react-native-gesture-handler
```



### 安装`react-native-reanimated`

https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/getting-started/

```
npm install react-native-reanimated
```

配置`babel.config.js`, 添加`'react-native-reanimated/plugin'`插件:

```js
module.exports = {
    presets: ["module:metro-react-native-babel-preset"],
    env: {
        production: {
            plugins: ["react-native-paper/babel"],
        },
    },
    plugins: ["react-native-reanimated/plugin"],
};
```

注意不是配置在env的plugins上, 否则报错`DrawerViewBase  Cannot read property 'isConfigured' of undefined`.



### 安装`react-native-status-bar-height`

```
npm install react-native-status-bar-height
```

### 安装`react-native-screens`

```
npm install react-native-screens
```



### Eslint

https://deku.posstree.com/en/react-native/eslint-prettier-husky-lint-staged/

运行eslint

```shell
npm run lint
npx eslint ./src/**/*.jsx --fix
npx eslint ./src/**/*.js --fix
```



### 安装多国语言`i18next`

https://github.com/i18next/i18next

```
npm install react-i18next i18next --save
```

**使用**

```jsx
// ./src/translations/cn/index.js
// 翻译文件
export { default as screenStart } from "./screen_start.json";
export { default as screenLogin } from "./screen_login.json";
export { default as validators } from "./validators.json";

// ./src/translations/index.js
// 定义翻译
import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import * as cn from "./cn";
import * as en from "./en";
const ns = Object.keys(cn);
export const defaultNS = ns[0];
void i18n.use(initReactI18next).init({
    ns,
    defaultNS,
    resources: {
        cn,
        en,
    },
    lng: "cn",
    fallbackLng: "cn",
    interpolation: {
        escapeValue: false, // not needed for react as it escapes by default
    },
    compatibilityJSON: "v3",
});
export default i18n;

// ./src/screens/LoginScreen.js
// jsx中使用
import { useTranslation } from "react-i18next";
const { t } = useTranslation(["screenLogin"]);
<TextInput label={t("screenLogin:accountInputTextLabel")} />

// ./src/helpers/nameValidator.js
// js中使用
import i18n from "../translations";
const t = i18n.t;
t("validators:name.nullInputErrorHint");
```



### 安装MMKV本地存储

Github: https://github.com/mrousavy/react-native-mmkv

```shell
npm i react-native-mmkv
# yarn add react-native-mmkv
```

**使用**

```js
// ./src/services/local-storage/index.js
import { MMKV } from "react-native-mmkv";
export default new MMKV();

// ./src/screens/LoginScreen.js
import LocalStorage from "../services/local-storage";
LocalStorage.getString("account");           // get
LocalStorage.set("account", account.value);  // set
LocalStorage.delete("account");              // delete
```

附: [react-native-mmkv  vs  react-native-mmkv-storage](https://github.com/mrousavy/react-native-mmkv/issues/100)

如果报错:

```
* What went wrong:
Execution failed for task ':react-native-mmkv:buildCMakeDebug[x86_64]'.
> com.android.ide.common.process.ProcessException: ninja: Entering directory `F:\Zixv\lcmp.client\LowCarbonManagement\node_modules\react-native-mmkv\android\.cxx\Debug\5r5d4l1g\x86_64'

  C++ build system [build] failed while executing:
      @echo off
      "D:\\Dev\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe" ^
        -C ^
        "F:\\Zixv\\lcmp.client\\LowCarbonManagement\\node_modules\\react-native-mmkv\\android\\.cxx\\Debug\\5r5d4l1g\\x86_64" ^
        reactnativemmkv
    from F:\Zixv\lcmp.client\LowCarbonManagement\node_modules\react-native-mmkv\android
  ninja: error: exception: 0xC0000005
  ninja: warning: minidump created: C:\Users\<USER>\AppData\Local\Temp\\ninja_crash_dump_17624.dmp
```

尝试通过以下方式解决:

1. 重装mmkv: `npm uninstall react-native-mmkv`, `npm i react-native-mmkv`
2. 运行doctor诊断`npx react-native doctor`

编译一定要先清缓:

1. `cd .\android\ && ./gradlew.bat clean`
2. `yarn start --reset-cache`

最重要的, 编译时一定要连上设备!!!



### 安装Zustand

Github: https://github.com/pmndrs/zustand

文档: https://docs.pmnd.rs/zustand/getting-started/introduction

```shell
npm install zustand
```

**使用**

1. 包装

   ```js
   import { create } from "zustand";
   import { persist, createJSONStorage } from "zustand/middleware";
   import LocalStorage from "../local-storage";

   // 创建一个Zustand状态管理
   const createZustandState = (...stateDefinitions) => {
       return create()((set, get) => stateDefinitions.map(stateDef => stateDef(set, get)).reduce(((r, c) => Object.assign(r, c)), {}),);
   };

   // 使用zustand, 同时存储到MMKV
   const ZustandMMKVStorage = {
       setItem: (name, value) => {
           return LocalStorage.set(name, value);
       },
       getItem: (name) => {
           const value = LocalStorage.getString(name);
           return value ?? null;
       },
       removeItem: (name) => {
           return LocalStorage.delete(name);
       },
   };
   // 创建一个Zustand状态管理, 并且存储到MMKV
   const createZustandStoredState = (storeName, ...stateDefinitions) => {
       return create()(
           persist((set, get) => stateDefinitions.map(stateDef => stateDef(set, get)).reduce(((r, c) => Object.assign(r, c)), {}),
               {
                   name: storeName,
                   storage: createJSONStorage(() => ZustandMMKVStorage),
               },
           ),);
   };
   ```

2. 使用

   ```js
   // 定义状态对象
   const userStatDefinition = (set, get) => ({
       name:        { value: "", error: "" },
       account:     { value: "", error: "" },
       password:    { value: "", error: "" },
       setName:     (name, errMsg) => set({ name:     { value: name, error: errMsg }}),
       setAccount:  (acc,  errMsg) => set({ account:  { value: acc,  error: errMsg }}),
       setPassword: (pwd,  errMsg) => set({ password: { value: pwd,  error: errMsg }}),
       clearUserState: () => set({
           name: { value: "", error: "" },
           account: { value: "", error: "" },
           password: { value: "", error: "" },
       }),
   });
   
   // 创建zustand状态管理对象
   const userStoredState = createZustandState(userStatDefinition); // 不使用本地存储
   const userStoredState = createZustandStoredState("user-state", userStatDefinition); // 存储到本地
   
   // 使用, 注意要在函数组件中使用
   // 注意, 在Zustand5中, 必需使用useShallow, 否则报错: Maximum update depth exceeded.
   // 首先定义钩子
   const userName = userStoredState(useShallow(state => state.name));
   const userAccount = userStoredState(useShallow(state => state.account));
   const userPassword = userStoredState(useShallow(state => state.password));
   const setUserName = userStoredState(useShallow(state => state.setName));
   const setUserAccount = userStoredState(useShallow(state => state.setAccount));
   const setUserPassword = userStoredState(useShallow(state => state.setPassword));
   // 或者通过解构
   const [userName, userAccount, userPassword] = userStoredState(useShallow(state) => [state.name, state.account, state.password]));
   const [setUserName, setUserAccount, setUserPassword] = userStoredState(useShallow(state) => [state.setName, state.setAccount, state.setPassword]));
   
   // 读取状态值
   userName.value;
   userAccount.value;
   userPassword.value;
   // 设置新的状态值
   setUserName(userName.value, nameError);
   setUserAccount(userAccount.value, accountError);
   setUserPassword(userPassword.value, passwordError);
   ```




### 安装HTTP客户端ky

https://www.npmjs.com/package/ky

https://github.com/sindresorhus/ky

```shell
npm i ky
```

#### ky文档翻译

https://github.com/sindresorhus/ky

##### `ky(input, options?)`

`input`, `options`与`fetch`的参数相同, 但是`credentials`默认是`same-origin`, ky相比fetch添加了更多选项.

此方法返回一个具有[Body方法](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API/Using_Fetch#body)的[响应对象](https://developer.mozilla.org/en-US/docs/Web/API/Response), 因此可以直接调用`ky.get(input).json()`而不需要先等待Response. 通过这种方式调用时, 会根据所使用的body方法设置恰当的Accept标头(注: Response.json()是一个body方法). 与`window.Fetch()`不同的是, ky的这些方法, 如果响应状态码不是2xx时就会抛出一个HTTPError错误. 同时, 如果响应体为空, 或者响应状态码为204(No Content), `.json()`会返回一个空串而不是抛出解析错误.

ky包含`ky.get`, `ky.put`, `ky.post`, `ky.patch`, `ky.head`, `ky.delete`几种变体, 用法与`ky`一样, 只是将`options.method`设置成相应的方法名称.
`**options`对象具体的选项**

1. `method`: 字符串. 默认GET, 可以是GET, POST, PUT, PATCH, HEAD, DELETE. 为了与服务器相一致, 其内部转换成大写.

2. `json`: 对象类型, 或者任何可以被JSON.stringfy()接受的值.

3. `searchParams`: 字符串. 或者可以被[`URLSearchParams()`](https://developer.mozilla.org/en-US/docs/Web/API/URLSearchParams/URLSearchParams)接受的值, 默认"". 用于URL中的搜索串.

4. prefixUrl: 字符串或者URL类型. 拼接到`input`的一个前缀, prefixUrl末尾的斜杠`/`如果没用就会自动添加. 但如果使用了此选项, 则`input`参数**不能**以斜杠`/`开头.

5. retry: 整数或对象. 对http请求重试的配置. 此配置对象包括如下选项:

   * limit: 最大重试次数, 整数, 默认2.

   * methods: 字符串数组, 需要重试的http方法, 允许下列字符串: get, put, head, delete, options, trace.

   * statusCodes: 整数数组, 需要重试的响应码, 默认408 413 429 500 502 503 504.

   * maxRetryAfter: undefined或整数, 最大的[Retry-After](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Retry-After)标头时间, 默认`undefined`表示使用`options.timeout`. 如果`Retry-After`标头大于maxRetryAfter, 就会取消此请求(意思是客户端不能接受服务端的重试时间从而主动放弃重试).

   * backoffLimit: undefined或者整数, 每次重试的最大延迟毫秒数. 如果固定1秒重试, 就设置为1000. 默认下是`0.3 * (2 ** (attemptCount - 1)) * 1000`, 与`delay`计算公式一样.

   * delay: 函数类型, 用于计算每次重试之间的时长, 此函数接受重试次数作为参数, 开始于1, 返回的是毫秒数, 默认值, attemptCount => 0.3 * (2 ** (attemptCount - 1)) * 1000, 即300-600-1200这样增长.

     ```js
     import ky from 'ky';
     const json = await ky('https://example.com', {
     	retry: {
     		limit: 10,
     		methods: ['get'],
     		statusCodes: [413],
     		backoffLimit: 3000
     	}
     }).json();
     ```

6. timeout: 整数或false, 默认10000. 获得响应的超时毫秒数, 包括重试. 因此超过timeout后就不会再重试了. 如果为false就表示不超时.

7. hooks: 对象`object<string, Function[]>`, 默认`{beforeRequest: [], beforeRetry: [], afterResponse: []}`. Hooks允许在请求的生命周期内做一些修饰, 钩子函数可以是异步的, 按顺序执行.

   * beforeRequest: 在请求发送之前修改请求对象, 此钩子函数接收`request`和`options`作为参数, 可在其中修改`request.headers`.

     这个钩子可以返回一个[Request](https://developer.mozilla.org/en-US/docs/Web/API/Request)对象来替换原先的请求, 或者返回一个[Response](https://developer.mozilla.org/en-US/docs/Web/API/Response)对象来避免发起http请求, 这可用于模仿请求或作其它用途, 如果返回了这两种请求之一, 其它的beforeRequest钩子就不会再调用了, 因此这个钩子函数应该放在最后.

   * beforeRetry: 在重试之前修改请求对象. 此钩子函数的参数是一个对象, 即`{request, options, error, retryCount}`.

     如果这个请求接收到了响应(注意这是重试, 因此已可能收到响应), 错误就是`HTTPError`类型, 并且这个响应对象位于`error.response`. 注意有些类型的错误, 比如网络错误, 并不会收到响应, 这时错误就不是`HTTPError`.

     可以通过抛出错误的方式阻止ky去重试, ky不会去处理, 此错误会传播到请求的初始器(request initiator)上. 这种情况下, 其它的钩子也不会再调用. 也可以通过返回`ky.stop`来阻止错误的传播, 但具有局限性, 见后面关于`ky.stop`的文档.

     ```js
     import ky from 'ky';
     const api = ky.extend({
     	hooks: {
     		beforeRequest: [
     			request => {
     				request.headers.set('X-Requested-With', 'ky');
     			}
     		]
     	}
     });
     const response = await api.get('https://example.com/api/users');
     ```

   * beforeError: 在抛出HTTPError之前修改这个HTTPError错误对象. 这个钩子函数接收一个HTTPError作为参数, 并且返回一个HTTPERROR实例.

     ```js
     import ky from 'ky';
     await ky('https://example.com', {
     	hooks: {
     		beforeError: [
     			error => {
     				const {response} = error;
     				if (response && response.body) {
     					error.name = 'GitHubError';
     					error.message = `${response.body.message} (${response.status})`;
     				}
     				return error;
     			}
     		]
     	}
     });
     ```

   * afterResponse: 用于读取并且修改响应, 其参数是`{request, options, response}`对象, 其中response是响应对象的拷贝. 如果返回值是[Response](https://developer.mozilla.org/en-US/docs/Web/API/Response)实例, 这个返回值就作为Ky的响应对象.

     ```js
     import ky from 'ky';
     const response = await ky('https://example.com', {
     	hooks: {
     		afterResponse: [
     			(_request, _options, response) => {
     				// You could do something with the response, for example, logging.
     				log(response);
     				// Or return a `Response` instance to overwrite the response.
     				return new Response('A different response', {status: 200});
     			},
     			// Or retry with a fresh token on a 403 error
     			async (request, options, response) => {
     				if (response.status === 403) {
     					// Get a fresh token
     					const token = await ky('https://example.com/token').text();
     					// Retry with the token
     					request.headers.set('Authorization', `token ${token}`);
     					return ky(request);
     				}
     			}
     		]
     	}
     });
     ```

8. throwHttpErrors: 布尔值, 默认`true`. 当响应码不是2xx时抛出HTTPError错误. 如果要让重定向(`307 Temporary Redirect`, `308 Permanent Redirect`)也抛出异常, 就要将[`redirect`](https://developer.mozilla.org/en-US/docs/Web/API/fetch#parameters)选项设置为`manual`.

   将这个选项设置为`false`有时也许会有用, 比如只是用于检查资源是否存在并且期望得到一个错误响应.

   注意: 如果设置成`false`, 这个错误响应就会认为是成功了, 请求不会再重试.

9. onDownloadProgress: 函数类型. 下载进程的事件句柄. 此函数接收`progress`和`chunk`两个参数:

   * `progress`对象, `{percent, transferredBytes, totalBytes}`, 如果不能检索到下载体的大小, `totalBytes`就为0.

   * `chunk`是`UintArray`实例, 开始时是空数组.

     ```js
     import ky from 'ky';
     const response = await ky('https://example.com', {
     	onDownloadProgress: (progress, chunk) => {
     		// Example output:
     		// `0% - 0 of 1271 bytes`
     		// `100% - 1271 of 1271 bytes`
     		console.log(`${progress.percent * 100}% - ${progress.transferredBytes} of ${progress.totalBytes} bytes`);
     	}
     });
     ```

10. parseJson: 函数, 默认是`JSON.parse()`. 用于指定用户定义的json解析函数. 其用例:

    1. 通过[bourne](https://github.com/hapijs/bourne)来解析JSON一边保护原型污染.
    2. 通过JSON.parse()的[reviver](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/parse)选项来解析JSON.

    ```js
    import ky from 'ky';
    import bourne from '@hapijs/bourne';
    const json = await ky('https://example.com', {
    	parseJson: text => bourne(text)
    }).json();
    ```

11. fetch: 函数, 默认是`fetch`. 用于指定用户定义的`fetch`函数, 必须与[Fetch API](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API)兼容. 用例:

    1. 使用自定义的`fetch`实现, 比如[isomorphic-unfetch](https://www.npmjs.com/package/isomorphic-unfetch).
    2. 使用由某些框架提供使用服务端渲染(SSR)的`fetch`包装函数.

    ```js
    import ky from 'ky';
    import fetch from 'isomorphic-unfetch';
    const json = await ky('https://example.com', {fetch}).json();
    ```

##### ky.extend(defaultOptions)

创建一个新的ky实例, 并且通过自己的选项覆盖默认选项. 与`ky.create()`相比, `ky.extend()`从其父对象集成了默认的选项.

```js
import ky from 'ky';

const url = 'https://sindresorhus.com';
const original = ky.create({
	headers: {
		rainbow: 'rainbow',
		unicorn: 'unicorn'
	}
});
const extended = original.extend({
	headers: {
		rainbow: undefined
	}
});
const response = await extended(url).json();

console.log('rainbow' in response); //=> false
console.log('unicorn' in response); //=> true
```

##### ky.create(defaultOptions)

使用全新的默认选项创建一个新的ky实例.

```js
import ky from 'ky';

// On https://my-site.com
const api = ky.create({prefixUrl: 'https://example.com/api'});
const response = await api.get('users/123');
//=> 'https://example.com/api/users/123'
const response = await api.get('/status', {prefixUrl: ''});
//=> 'https://my-site.com/status'
```

##### ky.stop

ky.stop是一个符号, 它可以通过`beforeRetry`钩子返回以便停止重试, 并且会(以短路方式)阻止剩余的beforeRetry钩子的调用.

需要注意的是, 返回这个符号会终止ky, 并且返回的响应是`undefined`, 因此在访问响应结果之前要先做检查, 或者使用[?.运算符](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/Optional_chaining)来判断. 此外, 它不与body方法兼容, 比如`.json()`, `.text()`, 因为没有没有响应来解析. 一般来说, **建议**抛出错误而*不是*返回这个ky.stop符号, 抛出错误也会导致ky的终止, 并能够避免这些限制.

ky.stop的一个合法的用例是, 用在创建带副作用的请求时阻止其重试 其中返回的数据不重要, 比如, 先服务端写入客户端行为的日志.

```js
import ky from 'ky';

const options = {
	hooks: {
		beforeRetry: [
			async ({request, options, error, retryCount}) => {
				const shouldStopRetry = await ky('https://example.com/api');
				if (shouldStopRetry) {
					return ky.stop;
				}
			}
		]
	}
};

// Note that response will be `undefined` in case `ky.stop` is returned.
const response = await ky.post('https://example.com', options);

// Using `.text()` or other body methods is not supported.
const text = await ky('https://example.com', options).text();
```

#### HTTPError

通过`instanceof`检测, 这个错误对象的`response`属性, 其值是[Response](https://developer.mozilla.org/en-US/docs/Web/API/Response)对象. `request`属性, 其值是[Request](https://developer.mozilla.org/en-US/docs/Web/API/Request)对象. `options`属性, 其值是标准化选项(normalized options, 即, 当使用`ky.create()`创建时传入的选项, 或者直接就是执行这个请求时传入的选项).

如果要读取HTTPError对象的实际响应, 只需在响应对象上调用对应的解析方法, 例如:

```js
try {
	await ky('https://example.com').json();
} catch (error) {
	if (error.name === 'HTTPError') {
		const errorJson = await error.response.json();
	}
}
```

#### TimeoutError

请求超时抛出的错误, 包含`request`属性, 其值是[Request](https://developer.mozilla.org/en-US/docs/Web/API/Request)对象.

#### **Tips**

##### 发送表单数据

使用ky发送表单数据与fetch一样, 只需将[FormData](https://developer.mozilla.org/en-US/docs/Web/API/FormData)实例传递个`body`选项. `Content-Type`标头会自动设置成`multipart/form-data`.

```js
import ky from 'ky';

// `multipart/form-data`
const formData = new FormData();
formData.append('food', 'fries');
formData.append('drink', 'icetea');

const response = await ky.post(url, {body: formData});
```

**注意注意注意**: 不要使用`URLSearchParams`, RN会报错`URLSearchParams.set is not implemented`. 示例中下面这段代码在RN中会报错(在web浏览器上正确):

```js
import ky from 'ky';

// `application/x-www-form-urlencoded`
const searchParams = new URLSearchParams();
searchParams.set('food', 'fries');
searchParams.set('drink', 'icetea');

const response = await ky.post(url, {body: searchParams});
```

##### 设置自定义的`Content-Type`

Ky对每个请求都会基于请求体中的数据自动设置合适的[Content-Type](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Type)标头. 但有些API需要自定义, 非标准的内容类型, 比如`application/x-amz-json-1.1`. 使用`headers`选项, 可以手动重载内容类型.

```js
import ky from 'ky';

const json = await ky.post('https://example.com', {
	headers: {
		'content-type': 'application/json'
	},
	json: {
		foo: true
	},
}).json();

console.log(json);
//=> `{data: '🦄'}`
```

##### 取消

Fetch(以及Ky)具有内置的请求取消支持, 通过[`AbortController API`](https://developer.mozilla.org/en-US/docs/Web/API/AbortController), [进一步参考](https://developer.chrome.com/blog/abortable-fetch). 例如:

```js
import ky from 'ky';

const controller = new AbortController();
const {signal} = controller;

setTimeout(() => {
	controller.abort();
}, 5000);

try {
	console.log(await ky(url, {signal}).text());
} catch (error) {
	if (error.name === 'AbortError') {
		console.log('Fetch aborted');
	} else {
		console.error('Fetch error:', error);
	}
}
```



### 安装react-hook-form

主页: [Github](https://github.com/react-hook-form/react-hook-form), [Nodejs](https://www.npmjs.com/package/react-hook-form), [官网](https://www.react-hook-form.com/).

resolvers: [Github](https://github.com/react-hook-form/resolvers), [Nodejs](https://www.npmjs.com/package/@hookform/resolvers).

joi: [官网](https://joi.dev/), [Github](https://github.com/hapijs/joi).

@hookform/error-message: https://react-hook-form.com/docs/useformstate/errormessage

安装:

```shell
npm i react-hook-form
npm i @hookform/resolvers
npm i joi
npm install @hookform/error-message
```

注意, 安装joi后会报错`ReferenceError: Property 'TextEncoder' doesn't exist, js engine: hermes`, 其原因是web和RN环境的差异, 问题的讨论见[Github](https://github.com/hapijs/joi/issues/2141).

解决方案: 安装`fast-text-encoding`, 同时导入joi的同时也要导入此库

```js
import { joiResolver } from "@hookform/resolvers/joi";
import "fast-text-encoding";
import Joi from "joi";
```

**踩坑记录**

注意`strip()`的使用, `strip()`的意思是从结果中把改记录去掉, 而不是剪裁空白符.

例如: `Joi.string().strip().min(2).max(10).required()`表示的是这条规则对应的值不出现在传递给react-hook-form的`handleSubmit`的回调函数参数中.

去掉空格后验证使用的是`trim()`, 因此这条规则是正确的: `Joi.string().trim().min(2).max(10).required()`, 但同时要注意, 这**不仅**会去掉空白符后进行验证, 传递给`handleSubmit`的数据两端的空白符**也**会去掉!

#### 重要: 使用[`useWatch`](https://react-hook-form.com/docs/usewatch)须知

由于`useWatch`的初始值是调用`useForm`时传入的`defaultValues`中的值, 因此, 如果使用`useWatch`监控的值初始化在对应hook form值的初始化之后, 监控值就不能正确地捕捉到当前的值. 因此`useWatch`的值必需在远程同步或本地数据恢复之前定义, 否则进入屏幕后计算公式是以初始值计算的结果. 即, 必需在useEffect(()=>{...}, [])之前定义`useWatch`.



### 安装`@react-native-community/netinfo`



```shell
npm i @react-native-community/netinfo
```





### 安装react-query

Github: https://github.com/TanStack/query

Npm: https://www.npmjs.com/package/@tanstack/react-query

```
npm i @tanstack/react-query
```

注意不要直接安装`react-query`, 见[文档](https://tanstack.com/query/latest/docs/react/installation).

#### 用法

1. 添加Provider

   ```js
   // 创建QueryClient实例, 然后在顶层组件添加Provider
   import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
   const queryClient = new QueryClient();
   function App() {
   	return (
   		<QueryClientProvider client={queryClient}>
   			<ThemeProvider storage={storage}>
   				<ApplicationNavigator />
   			</ThemeProvider>
   		</QueryClientProvider>
   	);
   }
   ```

2. `useQuery`

   ```jsx
   // useQuery
   import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
   const [currentId, setCurrentId] = useState(-1);
   const { isSuccess, isError, data, isFetching } = useQuery({
       queryKey: ['example', currentId],
       queryFn: () => {
           return fetchOne(currentId);
       },
       enabled: currentId >= 0,
   });
   useEffect(() => {
       if (isSuccess) {
           Alert.alert(t('example:welcome', data.name));
       }
   }, [isSuccess, data]);


   ```

3. `useMutation

   ```jsx
   import axios from 'axios';
   import { useMutation, useQuery, useQueryClient } from 'react-query';


   const Demo1 = () => {
       // 访问App QueryClientProvider提供的client
       const queryClient = useQueryClient();
       const query = useQuery('posts', () => axios.get('https://jsonplaceholder.typicode.com/posts'))
       console.log(query);
       const { data, isLoading, isError } = query;

       const { mutate } = useMutation(() => axios.delete('https://jsonplaceholder.typicode.com/posts/1'), {
           onSuccess: () => {
               // 错误处理和刷新
               queryClient.invalidateQueries('posts')
           },
       });

       if (isError) { return <div>error</div>; }
       if (isLoading) { return <div>loading</div>; }

       return (
           <>
               <button onClick={() => { mutate() }} > Delete </button>
               <ul> {(data?.data as unknown as dataType[])?.map(d => <li key={d.id}>{d.title}</li>)} </ul>
           </>
       )
   };
   ```

### 安装datetime picker

Date Picker和Time Picker库

#### react-native-paper-dates(不推荐)

https://github.com/web-ridge/react-native-paper-dates

https://www.reactnativepaperdates.com/

```
npm install react-native-paper-dates --save
```

注: 不推荐! 加载慢, 时间设置有问题.



#### datetimepicker(当前使用)

https://github.com/react-native-datetimepicker/datetimepicker

```
npm install @react-native-community/datetimepicker --save
```



### 安装``expo-image-picker`

**注意**: 由于expo带来其它编译问题, 2024/06/13已废弃该库.

官网: https://docs.expo.dev/versions/latest/sdk/imagepicker/

NPM: https://www.npmjs.com/package/expo-image-picker

按照官网文档步骤, 首先安装expo模块, 按照[自动安装](https://docs.expo.dev/bare/installing-expo-modules/#automatic-installation)方法, 运行`npx install-expo-modules@latest`, 但注意RN0.74下metro.config.js有不同的配置, 使用RN提供的配置即可. 同时, 在非mac下, pod安装肯定是失败的. 安装是否成功, [参考](https://docs.expo.dev/bare/installing-expo-modules/#verifying-installation), 但要先编译版本.

以下步骤参考文档: https://github.com/expo/expo/tree/sdk-51/packages/expo-image-picker

上一步完成后, 安装: `npx expo install expo-image-picker`

配置:

Android, F:\Zixv\lcmp.client\LowCarbonManagement\android\app\src\main\AndroidManifest.xml

```xml
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
```

IOS: F:\Zixv\lcmp.client\LowCarbonManagement\ios\LowCarbonManagement\Info.plist

```xml
<key>NSPhotoLibraryUsageDescription</key>
<string>Give $(PRODUCT_NAME) permission to save photos</string>
<key>NSCameraUsageDescription</key>
<string>Give $(PRODUCT_NAME) permission to access your camera</string>
<key>NSMicrophoneUsageDescription</key>
<string>Give $(PRODUCT_NAME) permission to use your microphone</string>
```

新建文件`app.config.js`(文档上说也可以添加在`app.json`, 这里使用新文件, 以免对老文件产生影响), 添加内容:

```js
{
  "expo": {
    "plugins": ["expo-image-picker"]
  }
}
```

配置插件(暂略): https://docs.expo.dev/versions/latest/sdk/imagepicker/#configuration-in-appjsonappconfigjs

举例: https://docs.expo.dev/versions/latest/sdk/imagepicker/#usage

安装后, 会遇到打release包失败的情况.

提示: AssertionError [ERR_ASSERTION]: Assets must have hashed files. Ensure the expo-asset plugin is installed.

安装包: npx expo install expo-asset

配置`app.json`(清空app.config.js内容):

```json
{
  "name": "LowCarbonManagement",
  "displayName": "智能低碳",
  "expo": {
    "name": "智能低碳",
    "slug": "LowCarbonManagement",
    "plugins": [
      [
        "expo-asset",
        {
          "assets": [
            "path/to/file.png",
            "path/to/directory"
          ]
        }
      ],
      "expo-image-picker"
    ]
  }
}
```

配置`metro.config.js`

```js
const {getDefaultConfig, mergeConfig} = require("@react-native/metro-config");

/**
 * Metro configuration
 * https://reactnative.dev/docs/metro
 *
 * @type {import('metro-config').MetroConfig}
 */
const config = {
    transformer: {
        assetPlugins: ['expo-asset/tools/hashAssetFiles'],
        getTransformOptions: async () => ({
            transform: {
                experimentalImportSupport: false,
                inlineRequires: false
            }
        })
    }
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
```

**打包**

首先清除原来内容: `./gradlew clean`

打包: `./gradlew assembleRelease `

大概需要13分钟.



### 安装ImagePicker

参考: https://dev.to/ajmal_hasan/react-native-fileimage-picker-1o2j

1. 安装`react-native-image-crop-picker`

https://github.com/ivpusic/react-native-image-crop-picker

```
yarn add react-native-image-crop-picker
```

配置`android/app/build.gradle`:

```groovy
defaultConfig {
    ...
    vectorDrawables.useSupportLibrary = true
}
```

配置`android/app/src/main/AndroidManifest.xml`:

```xml
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
```

2. 安装`react-native-permissions`

   https://github.com/zoontek/react-native-permissions

```
yarn add react-native-permissions
```

配置: Andoid需配置`android/app/src/main/AndroidManifest.xml`分配权限, 与上一步重复, 不需再配置. iOS有较复杂的配置, 本项目暂未涉及, 具体见官网.

3. 安装`react-native-device-info`

   https://github.com/react-native-device-info/react-native-device-info

```
yarn add react-native-device-info
```

配置: 官网包含一些配置项, 但不配置下暂时没发现影响使用.

4. 安装`react-native-fs`

```
yarn add react-native-fs
```

配置: 官网包含一些配置项, 但不配置下暂时没发现影响使用.

5. 安装`react-native-compressor`

```
yarn add react-native-compressor
```

配置: 官网包含一些配置项, 但不配置下暂时没发现影响使用.



### 安装`react-native-bottom-sheet`

https://github.com/gorhom/react-native-bottom-sheet

https://ui.gorhom.dev/components/bottom-sheet/

安装:

```
yarn add @gorhom/bottom-sheet@^4
```

`bottom-sheet`没有单独的配置, 但注意需要先安装`react-native-reanimated`和`react-native-gesture-handler`并做好配置.

`react-native-gesture-handler`安装好后需要嵌入`GestureHandlerRootView`, 修改程序入口`App.jsx`:

```jsx
import { GestureHandlerRootView } from "react-native-gesture-handler";
...
export default function App() {
    useOnlineManager();
    useAppState(onAppStateChange);

    return (
        <GestureHandlerRootView style={{ flex: 1 }}>
            <QueryClientProvider client={queryClient}>
                <PaperContainer/>
            </QueryClientProvider>
        </GestureHandlerRootView>
    );
}
```









## 错误记录

### 编译错误

通过`npx react-native doctor`查看问题源.

```
PS F:\Zixv\lcmp.client\LowCarbonManagement> npx react-native doctor
Common
 ✓ Node.js - Required to execute JavaScript code
 ✓ npm - Required to install NPM dependencies
 ● Metro - Metro Bundler is not running

Android
 ✖ Adb - No devices and/or emulators connected. Please create emulator with Android Studio or connect Android device.
 ✓ JDK - Required to compile Java code
 ✖ Android Studio - Required for building and installing your app on Android
 ✓ ANDROID_HOME - Environment variable that points to your Android SDK installation
 ✓ Gradlew - Build tool required for Android builds
 ✖ Android SDK - Required for building and installing your app on Android
   - Versions found: N/A
   - Version supported: 34.0.0

Errors:   3
Warnings: 1

Attempting to fix 3 issues...
```



### Unable to load script

红屏错误, Unable to load script.

解决方法:

```shell
mkdir android/app/src/main/assets

npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res
```

参考:

[1] https://www.youtube.com/watch?v=USPgq3A7Xyg

[2] https://www.cnblogs.com/allen12/p/15270449.html



### warn No apps connected

warn No apps connected. Sending "reload" to all React Native apps failed. Make sure your app is running in the simulator or on a phone connected via USB.

如果遇到这个问题, 仍然是执行上面命令, 可能能够解决:

```shell
npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res
```



### React has detected a change in the order of Hooks

https://legacy.reactjs.org/docs/hooks-rules.html

```shell
npm install eslint-plugin-react-hooks --save-dev
```

**eslint配置**

```js
// Your ESLint configuration
{
  "plugins": [
    // ...
    "react-hooks"
  ],
  "rules": {
    // ...
    "react-hooks/rules-of-hooks": "error", // Checks rules of Hooks
    "react-hooks/exhaustive-deps": "warn" // Checks effect dependencies
  }
}
```

#### 钩子规则(翻译)

1. **只能在顶层调用钩子**. 不能在循环, 条件, 嵌套函数中调用钩子. 只能在React函数的顶层使用. 遵循这条规则, 可以确保钩子与组件渲染保持相同的顺序. 这使得在具有多个useState和useEffect调用之间保持正确的钩子状态.
2. **只在React函数中调用钩子**. 不要在普通的的JS函数中调用钩子. 由此: 可以在React函数组件中调用钩子. 可以在自定义钩子中调用钩子.

#### 踩坑记录

在`userStoredState.js`文件中定义了一系列钩子:

```js
// 不允许
export const userName        = userStoredState((state) => state.name);
export const userAccount     = userStoredState((state) => state.account);
export const userPassword    = userStoredState((state) => state.password);
export const setUserName     = userStoredState((state) => state.setName);
export const setUserAccount  = userStoredState((state) => state.setAccount);
export const setUserPassword = userStoredState((state) => state.setPassword);
```

这是**不允许**的!!!

当是认为的是, 钩子是对状态的一个引用, 从而可以只在一个地方定义并导出, 在别的地方只需导入即可使用, 完全隐藏了钩子的细节. 这是错的, 违反了钩子规则. 钩子只能在需要使用的函数组件中定义.







## adb命令

```cmd
# 列出所有包
adb shell pm list packages
# 列出指定包
adb shell pm list packages | grep -i <some_idea>

# 删除包
adb uninstall <package_name>
# 举例
adb uninstall com.example.myapp
```



## 使用MuMu模拟器调试

1. 打开Android Studio
2. 安装MuMu模拟器, 设置模拟器的分辨率, 手机型号(仅第一次需要).
3. 启动MuMu模拟器
4. 到模拟器的shell目录, 例如`D:\Program Files\MuMuPlayer\shell`
5. 打开终端, 输入`adb connect 127.0.0.1:7555`
6. 此时在Android Studio的设备管理器上应能看到新增设备
7. 在工程目录下运行`yarn start`

注意: `adb connect`之前必须先启动模拟器.



## 升级React Native

除了遵循RN官方指南的修改, 可能仍然因为各种未知原因导致`yarn start`失败:

```
> Configure project :react-native-reanimated
Android gradle plugin: 8.1.1
Gradle: 8.3
```

最终的方法是生成全新的库和工程文件([参考](https://github.com/mrousavy/react-native-vision-camera/issues/2290)):

```
LowCarbonManagement> cd android
.gradlew clean # windows下是./gradlew.bat clean
# 删掉一下目录:
rm -rf android/.gradle android/.idea android/app/build android/build
# 删掉文件: package-lock.json yarn.lock
rm -rf package-lock.json yarn.lock
# 删掉node_modules目录:
rm -rf node_modules
```

之后安装包

```shell
npm install
```

用Android Studio打开工程目录(.../LowCarbonManagement/android), 经过较长时间等待安装工程文件.

有几个`.aar`文件很大, 比如`react-android-0.73.3-debug.aar`, 可以先在Android Studio上找到下载地址, 然后手动下载. 之后到`C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.react\react-android\0.73.3\`下手动复制文件.

下载地址: https://repo.maven.apache.org/maven2/com/facebook/react/react-android/0.73.3/

`react-android`目录包含: .module, .pom, -debug.aar, -release.aar四个子目录, 其中.aar文件需要手动下载.

`com.facebook.react\hermes-android`目录中的.aar文件如果不能在线更新出来也需要手动下载.

打包apk发布版需要`hermes-android-0.73.5-release.aar`

注, 所在本地目录的目录名同名的**`.sha1`**文件的内容.

重新启动调试:

```shell
yarn start --reset-cache
```

### 升级Gradle

升级React Native大版本(小版本也有可能)会要求升级Gradle, 但可以因为网络原因导致下载失败, 这时需要手动下载, 终端提示中会有gradle地址, 在android/gradle/wrapper/gradle-wrapper.properties中也配置了.

1. 手动下载这个文件, 例如`gradle-8.6-all.zip`.
2. 进入目录`C:\Users\<USER>\.gradle\wrapper\dists`, 找到对应版本, 进入对应目录, 运行`yarn start`, 这个目录会出现两个文件. 终止终端命令, 杀掉java进程, 将`gradle-8.6-all.zip`文件复制到此目录, 重新运行`yarn start`.

### 升级bug

`Could not move temporary workspace (...) to immutable location (...)`

运行`npx react-native doctor`查看问题.

### 升级到0.74

1. 按照升级助手修改配置: https://react-native-community.github.io/upgrade-helper/

2. 执行`ncu`, 升级库, 但不要升级`react`

3. 升级Gradle到8.6.

4. 安装yarn, 执行`yarn set version 3.6.4`, RN推荐默认的是3.6.4版本, 注意这条命令会修改`package.json`(`"packageManager": "yarn@3.6.4"`). [安装参考](https://yarnpkg.com/getting-started/install).

5. 按照前面的方法, 删掉android目录下的`.gradle`, `.idea`等目录.

6. 执行`yarn install`(不能用`npm install`).

7. 使用Android Studio打开工程文件, 重建工程, 这步主要是安装`.aar`文件, 可按照前面的方法手动安装.

8. 执行`yarn start --reset-cache`, 如果遇到报错`DebuggingOverlayRegistry.js: Class private methods are not enabled. Please add `@babel/plugin-transform-private-methods` to your configuration.`, 就添加库`yarn add @babel/plugin-transform-private-methods --dev`, 并且配置`babel.config.json`:

   ```js
       overrides: [{
           "plugins": [
               ["@babel/plugin-transform-private-methods", {
                   "loose": true
               }]
           ]
       }]
   ```

   参考: [npm](https://www.npmjs.com/package/@babel/plugin-transform-private-methods), [babel](https://babel.dev/docs/babel-plugin-transform-private-methods).

9. 继续执行`yarn start --reset-cache`.

补充: 在RN0.78.2中, 发现删掉`babel/plugin-transform-private-methods`不影响编译, 因此删掉了.

### 升级到0.75

方法同上.

### 升级到0.76

方法同上。



## 升级库

react native升级到github的release处查看升级助手.

其它库的升级运行`ncu`.

```shell
ncu -u react-i18next
```

`ncu -u XXX`只是更新`package.json`记录, 完成后执行`npm install`安装.

如果ncu报错, 提示`Error: read ECONNRESET`, 原因是网络连接失败, 解决方法是设置代理:

```shell
npm config set proxy http://localhost:7890
```

注意, 不能把所有库都更新到最新版本, RN本身依赖于某些库的特定版本, 特别是dev库, 更新时需要参考[`upgrade-helper`](https://react-native-community.github.io/upgrade-helper/)的`package.json`看哪些库不能更新.



## 导航

`<TouchableOpacity onPress={() => navigation.replace("RegisterScreen")}>`, 此处使用`navigation.replace`, 会将当前[导航状态](https://reactnavigation.org/docs/navigation-state/)的`index`对应的路由替换成目标屏幕, 这可能会导致`goBack`不能使用, 并报错:

```
 ERROR  The action 'GO_BACK' was not handled by any navigator.

Is there any screen to go back to?
```

修复方法是, 将`navigation.replace`改成`navigation.navigate`, 这样会增加`index`, 因此`goBack`可以使用.



## 类型检测PropTypes

Github: https://github.com/facebook/prop-types

文档: https://legacy.reactjs.org/docs/typechecking-with-proptypes.html





## 打包apk

### 修改App名称

1. 修改`./app.json`

   ```json
   {
     "name": "LowCarbonManagement",
     "displayName": "智能低碳"
   }
   ```

2. Android: 修改`./android/app/src/main/res/values/strings.xml`

   ```xml
   <resources>
       <string name="app_name">智能低碳</string>
   </resources>
   ```

   IOS: 修改`./iosLowCarbonManagement/Info.plist`, 如果有空格, 就用`&#x2007;`代替, 例如`My&#x2007;App`

   ```plist
   	<key>CFBundleDisplayName</key>
   	<string>智能低碳</string>
   ```

参考: https://stackoverflow.com/questions/34794679/change-app-name-in-react-native



### Logo

目录: `./android/app/src/main/res/mipmap-xxxx`各个目录下, 方形logo文件名名为`ic_launcher.png`, 圆形log文件名为`ic_launcher_round.png`. 各目录下分辨率分别为:

- 48*48 `ic_launcher.png` in `mipmap-mdpi`.
- 72*72 `ic_launcher.png` in `mipmap-hdpi`.
- 96*96 `ic_launcher.png` in `mipmap-xhdpi`.
- 144*144 `ic_launcher.png` in `mipmap-xxhdpi`.
- 192*192 `ic_launcher.png` in `mipmap-xxxhdpi`

Illustrator可以通过`文件 -> 导出 -> 导出为多种屏幕所用格式`, 点选"整篇文档", 导出到`ic_launcher`文件夹, 缩放从48px到196px, 之后会在`ic_launcher`文件夹下创建多个文件夹, 其中包含对应分辨率的png文件, 把这些文件复制到对应的工程目录中, apk安装后会显示正确的图标.

### App改名

修改App在手机上显示的名称.

Android: 修改`\android\app\src\main\res\values\strings.xml`

```xml
<resources>
    <string name="app_name">智能低碳</string>
</resources>
```

清缓, 尝试以下操作:

1. cd android && ./gradlew clean
2. yarn start --reset-cache
3. Android Studio, Invalidate Caches...
4. 删掉build目录

### 生成keystore文件

注意, React Native[官网](https://reactnative.dev/docs/signed-apk-android)和React Native[中文网](https://reactnative.cn/docs/signed-apk-android)关于生成签名密钥的名称不一样, 必要时两者都需要参考. 这里使用官网的名称.

首先生成密钥文件(假设密码是`"v0Wcg3pT"`):

```
D:\Dev\Java> .\jdk-17\bin\keytool -genkeypair -v -storetype PKCS12 -keystore my-upload-key.keystore -alias my-key-alias -keyalg RSA -keysize 2048 -validity 10000
```

这会在`D:\Dev\Java`目录下生成一个`my-upload-key.keystore`文件.

### 设置Gradle变量

修改`android/gradle.properties`文件(或者`~/.gradle/gradle.properties`文件), 在源文件末尾添加:

```gradle
MYAPP_UPLOAD_STORE_FILE=my-upload-key.keystore
MYAPP_UPLOAD_KEY_ALIAS=my-key-alias
MYAPP_UPLOAD_STORE_PASSWORD=v0Wcg3pT
MYAPP_UPLOAD_KEY_PASSWORD=v0Wcg3pT
```

### 给App的Gradle配置添加签名配置

修改`android/app/build.gradle`, 添加`android`的`signingConfig.release`和`buildTypes.release`配置:

```
signingConfigs {
    debug {
        storeFile file('debug.keystore')
        storePassword 'android'
        keyAlias 'androiddebugkey'
        keyPassword 'android'
    }
    release {
        if (project.hasProperty('MYAPP_UPLOAD_STORE_FILE')) {
            storeFile file(MYAPP_UPLOAD_STORE_FILE)
            storePassword MYAPP_UPLOAD_STORE_PASSWORD
            keyAlias MYAPP_UPLOAD_KEY_ALIAS
            keyPassword MYAPP_UPLOAD_KEY_PASSWORD
        }
    }
}

    buildTypes {
    debug {
        signingConfig signingConfigs.debug
    }
    release {
        // Caution! In production, you need to generate your own keystore file.
        // see https://reactnative.dev/docs/signed-apk-android.
        signingConfig signingConfigs.debug
        minifyEnabled enableProguardInReleaseBuilds
        proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
        signingConfig signingConfigs.release
    }
}
```

### Release和Debug版本使用不同包名

目的是让同一个设备上可以同时安装Release和Debug版.

``` AI Prompt
我使用react native开发的应用, 在同一台android设备上安装了debug版, 当我安装release版时, 系统提示我"软件包与现有软件包存在冲突", 我希望能够同时安装debug版和release版, 需要如何设置? 我的React Native是0.78.2, 请给出详细步骤.
```



1. 修改`android/app/build.gradle`

   ```gradle
       buildTypes {
           debug {
               // 添加这一行
               applicationIdSuffix ".debug"
               signingConfig signingConfigs.debug
           }
           release {
               // Caution! In production, you need to generate your own keystore file.
               // see https://reactnative.dev/docs/signed-apk-android.
               signingConfig signingConfigs.release
               minifyEnabled enableProguardInReleaseBuilds
               proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
               signingConfig signingConfigs.release
           }
       }
       packa
   ```



2. 创建文件目录`android/app/src/debug/res/values/strings.xml`

   ```xml
   <resources>
       <string name="app_name">你的应用名 Debug</string>
   </resources>
   ```



3. 运行(通过数据线直接编译安装到设备)

   ```bash
   # debug版
   npx react-native run-android --appIdSuffix debug
   # 或者：
   yarn android --appIdSuffix debug
   
   # release版
   npx react-native run-android --mode=release
   # 或者:
   yarn android --mode=release
   ```

   如果失败就清理缓存`./gradlew clean`

4. 构建 Debug APK

   ```bash
   # 方法一：使用 Gradle
   ./gradlew assembleDebug
   # 方法二：使用 React Native CLI (它会调用 Gradle)
   npx react-native build-android --mode=debug
   ```



5. 构建 Release APK

   ```bash
   # 方法一：使用 Gradle
   ./gradlew assembleRelease
   
   # 方法二：使用 React Native CLI (它会调用 Gradle)
   npx react-native build-android --mode=release
   
   # 方法三: 直接编译到设备
   npx react-native run-android --mode=release
   ```



6. b



### 生成发布版AAB

```
npx react-native build-android --mode=release
```

### 测试发布版构建

首先从测试设备上删除以安装的app.

```
yarn android --mode release
```

### APK发布版打包

打包需要`react-android-0.73.3-release.aar`文件, 位于`C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.react\react-android\0.73.3`目录, 通常该文件下载非常缓慢, 可以到https://repo.maven.apache.org/maven2/com/facebook/react/react-android/0.73.3/下载, 其本都目录名称是该文件的`.sha1`文件的内容.

同理, 还有`hermes-android-0.73.3-release.aar`

```
cd android
PS F:\Zixv\lcmp.client\LowCarbonManagement\android> .\gradlew.bat assembleRelease

> Configure project :react-native-reanimated
Android gradle plugin: 8.1.1
Gradle: 8.3

Deprecated Gradle features were used in this build, making it incompatible with Gradle 9.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

For more on this, please refer to https://docs.gradle.org/8.3/userguide/command_line_interface.html#sec:command_line_warnings in the Gradle documentation.

BUILD SUCCESSFUL in 19s
316 actionable tasks: 27 executed, 289 up-to-date
```

文件位于`android/app/build/outputs/apk/release/app-release.apk`

注意, 设备上安装release版apk之后, 必须手动删掉后才能安装debug版, 否则会报错:

```
FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':app:installDebug'.
> java.util.concurrent.ExecutionException: com.android.builder.testing.api.DeviceException: com.android.ddmlib.InstallException: INSTALL_FAILED_UPDATE_INCOMPATIBLE: Package com.lowcarbonmanagement signatures do not match previously installed version; ignoring!
```



### HTTP无法发送消息问题

安装生成的app-release.apk, 会发现无法发送消息, 其原因是React Native认为http不安全而默认禁用了. 解决方法是修改`android\app\src\main\AndroidManifest.xml`文件, 在`<application>`下添加`android:usesCleartextTraffic="true"`:

```xml
    <application
	  ...
      android:theme="@style/AppTheme"
      android:usesCleartextTraffic="true">
      <activity
	   ...
      </activity>
    </application>
```

### RN0.74打包尺寸太大问题

打开`android\app\src\main\AndroidManifest.xml`, 向`      android:extractNativeLibs="true"`添加`android:extractNativeLibs="true"`.

```xml
    <application
      android:name=".MainApplication"
      android:label="@string/app_name"
      android:icon="@mipmap/ic_launcher"
      android:roundIcon="@mipmap/ic_launcher_round"
      android:allowBackup="false"
      android:theme="@style/AppTheme"
      android:usesCleartextTraffic="true"
      android:extractNativeLibs="true">
      <activity
...
      </activity>
    </application>
```

Ref: https://github.com/facebook/react-native/issues/44291

从AGP 4.2.0k开始, 不推荐把`android:extractNativeLibs`配置在`AndroidManifest.xml`中, 而是配置在`android/app/build.gradle`, 因此首先要删掉`AndroidManifest.xml`中的配置项, 在`build.gradle`中添加`useLegacyPackaging`配置:

```
android {
    compileSdkVersion 33
    defaultConfig {
        // 其他配置
    }
    packagingOptions {
        jniLibs {
            useLegacyPackaging true // 相当于 extractNativeLibs="true"
        }
    }
}
```

- useLegacyPackaging true：等同于 extractNativeLibs="true"，压缩原生库并在安装时解压。
- useLegacyPackaging false：等同于 extractNativeLibs="false"，原生库未压缩，直接加载。

编辑完成后测试打包, 运行 `./gradlew clean` 和 `./gradlew assembleRelease`

参考: https://developer.android.com/guide/topics/manifest/application-element#extractNativeLibs



## Ngix反向代理

App服务器不能直接暴露到外网, 而要通过nginx进行代理:

在`nginx.conf`中配置:

```nginx
server {
    listen       5577;
    server_name  localhost;
    server_tokens off;
    client_max_body_size 30M;
    client_body_buffer_size 1M;

    root   /home/<USER>/projects/lcmp.server/www/;
    location / {
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_pass http://localhost:5000;
    }

    # Serve static files with nginx, not the Lisp app.
    # Serve http://url/static/*
    location /static/ {
        alias /home/<USER>/projects/lcmp.server/www/static/;
    }

    # But if the file has the listed extensions, this rule has the highest privilege
    # Serve http://url/*.*/*.apk|txt
    location ~ \.(apk|png|pdf) {
        root /home/<USER>/projects/lcmp.server/www/static/;
    }
}
```

其中, listen=5577表示暴露给外网的http端口号. `root`是nginx服务的根目录, 内部`location`中的root不能超出这个根目录范围. 第一个`location`用于代理app消息, 其中包含标头的设置, `proxy_pass http://localhost:5000`表示代理到端口号为5000的真实lisp服务器. 第二个`location`用于nginx直接服务`/static/`下的所有文件, 访问其中文件通过`http://url/static/1.txt`这种路径才能访问, 第三个`location`是服务某些特定的扩展名, 注意其`root`指向了`.../static/`目录, 因此对于`static/1.txt`的文件, 其访问路径是`http://url/1.txt`.

注意, 第二个location必需使用alias, 不能使用root, 在此由于路径与总root的一直, alias一行也可省略!

优先级, 第三个location> 第二个location > 第一个location. 因此, 假设有个文件`./www/static/1.png`, 访问它是通过`http://ipaddress:5577/1.png`, 而不是`http://ipaddress:5577/static/1.png`. 不满足第二第三各location的http请求都由第一个location去处理.

对于Hunchentoot的重定向有一个额外的问题, 处理方式见参考[1].

参考:

[1] https://stackoverflow.com/questions/72062476/how-to-redirect-to-https-when-hunchentoot-is-behind-a-reverse-proxy

[2] https://blog.ponto-dot.com/2009/08/18/hunchentoot-behind-proxy-server/

[3] https://lispcookbook.github.io/cl-cookbook/web.html

[4] https://docs.nginx.com/nginx/admin-guide/web-server/serving-static-content/

[5] https://learn.genieframework.com/docs/reference/workflow/nginx-reverse-proxy



## 安装React Native开发环境

1. 下载Android Studio.
2. 到React Native官网, 根据指南安装Android Studio.
3. 安装JDK, 将JDK加入系统路径.
4. 在Android Studio上安装Sdk(此步可以合并到第二步).
5. 添加环境变量ANDROID_HOME为`D:\Dev\Android\Sdk`.
6. 添加用户路径: `D:\Dev\Android\Sdk\platform-tools`.
7. 安装网易Mumu模拟器, 打开模拟器, 连接adb.
8. 从Git克隆项目到本地.
9. npm install
10. 用Android Studio打开项目下android目录, 重建android工程(因为要下载gradle等, 时间非常久).
11. (可选)运行D:\Zixv\lcmp.client\android> .\gradlew clean
12. 安装yarn, 运行`yarn start --reset-cache`

遇到网络问题:
```
Android gradle plugin: 8.1.1
Gradle: 8.3
IOException: https://dl.google.com/android/repository/addons_list-5.xml
java.net.ConnectException: Connection refused: connect
IOException: https://dl.google.com/android/repository/addons_list-4.xml
java.net.ConnectException: Connection refused: connect
IOException: https://dl.google.com/android/repository/addons_list-3.xml
java.net.ConnectException: Connection refused: connect
IOException: https://dl.google.com/android/repository/addons_list-2.xml
java.net.ConnectException: Connection refused: connect
IOException: https://dl.google.com/android/repository/addons_list-1.xml
java.net.ConnectException: Connection refused: connect
Failed to download any source lists!
IO exception while downloading manifest:
java.net.ConnectException: Connection refused: connect
        at java.base/sun.nio.ch.Net.connect0(Native Method)
        at java.base/sun.nio.ch.Net.connect(Net.java:579)
        at java.base/sun.nio.ch.Net.connect(Net.java:568)
```
如果遇到相同问题, 配置Android Studio: File > Settings > Appearance & Behavior > System Settings > HTTP Proxy, 改为No Proxy(或者按照上网软件的代理进行设置).
到`C:\Users\<USER>\.gradle`目录, 修改`gradle.properties`文件, 注释掉关于代理的信息:
```
# systemProp.http.proxyHost=
# systemProp.http.proxyPort=80
# systemProp.https.proxyHost=
# systemProp.https.proxyPort=80
```

此外, 设置Windows Terminal代理

```cmd
set http_proxy=http://127.0.0.1:7890
set https_proxy=http://127.0.0.1:7890
```
