import DateTimePicker, { DateTimePickerAndroid } from "@react-native-community/datetimepicker";
import React, { useEffect, useState } from "react";
import { Platform, Pressable, StyleSheet } from "react-native";
import { TextInput, useTheme } from "react-native-paper";
import { SafeAreaView } from "react-native-safe-area-context";
import { UTC1900To1970, UTC1970To1900, toTimeString } from "../utils/time";
//import PropTypes from "prop-types";

/**
 * 由于CellTextInput的左边有固定的文字标签,
 * 因此在这里, 原本用作TextInput的label的内容, 在此被placeholder取代
 * @param {Object} arg
 * @param {"date" | "time"} arg.mode picker mode, should be "date" or "time"
 * @param {string} arg.placeholder placeholder of the result displayed
 * @param {integer} arg.dialogState milliseconds since 1900-01-01, passed from the server
 * @param {(utc:integer)=>{}} arg.onDialogConfirm
 * @param {Object} arg.props
 * @param {bool} arg.props.disabled
 */
const ControlledCellDateTimePicker = ({ mode = "date", placeholder, dialogState, onDialogConfirm, editable = true, ...props }) => {

    const [visible, setVisible] = useState(false); //  对于非android系统有效

    const [labelValue, setLabelValue] = useState(dialogState ? toTimeString(new Date(UTC1900To1970(dialogState))).substring(0, 10) : "");

    const date = dialogState ? new Date(UTC1900To1970(dialogState)) : new Date();
    const onChange = (event, selectedDatetime) => {
        if (event.type === "set") {
            const newUTC1900 = UTC1970To1900(selectedDatetime.getTime());
            onDialogConfirm ? onDialogConfirm(newUTC1900) : undefined;
            setLabelValue(toTimeString(selectedDatetime).substring(0, 10));
        }
        setVisible(false);
    };

    useEffect(()=>{
        setLabelValue(dialogState ? toTimeString(new Date(UTC1900To1970(dialogState))).substring(0, 10) : "");
    }, [dialogState]);

    // for Android only
    const showAndroidPicker = () => {
        DateTimePickerAndroid.open({
            value: date,
            onChange: onChange,
            mode: mode,
            is24Hour: true,
        });
    };


    const theme = useTheme();
    const styles = StyleSheet.create({
        input: {
            width: "100%",
            backgroundColor: theme.colors.surface,
            fontSize: 18,
            //borderWidth: 1,
            //borderColor: "red",
        },
        error: {
            fontSize: 13,
            color: theme.colors.error,
            paddingTop: 8,
        },
    });

    return (
        <>
            <Pressable onPress={() => {
                //if (!props.disabled) {
                if (editable) {
                    Platform.OS === "android" ? showAndroidPicker() : setVisible(true);
                }
            }} >
                <TextInput
                    value={labelValue}  // 如果name为假值时就显示空串
                    placeholder={placeholder}
                    editable={false}
                    disabled={!editable}
                    style={styles.input}
                    selectionColor={theme.colors.primary}
                    underlineColor={theme.colors.elevation.level0}
                    outlineColor={theme.colors.elevation.level0} // 透明, 文本外框颜色
                    placeholderTextColor={theme.colors.elevation.level5} // 占位符文字颜色
                    mode={"outlined"}
                    right={editable && <TextInput.Icon icon="chevron-down" onPress={() => {
                        //if (!props.disabled) {
                        Platform.OS === "android" ? showAndroidPicker() : setVisible(true);
                    }} />}
                    {...props}
                />
            </Pressable>

            {(Platform.OS !== "android") && visible &&
                <SafeAreaView>
                    <DateTimePicker
                        testID="dateTimePicker"
                        value={date}
                        mode={mode}
                        is24Hour={true}
                        onChange={onChange}
                    />
                </SafeAreaView>
            }
        </>

    );
};

/*
ControlledCellDateTimePicker.propTypes = {
    mode:            PropTypes.string,
    placeholder:     PropTypes.string,
    onDialogConfirm: PropTypes.func,
    dialogState:     PropTypes.number,
    props:           PropTypes.shape({
        disabled: PropTypes.bool,
    }),
};
*/

export default ControlledCellDateTimePicker;
