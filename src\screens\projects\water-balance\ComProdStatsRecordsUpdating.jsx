import { joi<PERSON>esolver } from "@hookform/resolvers/joi";
import "fast-text-encoding";
import <PERSON><PERSON> from "joi";
import debounce from "lodash/debounce";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { useForm, useWatch } from "react-hook-form";
import { Alert, StyleSheet, View } from "react-native";
import { Divider, Snackbar } from "react-native-paper";
import Button from "../../../components/Button";
import ControlledTextInput from "../../../components/ControlledTextInput";
import { DialogToConfirm } from "../../../components/DialogToConfirm";
import HeaderBar from "../../../components/HeaderBar";
import { LoadingIndicator } from "../../../components/LoadingIndicator";
import { creatMMKVStore, makeStoredValueToFormValue } from "../../../services/local-storage";
import { callOneByOne, makeDataFeeder, parseIndustryCode } from "../../../utils";
import { checkPermits } from "../../../utils/permits";
import { isServerDataNewer, makeGetClientCversion, radioIdToObject } from "../../../utils/records";
import { validatorBase } from "../../../utils/validatorBase";
import ScreenWrapper from "../../ScreenWrapper";

// 新组件需要重新!!
import { UPD_WATER_BALANCE_RECORDS as pageMainKey } from "../../../config/keysConfig";
import { wbComProdStatStates as selectorStates } from "../../../hooks/selectorStates";
// 本表单无下拉输入
//import { wbWaterUsageUpdateStates as selectorStates } from "../../../hooks/selectorStates";
import { useShallow } from "zustand/shallow";
import {
    makeReqDelWaterBalanceRecords as recordDeleteQueryMaker,
    makeReqGetWaterBalanceRecords as recordSelectQueryMaker,
    makeReqUpdWaterBalanceRecords as recordUpdateQueryMaker,
} from "../../../api/projectRecordsQueries";
import { FormListNodesMapper } from "../../../components/FormListNodesMapper";
import { comProductionStatYearEnum_WithTips, comProductionStatYearEnum_WithoutTips, commonScaleEnum, findEnumItem } from "../../../config/waterBalance";
import { roundNearest, roundNearestTo } from "../../../utils/numeric";
import { onPreSubmitError } from "../../../utils/screens";
import { thisYear } from "../../../utils/time";


const dataFeeder = makeDataFeeder();

/**
 * 工业表格: 表: 生产情况统计表, 书: 企业近三年生产情况统计
 * 服务业对应表格是: ComOpStatsRecordsUpdating.jsx
 * 本表单的三种单位因子统一使用commonScaleEnum
 * 注意, 项目数据表单与工作台管理表单的数据有一个显著的区别:
 * 项目数据不需要像用户编号那样脱敏, 两边传输的数据是直接的, 不需要做{id, name}这种映射, 部分需要映射的可以在客户端本地完成,
 * @param {object} args
 * @param {object} args.navigation
 * @param {object} args.route
 * @returns
 */
const ComProdStatsRecordsUpdating = ({ navigation, route }) => {
    console.log("ComProdStatsRecordsUpdating pageMeta from nav:", route.params.pageMeta);
    console.log("ComProdStatsRecordsUpdating projMeta from nav:", route.params.projMeta);
    const projSubclass = route.params.projMeta.subclass; // 1表, 2书
    const projIndustry = parseIndustryCode(route.params.projMeta.industry); // 1 -> industry, 2 -> service
    const industryBookP = projIndustry === "industry" && projSubclass === 2;

    const { queryKwd, id: recordPubid, name: recordName, formType } = route.params.pageMeta; // recordPubid is the alias of id
    const saveButtonIcon = "content-save-all-outline";
    //const formDisabledGlobal = checkPermits() ? false : true; // old marked
    const formEditableGlobal = checkPermits() ? (formType === 0 ? true : false) : false;

    // 使用memoize来优化
    const { setStore, getStore, clearStore, setStoreObject } = useMemo(() => {
        console.log("creatMMKVStore memo:", pageMainKey.store, queryKwd, recordPubid);
        return creatMMKVStore(`${pageMainKey.store}/${queryKwd}/${recordPubid}`);
    }, [queryKwd, recordPubid]);

    const storedValueToFormValue = useRef(makeStoredValueToFormValue(getStore));
    const getClientCversion = useRef(makeGetClientCversion(() => getStore("mainCversion"), () => getStore("subCversion")));

    // 客户端cversion = `${mainCversion}.${subCversion}`
    (getStore("mainCversion") === undefined) ? setStore("mainCversion", 0) : undefined; // 客户端主版本号
    (getStore("subCversion")  === undefined) ? setStore("subCversion",  0) : undefined; // 客户端次版本号, 修改一次自增一次
    //old marked
    //let subCversion  = getStore("subCversion");  // 次版本号, 表示本地更改次数, 用于程序上对子版本号的增加, 用于判断与服务端版本的差异, 同时可用于判断避免重复从MMKV获取数据
    // 次版本号, 表示本地更改次数, 用于程序上对子版本号的增加, 用于判断与服务端版本的差异, 同时可用于判断避免重复从MMKV获取数据
    const subCversionRef = useRef(getStore("subCversion"));

    // 新组件需要修改!!
    const bottomBarLabels = { ok: "提交" };                    // 底部按钮显示文字
    const [snackBarMessage, setSnackBarMessage] = useState("更新表单遇到错误"); // 下方错误提示文字内容

    const [
        yearState,
        waterUnitScaleState,
        setYearState,
        setWaterUnitScaleState,
        resetSelectorStates
    ] = selectorStates(useShallow(state => [
        state.year,
        state.waterUnitScale,
        state.setYear,
        state.setWaterUnitScale,
        state.resetStates,
    ])); // radio组件状态


    const [confimDialogConfig, setConfirmDialogConfig] = useState({});       // 对话框信息, {title, text, okLabel, onOK}, 但显示对话框的变量在showConfirm

    // 新组件不需改动
    const [screenTitle,      setScreenTitle]      = useState(route.params.pageMeta.name); // 屏幕上方导航栏标题
    const [okButtonLoading,  setOkButtonLoading]  = useState(false);            // 右下方按钮图标是否旋转
    const [okButtonDisabled, setOkButtonDisabled] = useState(!checkPermits());  // 右下方按钮是否禁用
    const [showSnackbar,     setShowSnackbar]     = useState(false);            // 下方是否显示错误通知
    const [showConfirm,      setShowConfirm]      = useState(false);            // 是否显示确认对话框
    const [loadingIndicatorVisible, setLoadingIndicatorVisible] = useState(false); // 是否显示loading
    const [unitToggleState,  setUnitToggleState]  = useState(false);            // 用于控制切换单位后刷新数据
    const delQueryInfo = useRef({ id: recordPubid, name: recordName });         // 用于删除本记录的查询变量

    const comProductionStatYearEnum = industryBookP ? comProductionStatYearEnum_WithTips : comProductionStatYearEnum_WithoutTips;
    const yearDataProviderRef                = useRef(comProductionStatYearEnum);
    //const waterUnitScaleStateDataProviderRef = useRef(productOutputUnitScaleEnum);
    const unitScaleStateDataProviderRef      = useRef(commonScaleEnum);
    //const waterTypeStateDataProviderRef = useRef([{ id: 1, name: "自来水" }, { id: 2, name: "地表水" }, { id: 3, name: "地下水" }, { id: 4, name: "市政中水" }]);

    // 用于存储waterScale的值, 当unitToggleState发生改变时需要通过该值更新表单字段
    const waterUnitScaleRef = useRef(0);


    // Define form
    // 新组件需要重新定义!!
    const schema = Joi.object({
        name:         validatorBase.waterBalance.comProdStats.name,
        year:         validatorBase.waterBalance.comProdStats.year,
        testDays:     validatorBase.waterBalance.comProdStats.testDays,
        productName:  validatorBase.waterBalance.comProdStats.textField.required,
        productUnit:  validatorBase.waterBalance.comProdStats.textField.unrequired,
        materialTech: validatorBase.waterBalance.comProdStats.longTextField.required,
        //processRoute: validatorBase.waterBalance.comProdStats.textField,
        prodCapacity: validatorBase.waterBalance.comProdStats.floatField,
        actualOutput: validatorBase.waterBalance.comProdStats.floatField,
        waterIntake:  validatorBase.waterBalance.comProdStats.floatField,
        wipup:        validatorBase.waterBalance.comProdStats.floatField,
        others:       validatorBase.waterBalance.comProdStats.longTextField.unrequired,
        remarks:      validatorBase.waterBalance.comProdStats.largeTextField.unrequired,
        waterScale:   validatorBase.waterBalance.commons.waterScale,
    });

    // 新组件需要重新定义defaultValues!!
    const {
        handleSubmit,
        control,
        resetField,
        reset,
        formState: { errors },
        setValue,
        getValues,
    } = useForm({
        resolver: joiResolver(schema),
        //reValidateMode: "onBlur", // https://github.com/orgs/react-hook-form/discussions/10327
        defaultValues: { // 默认值在组件载入时会被更新, 这里的初始值正常情况下没有用处
            name:         "",
            year:         "",
            testDays:     "",
            productName:  "",
            productUnit:  "",
            materialTech: "",
            //processRoute: "",
            prodCapacity: "",
            actualOutput: "",
            waterIntake:  "",
            wipup:        "",
            others:       "",
            remarks:      "",
            waterScale:   "",
        },
    });

    // Query: select record
    const onRecordSelectSuccess = (data) => { // data: {"id": 3, "leader": 0, "name": "DEPT-03", "superior": 0}
        // 注意, 进入onSuccess之前就意味着服务端版本比客户端版本新, 因此要更新客户端主版本号
        console.log("onRecordSelectSuccess:", data);
        if (data.STATUS === 0) {
            setStore("mainCversion", data.DATA.cversion);
            setStore("subCversion", 0);
            const factor = 10 ** (data.DATA.waterScale || 0);

            const formObjects  = {
                name:         String(data.DATA.name),
                year:         String(data.DATA.year),
                testDays:     String(data.DATA.testDays),
                productName:  String(data.DATA.productName),
                productUnit:  String(data.DATA.productUnit),
                materialTech: String(data.DATA.materialTech),
                //processRoute: String(data.DATA.processRoute),
                prodCapacity: String(data.DATA.prodCapacity / factor),
                actualOutput: String(data.DATA.actualOutput / factor),
                waterIntake:  String(data.DATA.waterIntake  / factor),
                wipup:        String(data.DATA.wipup),
                others:       String(data.DATA.others),
                remarks:      String(data.DATA.remarks),
                waterScale:   String(data.DATA.waterScale || 0),
            };
            reset(formObjects);           // 重置react-form

            const storeObjects = checkPermits() && {
                ...formObjects,
                year:       radioIdToObject(yearDataProviderRef.current,                data.DATA.year),
                //waterScale: radioIdToObject(waterUnitScaleStateDataProviderRef.current, data.DATA.waterScale),
                waterScale: radioIdToObject(unitScaleStateDataProviderRef.current, data.DATA.waterScale),
                //waterType: radioIdToObject(waterTypeStateDataProviderRef.current, data.DATA.waterType),
            };
            storeObjects && setStoreObject(storeObjects, false); // 判断用户权限, 把服务端数据写到mmkv存储, 只有可写用户才需要本地存储, 不会首先清空老数据

            // 设置selector菜单状态
            setYearState(radioIdToObject(yearDataProviderRef.current, data.DATA.year));
            //setWaterUnitScaleState(radioIdToObject(waterUnitScaleStateDataProviderRef.current, data.DATA.waterScale));
            setWaterUnitScaleState(radioIdToObject(unitScaleStateDataProviderRef.current, data.DATA.waterScale));
            //setWaterTypeRadioState(radioIdToObject(waterTypeStateDataProviderRef.current, data.DATA.waterType));
            //setMainUseRadioState(radioIdToObject(mainUseStateDataProviderRef.current, data.DATA.mainUse));

            waterUnitScaleRef.current = data.DATA.waterScale || 0; // 根据服务端数据更新
            // 设置屏幕标题
            (screenTitle !== formObjects.name) && setScreenTitle(formObjects.name);
        } else  {
            console.log("STATUS !== 0", data.STATUS);
            setSnackBarMessage(`表单数据拉取发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        }
    };
    const onRecordSelectError = (err) => {
        console.log("Record selecting error:", err);
    };
    const onRecordSelectSettled = (err) => {
        loadingIndicatorVisible && setLoadingIndicatorVisible(false);
    };
    const recordSelectQuery = recordSelectQueryMaker([queryKwd, "get", recordPubid], onRecordSelectSuccess, onRecordSelectError, onRecordSelectSettled);

    // Query update
    // 新组件需要重新定义setDiagText!!
    const onHandleSubmit = (data) => {
        const factor = 10 ** Number(waterUnitScaleRef.current);
        setOkButtonDisabled(true);
        setOkButtonLoading(true);
        dataFeeder({
            ...data,
            name: projSubclass === 2 ? `${findEnumItem(data.year, comProductionStatYearEnum)?.name}/${data.productName}/${data.materialTech}` : `${data.productName}/${data.materialTech}`,
            cversion: getClientCversion.current(),
            statType: data.year < 365 ? 1 : 0, // 修改后, 凡是判断为当前所在年度都默认为测试期间数据
            prodCapacity: data.prodCapacity * factor,
            actualOutput: data.actualOutput * factor,
            waterIntake:  data.waterIntake  * factor,
        }); // append client cversion
        recordUpdateQuery.mutate();
    };

    // 新组件不需改动
    const onRecordUpdateSuccess = (data) => {
        console.log("response success, data: ", data);
        if (data.STATUS !== 0) {
            setSnackBarMessage(`表单更新发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        } else {
            // 设置确认框数据
            setConfirmDialogConfig({
                title: "更新表单",
                text: "表单信息更新成功!",
                okLabel: "确定",
                onOK: () => {
                    setShowConfirm(false);
                    recordSelectQuery.mutate();
                }
            });
            setShowConfirm(true);
        }
    };
    const onRecordUpdateError = (error) => {
        setSnackBarMessage(`表单更新发生错误, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const onRecordUpdateSettled = (data, error) => {
        setOkButtonDisabled(false);
        setOkButtonLoading(false);
    };
    const recordUpdateQuery = recordUpdateQueryMaker(dataFeeder, [queryKwd, "upd", recordPubid], onRecordUpdateSuccess, onRecordUpdateError, onRecordUpdateSettled);

    // query delete
    const recordDeleteOnSuccess = () => {
        clearStore();
        navigation.goBack();
    };
    const recordDeleteOnError = (error) => {
        setSnackBarMessage(`表单删除失败, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const recordDeleteQuery = recordDeleteQueryMaker([queryKwd, "del", recordPubid], recordDeleteOnSuccess, recordDeleteOnError);

    const deleteAlert = () => {
        return (
            Alert.alert("删除表单", `删除后不可撤销, 请确认是否删除: ${delQueryInfo.current?.name}?`, [
                { text: "取消" },
                { text: "确定", onPress: () => recordDeleteQuery.mutate() },
            ])
        );
    };

    const clearCache = () => {
        return (
            Alert.alert("清空本地缓存", "1. 用于未知原因导致数据无法提交的情况\r\n2. 未提交的内容将会丢失, 请做好数据截图备份\r\n3. 已提交到服务端的数据不受影响\r\n4. 确认清空请点击“确认”按钮", [
                { text: "取消" },
                { text: "确定", onPress: () => { clearStore(); navigation.goBack(); } },
            ])
        );
    };

    const actualOutput = Number(useWatch({ control, name: "actualOutput" }));
    const waterIntake = Number(useWatch({ control, name: "waterIntake" }));

    const updateGlobalState = debounce((actualOutput, waterIntake) => {
        //const factor = 10 ** Number(waterUnitScaleRef.current); // 连着单位因子相同, 因此不再需要作除
        const wipup = actualOutput === 0 ? 0 : waterIntake / actualOutput;
        setValue("wipup", `${roundNearestTo(wipup, 3)}`);
        setStore("wipup", getValues("wipup"));
        subCversionRef.current++;
    }, 100);

    // 注意, 这个useEffect必需在恢复本地存储之前运行, 否则初始化时不会触发
    useEffect(() => {
        updateGlobalState(actualOutput, waterIntake);
    }, [actualOutput, waterIntake]);


    // Hook: onMount fetch data
    useEffect(() => {
        // 优化方案: 根据用户权限进行mutate, 只有可写用户才需要本地存储
        const localCversion = getClientCversion.current();
        const remoteCversion = route.params.pageMeta.cversion;
        console.log("localCversion:", localCversion, "remoteSversion:", remoteCversion, "subCversionRef:", subCversionRef.current);

        const currentYear = thisYear();
        // 在此处理默认值, current的第二个参数是默认值, 如果是复合值, 还需要在下面设置组件的值.
        const restoreLocalData = () => {
            const formObjects = { // 默认值用户表单的数据显示
                name:         storedValueToFormValue.current("name"),
                year:         industryBookP ? storedValueToFormValue.current("year")     : storedValueToFormValue.current("year",     `${currentYear}`),
                testDays:     industryBookP ? storedValueToFormValue.current("testDays") : storedValueToFormValue.current("testDays", "7"),
                productName:  storedValueToFormValue.current("productName"),
                productUnit:  storedValueToFormValue.current("productUnit"),
                materialTech: storedValueToFormValue.current("materialTech"),
                //processRoute: storedValueToFormValue.current("processRoute"),
                prodCapacity: storedValueToFormValue.current("prodCapacity"),
                actualOutput: storedValueToFormValue.current("actualOutput"),
                waterIntake:  storedValueToFormValue.current("waterIntake"),
                wipup:        storedValueToFormValue.current("wipup"),
                others:       storedValueToFormValue.current("others"),
                remarks:      storedValueToFormValue.current("remarks"),
                waterScale:   storedValueToFormValue.current("waterScale", "0"),
            };
            reset(formObjects); // 重置react-form数据

            // 设置selector数据
            const defaultWaterUnitScale = 0;
            industryBookP || setYearState(getStore("year") || { id: currentYear, name: `${currentYear}` });
            //setWaterUnitScaleState(getStore("waterScale") || radioIdToObject(waterUnitScaleStateDataProviderRef.current, defaultWaterUnitScale));
            setWaterUnitScaleState(getStore("waterScale") || radioIdToObject(unitScaleStateDataProviderRef.current, defaultWaterUnitScale));
            //setWaterTypeRadioState(getStore("waterType"));
            //setMainUseRadioState(getStore("mainUse"));

            waterUnitScaleRef.current = getStore("waterScale")?.id || defaultWaterUnitScale; // 根据本地存储更新
        };

        if (formType === 1) {
            recordSelectQuery.mutate();
        } else if (localCversion === 0 && remoteCversion > 0) {
            console.log("Local cversion === 0, serve may have something nontrivial initialized, query mutate!");
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        } else if (isServerDataNewer(localCversion, remoteCversion) && subCversionRef.current > 0) { // 服务端有更新数据, 但本地有修改
            console.log("Client cversion fell behind the server cversion, someone might modified the database, choose to use the data version!");
            Alert.alert("注意", "由于其他人向服务器提交了数据, 服务端数据比本地数据新, 请选择使用哪份数据?", [
                { text: "使用本地数据", onPress: () => { setStore("mainCversion", remoteCversion); restoreLocalData(); } },
                { text: "使用服务端数据", onPress: () => { setLoadingIndicatorVisible(true); recordSelectQuery.mutate(); } },
            ]);
        } else if (isServerDataNewer(localCversion, remoteCversion)) { // localCversion==0时要mutate是因为服务端可能在创建记录时生成了必要数据
            console.log("Server cversion may be newer, query mutate:", localCversion, remoteCversion);
            setLoadingIndicatorVisible(true);
            recordSelectQuery.mutate();
        }
        else {
            console.log("Local cversion >= server cversion, query will not mutate!");
            restoreLocalData();
        }
    }, []);

    // Hook: refresh data by unit changing
    useEffect(() => {
        //if (waterUnitScaleState.id !== undefined && waterUnitScaleRef.current !== waterUnitScaleState.id) { // waterUnitScaleState may not be updated in realtime
        if (unitToggleState === true ) {
            const oldFactor = 10 ** Number(waterUnitScaleRef.current);
            const newFactor = 10 ** Number(waterUnitScaleState.id);
            const changedFields = { // 默认值用户表单的数据显示
                prodCapacity: String(roundNearest(getValues("prodCapacity") * oldFactor / newFactor)),
                actualOutput: String(roundNearest(getValues("actualOutput") * oldFactor / newFactor)),
                waterIntake:  String(roundNearest(getValues("waterIntake")  * oldFactor / newFactor)),
            };
            for (const [key, val] of Object.entries(changedFields)) {
                setValue(key, val); // set react hook form
                setStore(key, val); // set mmkv
            }

            waterUnitScaleRef.current = waterUnitScaleState.id || 0;
            setUnitToggleState(false);
        }
    }, [unitToggleState]);

    const refreshUnits = () => { setUnitToggleState(true); };
    const calcUnit = (unit, unitStatObj = waterUnitScaleState) => {
        if (unitStatObj.id === 0) { return unit; }
        else if (unitStatObj.id === 4) { return "万" + unit; }
        else if (unitStatObj.id === 3) { return "千" + unit; }
        else if (unitStatObj.id === 2) { return "百" + unit; }
        else if (unitStatObj.id === 1) { return "十" + unit; }
        else { /*log.warn("Invalid unit scale: %s, id: %s", unit, unitStatObj.id);*/ return unit; }
    };
    // 统一配置提示文字和
    const fieldTips = {
        prodCapacity: { toolTip: "单位指具体产品的基础单位, 例如吨, 件等。", placeholder: "" },
        actualOutput: { toolTip: "单位指具体产品的基础单位, 例如吨, 件等。", placeholder: "" },
        wipup:        { toolTip: "单位指具体产品的基础单位, 例如吨, 件等。", placeholder: "" },
    };
    const FieldsConfig_1_book = [
        {
            inputs: [
                //{ name: "name",         label: "表单名称",      unit: "",               type: "PLAIN", editable: true, placeholder: "", multiline: true, },
                { name: "productName",  label: "产品名称",      unit: "",               type: "PLAIN", editable: true,  placeholder: "", },
                { name: "productUnit",  label: "产品单位",      unit: "",               type: "PLAIN", editable: true,  placeholder: "件, 吨, m³, ...", },
                { name: "year",         label: "年份",         unit: "",               type: "RADIO", editable: true,  placeholder: "", selectorState: yearState,           setSelectorState: setYearState,           dataProvider: comProductionStatYearEnum,},
                { name: "waterScale",   label: "产能/产量/取水量单位放缩因子", unit: "",   type: "RADIO", editable: true, placeholder: "", selectorState: waterUnitScaleState,  setSelectorState: setWaterUnitScaleState, dataProvider: commonScaleEnum, cb: refreshUnits },
                { name: "materialTech", label: "原材料/工艺路线",unit: "",               type: "PLAIN", editable: true,  placeholder: "", props: {multiline: true}, },
                { name: "prodCapacity", label: "产能",         unit: calcUnit("单位/a"),  type: "PLAIN", editable: true,  placeholder: fieldTips.prodCapacity.placeholder, toolTip: fieldTips.prodCapacity.toolTip, },
                { name: "actualOutput", label: "实际产量",      unit: calcUnit("单位/a"),  type: "PLAIN", editable: true,  placeholder: fieldTips.actualOutput.placeholder, toolTip: fieldTips.actualOutput.toolTip, },
                { name: "waterIntake",  label: "取水量",       unit: calcUnit("m³"),    type: "PLAIN", editable: true,  placeholder: "", },
                { name: "wipup",        label: "单位产品取水量", unit: "m³/单位",           type: "PLAIN", editable: false,  placeholder: fieldTips.wipup.placeholder, toolTip: fieldTips.wipup.toolTip, },
                { name: "others",       label: "其它",         unit: "",               type: "PLAIN", editable: true,   placeholder: "", },
            ]
        },
    ];
    const FieldsConfig_1_table = [
        {
            inputs: [
                //{ name: "name",         label: "表单名称",      unit: "",             type: "PLAIN", editable: true, placeholder: "", multiline: true, },
                { name: "productName",  label: "产品名称",        unit: "",             type: "PLAIN", editable: true,  placeholder: "", },
                { name: "productUnit",  label: "产品单位",      unit: "",               type: "PLAIN", editable: true,  placeholder: "件, 吨, m³, ...", },
                //{ name: "year",         label: "年份",           unit: "",             type: "RADIO", editable: true,  placeholder: "", selectorState: yearState,           setSelectorState: setYearState,           dataProvider: comProductionStatYearEnum,},
                { name: "testDays",     label: "测试时段",        unit: "天",           type: "PLAIN", editable: true,  placeholder: "", },
                { name: "waterScale",   label: "产能/产量/取水量单位放缩因子",  unit: "",  type: "RADIO", editable: true, placeholder: "", selectorState: waterUnitScaleState,  setSelectorState: setWaterUnitScaleState, dataProvider: commonScaleEnum, cb: refreshUnits },
                { name: "materialTech", label: "生产原料",        unit: "",              type: "PLAIN", editable: true,  placeholder: "", props: {multiline: true}, },
                { name: "prodCapacity", label: "设计年产量",       unit: calcUnit("单位"),  type: "PLAIN", editable: true,  placeholder: fieldTips.prodCapacity.placeholder, toolTip: fieldTips.prodCapacity.toolTip, },
                { name: "actualOutput", label: "测试期间实际产量",  unit: calcUnit("单位"),  type: "PLAIN", editable: true,  placeholder: fieldTips.actualOutput.placeholder, toolTip: fieldTips.actualOutput.toolTip, },
                { name: "waterIntake",  label: "测试期间取水量(不含非生产用水)",    unit: calcUnit("m³"), type: "PLAIN", editable: true,  placeholder: "", },
                { name: "wipup",        label: "单位产品取水量",   unit: "m³/单位",          type: "PLAIN", editable: false,  placeholder: fieldTips.wipup.placeholder, toolTip: fieldTips.wipup.toolTip, },
                { name: "others",       label: "其它",           unit: "",              type: "PLAIN", editable: true,   placeholder: "", },
            ]
        },
    ];

    const FieldsConfig_2_book = [];
    const FieldsConfig_2_table = [];

    const FieldsConfig_3_book = [
        // 其它部分
        {
            inputs: [
                //{ name: "others",  label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "", },
                //{ name: "remarks", label: "备注", unit: "", type: "PLAIN", editable: true, placeholder: "", },
            ]
        },
    ];
    const FieldsConfig_3_table = [
        // 其它部分
        {
            inputs: [
                //{ name: "others",  label: "其它", unit: "", type: "PLAIN", editable: true, placeholder: "", },
                //{ name: "remarks", label: "备注", unit: "", type: "PLAIN", editable: true, placeholder: "", },
            ]
        },
    ];

    //const FieldsConfig = [...FieldsConfig_1, ...FieldsConfig_2, ...FieldsConfig_3];
    let FieldsConfig = [];
    switch(projSubclass) {
        case 1: // 表
            FieldsConfig = [...FieldsConfig_1_table, ...FieldsConfig_2_table, ...FieldsConfig_3_table];
            break;
        case 2: // 书
            FieldsConfig = [...FieldsConfig_1_book, ...FieldsConfig_2_book, ...FieldsConfig_3_book];
            break;
        default:
            console.warn("Unknown subclass:", projSubclass);
            FieldsConfig = [];
            break;
    }

    return (
        <View style={{ flex: 1 }}>

            <HeaderBar
                //title={screenTitle}
                navigation={navigation}
                goBackCallback={() => {
                    callOneByOne(
                        //() => { _mainCversionGetter=undefined; _subCversionGetter=undefined;},
                        resetSelectorStates,
                    );
                    /*callOneByOne(clearStore, resetSelectorStates)*/  // 返回前清空mmkv存储因为临时数据不再需要, 重置状态使得页面切换后不会使用前面的数据
                }}
                menuItemArray={[{ title: "删除表单", action: deleteAlert }, { title: "清空缓存", action: clearCache }]}
            />

            <ScreenWrapper contentContainerStyle={styles.container}>

                <View style={styles.formEntry}>

                    <FormListNodesMapper
                        fieldsConfig={FieldsConfig}
                        subCversionRef={subCversionRef}
                        control={control}
                        setStore={setStore}
                        resetField={resetField}
                        errors={errors}
                        setSnackBarMessage={setSnackBarMessage}
                        setShowSnackbar={setShowSnackbar}
                        editable={formEditableGlobal}
                        rowStyle={styles.rowContainer}
                    />

                    <Divider />
                    {formType === 0 && <ControlledTextInput
                        name="remarks"
                        rowLabel="备注"
                        control={control}
                        placeholder=""
                        onChangeText={(text) => {
                            subCversionRef.current++;
                            setStore("subCversion", subCversionRef.current);
                            setStore("remarks", text);
                        }}
                        onClearText={() => {
                            subCversionRef.current++;
                            setStore("subCversion", subCversionRef.current);
                            setStore("remarks", "");
                            resetField("remarks", { defaultValue: "" });
                        }}
                        editable={formEditableGlobal}
                        mode="flat"
                        multiline={true}
                        formError={errors}
                        style={styles.rowContainerLongText}
                        //disabled={formDisabledGlobal}
                        required={false}
                    />}
                    <Divider bold={true}/>

                </View>

                <View style={styles.button}>
                    <Button mode="contained"
                        disabled={okButtonDisabled}
                        loading={okButtonLoading}
                        icon={saveButtonIcon}
                        onPress={handleSubmit(onHandleSubmit, (formErr) => onPreSubmitError(formErr, getStore, "ComProdStatsRecordsUpdating"))}
                    >{bottomBarLabels.ok}</Button>
                </View>
            </ScreenWrapper>

            <LoadingIndicator
                title={""}
                message={"加载中....."}
                visible={loadingIndicatorVisible}
                onClose={()=>setLoadingIndicatorVisible(false)}
            />

            <Snackbar
                style={styles.snackBar}
                visible={showSnackbar}
                onDismiss={() => setShowSnackbar(false)}
                onIconPress={() => setShowSnackbar(false)}
                duration={Snackbar.DURATION_LONG}
            >
                {snackBarMessage}
            </Snackbar>

            <DialogToConfirm
                visible={showConfirm}
                title={confimDialogConfig.title ? confimDialogConfig.title : ""}
                text={confimDialogConfig.text ? confimDialogConfig.text : ""}
                onOK={confimDialogConfig.onOK ? confimDialogConfig.onOK : () => setShowConfirm(false)}
                okBtnLabel={confimDialogConfig.okLabel ? confimDialogConfig.okLabel : "确认"}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    rowContainer:{
        firstColumn: {
            flex: 5,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 6,
        // /borderWidth: 1,
        // height: 40,
        },
    },
    rowContainerLongText: {
        firstColumn: {
            flex: 1,
            //borderWidth: 1,
            //height: 40,
            content: {
                fontSize: 18,
            }
        },
        secondColumn: {
            flex: 4,
            // /borderWidth: 1,
            // height: 40,
        },
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        width: "80%",
        alignSelf: "center",
    },
    formEntry: {
        margin: 8,
    },
    snackBar: {
        flexDirection: "column",
    },
});

export default ComProdStatsRecordsUpdating;
