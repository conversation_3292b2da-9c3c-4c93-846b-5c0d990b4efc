import React, { useEffect, useRef, useState } from "react";
import { Divider, Appbar, useTheme, Snackbar } from "react-native-paper";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { StyleSheet, View, Alert } from "react-native";
import { useForm } from "react-hook-form";
import { joiResolver } from "@hookform/resolvers/joi";
import "fast-text-encoding";
import <PERSON><PERSON> from "joi";
import ScreenWrapper from "../ScreenWrapper";
import HeaderBar from "../../components/HeaderBar";
import BottomBarButton from "../../components/BottomBarButton";
import { DialogToConfirm } from "../../components/DialogToConfirm";
import ControlledTextInput from "../../components/ControlledTextInput";
import ControlledRadioInputWithQuery from "../../components/ControlledRadioInputWithQuery";
import ControlledCheckboxInputWithQuery from "../../components/ControlledCheckboxInputWithQuery";
import cloneDeep from "lodash/cloneDeep";
import { creatMMKVStore } from "../../services/local-storage";
import { callOneByOne, makeDataFeeder } from "../../utils";
import { validatorBase } from "../../utils/validatorBase";

// 新组件需要重新!!
import { ORG_DEPARTMENT_UPDATE as pageMainKey } from "../../config/keysConfig";
import { queryGetDepartmentPage as pageFetchQuery } from "../../api/selectingQueries";
import { makeRequestUpdateDepartment as updateQueryClient } from "../../api/updatingQueries";
import { makeRequestDeleteDepartment as deleteQueryClient } from "../../api/deletingQueries";
import { makeRequestListingDepartments as allDeptsQueryClient } from "../../api/listingQueries";
import { makeRequestListingUsers       as allUsersQueryClient } from "../../api/listingQueries";
//import { queryGetDepartmentMembers } from "../../api/selectingQueries";
import { deptUpdateState } from "../../hooks/selectorStates";
import { useShallow } from "zustand/shallow";
import { onPreSubmitError } from "../../utils/screens";


// 新组件需要重新设置. 注意这里虽然设置了存储 但实际上没使用存储内容,
const { setStore, getStore, clearStore, setStoreObject } = creatMMKVStore(pageMainKey.store);
const dataFeeder = makeDataFeeder();

const saveButtonIcon = "content-save-all-outline";

const DepartmentsUpdating = ({ navigation, route }) => {
    //console.log("Page info passed from nav:", route.params.pageMeta);

    // render++;
    const { bottom, left, right } = useSafeAreaInsets();
    const theme = useTheme();
    const height = theme.isV3 ? 80 : 56;

    // 新组件需要修改!!
    const bottomBarLabels = { ok: "更新", cancel: "取消" };                    // 底部按钮显示文字
    const [snackBarMessage, setSnackBarMessage] = useState("更新部门遇到错误"); // 下方错误提示文字内容
    const [
        superiorState,
        membersState,
        leaderState,
        viceLeaderState,
        setSuperiorState,
        setMembersState,
        setLeaderState,
        setViceLeaderState,
        resetStates, // 离开页面前重置状态, 否则进入新的更新页面会闪现前面使用的数据
    ] = deptUpdateState(useShallow(state => [state.superior, state.members, state.leader, state.viceLeader, state.setSuperior, state.setMembers, state.setLeader, state.setViceLeader, state.resetStates])); // radio组件状态
    const [confimDialogConfig, setConfirmDialogConfig] = useState({});       // 对话框信息, {title, text, okLabel, onOK}, 但显示对话框的变量在showConfirm

    // 新组件不需改动
    const [okButtonLoading, setOkButtonLoading] = useState(false);           // 右下方按钮图标是否旋转
    const [okButtonDisabled, setOkButtonDisabled] = useState(false);         // 右下方按钮是否禁用
    const [cancelButtonDisabled, setCancelButtonDisabled] = useState(false); // 左下方按钮是否禁用
    const [showSnackbar, setShowSnackbar] = useState(false);                 // 下方是否显示错误通知
    const [showConfirm, setShowConfirm] = useState(false);                   // 是否显示确认对话框
    const delQueryInfo  = useRef({});                                        // 用于删除本记录的查询变量

    const memberDataProviderRef = useRef([]); // 成员数据记录在引用中, 用作负责人的数据源, 必需初始化为空数组

    const formDefaults  = useRef({});
    const storeDefaults = useRef({});
    const selectorDefaults = useRef({});
    const onPageInfoQuerySuccess = (data) => { // data: {"id": 3, "leader": 0, "name": "DEPT-03", "superior": 0}
        console.log("onPageInfoQuerySuccess................", data);
        if (data.STATUS === 0) {
            delQueryInfo.current  = { id: data.DATA.id, name: data.DATA.name };
            formDefaults.current  = {
                //id: data.DATA.id,
                name:       data.DATA.name,
                superior:   data.DATA.superior,
                members:    data.DATA.members ? data.DATA.members.filter(item=>item).map(item => item.id) : [],
                leader:     data.DATA.leader.id,
                viceLeader: data.DATA.viceLeader.id,
            };
            storeDefaults.current = {
                //id: data.DATA.id,
                name:       data.DATA.name,
                superior:   { id: data.DATA.superior, name: data.DATA.superiorName || "" }, // 上级为0时服务端返回的名称是false
                members:    data.DATA.members.filter(item=>item),
                leader:     { id: data.DATA.leader.id, name: data.DATA.leader.name },
                viceLeader: { id: data.DATA.viceLeader.id, name: data.DATA.viceLeader.name },
            };
            selectorDefaults.current = {
                superior:   { id: data.DATA.superior, name: data.DATA.superiorName || "" },
                members:    data.DATA.members.filter(item=>item),
                leader:     { id: data.DATA.leader.id, name: data.DATA.leader.name },
                viceLeader: { id: data.DATA.viceLeader.id, name: data.DATA.viceLeader.name },
            };
            //console.log("form default members:", formDefaults.current.members);
            reset(formDefaults.current);                                     // 重置react-form
            setStoreObject(storeDefaults.current);                           // 设置mmkv存储
            // 设置selector菜单状态
            setSuperiorState(cloneDeep(selectorDefaults.current.superior));
            setLeaderState(cloneDeep(selectorDefaults.current.leader));
            setViceLeaderState(cloneDeep(selectorDefaults.current.viceLeader));
            setMembersState(cloneDeep(selectorDefaults.current.members));
            // 设置部门负责人选项的默认数据
            memberDataProviderRef.current = cloneDeep(selectorDefaults.current.members);
        }
    };
    const pageInfoQuery = pageFetchQuery(route.params.pageMeta.id, onPageInfoQuerySuccess); // pageMeta was set in ListingTemplate.jsx

    // 新组件需要重新定义!!
    const schema = Joi.object({
        //id:       validatorBase.deptId,
        name:       validatorBase.deptName,
        superior:   validatorBase.deptId,
        members:    validatorBase.deptMembers.unrequired,
        leader:     validatorBase.userPubid.unrequired,
        viceLeader: validatorBase.userPubid.unrequired,
    });

    // 新组件需要重新定义defaultValues!!
    const {
        handleSubmit,
        control,
        resetField,
        reset,
        formState: { errors },
    } = useForm({
        resolver: joiResolver(schema),
        //reValidateMode: "onBlur", // https://github.com/orgs/react-hook-form/discussions/10327
        defaultValues: {
            //id: 0,
            name: "",
            superior: 0,
            members: [],
            leader: "",
            viceLeader: "",
        },
    });

    // 新组件需要重新定义setDiagText!!
    const onHandleSubmit = (data) => {
        setCancelButtonDisabled(true);
        setOkButtonDisabled(true);
        setOkButtonLoading(true);
        dataFeeder(data);
        submitQuery.mutate();
    };
    // 新组件不需改动
    const onCancel = () => {
        clearStore();
        // 每增加一个下拉框都要设置各自的默认值
        reset(formDefaults.current);                       // 重置react-form
        setStoreObject(storeDefaults.current);             // 重置mmkv存储
        // 重置下拉组件状态
        setSuperiorState(selectorDefaults.current.superior);
        setLeaderState(selectorDefaults.current.leader);
        setViceLeaderState(selectorDefaults.current.viceLeader);
        setMembersState(selectorDefaults.current.members);
    };

    // 新组件不需改动
    const commitOnSuccess = (data) => {
        console.log("response success, data: ", data);
        if(data.STATUS !== 0) {
            setSnackBarMessage(`部门更新发生错误, 错误码: ${data.STATUS}`);
            setShowSnackbar(true);
        } else {
            // 设置确认框数据
            setConfirmDialogConfig({
                title: "更新部门",
                text: "部门信息更新成功!",
                okLabel: "确定",
                onOK: () => { pageInfoQuery.mutate(); setShowConfirm(false); }});
            setShowConfirm(true);
        }
    };
    const commitOnError = (error) => {
        setSnackBarMessage(`部门更新发生错误, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const commitOnSettled = (data, error) => {
        setCancelButtonDisabled(false);
        setOkButtonDisabled(false);
        setOkButtonLoading(false);
    };
    const submitQuery = updateQueryClient(dataFeeder, route.params.pageMeta.id, commitOnSuccess, commitOnError, commitOnSettled);

    useEffect(() => {
        pageInfoQuery.mutate();
    }, []);

    const deleteOnSuccess = () => {
        navigation.goBack();
    };
    const deleteOnError = (error) => {
        setSnackBarMessage(`部门删除失败, 错误消息: ${error.message}`);
        setShowSnackbar(true);
    };
    const deleteQuery = deleteQueryClient(delQueryInfo.current?.id, deleteOnSuccess, deleteOnError);

    const deleteAlert = () =>{
        return (
            Alert.alert("删除部门", `请确认是否删除: ${delQueryInfo.current?.name}?`, [
                { text: "取消" },
                { text: "确定", onPress: () => deleteQuery.mutate() },
            ])
        );
    };

    return (
        <View style={{ flex: 1 }}>

            <HeaderBar
                title={"更新部门"}
                navigation={navigation}
                goBackCallback={() => callOneByOne(clearStore, resetStates)} // 返回前清空mmkv存储因为临时数据不再需要, 重置状态使得页面切换后不会使用前面的数据
                menuItemArray={[{ title: "删除部门", action: deleteAlert }]}
            />

            <ScreenWrapper contentContainerStyle={styles.container} style={{marginBottom: height+ bottom}}>
                {/*<Text>Render: {render}</Text>*/}

                <View style={styles.formEntry}>
                    <ControlledTextInput
                        rowLabel="部门名称"
                        control={control}
                        name="name"
                        placeholder="请填写部门名称"
                        disabled={false}
                        onChangeText={(text) => setStore("name", text)}
                        onClearText={() => {
                            setStore("name", "");
                            resetField("name", { defaultValue: "" });
                        }}
                        editable={true}
                        mode="outlined"
                        formError={errors}
                        required={true}
                        multiline={true}
                    />

                    <ControlledRadioInputWithQuery
                        name="superior"
                        rowLabel="上级部门"
                        control={control}
                        placeholder="请拉选上级部门"
                        disabled={false}
                        onDialogConfirm={(obj) => { setStore("superior", obj); setSuperiorState(obj);}}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        queryClient={allDeptsQueryClient}
                        dataProvider={allDeptsQueryClient}
                        setFetchFailMessage={setSnackBarMessage}
                        setDisplayFailBar={setShowSnackbar}
                        dialogState={superiorState}
                        setDialogState={setSuperiorState}
                        defaultNullOptionsTip={"未找到部门数据, 请创建部门后再尝试!"}
                    />

                    <ControlledCheckboxInputWithQuery
                        name="members"
                        rowLabel="部门成员"
                        control={control}
                        placeholder="请拉选部门成员"
                        onDialogConfirm={(obj) => {
                            console.log("confirm members: ", obj);
                            setStore("members", obj);
                            setMembersState(obj);
                            memberDataProviderRef.current = obj;
                        }}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        //queryClient={allUsersQueryClient}
                        dataProvider={allUsersQueryClient}
                        setFetchFailMessage={setSnackBarMessage}
                        setDisplayFailBar={setShowSnackbar}
                        dialogState={membersState}
                        setDialogState={setMembersState}
                        defaultNullOptionsTip={"未找到成员数据, 请检查您的网络或联系管理员!"}
                    />

                    <ControlledRadioInputWithQuery
                        name="leader"
                        rowLabel="部门负责人"
                        control={control}
                        placeholder="请拉选部门负责人"
                        onDialogConfirm={(obj) => {
                            console.log("confirm leader: ", obj);
                            setStore("leader", obj);
                            setLeaderState(obj);
                        }}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        //dataProvider={(onSuccess, onError, onSettled) => queryGetDepartmentMembers(route.params.pageMeta.id, onSuccess, onError, onSettled)}
                        dataProvider={memberDataProviderRef}
                        setFetchFailMessage={setSnackBarMessage}
                        setDisplayFailBar={setShowSnackbar}
                        dialogState={leaderState}
                        setDialogState={setLeaderState}
                        defaultNullOptionsTip={"未找到部门成员数据, 请拉选部门成员后再尝试."}
                    />

                    <ControlledRadioInputWithQuery
                        name="viceLeader"
                        rowLabel="部门负责人(副)"
                        control={control}
                        placeholder="请拉选部门负责人(副)"
                        onDialogConfirm={(obj) => {
                            console.log("confirm vice leader: ", obj);
                            setStore("viceLeader", obj);
                            setLeaderState(obj);
                        }}
                        okBtnLabel={"确定"}
                        cancelBtnLabel={"取消"}
                        formError={errors}
                        //dataProvider={(onSuccess, onError, onSettled) => queryGetDepartmentMembers(route.params.pageMeta.id, onSuccess, onError, onSettled)}
                        dataProvider={memberDataProviderRef}
                        setFetchFailMessage={setSnackBarMessage}
                        setDisplayFailBar={setShowSnackbar}
                        dialogState={viceLeaderState}
                        setDialogState={setViceLeaderState}
                        defaultNullOptionsTip={"未找到部门成员数据, 请拉选部门成员后再尝试."}
                    />

                </View>

            </ScreenWrapper>

            <Appbar
                style={[
                    styles.bottom,
                    {
                        height: height + bottom,
                    },
                    theme.isV3 && {
                        backgroundColor: theme.colors.elevation.level2,
                    },
                ]}
                safeAreaInsets={{ bottom, left, right }}
                theme={{ mode: "adaptive" }}
            //mode='center-aligned'
            >

                <View style={styles.bottomBarContainer}>
                    <Divider />
                    <View style={styles.bottomBarComponents}>
                        <BottomBarButton
                            label={bottomBarLabels.cancel}
                            disabled={cancelButtonDisabled}
                            onPress={onCancel}
                        />
                        <BottomBarButton
                            label={bottomBarLabels.ok}
                            loading={okButtonLoading}
                            disabled={okButtonDisabled}
                            icon={saveButtonIcon}
                            onPress={handleSubmit(onHandleSubmit, (formErr) => onPreSubmitError(formErr, getStore, "DepartmentsUpdating"))}
                        />
                    </View>
                </View>
            </Appbar>

            <Snackbar
                style={styles.snackBar}
                visible={showSnackbar}
                onDismiss={() => setShowSnackbar(false)}
                onIconPress={() => setShowSnackbar(false)}
                duration={Snackbar.DURATION_LONG}
            >
                {snackBarMessage}
            </Snackbar>

            <DialogToConfirm
                visible={showConfirm}
                title={confimDialogConfig.title ? confimDialogConfig.title : ""}
                text={confimDialogConfig.text ? confimDialogConfig.text : ""}
                onOK={confimDialogConfig.onOK ? confimDialogConfig.onOK : () => setShowConfirm(false)}
                okBtnLabel={confimDialogConfig.okLabel ? confimDialogConfig.okLabel : "确认"}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        //paddingHorizontal: 6,
        //borderWidth: 1,
        //borderColor: "black",
    },
    firstColumn: {
        flex: 2,
        //borderWidth: 1,
        //height: 40,
        content: {
            fontSize: 18,
        }
    },
    secondColumn: {
        flex: 3,
        // /borderWidth: 1,
        // height: 40,
    },
    bottom: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomBarContainer: {
        width: "100%",
        height: "100%",
    },
    bottomBarComponents: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        //borderWidth: 1,
        //borderColor: "red",
        paddingVertical: 10,
        paddingHorizontal: 15,
        height: "100%",
    },
    button: {
        flex: 1,
        marginHorizontal: 6
    },
    formEntry: {
        margin: 8,
    },
    snackBar: {
        flexDirection: "column",
    },
});

export default DepartmentsUpdating;
